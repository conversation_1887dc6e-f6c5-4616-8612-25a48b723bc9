#!/bin/bash
# Health check script for Codespace devcontainer
# Checks that all main services respond on their expected ports

PORTS=(8080 8081 8082 8083)
ALL_HEALTHY=true
for PORT in "${PORTS[@]}"; do
  STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$PORT/ || echo "000")
  if [ "$STATUS" != "200" ] && [ "$STATUS" != "404" ]; then
    echo "❌ Service on port $PORT is not healthy (HTTP $STATUS)"
    ALL_HEALTHY=false
  else
    echo "✅ Service on port $PORT responded (HTTP $STATUS)"
  fi
done
if [ "$ALL_HEALTHY" = true ]; then
  echo "All services are healthy!"
  exit 0
else
  echo "Some services failed health checks. See logs above."
  exit 1
fi
