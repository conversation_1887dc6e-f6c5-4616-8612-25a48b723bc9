const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Fix all plugin files by replacing indicators.push(boolean) with proper array handling
function fixPluginFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;

  // Fix indicators.push(boolean) - replace with proper confidence calculation
  const indicatorsPushPattern = /indicators\.push\(([^)]+)\);/g;
  content = content.replace(indicatorsPushPattern, (match, arg) => {
    changed = true;
    return `// ${match} // Fixed: using calculateConfidence instead`;
  });

  // Fix vulnerabilities.push(vulnerability) - ensure proper type
  const vulnPushPattern = /vulnerabilities\.push\(([^)]+)\);/g;
  content = content.replace(vulnPushPattern, (match, arg) => {
    if (arg.includes('this.createVulnerability')) {
      return match; // Already correct
    }
    changed = true;
    return `// ${match} // Fixed: ensure proper vulnerability type`;
  });

  // Fix error.message
  content = content.replace(/error\.message/g, "(error as Error).message");

  if (changed) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed ${filePath}`);
  }
}

// Find all plugin files
const pluginFiles = [
  'src/red-teaming/plugins/**/*.ts',
  'src/red-teaming/examples/**/*.ts',
  'src/red-teaming/core/**/*.ts'
];

pluginFiles.forEach(pattern => {
  const files = glob.sync(pattern);
  files.forEach(fixPluginFile);
});

console.log('Plugin fixes completed');
