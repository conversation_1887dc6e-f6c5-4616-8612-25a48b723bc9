
# Run Playwright tests
# echo "🔄 Running Playwright tests"
# ./scripts/e2e-docker-compose.sh local

# Detect changed folders since last push
echo "🔍 Detecting changed folders..."
CHANGED_FOLDERS=$(git diff --name-only HEAD origin/$(git rev-parse --abbrev-ref HEAD) 2>/dev/null | grep -E "workspace/" | awk -F/ '{ print $1"/"$2"/"$3 }' | sort | uniq | tr '\n' ',' | sed 's/,$//')

if [ -z "$CHANGED_FOLDERS" ]; then
  echo "🔍 No changes detected in workspace folders, checking for uncommitted changes..."
  CHANGED_FOLDERS=$(git diff --name-only HEAD | grep -E "workspace/" | awk -F/ '{ print $1"/"$2"/"$3 }' | sort | uniq | tr '\n' ',' | sed 's/,$//')
fi

if [ -z "$CHANGED_FOLDERS" ]; then
  echo "🔍 No changes detected in workspace folders, checking last commit..."
  CHANGED_FOLDERS=$(git diff --name-only HEAD HEAD~1 | grep -E "workspace/" | awk -F/ '{ print $1"/"$2"/"$3 }' | sort | uniq | tr '\n' ',' | sed 's/,$//')
fi

if [ -n "$CHANGED_FOLDERS" ]; then
  echo "🔄 Running tests for changed folders: $CHANGED_FOLDERS"
  cd ./workspace/clients/tests/
  CHANGED_FOLDERS="$CHANGED_FOLDERS" npm run start:dev:local
else
  echo "⚠️ No changes detected in workspace folders, running all tests..."
  cd ./workspace/clients/tests/
  npm run start:dev:local
fi
