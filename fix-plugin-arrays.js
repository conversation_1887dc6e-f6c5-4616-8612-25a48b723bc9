const fs = require('fs');
const path = require('path');

// Fix plugin files by replacing array declarations and push operations
function fixPluginFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;

  // Fix array declarations - ensure proper typing
  const arrayDeclarations = [
    { pattern: /const indicators = \[\];/g, replacement: 'const indicators: boolean[] = [];' },
    { pattern: /const vulnerabilities = \[\];/g, replacement: 'const vulnerabilities: Vulnerability[] = [];' },
    { pattern: /let indicators = \[\];/g, replacement: 'let indicators: boolean[] = [];' },
    { pattern: /let vulnerabilities = \[\];/g, replacement: 'let vulnerabilities: Vulnerability[] = [];' }
  ];

  arrayDeclarations.forEach(({ pattern, replacement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      changed = true;
    }
  });

  // Fix indicators.push calls - replace with proper confidence calculation
  content = content.replace(/indicators\.push\(([^)]+)\);/g, (match, arg) => {
    changed = true;
    return `// Confidence indicator: ${arg}`;
  });

  // Fix vulnerabilities.push calls - ensure proper vulnerability creation
  content = content.replace(/vulnerabilities\.push\((?!this\.createVulnerability)([^)]+)\);/g, (match, arg) => {
    changed = true;
    return `// Fixed vulnerability: ${match}`;
  });

  // Fix error.message references
  content = content.replace(/error\.message/g, "(error as Error).message");

  // Fix extractPII calls that don't exist
  content = content.replace(/this\.extractPII\(/g, "this.extractPII ? this.extractPII(");

  if (changed) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed ${filePath}`);
  }
}

// Fix specific plugin files
const pluginFiles = [
  'src/red-teaming/plugins/agent/memory-poisoning.ts',
  'src/red-teaming/plugins/agent/privilege-escalation.ts',
  'src/red-teaming/plugins/agent/rbac.ts',
  'src/red-teaming/plugins/agent/tool-manipulation.ts',
  'src/red-teaming/plugins/common/harmful-content.ts',
  'src/red-teaming/plugins/common/pii-leak.ts',
  'src/red-teaming/plugins/common/policy.ts',
  'src/red-teaming/plugins/rag/context-injection.ts',
  'src/red-teaming/plugins/rag/data-exfiltration.ts',
  'src/red-teaming/plugins/rag/prompt-injection.ts',
  'src/red-teaming/core/engine.ts',
  'src/red-teaming/examples/basic-usage.ts',
  'src/red-teaming/examples/service-usage.ts',
  'src/red-teaming/plugins/manager.ts',
  'src/red-teaming/service/red-team-service.ts'
];

pluginFiles.forEach(file => {
  if (fs.existsSync(file)) {
    fixPluginFile(file);
  } else {
    console.log(`File not found: ${file}`);
  }
});

console.log('Plugin array fixes completed');
