{"result": [{"id": "0337a8705197453693ed5f801f01d784", "paused": false, "description": "Allow All OPTIONs", "action": "skip", "ref": "fe3b129433344a92b0aa66abfb34c4ac", "filter": {"id": "1aaba111cbfa40a98c6dfad46b6b7a8d", "expression": "(http.request.method eq \"OPTIONS\")", "paused": false}, "created_on": "2025-04-28T05:08:37Z", "modified_on": "2025-05-01T11:18:33Z"}, {"id": "f4b084d640b44474b672eceda407bd35", "paused": false, "description": "Enforce mTLS authentication (except OPTIONS) - TEMPORARILY DISABLED", "action": "block", "ref": "6689d726011240d79cc5f43f19557930", "filter": {"id": "e797ca75f4f64c8dbeb62bdced70016d", "expression": "(http.request.method ne \"OPTIONS\") and (\n  (not cf.tls_client_auth.cert_verified and http.host eq \"api.stage.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"api.dev.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"api.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"open-parse.stage.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"open-parse.dev.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"open-parse.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"pyannote.stage.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"pyannote.dev.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"pyannote.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"ffmpeg.stage.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"ffmpeg.dev.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"ffmpeg.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"webhook.stage.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"webhook.dev.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"webhook.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"live.stage.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"live.dev.divinci.app\") or\n  (not cf.tls_client_auth.cert_verified and http.host eq \"live.divinci.app\")\n)", "paused": false}, "created_on": "2025-04-28T05:18:38Z", "modified_on": "2025-05-01T11:18:35Z"}], "success": true, "errors": [], "messages": [], "result_info": {"page": 1, "per_page": 25, "count": 2, "total_count": 2, "total_pages": 1}}