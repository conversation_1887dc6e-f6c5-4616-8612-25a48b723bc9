{"version": "2.0.0", "tasks": [{"label": "Start Mock API Servers", "type": "shell", "command": "node", "args": ["mock-api-servers.js"], "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "Start Web Client", "type": "shell", "command": "npm", "args": ["run", "webpack:codespace"], "options": {"cwd": "${workspaceFolder}/workspace/clients/web"}, "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "Start Public API", "type": "shell", "command": "npm", "args": ["run", "start:dev"], "options": {"cwd": "${workspaceFolder}/workspace/servers/public-api"}, "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "Start Public API Live", "type": "shell", "command": "npm", "args": ["run", "start:dev"], "options": {"cwd": "${workspaceFolder}/workspace/servers/public-api-live"}, "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "Start Public API Webhook", "type": "shell", "command": "npm", "args": ["run", "start:dev"], "options": {"cwd": "${workspaceFolder}/workspace/servers/public-api-webhook"}, "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "Start All Services", "dependsOrder": "parallel", "dependsOn": ["Start Mock API Servers", "Start Web Client", "Start Public API", "Start Public API Live", "Start Public API Webhook"]}]}