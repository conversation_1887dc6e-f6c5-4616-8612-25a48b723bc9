{"name": "Divinci Server - TypeScript Node.js", "image": "mcr.microsoft.com/devcontainers/typescript-node:1-22", "features": {"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {"moby": false, "dockerDashComposeVersion": "v2"}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-playwright.playwright", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-azuretools.vscode-docker", "GitHub.copilot", "GitHub.copilot-chat"], "settings": {"typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.workingDirectories": ["workspace/clients", "workspace/servers", "workspace/resources"], "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/build/**": true, "**/.git/**": true}}}}, "forwardPorts": [8080, 9080, 8081, 8083, 27017, 6379], "portsAttributes": {"8080": {"label": "Web Client", "onAutoForward": "notify"}, "9080": {"label": "Public API", "onAutoForward": "notify"}, "8081": {"label": "Public API Live", "onAutoForward": "notify"}, "8083": {"label": "Webhook API", "onAutoForward": "notify"}, "27017": {"label": "MongoDB", "onAutoForward": "silent"}, "6379": {"label": "Redis", "onAutoForward": "silent"}}, "postCreateCommand": "pnpm install", "postStartCommand": "/bin/bash .devcontainer/postStart.sh", "containerEnv": {"COMPOSE_BAKE": "true", "NODE_ENV": "development", "ENVIRONMENT": "development"}, "remoteUser": "node"}