const fs = require('fs');
const path = require('path');

// Comprehensive fix for all remaining TypeScript errors
function fixAllErrors(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;

  // Fix error.message references
  if (content.includes('error.message') && !content.includes('(error as Error).message')) {
    content = content.replace(/error\.message/g, "(error as Error).message");
    changed = true;
  }

  // Fix error.stack references
  if (content.includes('error.stack') && !content.includes('(error as Error).stack')) {
    content = content.replace(/error\.stack/g, "(error as Error).stack");
    changed = true;
  }

  // Fix error.name references
  if (content.includes('error.name') && !content.includes('(error as Error).name')) {
    content = content.replace(/error\.name/g, "(error as Error).name");
    changed = true;
  }

  // Fix console.error(error) to console.error(error as Error)
  content = content.replace(/console\.error\("([^"]+)", error\)/g, 'console.error("$1", error as Error)');
  content = content.replace(/console\.error\('([^']+)', error\)/g, "console.error('$1', error as Error)");

  // Fix throw error to throw error as Error
  content = content.replace(/throw error;/g, 'throw error as Error;');

  if (changed) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed all errors in ${filePath}`);
  }
}

// Get all TypeScript files in the red-teaming directory
function getAllTsFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        traverse(fullPath);
      } else if (item.endsWith('.ts')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Fix all TypeScript files in the red-teaming directory
const redTeamingDir = 'src/red-teaming';
if (fs.existsSync(redTeamingDir)) {
  const tsFiles = getAllTsFiles(redTeamingDir);
  
  tsFiles.forEach(file => {
    fixAllErrors(file);
  });
  
  console.log(`Processed ${tsFiles.length} TypeScript files`);
} else {
  console.log('Red teaming directory not found');
}

console.log('All error fixes completed');
