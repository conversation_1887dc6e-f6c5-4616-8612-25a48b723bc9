/**
 * Red Teaming as a Service (RTaaS) Usage Examples
 */

import {
  RedTeamService,
  DivinciRedTeamIntegration,
  ServiceConfig,
  AssessmentRequest,
  AssessmentFilter,
} from "../service";
import { ConfigTemplates } from "../core/config";

/**
 * Example service configuration
 */
const serviceConfig: ServiceConfig = {
  maxConcurrentAssessments: 5,
  defaultAssessmentTimeout: 300000, // 5 minutes
  queueProcessingInterval: 10000, // 10 seconds
  reportRetentionDays: 90,
  enableWebhooks: true,
  enableScheduling: true,
  enableMetrics: true,
  rateLimiting: {
    enabled: true,
    requestsPerMinute: 60,
    requestsPerHour: 1000,
  },
  storage: {
    type: "mongodb",
    connectionString: "mongodb://localhost:27017/redteam",
    options: {},
  },
};

/**
 * Example 1: Basic Service Setup and Assessment Creation
 */
async function basicServiceUsage() {
  console.log("🚀 Setting up Red Team Service...");

  // Initialize service
  const service = new RedTeamService(serviceConfig);
  const integration = new DivinciRedTeamIntegration(service);

  // Create an assessment
  const assessmentRequest: AssessmentRequest = {
    name: "RAG Security Assessment",
    description: "Comprehensive security test of RAG system",
    config: ConfigTemplates.ragSecurity({
      endpoint: "https://api.example.com/rag",
      purpose: "Service-based RAG assessment",
      numTests: 50,
    }),
  };

  try {
    const response = await service.createAssessment(
      "client-123",
      assessmentRequest
    );

    console.log("✅ Assessment created:");
    console.log(`- ID: ${response.assessment.id}`);
    console.log(`- Status: ${response.assessment.status}`);
    console.log(
      `- Estimated Duration: ${Math.round(response.estimatedDuration! / 1000)}s`
    );
    console.log(`- Queue Position: ${response.queuePosition}`);

    return response.assessment.id;
  } catch (error) {
    console.error("❌ Failed to create assessment:", (error as Error).message);
    throw error as Error;
  }
}

/**
 * Example 2: White Label Integration
 */
async function whiteLabelIntegration() {
  console.log("🏷️ Testing White Label Integration...");

  const service = new RedTeamService(serviceConfig);
  const integration = new DivinciRedTeamIntegration(service);

  try {
    // Assess a white label release
    const assessmentId = await integration.assessWhiteLabelRelease(
      "whitelabel-demo",
      "release-v2.1.0",
      "client-456",
      {
        assessmentType: "comprehensive",
        numTests: 75,
        customPolicies: [
          "The system must not disclose any proprietary business information",
          "All responses must comply with GDPR data protection requirements",
        ],
      }
    );

    console.log(`✅ White label assessment created: ${assessmentId}`);

    // Monitor progress
    let completed = false;
    while (!completed) {
      await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait 5 seconds

      const progress = await service.getAssessmentProgress(
        assessmentId,
        "client-456"
      );
      if (progress) {
        console.log(
          `📊 Progress: ${progress.completedTests}/${progress.totalTests} tests completed`
        );
        console.log(
          `🚨 Vulnerabilities found: ${progress.vulnerabilitiesFound}`
        );

        const assessment = await service.getAssessment(
          assessmentId,
          "client-456"
        );
        if (assessment?.status === "completed") {
          completed = true;
          console.log("✅ Assessment completed!");

          // Export report
          const htmlReport = await service.exportReport(
            assessmentId,
            "client-456",
            "html"
          );
          console.log("📄 HTML report generated");
        }
      }
    }

    return assessmentId;
  } catch (error) {
    console.error("❌ White label integration failed:", (error as Error).message);
    throw error as Error;
  }
}

/**
 * Example 3: AI Assistant Assessment
 */
async function aiAssistantAssessment() {
  console.log("🤖 Testing AI Assistant Assessment...");

  const service = new RedTeamService(serviceConfig);
  const integration = new DivinciRedTeamIntegration(service);

  try {
    const assessmentId = await integration.assessAIAssistant(
      "assistant-legal-advisor",
      "client-789",
      {
        testPromptModeration: true,
        testRAGCapabilities: true,
        customTests: [
          "The assistant must not provide specific legal advice",
          "All responses must include appropriate disclaimers",
        ],
      }
    );

    console.log(`✅ AI Assistant assessment created: ${assessmentId}`);

    // Wait for completion (simplified)
    await new Promise((resolve) => setTimeout(resolve, 30000)); // Wait 30 seconds

    const assessment = await service.getAssessment(assessmentId, "client-789");
    if (assessment?.report) {
      console.log("📊 Assessment Results:");
      console.log(
        `- Risk Level: ${assessment.report.summary.riskLevel.toUpperCase()}`
      );
      console.log(
        `- Vulnerabilities: ${assessment.report.summary.vulnerabilitiesFound}`
      );
      console.log(`- Score: ${assessment.report.summary.overallScore}/100`);
    }

    return assessmentId;
  } catch (error) {
    console.error("❌ AI Assistant assessment failed:", (error as Error).message);
    throw error as Error;
  }
}

/**
 * Example 4: Scheduled Assessments
 */
async function scheduledAssessments() {
  console.log("⏰ Setting up Scheduled Assessments...");

  const service = new RedTeamService(serviceConfig);
  const integration = new DivinciRedTeamIntegration(service);

  try {
    // Schedule daily assessments for a white label
    const assessmentIds = await integration.scheduleWhiteLabelAssessments(
      "whitelabel-production",
      "client-production",
      {
        cronExpression: "0 2 * * *", // Daily at 2 AM
        assessmentTypes: ["rag", "agent"],
        enabled: true,
      }
    );

    console.log(`✅ Scheduled ${assessmentIds.length} recurring assessments:`);
    assessmentIds.forEach((id, index) => {
      console.log(`${index + 1}. ${id}`);
    });

    return assessmentIds;
  } catch (error) {
    console.error("❌ Failed to schedule assessments:", (error as Error).message);
    throw error as Error;
  }
}

/**
 * Example 5: Assessment Management and Filtering
 */
async function assessmentManagement() {
  console.log("📋 Testing Assessment Management...");

  const service = new RedTeamService(serviceConfig);

  try {
    // List assessments with filtering
    const filter: AssessmentFilter = {
      status: "completed" as const,
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
      hasVulnerabilities: true,
    };

    const assessments = await service.listAssessments(
      "client-123",
      filter,
      { field: "createdAt", direction: "desc" },
      { page: 1, pageSize: 10 }
    );

    console.log(`📊 Found ${assessments.total} assessments matching criteria:`);
    assessments.assessments.forEach((assessment, index) => {
      console.log(
        `${index + 1}. ${assessment.name} - ${assessment.status} - ${
          assessment.report?.summary.vulnerabilitiesFound || 0
        } vulnerabilities`
      );
    });

    // Get service metrics
    const metrics = await service.getMetrics();
    console.log("\n📈 Service Metrics:");
    console.log(`- Total Assessments: ${metrics.totalAssessments}`);
    console.log(`- Running Assessments: ${metrics.runningAssessments}`);
    console.log(
      `- Average Duration: ${Math.round(metrics.averageDuration / 1000)}s`
    );
    console.log(`- System Load: ${metrics.systemLoad.toFixed(1)}%`);
    console.log(`- Queue Length: ${metrics.queueLength}`);

    return { assessments, metrics };
  } catch (error) {
    console.error("❌ Assessment management failed:", (error as Error).message);
    throw error as Error;
  }
}

/**
 * Example 6: Security Report Generation
 */
async function securityReportGeneration() {
  console.log("📄 Generating Security Reports...");

  const service = new RedTeamService(serviceConfig);
  const integration = new DivinciRedTeamIntegration(service);

  try {
    // Generate comprehensive security report for a white label
    const report = await integration.generateWhiteLabelSecurityReport(
      "whitelabel-demo",
      "client-456",
      {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        endDate: new Date(),
      }
    );

    console.log("📊 Security Report Summary:");
    console.log(`- Total Assessments: ${report.summary.totalAssessments}`);
    console.log(
      `- Vulnerabilities Found: ${report.summary.vulnerabilitiesFound}`
    );
    console.log(
      `- Overall Risk Level: ${report.summary.riskLevel.toUpperCase()}`
    );
    console.log(
      `- Last Assessment: ${report.summary.lastAssessment.toLocaleDateString()}`
    );

    console.log("\n📋 Assessment Breakdown:");
    report.assessments.forEach((assessment, index) => {
      console.log(
        `${index + 1}. ${assessment.type.toUpperCase()} - ${
          assessment.vulnerabilities
        } vulnerabilities (${assessment.riskLevel})`
      );
    });

    console.log("\n💡 Recommendations:");
    report.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });

    return report;
  } catch (error) {
    console.error("❌ Security report generation failed:", (error as Error).message);
    throw error as Error;
  }
}

/**
 * Example 7: Bulk Assessment Operations
 */
async function bulkAssessmentOperations() {
  console.log("📦 Testing Bulk Assessment Operations...");

  const service = new RedTeamService(serviceConfig);

  const endpoints = [
    "https://api.example.com/rag-v1",
    "https://api.example.com/rag-v2",
    "https://api.example.com/agent-v1",
  ];

  try {
    const assessmentIds: string[] = [];

    // Create multiple assessments
    for (const [index, endpoint] of endpoints.entries()) {
      const request: AssessmentRequest = {
        name: `Bulk Assessment ${index + 1}`,
        description: `Assessment of ${endpoint}`,
        config: ConfigTemplates.comprehensive({
          endpoint,
          purpose: `Bulk assessment of ${endpoint}`,
          numTests: 25,
        }),
      };

      const response = await service.createAssessment("client-bulk", request);
      assessmentIds.push(response.assessment.id);
      console.log(
        `✅ Created assessment ${index + 1}: ${response.assessment.id}`
      );
    }

    // Monitor all assessments
    console.log("\n📊 Monitoring bulk assessments...");
    let allCompleted = false;

    while (!allCompleted) {
      await new Promise((resolve) => setTimeout(resolve, 10000)); // Wait 10 seconds

      const statuses = await Promise.all(
        assessmentIds.map((id) => service.getAssessment(id, "client-bulk"))
      );

      const completed = statuses.filter(
        (a) => a?.status === "completed"
      ).length;
      const failed = statuses.filter((a) => a?.status === "failed").length;
      const running = statuses.filter((a) => a?.status === "running").length;

      console.log(
        `📈 Progress: ${completed} completed, ${running} running, ${failed} failed`
      );

      if (completed + failed === assessmentIds.length) {
        allCompleted = true;
        console.log("✅ All bulk assessments completed!");

        // Summary
        const totalVulnerabilities = statuses.reduce(
          (sum, a) => sum + (a?.report?.summary.vulnerabilitiesFound || 0),
          0
        );
        console.log(`🚨 Total vulnerabilities found: ${totalVulnerabilities}`);
      }
    }

    return assessmentIds;
  } catch (error) {
    console.error("❌ Bulk assessment operations failed:", (error as Error).message);
    throw error as Error;
  }
}

/**
 * Run all service examples
 */
async function runAllServiceExamples() {
  console.log("🚀 Running All Red Team Service Examples...\n");

  try {
    await basicServiceUsage();
    console.log("\n" + "=".repeat(50) + "\n");

    await whiteLabelIntegration();
    console.log("\n" + "=".repeat(50) + "\n");

    await aiAssistantAssessment();
    console.log("\n" + "=".repeat(50) + "\n");

    await scheduledAssessments();
    console.log("\n" + "=".repeat(50) + "\n");

    await assessmentManagement();
    console.log("\n" + "=".repeat(50) + "\n");

    await securityReportGeneration();
    console.log("\n" + "=".repeat(50) + "\n");

    await bulkAssessmentOperations();

    console.log("\n✅ All service examples completed successfully!");
  } catch (error) {
    console.error("\n❌ Service example execution failed:", (error as Error).message);
    throw error as Error;
  }
}

// Export for use in other files
export {
  basicServiceUsage,
  whiteLabelIntegration,
  aiAssistantAssessment,
  scheduledAssessments,
  assessmentManagement,
  securityReportGeneration,
  bulkAssessmentOperations,
  runAllServiceExamples,
};
