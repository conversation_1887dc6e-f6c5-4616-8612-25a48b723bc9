// Red Teaming Module - Main Entry Point

// Core Components
export { RedTeamEngine } from "./core/engine";
export {
  ConfigValidator,
  RedTeamConfigBuilder,
  ConfigTemplates,
} from "./core/config";
export { ReportGenerator } from "./core/reporting";

// Plugin System
export { PluginManager } from "./plugins/manager";
export { BasePlugin } from "./plugins/base";

// Service Layer (optional - only if service is needed)
// export { RedTeamService, DivinciRedTeamIntegration } from "./service";

// Types
export type {
  RedTeamConfig,
  RedTeamReport,
  TestCase,
  TestResult,
  Vulnerability,
  VulnerabilityType,
  Plugin,
  PluginConfig,
} from "./types";

// Helper utilities
export { RedTeamHelpers } from "./fix-types";
