// Mock notification service implementation
export class NotificationService {
  constructor(config: any) {
    console.log('Mock NotificationService initialized');
  }
  
  async handleEvent(event: any): Promise<void> {
    console.log('Mock: Handling event', event.type);
  }
  
  async sendAssessmentCompleted(assessment: any): Promise<void> {
    console.log('Mock: Sending completion notification for', assessment.id);
  }
  
  async sendAssessmentFailed(assessment: any, error: any): Promise<void> {
    console.log('Mock: Sending failure notification for', assessment.id);
  }
}
