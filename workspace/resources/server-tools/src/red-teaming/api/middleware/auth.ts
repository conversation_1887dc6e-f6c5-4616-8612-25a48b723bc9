import { Request, Response, NextFunction } from 'express';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        clientId: string;
        permissions: string[];
      };
    }
  }
}

/**
 * Authentication middleware
 */
export function authenticate(req: Request, res: Response, next: NextFunction): void {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
    return;
  }

  const token = authHeader.substring(7);
  
  // TODO: Implement actual token validation
  // For now, mock authentication
  if (token === 'valid-api-key') {
    req.user = {
      clientId: 'client-123',
      permissions: [
        'create_assessments',
        'view_assessments',
        'cancel_assessments',
        'export_reports',
        'view_metrics',
        'view_reports'
      ]
    };
    next();
  } else {
    res.status(401).json({
      success: false,
      error: 'Invalid authentication token'
    });
  }
}

/**
 * Authorization middleware
 */
export function authorize(requiredPermissions: string[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    const hasPermission = requiredPermissions.every(permission =>
      req.user!.permissions.includes(permission)
    );

    if (!hasPermission) {
      res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      });
      return;
    }

    next();
  };
}
