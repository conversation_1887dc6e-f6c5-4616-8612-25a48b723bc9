import { Request, Response, NextFunction } from 'express';

/**
 * Request validation middleware
 */
export function validateRequest(validationType: string) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      switch (validationType) {
        case 'createAssessment':
          validateCreateAssessment(req.body);
          break;
        case 'exportReport':
          validateExportReport(req.body);
          break;
        case 'assessWhiteLabel':
          validateAssessWhiteLabel(req.body);
          break;
        case 'assessAssistant':
          validateAssessAssistant(req.body);
          break;
        case 'assessModeration':
          validateAssessModeration(req.body);
          break;
        case 'assessRAG':
          validateAssessRAG(req.body);
          break;
        default:
          throw new Error(`Unknown validation type: ${validationType}`);
      }
      next();
    } catch (error) {
      res.status(400).json({
        success: false,
        error: (error as Error).message
      });
    }
  };
}

function validateCreateAssessment(body: any): void {
  if (!body.name || typeof body.name !== 'string') {
    throw new Error('Assessment name is required and must be a string');
  }
  
  if (!body.config || typeof body.config !== 'object') {
    throw new Error('Assessment config is required and must be an object');
  }
  
  if (!body.config.target || typeof body.config.target !== 'object') {
    throw new Error('Target configuration is required');
  }
  
  if (!body.config.plugins || !Array.isArray(body.config.plugins)) {
    throw new Error('Plugins array is required');
  }
}

function validateExportReport(body: any): void {
  const validFormats = ['json', 'yaml', 'html', 'markdown'];
  
  if (!body.format || !validFormats.includes(body.format)) {
    throw new Error(`Format must be one of: ${validFormats.join(', ')}`);
  }
}

function validateAssessWhiteLabel(body: any): void {
  if (!body.releaseId || typeof body.releaseId !== 'string') {
    throw new Error('Release ID is required and must be a string');
  }
}

function validateAssessAssistant(body: any): void {
  // Optional validation for assistant assessment
  if (body.testPromptModeration !== undefined && typeof body.testPromptModeration !== 'boolean') {
    throw new Error('testPromptModeration must be a boolean');
  }
  
  if (body.testRAGCapabilities !== undefined && typeof body.testRAGCapabilities !== 'boolean') {
    throw new Error('testRAGCapabilities must be a boolean');
  }
}

function validateAssessModeration(body: any): void {
  if (body.testPrompts && !Array.isArray(body.testPrompts)) {
    throw new Error('testPrompts must be an array');
  }
}

function validateAssessRAG(body: any): void {
  // Optional validation for RAG assessment
  if (body.testDataPoisoning !== undefined && typeof body.testDataPoisoning !== 'boolean') {
    throw new Error('testDataPoisoning must be a boolean');
  }
  
  if (body.testContextInjection !== undefined && typeof body.testContextInjection !== 'boolean') {
    throw new Error('testContextInjection must be a boolean');
  }
}
