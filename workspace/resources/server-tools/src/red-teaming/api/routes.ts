import { Router, Request, Response } from "express";
import { RedTeamService } from "../service/red-team-service";
import { DivinciRedTeamIntegration } from "../integrations/divinci-integration";
import {
  AssessmentRequest,
  AssessmentFilter,
  AssessmentSort,
  PaginationOptions,
  ReportExportRequest,
} from "../service/types";
import { authenticate, authorize } from "./middleware/auth";
import { validateRequest } from "./middleware/validation";
import { rateLimit } from "./middleware/rate-limit";

/**
 * Red Team API Routes
 */
export function createRedTeamRoutes(
  redTeamService: RedTeamService,
  integration: DivinciRedTeamIntegration
): Router {
  const router = Router();

  // Apply authentication and rate limiting to all routes
  router.use(authenticate);
  router.use(rateLimit);

  /**
   * POST /assessments
   * Create a new red team assessment
   */
  router.post(
    "/assessments",
    authorize(["create_assessments"]),
    validateRequest("createAssessment"),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || "anonymous";
        const request: AssessmentRequest = req.body;

        const response = await redTeamService.createAssessment(
          clientId,
          request
        );

        res.status(201).json({
          success: true,
          data: response,
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * GET /assessments
   * List assessments with filtering and pagination
   */
  router.get(
    "/assessments",
    authorize(["view_assessments"]),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || 'anonymous';

        // Parse query parameters
        const filter: AssessmentFilter = {
          status: req.query.status as any,
          startDate: req.query.startDate
            ? new Date(req.query.startDate as string)
            : undefined,
          endDate: req.query.endDate
            ? new Date(req.query.endDate as string)
            : undefined,
          targetType: req.query.targetType as string,
          hasVulnerabilities: req.query.hasVulnerabilities === "true",
        };

        const sort: AssessmentSort = {
          field: (req.query.sortField as any) || "createdAt",
          direction: (req.query.sortDirection as any) || "desc",
        };

        const pagination: PaginationOptions = {
          page: parseInt(req.query.page as string) || 1,
          pageSize: parseInt(req.query.pageSize as string) || 20,
        };

        const response = await redTeamService.listAssessments(
          clientId,
          filter,
          sort,
          pagination
        );

        res.json({
          success: true,
          data: response,
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * GET /assessments/:id
   * Get a specific assessment
   */
  router.get(
    "/assessments/:id",
    authorize(["view_assessments"]),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || 'anonymous';
        const assessmentId = req.params.id;

        const assessment = await redTeamService.getAssessment(
          assessmentId,
          clientId
        );

        if (!assessment) {
          return res.status(404).json({
            success: false,
            error: "Assessment not found",
          });
        }

        res.json({
          success: true,
          data: assessment,
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * DELETE /assessments/:id
   * Cancel an assessment
   */
  router.delete(
    "/assessments/:id",
    authorize(["cancel_assessments"]),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || 'anonymous';
        const assessmentId = req.params.id;

        const cancelled = await redTeamService.cancelAssessment(
          assessmentId,
          clientId
        );

        if (!cancelled) {
          return res.status(404).json({
            success: false,
            error: "Assessment not found or cannot be cancelled",
          });
        }

        res.json({
          success: true,
          message: "Assessment cancelled successfully",
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * GET /assessments/:id/progress
   * Get assessment progress
   */
  router.get(
    "/assessments/:id/progress",
    authorize(["view_assessments"]),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || 'anonymous';
        const assessmentId = req.params.id;

        const progress = await redTeamService.getAssessmentProgress(
          assessmentId,
          clientId
        );

        if (!progress) {
          return res.status(404).json({
            success: false,
            error: "Assessment not found",
          });
        }

        res.json({
          success: true,
          data: progress,
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * POST /assessments/:id/export
   * Export assessment report
   */
  router.post(
    "/assessments/:id/export",
    authorize(["export_reports"]),
    validateRequest("exportReport"),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || 'anonymous';
        const assessmentId = req.params.id;
        const { format } = req.body;

        const reportData = await redTeamService.exportReport(
          assessmentId,
          clientId,
          format
        );

        // Set appropriate content type
        const contentTypes = {
          json: "application/json",
          yaml: "application/x-yaml",
          html: "text/html",
          markdown: "text/markdown",
        };

        res.setHeader("Content-Type", contentTypes[format] || "text/plain");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="assessment-${assessmentId}.${format}"`
        );
        res.send(reportData);
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * POST /whitelabel/:id/assess
   * Create assessment for white label release
   */
  router.post(
    "/whitelabel/:id/assess",
    authorize(["create_assessments"]),
    validateRequest("assessWhiteLabel"),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || 'anonymous';
        const whitelabelId = req.params.id;
        const { releaseId, assessmentType, numTests, customPolicies } =
          req.body;

        const assessmentId = await integration.assessWhiteLabelRelease(
          whitelabelId,
          releaseId,
          clientId,
          { assessmentType, numTests, customPolicies }
        );

        res.status(201).json({
          success: true,
          data: { assessmentId },
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * POST /assistant/:id/assess
   * Create assessment for AI assistant
   */
  router.post(
    "/assistant/:id/assess",
    authorize(["create_assessments"]),
    validateRequest("assessAssistant"),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || 'anonymous';
        const assistantId = req.params.id;
        const options = req.body;

        const assessmentId = await integration.assessAIAssistant(
          assistantId,
          clientId,
          options
        );

        res.status(201).json({
          success: true,
          data: { assessmentId },
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * POST /moderation/:id/assess
   * Create assessment for prompt moderation
   */
  router.post(
    "/moderation/:id/assess",
    authorize(["create_assessments"]),
    validateRequest("assessModeration"),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || 'anonymous';
        const moderationConfigId = req.params.id;
        const { testPrompts } = req.body;

        const assessmentId = await integration.assessPromptModeration(
          moderationConfigId,
          clientId,
          testPrompts
        );

        res.status(201).json({
          success: true,
          data: { assessmentId },
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * POST /rag/:id/assess
   * Create assessment for RAG system
   */
  router.post(
    "/rag/:id/assess",
    authorize(["create_assessments"]),
    validateRequest("assessRAG"),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || 'anonymous';
        const ragConfigId = req.params.id;
        const options = req.body;

        const assessmentId = await integration.assessRAGSystem(
          ragConfigId,
          clientId,
          options
        );

        res.status(201).json({
          success: true,
          data: { assessmentId },
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * GET /metrics
   * Get service metrics
   */
  router.get(
    "/metrics",
    authorize(["view_metrics"]),
    async (req: Request, res: Response) => {
      try {
        const metrics = await redTeamService.getMetrics();

        res.json({
          success: true,
          data: metrics,
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  /**
   * GET /whitelabel/:id/security-report
   * Generate security report for white label
   */
  router.get(
    "/whitelabel/:id/security-report",
    authorize(["view_reports"]),
    async (req: Request, res: Response) => {
      try {
        const clientId = req.user?.clientId || 'anonymous';
        const whitelabelId = req.params.id;

        const startDate = req.query.startDate
          ? new Date(req.query.startDate as string)
          : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago

        const endDate = req.query.endDate
          ? new Date(req.query.endDate as string)
          : new Date();

        const report = await integration.generateWhiteLabelSecurityReport(
          whitelabelId,
          clientId,
          { startDate, endDate }
        );

        res.json({
          success: true,
          data: report,
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
        });
      }
    }
  );

  return router;
}
