import {
  Plugin,
  PluginConfig,
  TestCase,
  TestResult,
  VulnerabilityType,
  Vulnerability,
} from "../types";
import { RedTeamHelpers } from "../fix-types";

/**
 * Base class for red team plugins
 */
export abstract class BasePlugin implements Plugin {
  abstract readonly id: string;
  abstract readonly name: string;
  abstract readonly description: string;
  abstract readonly version: string;
  abstract readonly vulnerabilityTypes: VulnerabilityType[];

  /**
   * Generate test cases for this plugin
   */
  abstract generateTestCases(
    config: PluginConfig,
    context?: any
  ): Promise<TestCase[]>;

  /**
   * Evaluate the result of a test case
   */
  abstract evaluateResult(
    testCase: TestCase,
    output: string
  ): Promise<TestResult>;

  /**
   * Helper method to create a test case
   */
  protected createTestCase(
    description: string,
    input: string,
    expectedBehavior?: string,
    metadata?: Record<string, any>
  ): TestCase {
    return {
      id: this.generateTestId(),
      description,
      input,
      expectedBehavior,
      metadata,
      plugin: this.id,
    };
  }

  /**
   * Helper method to create a vulnerability
   */
  protected createVulnerability(
    type: VulnerabilityType,
    severity: "low" | "medium" | "high" | "critical",
    description: string,
    evidence: string,
    recommendation?: string,
    cwe?: string,
    owasp?: string
  ): Vulnerability {
    return {
      type,
      severity,
      description,
      evidence,
      recommendation,
      cwe,
      owasp,
    };
  }

  /**
   * Helper method to create a test result
   */
  protected createTestResult(
    testCase: TestCase,
    output: string,
    passed: boolean,
    vulnerabilities: Vulnerability[] = [],
    score?: number,
    metadata?: Record<string, any>
  ): TestResult {
    return {
      testCase,
      output,
      passed,
      vulnerabilities,
      score,
      metadata,
      duration: 0, // Will be set by the engine
      timestamp: new Date(),
    };
  }

  /**
   * Generate a unique test ID
   */
  private generateTestId(): string {
    return `${this.id}-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;
  }

  /**
   * Sanitize output for logging/reporting
   */
  protected sanitizeOutput(output: string, maxLength: number = 500): string {
    return RedTeamHelpers.sanitizeOutput(output, maxLength);
  }

  /**
   * Check if output contains sensitive patterns
   */
  protected containsSensitiveData(output: string): boolean {
    return RedTeamHelpers.containsSensitiveData(output);
  }

  /**
   * Check if output contains harmful content
   */
  protected containsHarmfulContent(output: string): boolean {
    return RedTeamHelpers.containsHarmfulContent(output);
  }

  /**
   * Check if output indicates successful prompt injection
   */
  protected indicatesPromptInjection(
    output: string,
    injectionMarkers: string[]
  ): boolean {
    return RedTeamHelpers.indicatesPromptInjection(output, injectionMarkers);
  }

  /**
   * Calculate confidence score for vulnerability detection
   */
  protected calculateConfidence(indicators: boolean[]): number {
    return RedTeamHelpers.calculateConfidence(indicators);
  }

  /**
   * Determine severity based on confidence and impact
   */
  protected determineSeverity(
    confidence: number,
    impact: "low" | "medium" | "high"
  ): "low" | "medium" | "high" | "critical" {
    return RedTeamHelpers.determineSeverity(confidence, impact);
  }

  /**
   * Extract PII from output
   */
  protected extractPII(output: string): string[] {
    return RedTeamHelpers.extractPII(output);
  }
}
