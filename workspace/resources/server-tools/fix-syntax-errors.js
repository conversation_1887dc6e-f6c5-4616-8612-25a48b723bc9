const fs = require('fs');

// Fix syntax errors from the previous script
function fixSyntaxErrors(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;

  // Fix the broken extractPII calls
  const fixes = [
    {
      pattern: /this\.extractPII \? this\.extractPII\(/g,
      replacement: 'this.extractPII('
    }
  ];

  fixes.forEach(({ pattern, replacement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      changed = true;
    }
  });

  if (changed) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed syntax errors in ${filePath}`);
  }
}

// Fix the files with syntax errors
const filesToFix = [
  'src/red-teaming/plugins/agent/rbac.ts',
  'src/red-teaming/plugins/common/pii-leak.ts',
  'src/red-teaming/plugins/common/policy.ts',
  'src/red-teaming/plugins/rag/context-injection.ts',
  'src/red-teaming/plugins/rag/data-exfiltration.ts',
  'src/red-teaming/plugins/rag/prompt-injection.ts'
];

filesToFix.forEach(file => {
  if (fs.existsSync(file)) {
    fixSyntaxErrors(file);
  } else {
    console.log(`File not found: ${file}`);
  }
});

console.log('Syntax error fixes completed');
