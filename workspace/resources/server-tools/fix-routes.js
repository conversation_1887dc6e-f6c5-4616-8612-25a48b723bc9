const fs = require('fs');
const path = require('path');

// Fix the routes file by replacing all instances of req.user.clientId with req.user?.clientId || 'anonymous'
// and error.message with (error as Error).message

const routesPath = path.join(__dirname, 'src/red-teaming/api/routes.ts');
let content = fs.readFileSync(routesPath, 'utf8');

// Fix req.user.clientId
content = content.replace(/req\.user\.clientId/g, "req.user?.clientId || 'anonymous'");

// Fix error.message
content = content.replace(/error\.message/g, "(error as Error).message");

fs.writeFileSync(routesPath, content);
console.log('Fixed routes.ts');
