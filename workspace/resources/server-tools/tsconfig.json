{"compilerOptions": {"target": "es2022", "module": "nodenext", "moduleResolution": "nodenext", "types": ["node", "jest", "vitest/globals"], "lib": ["es2022", "dom"], "sourceMap": true, "outDir": "./dist", "esModuleInterop": true, "strict": false, "skipLibCheck": true, "removeComments": true, "forceConsistentCasingInFileNames": false, "noImplicitAny": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false}, "files": ["src/index.ts"], "include": ["./src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}