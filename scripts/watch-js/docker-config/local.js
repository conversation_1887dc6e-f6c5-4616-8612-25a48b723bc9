const path = require("path");
const { ROOT_DIR } = require("../constants");


const DOCKER_COMPOSE_FILE = path.resolve(ROOT_DIR, './docker/local.yml');


module.exports.DOCKER_COMPOSE_FILE = DOCKER_COMPOSE_FILE;

const CLIENTS = {
  "web": {
    "path": "/clients/web",
    "dockerName": "local-web-client"
  },
  "embed": {
    "path": "clients/embed",
    "dockerName": "local-embed-client"
  }
};

const SERVERS = {
  "public-api": {
    "path": "/servers/public-api",
    "dockerName": "local-api"
  },
  "public-api-live": {
    "path": "/servers/public-api-live",
    "dockerName": "local-api-live"
  },
  "public-api-webhook": {
    "path": "/servers/public-api-webhook",
    "dockerName": "local-api-webhook"
  },
};

const SERVICES = {
  ...CLIENTS,
  ...SERVERS
};

module.exports.SERVICES = SERVICES;
