
const path = require("path");
const { readFile, readdir, stat } = require('fs');
const { promisify } = require('util');

const readdirPromise = promisify(readdir);
const readFilePromise = promisify(readFile);
const statPromise = promisify(stat);

async function findModulesThatRequire(resourcePaths, searchDir) {
  const modules = [];

  const files = await readdirPromise(searchDir, { withFileTypes: true });

  await Promise.all(files.map(async (entry) => {
    if(!entry.isDirectory()) return;
    const modulePath = path.join(searchDir, entry.name);
    const dependencies = await getPackageDependencies(modulePath);

    if(dependencies.length === 0) return;
    for(const resourcePath of resourcePaths) {
      if(dependencies.includes(resourcePath)) {
        modules.push(modulePath);
        break;
      }
    }
  }));

  if(modules.length === 0) return [];

  const nested = await findModulesThatRequire(modules, searchDir);
  const combined = new Set([...modules, ...nested]);
  return Array.from(combined);
}

async function getPackageDependencies(packagePath) {
  const packageJsonPath = path.join(packagePath, 'package.json');
  if(!await existsPromise(packageJsonPath)) return [];

  const packageJson = JSON.parse(await readFilePromise(packageJsonPath, 'utf8'));

  const paths = [];
  for(const [depName, depVersion] of Object.entries(packageJson.dependencies || {})) {
    if(!/^file:/.test(depVersion) && !/^link:/.test(depVersion)) continue;
    const depPath = path.resolve(packagePath, depVersion.slice(5));
    paths.push(depPath);
  }

  return paths;
}


module.exports = {
  getPackageDependencies,
  findModulesThatRequire,
};

async function existsPromise(path){
  try {
    await statPromise(path);
    return true;
  }catch(e){
    return false;
  }
}