const { getPackageDependencies } = require('./package-utils');


async function orderResourcesByDependencies(resources) {

  const cachedDeps = new Map();
  const visited = new Set();

  await Promise.all(resources.map(async (resource) => (
    cachedDeps.set(resource, await getPackageDependencies(resource))
  )));

  async function visit(resource) {
    if(visited.has(resource)) return cachedDeps.get(resource) || [];
    visited.add(resource);
    const deps = await (async function(){
      const retrievedDeps = await getPackageDependencies(resource);
      cachedDeps.set(resource, retrievedDeps);
      return retrievedDeps;
    })();
    const fullDeps = new Set(deps);
    for(const dep of deps) {
      const depDeps = await visit(dep);
      console.log("Dep", dep, "has deps", depDeps);
      for(const depDep of depDeps) {
        fullDeps.add(depDep);
      }
    }
    const fullDepsArray = Array.from(fullDeps);
    cachedDeps.set(resource, fullDepsArray);
    return fullDepsArray;
  }
  const originalKeys = Array.from(cachedDeps.keys());
  for(const resource of originalKeys) {
    console.log('Visiting', resource);
    await visit(resource);
  }

  return resources.sort((a, b) => {
    const aDeps = cachedDeps.get(a);
    const bDeps = cachedDeps.get(b);
    if (aDeps.includes(b)) return 1;
    if (bDeps.includes(a)) return -1;
    return 0;
  });
}

module.exports = { orderResourcesByDependencies };
