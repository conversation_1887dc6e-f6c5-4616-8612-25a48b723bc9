const path = require('path');

const { RESOURCES_DIR, CLIENTS_DIR, SERVERS_DIR } = require('../constants');
const { orderResourcesByDependencies } = require("./resource-deps");
const { findModulesThatRequire } = require("./package-utils");


async function findDirtyModules(updatedResources) {

  console.log("updatedResources:", updatedResources);

  const dependentResources = await findModulesThatRequire(updatedResources, RESOURCES_DIR);
  const uniqueResources = Array.from(new Set([...updatedResources, ...dependentResources]));
  const orderedResources = await orderResourcesByDependencies(uniqueResources);

  const [clientsToPrepare, serversToPrepare] = await Promise.all([
    findModulesThatRequire(orderedResources, CLIENTS_DIR),
    findModulesThatRequire(orderedResources, SERVERS_DIR),
  ]);

  return {
    resources: orderedResources,
    clients: clientsToPrepare,
    servers: serversToPrepare
  };
}

module.exports = {
  findDirtyModules,
};
