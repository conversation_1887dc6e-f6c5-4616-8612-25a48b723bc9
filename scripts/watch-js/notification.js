const { spawn } = require('child_process');

const { ROOT_DIR } = require("./constants");
const { splitArgs } = require("./utils/child_process");

function makeBeep(numberOfBeeps){
  return new Promise((res, rej)=>{
    const args = splitArgs(`bash ${ROOT_DIR}/scripts/beep.sh ${numberOfBeeps || 3}`);
    console.log("args:", args);
    const child = spawn(args[0], args.slice(1), { stdio: "inherit" });
    child.on('error', (error) => {
      rej(error);
    });
    child.on('exit', (code) => {
      if(code === 0){
        res();
      } else {
        rej(new Error(`Beep script exited with code ${code}`));
      }
    });

    process.on('SIGTERM', () => child.kill());
    process.on('SIGINT', () => child.kill());
  })
}

module.exports = { makeBeep };


