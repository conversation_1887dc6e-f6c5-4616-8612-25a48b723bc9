

module.exports.splitArgs = function splitArgs(str) {
  const args = [];
  let currentArg = "";
  let inQuote = false;
  let escaped = false;
  let quoteChar = null;

  for (let i = 0; i < str.length; i++) {
    const char = str[i];

    if (escaped) {
      currentArg += char;
      escaped = false;
    } else if (char === '\\') {
      escaped = true;
    } else if (char === '"' || char === "'") {
      if (inQuote) {
        if (char === quoteChar && str[i - 1] !== '\\') {
          inQuote = false;
          quoteChar = null;
        } else {
          currentArg = currentArg.slice(0, -1) + char;
        }
      } else {
        inQuote = true;
        quoteChar = char;
      }
    } else if (char === ' ' && !inQuote) {
      if (currentArg.length > 0) {
        args.push(currentArg);
        currentArg = "";
      }
    } else {
      currentArg += char;
    }
  }

  if(inQuote){
    throw new Error("Unmatched quote in command");
  }
  if(escaped){
    throw new Error("Unmatched escape in command");
  }
  if (currentArg.length > 0) {
    args.push(currentArg);
  }

  return args;
}

