const { 
  runPrepareInModulesSerial,
  runPrepareInModulesParralel,
} = require("./npm-prepare");

const { restartService } = require("./docker");


async function buildAndRestart({ resources, clients, servers }){
  await runPrepareInModulesSerial(resources);

  await Promise.all([
    runPrepareInModulesParralel(clients, "CLIENTS"),
    runPrepareInModulesParralel(servers, "SERVERS"),
  ]);

  await Promise.all([
    Promise.all(clients.map(restartService)),
    Promise.all(servers.map(restartService)),
  ]);
}

module.exports = { buildAndRestart };
