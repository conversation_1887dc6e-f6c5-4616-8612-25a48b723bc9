const { promisify } = require('util');
const { exec } = require('child_process');
const execPromise = promisify(exec);

async function runPrepareInModulesSerial(modules, moduleType) {

  console.log("Running prepare in modules serially:", modules, moduleType);

  for(const modulePath of modules) {
    console.log(`Running 'npm run prepare' in ${modulePath}`);
    try {
      if(process.env.DEBUG !== "1"){
        await execPromise('npm run prepare', { cwd: modulePath, stdio: 'inherit' });
      }
    } catch (error) {
      console.error(`Failed to prepare module at ${modulePath}`);
      console.error(error.stderr);
      console.error(error.stdout);
      throw error;
    }
  }
}

async function runPrepareInModulesParralel(modules, moduleType) {

  console.log("Running prepare in modules in parallel", modules, moduleType);

  await Promise.all(modules.map(async (modulePath) => {
    console.log(`Running 'npm run prepare' in ${modulePath}`);
    try {
      if(process.env.DEBUG !== "1"){
        await execPromise('npm run prepare', { cwd: modulePath, stdio: 'inherit' });
      }
    } catch (error) {
      console.error(`Failed to prepare module at ${modulePath}`);
      console.error(error.stderr);
      console.error(error.stdout);
      throw error;
    }
  }));
}

module.exports = {
  runPrepareInModulesSerial,
  runPrepareInModulesParralel,
};
