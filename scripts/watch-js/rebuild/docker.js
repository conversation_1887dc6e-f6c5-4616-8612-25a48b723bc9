const { promisify } = require('util');
const { exec } = require('child_process');
const execPromise = promisify(exec);

const path = require("path");

const { DOCKER_COMPOSE_FILE } = require("../constants");

const LOCAL = require("../docker-config/local");
const TEST_API = require("../docker-config/test-api");

async function restartService(servicePath) {
  const { stdout, stderr } = await execPromise(`docker compose ls --format json`);
  const runningServices = JSON.parse(stdout);
  await Promise.all([LOCAL,TEST_API].map(async ({ DOCKER_COMPOSE_FILE, SERVICES })=>{
    const found = runningServices.find((service)=>(service.ConfigFiles === DOCKER_COMPOSE_FILE));
    if(!found) return;
    await findAndRestartService(servicePath, DOCKER_COMPOSE_FILE, SERVICES);
  }));
}

async function findAndRestartService(servicePath, dockerComposeFile, services){
  for(const service of Object.values(services)){
    if(!servicePath.endsWith(service.path)) continue;

    console.log(`Restarting client: ${service.dockerName}`);
    if(process.env.DEBUG !== "1"){
      const { stdout, stderr } = await execPromise(`docker compose -f ${dockerComposeFile} restart ${service.dockerName}`);
    }
    return true;
  }
  return false;
}

module.exports = {
  restartService
};