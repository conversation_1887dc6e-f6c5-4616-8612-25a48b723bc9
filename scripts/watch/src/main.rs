use crossbeam_channel::Receiver;
use log::{error, info};
use notify::{Recommended<PERSON>atcher, RecursiveM<PERSON>, Result as Notify<PERSON><PERSON><PERSON>, <PERSON><PERSON>};
use std::collections::HashSet;
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::{Duration, Instant};

// Timer and debounce constants
const DEBOUNCE_DELAY: Duration = Duration::from_secs(5);

fn collect_all_modules(root_dir: &Path) -> HashSet<PathBuf> {
    let mut all_module_dirs = HashSet::new();

    if let Ok(entries) = fs::read_dir(root_dir) {
        for entry in entries.flatten() {
            let path = entry.path();
            if path.is_dir() && is_valid_module_dir(&path) {
                all_module_dirs.insert(path);
            }
        }
    } else {
        info!("❌ Could not read directory: {}", root_dir.display());
    }

    all_module_dirs
}

fn is_valid_module_dir(path: &Path) -> bool {
    // Check for a common identifier, such as package.json or specific module markers
    path.ends_with("models")
        || path.ends_with("server-models")
        || path.ends_with("server-utils")
        || path.ends_with("server-tools")
        || path.ends_with("server-permissions")
        || path.ends_with("utils")
        || path.ends_with("server-globals")
}

fn run_build_in_changed_and_remaining(changed_modules: &[PathBuf], all_modules: &HashSet<PathBuf>) {
    info!("🏃🏻‍➡️ Building changed modules first...");
    let mut already_built = HashSet::new();

    // Build the changed modules first
    for module_dir in changed_modules {
        info!(
            "🏃🏻‍➡️ Running 'pnpm run prepare' in changed module: {}",
            module_dir.display()
        );

        let prepare_status = Command::new("pnpm")
            .args(&["run", "prepare"])
            .current_dir(module_dir)
            .status();

        match prepare_status {
            Ok(status) if status.success() => {
                info!(
                    "✅ Successfully built changed module: {}",
                    module_dir.display()
                );
                already_built.insert(module_dir.clone()); // Track the successfully built module
            }
            Ok(_) => {
                info!(
                    "⚠️ 'prepare' script failed or not configured in {}",
                    module_dir.display()
                );
            }
            Err(e) => {
                error!(
                    "❌ Error running 'pnpm run prepare' in {}: \n {}",
                    module_dir.display(),
                    e
                );
            }
        }
    }

    // Filter out the already-built modules from the all_modules set
    let remaining_modules: Vec<PathBuf> = all_modules.difference(&already_built).cloned().collect();

    if !remaining_modules.is_empty() {
        info!("🏃🏻‍➡️ Building remaining modules...");
        for module_dir in &remaining_modules {
            info!(
                "🏃🏻‍➡️ Running 'pnpm run prepare' in remaining module: {}",
                module_dir.display()
            );

            let prepare_status = Command::new("pnpm")
                .args(&["run", "prepare"])
                .current_dir(module_dir)
                .status();

            match prepare_status {
                Ok(status) if status.success() => {
                    info!(
                        "✅ Successfully built remaining module: {}",
                        module_dir.display()
                    );
                }
                Ok(_) => {
                    info!(
                        "⚠️ 'prepare' script failed or not configured in {}",
                        module_dir.display()
                    );
                }
                Err(e) => {
                    error!(
                        "❌ Error running 'pnpm run prepare' in {}: \n {}",
                        module_dir.display(),
                        e
                    );
                }
            }
        }
    }
}

fn restart_docker_services(is_running: Arc<Mutex<bool>>) {
    let mut is_running_guard = is_running.lock().unwrap();

    if *is_running_guard {
        info!("⚠️ Docker restart already running. Skipping this run.");
        return;
    }

    *is_running_guard = true;

    let services_to_restart = vec![
        "local-api",
        "local-api-live",
        "local-api-webhook",
        "local-web-client",
    ];
    for service in services_to_restart {
        info!("🚢 Restarting Docker service: {}", service);
        let status = Command::new("docker")
            .args(&[
                "compose",
                "-f",
                "../../docker/local.yml",
                "restart",
                service,
            ])
            .status();
        match status {
            Ok(status) if status.success() => {
                info!("✅ Successfully restarted Docker service: {}", service);
            }
            Ok(_) => {
                error!("❌ Failed to restart Docker service: {}", service);
            }
            Err(e) => {
                error!("❌ Error restarting Docker service {}: {}", service, e);
            }
        }
    }

    *is_running_guard = false;
}

fn should_ignore(path: &PathBuf) -> bool {
    let path_str = path.to_str().unwrap_or("");

    if path_str.split('/').any(|component| component == "dist") {
        return true;
    }

    path_str.contains("node_modules")
        || path_str.contains("workers")
        || path_str.contains("tests")
        || path_str.contains("pnpm-lock.yaml")
        || path_str.contains("yarn.lock")
}

fn process_event(
    saved_files: Arc<Mutex<HashSet<PathBuf>>>,
    is_running: Arc<Mutex<bool>>,
    all_module_dirs: &HashSet<PathBuf>,
) {
    let mut saved_files_guard = saved_files.lock().unwrap();
    let changed_module_dirs: HashSet<PathBuf> = saved_files_guard
        .iter()
        .filter_map(|path| {
            path.ancestors()
                .find(|ancestor| {
                    ancestor.ends_with("models")
                        || ancestor.ends_with("server-models")
                        || ancestor.ends_with("server-utils")
                        || ancestor.ends_with("server-tools")
                        || ancestor.ends_with("server-permissions")
                        || ancestor.ends_with("utils")
                        || ancestor.ends_with("server-globals")
                })
                .map(|module_dir| module_dir.to_path_buf())
        })
        .collect();

    if changed_module_dirs.is_empty() {
        info!("⚠️ No valid modules to process. Skipping 'build' and Docker restarts.");
        return;
    }

    // Pause Docker services
    pause_docker_services();

    // Build the changed modules first and then build the remaining modules
    let changed_module_dirs_vec: Vec<_> = changed_module_dirs.clone().into_iter().collect();
    run_build_in_changed_and_remaining(&changed_module_dirs_vec, all_module_dirs);

    // Unpause Docker services after the build
    unpause_docker_services();

    // Restart Docker services if needed
    info!("🚢 Restarting Docker services...");
    restart_docker_services(Arc::clone(&is_running));

    saved_files_guard.clear();
}

fn pause_docker_services() {
    let status = Command::new("docker")
        .args(&["compose", "-f", "../../docker/local.yml", "pause"])
        .status();
    match status {
        Ok(status) if status.success() => {
            info!("✅ Successfully paused Docker services.");
        }
        Ok(_) => {
            error!("❌ Failed to pause Docker services.");
        }
        Err(e) => {
            error!("❌ Error pausing Docker services: {}", e);
        }
    }
}

fn unpause_docker_services() {
    let status = Command::new("docker")
        .args(&["compose", "-f", "../../docker/local.yml", "unpause"])
        .status();
    match status {
        Ok(status) if status.success() => {
            info!("✅ Successfully unpaused Docker services.");
        }
        Ok(_) => {
            error!("❌ Failed to unpause Docker services.");
        }
        Err(e) => {
            error!("❌ Error unpausing Docker services: {}", e);
        }
    }
}

fn event_listener(
    rx: Receiver<notify::Result<notify::Event>>,
    saved_files: Arc<Mutex<HashSet<PathBuf>>>,
    is_running: Arc<Mutex<bool>>,
    all_module_dirs: HashSet<PathBuf>,
) {
    let mut last_event_time: Option<Instant> = None;

    loop {
        match rx.recv() {
            Ok(Ok(event)) => {
                let now = Instant::now();

                for path in event.paths {
                    if should_ignore(&path) {
                        continue;
                    }

                    let mut saved_files_guard = saved_files.lock().unwrap();
                    saved_files_guard.insert(path.clone());
                    info!("📝 File saved: {}", path.display());
                }

                if last_event_time.is_none()
                    || now.duration_since(last_event_time.unwrap()) > DEBOUNCE_DELAY
                {
                    last_event_time = Some(now);

                    let saved_files_clone = Arc::clone(&saved_files);
                    let is_running_clone = Arc::clone(&is_running);
                    let all_module_dirs_clone = all_module_dirs.clone();

                    thread::spawn(move || {
                        thread::sleep(DEBOUNCE_DELAY);
                        info!("⏳ Debounce delay passed. Processing events...");
                        process_event(saved_files_clone, is_running_clone, &all_module_dirs_clone);
                    });
                }
            }
            Ok(Err(e)) => error!("❌ Watcher error: {:?}", e),
            Err(e) => error!("❌ Channel receive error: {:?}", e),
        }
    }
}

fn main() -> NotifyResult<()> {
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();

    let script_dir = std::env::current_exe()?.parent().unwrap().to_path_buf();
    let workspace_path = script_dir.join("../../../../workspace/resources");

    info!(
        "👀 Now watching workspace path: {}",
        workspace_path.display()
    );

    let all_module_dirs = collect_all_modules(&workspace_path);

    let (tx, rx) = crossbeam_channel::unbounded();
    let mut watcher: RecommendedWatcher = notify::recommended_watcher(move |res| {
        tx.send(res).unwrap();
    })?;

    watcher.watch(&workspace_path, RecursiveMode::Recursive)?;

    let saved_files = Arc::new(Mutex::new(HashSet::new()));
    let is_running = Arc::new(Mutex::new(false));

    event_listener(
        rx,
        Arc::clone(&saved_files),
        Arc::clone(&is_running),
        all_module_dirs,
    );

    Ok(())
}
