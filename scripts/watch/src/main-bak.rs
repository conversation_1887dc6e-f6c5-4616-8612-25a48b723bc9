use crossbeam_channel::Receiver;
use log::{error, info};
use notify::{RecommendedWatcher, RecursiveMode, Result as Notify<PERSON><PERSON><PERSON>, Watcher};

use notify_debouncer_full::{new_debouncer, DebouncedEvent};
use std::collections::{HashMap, HashSet};
use std::fs::{self, read_to_string};
use std::path::{Path, PathBuf};
use std::process::Command;
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::{Duration, Instant};

const DEBOUNCE_DELAY: Duration = Duration::from_secs(2);
const LOG_INTERVAL: Duration = Duration::from_secs(33);

fn reset_watcher(watcher: &mut RecommendedWatcher, workspace_path: &PathBuf) -> notify::Result<()> {
    info!("🔄 Resetting file watcher...");

    // Stop watching the current directories
    watcher.unwatch(workspace_path)?;

    // Restart the watcher for the same path
    watcher.watch(workspace_path, RecursiveMode::Recursive)?;

    info!(
        "✅ Watcher successfully reset and rewatching: {}",
        workspace_path.display()
    );
    Ok(())
}

fn should_ignore(path: &PathBuf) -> bool {
    let path_str = path.to_str().unwrap();
    path_str.contains("node_modules")
        || path_str.contains("dist")
        || path_str.contains("workers")
        || path_str.contains("tests")
        || path_str.contains("tests")
        || path_str.contains("pnpm-lock.yaml")
        || path_str.contains("yarn.lock")
}

fn must_include(path: &PathBuf) -> bool {
    let path_str = path.to_str().unwrap();
    let result =
        path_str.contains("src") && (path_str.ends_with(".ts") || path_str.ends_with(".tsx"));
    info!("🔍 Path inclusion check for {}: {}", path.display(), result);
    result
}

fn is_inconsequential_change(path: &PathBuf) -> bool {
    if let Ok(content) = read_to_string(path) {
        let changes = content.chars().filter(|c| !c.is_whitespace()).count();
        let result = changes == 0;
        info!(
            "🔍 Inconsequential change check for {}: {}",
            path.display(),
            result
        );
        result
    } else {
        false
    }
}

fn determine_module(path: &PathBuf) -> Option<&'static str> {
    let path_str = path.to_str().unwrap();
    info!("🔍 Determining module for path: {}", path.display());
    if path_str.contains("server-models") {
        Some("server-models")
    } else if path_str.contains("server-globals") {
        Some("server-globals")
    } else if path_str.contains("server-tools") {
        Some("server-tools")
    } else if path_str.contains("server-utils") {
        Some("server-utils")
    } else if path_str.contains("server-permissions") {
        Some("server-permissions")
    } else if path_str.contains("utils") {
        Some("utils")
    } else if path_str.contains("models") {
        Some("models")
    } else {
        None
    }
}

fn debounce_after_prepare() {
    info!("🕒 Debouncing after prepare...");
    thread::sleep(DEBOUNCE_DELAY);
}

fn run_prepare_script(module_dir: &Path) -> bool {
    info!("🏃🏻‍ Running prepare script in {}", module_dir.display());

    let status = Command::new("pnpm")
        .args(&["run", "prepare"])
        .current_dir(module_dir)
        .status()
        .expect(&format!(
            "❌ Failed to run pnpm run prepare in {}",
            module_dir.display()
        ));

    if status.success() {
        info!("✅ Finished pnpm run prepare in {}", module_dir.display());
        true
    } else {
        error!(
            "❌ Failed to run prepare script in {}",
            module_dir.display()
        );
        false
    }
}

fn crawl_for_package_jsons(root_dir: &Path) -> Vec<PathBuf> {
    let mut package_files = Vec::new();

    fn crawl_dir(dir: &Path, package_files: &mut Vec<PathBuf>) {
        if let Ok(entries) = fs::read_dir(dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                if path.is_dir() {
                    if path.file_name().unwrap() == "node_modules" {
                        continue; // Skip `node_modules`
                    }
                    crawl_dir(&path, package_files);
                } else if path.ends_with("package.json") {
                    info!("📂 Found package.json: {}", path.display()); // Log each found package.json
                    package_files.push(path);
                }
            }
        } else {
            info!("❌ Could not read directory: {}", dir.display());
        }
    }

    crawl_dir(root_dir, &mut package_files);
    package_files
}

fn has_dependency(dependency_name: &str, package_json_paths: &[PathBuf]) -> bool {
    for package_json_path in package_json_paths {
        info!("🔍 Checking file: {}", package_json_path.display()); // Log the full path of the package.json

        if let Ok(content) = read_to_string(package_json_path) {
            if content.contains(&format!("@divinci-ai/{}", dependency_name)) {
                info!(
                    "✅ Found dependency for @divinci-ai/{} in file {}",
                    dependency_name,
                    package_json_path.display()
                );
                return true;
            } else {
                // info!(
                //     "❌ Did not find @divinci-ai/{} in {}. Continuing...",
                //     dependency_name,
                //     package_json_path.display()
                // );
            }
        } else {
            info!("❌ Could not read file: {}", package_json_path.display());
        }
    }

    info!(
        "ℹ️ No dependency for @divinci-ai/{} found in any package.jsons",
        dependency_name
    );
    false
}

fn run_prepare_scripts(service_dirs: &[PathBuf]) {
    for service_dir in service_dirs {
        info!(
            "🏃🏻‍➡️ Running prepare or build script in {}",
            service_dir.display()
        );

        let status = Command::new("pnpm")
            .args(&["run", "prepare"])
            .current_dir(service_dir)
            .status();

        match status {
            Ok(status) if status.success() => {
                info!("⏬ Finished pnpm run prepare in {}", service_dir.display());
            }
            Ok(_) => {
                error!(
                    "❌ Failed to run pnpm run prepare in {}",
                    service_dir.display()
                );
            }
            Err(e) => {
                error!(
                    "❌ Error running pnpm run prepare in {}: \n {}",
                    service_dir.display(),
                    e
                );
            }
        }
    }
}

fn restart_docker_services() {
    let services_to_restart = vec!["local-api", "local-api-live", "local-api-webhook"];

    for service in services_to_restart {
        info!("🚢 Restarting Docker service: {}", service);
        let status = Command::new("docker compose")
            .args(&["-f", "../../docker/local.yml", "restart", service])
            .status();

        match status {
            Ok(status) if status.success() => {
                info!("✅ Successfully restarted Docker service: {}", service);
            }
            Ok(_) => {
                error!("❌ Failed to restart Docker service: {}", service);
            }
            Err(e) => {
                error!("❌ Error restarting Docker service {}: {}", service, e);
            }
        }
    }
}

fn collect_dependency_dirs(script_dir: &Path) -> Vec<PathBuf> {
    // let resources_dir = script_dir.join("resources");
    let resources_dir = script_dir;

    let dep_dirs = vec![
        resources_dir.join("utils"),
        resources_dir.join("models"),
        resources_dir.join("server-utils"),
        resources_dir.join("server-globals"),
        resources_dir.join("server-tools"),
        resources_dir.join("server-models"),
        resources_dir.join("server-permissions"),
    ];

    let client_dirs = read_dir_entries(&script_dir.join("../..").join("clients"));
    let server_dirs = read_dir_entries(&script_dir.join("../..").join("servers"));
    let worker_dirs = read_dir_entries(&script_dir.join("../..").join("workers"));

    dep_dirs
        .into_iter()
        .chain(client_dirs)
        .chain(server_dirs)
        .chain(worker_dirs)
        .collect()
}

fn read_dir_entries(path: &Path) -> Vec<PathBuf> {
    if path.exists() {
        fs::read_dir(path)
            .expect("❌ Failed to read directory")
            .filter_map(|entry| entry.ok().map(|e| e.path()))
            .collect()
    } else {
        vec![]
    }
}

fn determine_services_to_restart(
    module: &str,
    service_dirs: &[PathBuf],
    package_jsons: &[PathBuf],
) -> HashSet<String> {
    let mut services_to_restart = HashSet::new();

    for service_dir in service_dirs {
        if has_dependency(module, package_jsons) {
            let service_name = service_dir
                .file_name()
                .unwrap()
                .to_str()
                .unwrap()
                .to_string();
            services_to_restart.insert(service_name.clone());
            info!("ℹ️ Service {} depends on module {}", service_name, module);
        }
    }

    services_to_restart
}

fn update_module(
    module: &str,
    module_path: &Path,
    service_dirs: &[PathBuf],
    package_jsons: &[PathBuf],
    in_progress_updates: Arc<Mutex<HashSet<PathBuf>>>,
    first_module: &mut Option<String>, // Track the first module dynamically
    watcher: &mut RecommendedWatcher,  // Add the watcher
    workspace_path: &PathBuf,          // Add the workspace path
) {
    info!(
        "🚧 Starting module update: {} for path: {}",
        module,
        module_path.display()
    );

    let module_dir = module_path
        .ancestors()
        .find(|ancestor| ancestor.ends_with(module))
        .expect(&format!("❌ Module directory not found for {}", module))
        .to_path_buf();

    if run_prepare_script(&module_dir) {
        debounce_after_prepare();

        let mut services_to_restart =
            determine_services_to_restart(module, service_dirs, package_jsons);

        // Track the first module that is processed
        if first_module.is_none() {
            *first_module = Some(module.to_string());
        }

        // Remove the first module from the list of services to restart
        if let Some(first_module_name) = &first_module {
            if module == first_module_name {
                services_to_restart.remove(module);
                info!(
                    "ℹ️ Removing first processed module from restart list: {}",
                    module
                );
            }
        }

        if !services_to_restart.is_empty() {
            info!(
                "💽 Services to restart for module {}: {:?}",
                module, services_to_restart
            );

            run_prepare_scripts(service_dirs, watcher, workspace_path); // Pass the watcher and path
        } else {
            info!("🙅🏻‍♂️ No services found to restart for module: {}", module);
        }

        {
            let mut in_progress = in_progress_updates.lock().unwrap();
            in_progress.remove(module_path);
        }

        info!(
            "✅ Removed {} from in-progress updates",
            module_path.display()
        );
    }
}

fn event_listener(
    rx: Receiver<Result<Vec<DebouncedEvent>, Vec<notify::Error>>>,
    service_dirs: Vec<PathBuf>,
    package_jsons: Vec<PathBuf>,
    in_progress_updates: Arc<Mutex<HashSet<PathBuf>>>,
    watcher: &mut RecommendedWatcher, // Now takes a mutable reference
    workspace_path: PathBuf,
) {
    let mut last_event_times: HashMap<PathBuf, Instant> = HashMap::new(); // Track last event times
    let mut last_log_time = Instant::now();
    let mut first_module: Option<String> = None;

    loop {
        match rx.recv() {
            Ok(Ok(events)) => {
                for event in events {
                    for path in &event.paths {
                        let now = Instant::now();

                        // Check if this path has triggered an event recently
                        if let Some(last_event_time) = last_event_times.get(path) {
                            if now.duration_since(*last_event_time) < DEBOUNCE_DELAY {
                                log::info!("☄️ Debouncing path: {}", path.display());
                                continue; // Skip processing if within debounce window
                            }
                        }

                        last_event_times.insert(path.clone(), now); // Update last event time

                        log::info!("📂 Event received for path: {}", path.display());

                        // Apply filtering: ignore trivial changes
                        if is_inconsequential_change(path) {
                            log::info!(
                                "🔍 Skipping inconsequential change for file: {}",
                                path.display()
                            );
                            continue; // Skip processing if change is inconsequential
                        }

                        // Check if the path is already in progress
                        let mut in_progress = in_progress_updates.lock().unwrap();
                        if in_progress.contains(path) {
                            log::info!("⚠️ Update already in progress for path: {}", path.display());
                            continue; // Skip if already being processed
                        }

                        // Mark this path as being processed
                        in_progress.insert(path.clone());

                        // Proceed to module update
                        if !should_ignore(path) && must_include(path) {
                            if let Some(module) = determine_module(path) {
                                update_module(
                                    module,
                                    path,
                                    &service_dirs,
                                    &package_jsons,
                                    Arc::clone(&in_progress_updates),
                                    &mut first_module,
                                    watcher,         // Pass watcher directly
                                    &workspace_path, // Pass the workspace path
                                );
                            }
                        }
                    }
                }
            }
            Ok(Err(e)) => log::error!("❌ Notify error: {:?}", e),
            Err(e) => log::error!("❌ Channel receive error: {:?}", e),
        }

        // Periodic logging to ensure the watcher is alive
        if last_log_time.elapsed() >= LOG_INTERVAL {
            log::info!("👀 Main thread still alive, watching for changes...");
            last_log_time = Instant::now();
        }
    }
}

fn main() -> NotifyResult<()> {
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();

    // Define the path to the workspace relative to the script's directory
    let script_dir = std::env::current_exe()
        .expect("Failed to determine the executable path")
        .parent()
        .expect("Failed to determine the executable directory")
        .to_path_buf();

    let workspace_path = script_dir.join("../../../../workspace/resources");

    log::info!(
        "👀 Now watching workspace path: {}",
        workspace_path.display()
    );

    // Create a channel for receiving events using crossbeam_channel
    let (tx, rx) = crossbeam_channel::unbounded();

    // Create the raw watcher (without debounce)
    let mut watcher: RecommendedWatcher = notify::recommended_watcher(move |res| {
        tx.send(res).unwrap();
    })?;

    // Start watching the path
    watcher.watch(&workspace_path, RecursiveMode::Recursive)?;

    // Handle file events
    loop {
        match rx.recv() {
            Ok(Ok(event)) => {
                log::info!("📂 Event: {:?}", event);
                // Handle the event here...
            }
            Ok(Err(e)) => log::error!("❌ Watcher error: {:?}", e),
            Err(e) => log::error!("❌ Channel error: {:?}", e),
        }
    }
}
