# mTLS Setup Guide

This guide explains how to set up mutual TLS (mTLS) authentication between the web client and public-api server in the <PERSON>vinci application.

## Overview

Mutual TLS (mTLS) provides two-way authentication where both the client and server verify each other's certificates. This adds an extra layer of security beyond standard TLS where only the server authenticates itself to the client.

In this implementation, we've set up:
1. Server-side certificate verification of client certificates
2. Client-side presentation of certificates to the server
3. Environment configuration to enable/disable mTLS

## Prerequisites

- OpenSSL for certificate generation
- A Cloudflare account with mTLS capabilities (for production)
- Understanding of certificate management

## Certificate Setup

### Generate Server Certificates

We've provided a script to generate the server certificates:

```bash
./scripts/generate-mtls-keys.sh
```

This script:
- Generates a server private key and self-signed certificate
- Converts the provided client certificate to PKCS#12 format for browser import
- Places all certificates in `/private-keys/local/certs/mtls/`

### Client Certificate

The client certificate is provided by Cloudflare and has been placed in:
- `/private-keys/local/certs/mtls/client.crt`
- `/private-keys/local/certs/mtls/client.key`

For browser usage, the certificate is also available as a PKCS#12 file:
- `/private-keys/local/certs/mtls/client.p12` (password: divinci)

## Enabling mTLS

### Server Configuration

Set these environment variables for the server:

```
ENABLE_MTLS=1
MTLS_CERT_DIR=/path/to/certs/mtls
```

The server will:
- Load certificates from the specified directory
- Require valid client certificates for all requests
- Reject connections with invalid or missing certificates

### Client Configuration

Set these environment variables for the client:

```
API_IS_SECURE=1
API_MTLS_ENABLED=1
```

For browser clients:
1. Import the `client.p12` file into your browser's certificate store
2. The browser will automatically present the certificate when requested

For Node.js clients:
- The client certificate is used automatically when making API requests

## Implementation Details

### Server Changes

In `workspace/servers/public-api/src/app.ts`:
- Server creates an HTTPS server with mTLS enabled when `ENABLE_MTLS=1`
- Requests without valid client certificates are rejected with a 403 error

### Client Changes 

In `workspace/clients/web/src/globals/api.ts`:
- API requests include client certificates when `API_MTLS_ENABLED=1`
- Browser environment relies on system certificate store
- Node.js environment configures HTTPS agent with certificates

## Production Setup with Cloudflare

For production deployment with Cloudflare:

1. Upload the client certificate to Cloudflare mTLS settings
2. Configure your domain to use mTLS verification
3. Set appropriate access policies for the API endpoints
4. Update environment variables to enable mTLS

## Troubleshooting

Common issues and solutions:

1. **Certificate not found**: Ensure the certificate paths are correct in environment variables
2. **Certificate rejected**: Verify the certificate is valid and not expired
3. **Browser not presenting certificate**: Import the PKCS#12 file and restart the browser
4. **Connection errors**: Ensure both client and server are configured for HTTPS