# ERROR NOTES
#### What to do when you're seeing red 🔴 👀


``` error
InferenceUpstreamError: ERROR 3001: Unknown internal error
```
``` error
 { message: 'AiError: AiError: Unknown internal error', code: 3001 }
```
``` source
ai/run/@cf/baai/bge-base-en-v1.5
Cloudflare
```
``` possible explaination
The text is too large for this word embeddings model.
```
``` explaination reference
https://discord.com/channels/595317990191398933/1138522314594582578/1183083880094371860
```

_ _ _

``` error
'InferenceUpstreamError: ERROR 3010: Invalid or incomplete input for the model',
```
``` source
Cloudflare AI Embedding Endpoint
```
``` possible explaination
Unknown
```
``` explaination reference
https://community.cloudflare.com/t/error-with-ai-workers/589199
```

_ _ _


``` error
D1_TYPE_ERROR: Type 'undefined' not supported for value 'undefined'
```
``` source
Cloudflare D1 Worker
```
``` possible explaination
Missing columns or fields in the database.
```
``` explaination reference
https://community.cloudflare.com/t/how-i-can-insert-a-new-data-to-db/538838/7?u=mikeumus
```

_ _ _


``` error
Cloudflare Worker sends a GET when trying to POST. 
```
``` Reference
https://community.cloudflare.com/t/worker-is-changing-the-post-method-to-get/110588/5
```

_ _ _

