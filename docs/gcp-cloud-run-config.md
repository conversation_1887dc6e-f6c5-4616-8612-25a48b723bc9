# GCP Cloud Run Configuration

This document provides instructions for configuring your service in GCP Cloud Run.

## Secret Volume Mount

The service requires a certificate for mTLS authentication. This certificate is mounted as a secret volume in the container.

### Configuration Steps

1. Go to GCP Console > Cloud Run > Your Service > Configuration > Secrets
2. Click "Add a Secret Volume"
3. Configure the first secret volume:
   - Name: `mtls-certs`
   - Mount path: `/etc/ssl/certs`
   - Secret: `server-crt`
4. Click "Add a Secret Volume" again
5. Configure the second secret volume:
   - Name: `mtls-key`
   - Mount path: `/etc/ssl/private`
   - Secret: `server-key`

### Verification

After configuring the secret volume, the following files should be accessible in the container:
- `/etc/ssl/certs/server.crt` - The server certificate
- `/etc/ssl/private/server.key` - The server private key

## Environment Variables

Make sure the following environment variables are set:

```
ENABLE_MTLS=1
MTLS_CERT_DIR=/etc/ssl
```

## Troubleshooting

### Certificate or Key Not Found

If you see an error like this:

```
Error: ENOENT: no such file or directory, open '/etc/ssl/certs/server.crt'
```

Or:

```
Error: ENOENT: no such file or directory, open '/etc/ssl/private/server.key'
```

This means the certificate or key is not mounted at the expected path. Check:

1. The secret volume configuration in GCP Cloud Run
2. The path in volume settings (should be `server.crt` and `server.key`)
3. The existence of the secret versions in GCP Secret Manager

### Duplicate Mount Paths

If you see an error like this:

```
Duplicate mount paths [/c] forbidden
```

This means you have multiple volume mounts trying to use the same path. Make sure you only have one volume mount with the path `/c`.

### PEM Format Error

If you see an error like this:

```
Error: error:0480006C:PEM routines::no start line
```

This means the certificate is not in the proper PEM format. Make sure your certificate:

1. Starts with `-----BEGIN CERTIFICATE-----`
2. Ends with `-----END CERTIFICATE-----`
3. Contains the base64-encoded certificate data between these markers

You can create a new secret with properly formatted certificates using:

```bash
./deploy/scripts/create-mtls-certs-secret.sh
```
