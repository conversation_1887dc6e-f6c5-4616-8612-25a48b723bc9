# Fixing Cloudflare Error 526 (Invalid SSL Certificate)

This document provides instructions for fixing Cloudflare Error 526 (Invalid SSL Certificate) when using GCP Cloud Run.

## Overview

Error 526 occurs when Cloudflare cannot establish a valid SSL connection with your origin server. This typically happens when:

1. Your origin server is using a self-signed certificate
2. Your origin server is using a certificate that's not trusted by Cloudflare
3. You're using "Full (Strict)" SSL mode in Cloudflare, which requires a valid certificate on your origin

## Solution

The solution is to use a Cloudflare Origin Certificate on your GCP Cloud Run service. This certificate is trusted by Cloudflare and will allow the SSL handshake to complete successfully.

## Step 1: Generate a Cloudflare Origin Certificate

1. Log in to your Cloudflare dashboard
2. Go to SSL/TLS > Origin Server
3. Click "Create Certificate"
4. Select "Generate private key and CSR with Cloudflare"
5. Choose RSA as the private key type
6. Add your domain: `*.stage.divinci.app` and `stage.divinci.app`
7. Set the certificate validity (15 years is fine for testing)
8. Click "Create"
9. Download both the Origin Certificate (PEM) and Private Key (PEM)
10. Save them as `origin-cert.pem` and `origin-key.pem` in one of these locations:
    - `private-keys/staging/certs/mtls/origin-cert.pem` and `private-keys/staging/certs/mtls/origin-key.pem`
    - Or in the root directory of your project

## Step 2: Download Cloudflare Root CA Certificates

Run the following script to download the Cloudflare Root CA certificates:

```bash
./deploy/scripts/download-cloudflare-ca-certs.sh staging
```

This will download the following files to both the current directory and the `private-keys/staging/certs/mtls` directory:
- `origin_ca_rsa_root.pem`: Cloudflare Origin CA — RSA Root Certificate
- `origin_ca_ecc_root.pem`: Cloudflare Origin CA — ECC Root Certificate

## Option 1: Run All Steps with a Single Script

Run the following script to execute all the necessary steps to fix the Error 526 issue:

```bash
./deploy/scripts/fix-cloudflare-error-526.sh staging
```

This script will:
1. Download Cloudflare Origin CA root certificates
2. Install the Cloudflare Origin Certificate
3. Add the Cloudflare Root CA to the certificate chain
4. Update GCP secrets

## Option 2: Run Each Step Individually

### Step 3: Install the Cloudflare Origin Certificate

Run the following script to install the Cloudflare Origin Certificate:

```bash
./deploy/scripts/install-cloudflare-origin-cert.sh staging
```

This will:
1. Copy the certificate files to the `private-keys/staging/certs/mtls` directory
2. Create/update the GCP secrets `server-crt` and `server-key`

### Step 4: Add the Cloudflare Root CA to the Certificate Chain

Run the following script to add the Cloudflare Root CA to the certificate chain:

```bash
./deploy/scripts/add-ca-to-certificates.sh staging
```

This will:
1. Add the appropriate Cloudflare Root CA certificate to your certificate chain
2. Update the certificate files in the `private-keys/staging/certs/mtls` directory

### Step 5: Update GCP Secrets

Run the following script to update the GCP secrets with the modified certificates:

```bash
./deploy/scripts/create-server-cert-secret.sh
```

## Step 6: Verify GCP Cloud Run Configuration

Make sure your GCP Cloud Run service has the following configuration:

1. Volume mounts:
   - Secret `server-crt` mounted at `/etc/ssl/certs`
   - Secret `server-key` mounted at `/etc/ssl/private`

2. Environment variables:
   - `ENABLE_MTLS=1`
   - `MTLS_CERT_DIR=/etc/ssl`

## Step 7: Restart Your Service

Restart your GCP Cloud Run service to apply the changes.

## Step 8: Verify the Fix

Run the following script to verify that the Error 526 issue has been fixed:

```bash
./deploy/scripts/tests/cf-cert-test.sh
```

If the test shows a 200 OK response or a 302 redirect (without Error 526), the issue has been fixed.

## Troubleshooting

If you're still experiencing Error 526 after following these steps:

1. Check Cloudflare SSL/TLS mode:
   - Make sure it's set to "Full (Strict)"
   - Temporarily try "Full" mode to see if it resolves the issue

2. Check certificate format:
   - Make sure the certificate is in PEM format
   - Make sure the certificate starts with `-----BEGIN CERTIFICATE-----`
   - Make sure the certificate ends with `-----END CERTIFICATE-----`

3. Check certificate chain:
   - Make sure the certificate chain is complete
   - Make sure the Cloudflare Root CA certificate is included in the chain

4. Check GCP Cloud Run configuration:
   - Make sure the volume mounts are correctly configured
   - Make sure the environment variables are correctly set

5. Check application logs:
   - Look for any certificate-related errors in the application logs
