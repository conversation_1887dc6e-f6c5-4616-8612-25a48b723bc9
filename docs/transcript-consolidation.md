- Create a transcript component (this will be used in multiple areas!)
- Make sure the transcript component can be used properly in
  - Normal AI Chats
  - WhiteLabel Chats
    - Does not have rating system - Should we just hide this component so we don't have to copy the entire component?
      - What triggers the hiding? 
  - WhiteLabel Thread Views
    - Orginization can add Comments

TranscriptOutlet
- Settings
  - Hide Rating System - whitelabel boolean

- Comment System
  - Similar to comments on a Google Doc. A sub-chat anchored to part of a chat.
- Organization System
  - The exsiting user.role could be used for Orgs too or is this it's own chat-doc related role specific? 
  - Org Roles:
    - Org Owner (super admin)
    - Org Admin (edit all permissions/chats)
    - Org User (read all permissions/chats)

Seperate Microservices
- Organization
- Rating/Trending