# Note
> Most Permissions don't apply to anonymous users

### Does User have Permission on Document
```mermaid
graph TD;

style Failure fill:#FAA,stroke:#F00


%% Request Permission to do something on a document
WantsPermission["User 'X' Wants Permissions to do 'Y' on document 'Z'"]

WantsPermission-->GetPermission{Get Permission Doc related to 'Z'}

GetPermission-->|Anonymous Users have Permission 'Y'| Success

GetPermission-->EnsureUser{Make Sure the User is Logged In}

EnsureUser--Success-->TestPermission{Test The Permission on the User}
EnsureUser--Fail-->Failure([Fail Permission])

TestPermission -->|Any Logged In User has Permission 'Y| Success
TestPermission -->|User 'X' In Group with Permission 'Y'| Success
TestPermission -->|User 'X' directly has Permission 'Y'| Success
TestPermission -->|User 'X' is owner of Permission 'Y'| Success

TestPermission -->|No Permission| Failure
```


### Update Document Permissions
```mermaid
graph LR;

%% Toggle Permissions of a document
PermissionForm["Update Permissions on document 'Z'"]

PermissionForm-->SetLoggedIn[Toggle Permission 'Y' of all Logged In Users]


PermissionForm-->ViewGroups[View Groups You Created or are In]
ViewGroups-->AddGroup[Add Group 'G' to toggle permissions]
PermissionForm-->GroupSlug[Enter the Group Slug]
GroupSlug-->AddGroup
AddGroup-->SetGroup[Toggle Permission 'Y' of Group 'G']
AddGroup-->RemoveGroup[Remove Group 'G' from permissions]


PermissionForm-->AddUser[Add User 'U' to toggle permissions]
AddUser-->SetUser[Toggle Permission 'Y' of User 'U']
AddUser-->RemoveUser[Remove User 'U' from permissions]


PermissionForm-->InviteOwner[Invite User 'U' to be owner of document]
InviteOwner-->RetractInvite[Retract Invitation for User 'U' to be owner]
InviteOwner-->AcceptInvite[User 'U' accepts invitation and becomes owner]

```