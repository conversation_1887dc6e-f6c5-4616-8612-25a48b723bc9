
### Can User Pay Amount?

```mermaid
graph TD;

style Failure fill:#FAA,stroke:#F00


WantsPayment([User 'U' wants to pay 'M' for service])

WantsPayment-->CheckSubStatus{Check the Subscription Status of User 'U'}

CheckSubStatus-- Has Subscription -->MaxAmount1[MaxAmount == $10]
CheckSubStatus-- No Subscription -->MaxAmount2[MaxAmount == $5]

WantsPayment-->PreviousTransactions[Get amount already paid as 'P']

PreviousTransactions-->TestUnder{Test 'M'+'P' <= MaxAmount}
MaxAmount1-->TestUnder
MaxAmount2-->TestUnder

TestUnder-- True -->AddM[Set 'P' = 'P' + 'M']
AddM-->Continue([Continue])
TestUnder-- False -->Failure([User Can't Pay])
```

### User Pays After Service Usage

#### Calculate
Some situations we cannot calculate the amount the user will pay until after the action is completed. This is a vulnerability. Hopefully, people won't go over their amount too much. Since each transaction is usually small we may be fortunate enough to avoid too much loss.

In some cases it is a vulnerability we must accept until we can calculate the action ahead of time such as RAG.

In other cases is is a vulnerability we must accept regardless. For example, we must pay for the amount of tokens in an openAI response. We have no idea the response length until after openai produces the amount.

In some cases we can actually specify the maximum tokens to use. However, if we limit a tool too much it could have unexpected results


```mermaid
graph TD;

ServiceStart([User wants uses Service 'S'])

ServiceStart-->ReserveAmount[Reserve Amount 'R' ahead of time to make sure It can't be abused]

ReserveAmount-->RunService[Run Service 'S']

RunService-->CalculateResult[Calculate <br /> Cost of 'S' Output as 'M']

CalculateResult-->GetDifference[Calculate <br /> Difference = 'M' - 'R']

GetDifference-->UpdatePaid[Set Paid = Paid + 'Difference']

UpdatePaid-->Continue([Continue])

```

