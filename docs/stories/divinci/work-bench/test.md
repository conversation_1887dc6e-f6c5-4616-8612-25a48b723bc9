### QA Testing

> Here the user creates individual prompts to test out releases or a combination of release components

```mermaid
graph TD;

style Fail1 fill:#FAA,stroke:#f00

Enters[Enters QA Test Page]
Enters -->CheckPermission1{Check if User has Test Permission}
CheckPermission1 -->|Success| GetParams[(Get Parameters from URL)]
CheckPermission1 -->|Fail| Fail1([Display No Permission Page])


GetParams-- If no components set --> Page[QA Test Page]
GetParams-- If components set -->SetConfig[(Set the Configuration)]
GetParams-- If release set -->SetConfig
SetConfig-->Page


Page-- Set Release -->SetConfig

Page-- Set Category -->SetConfig
Page-- Set Moderators -->SetConfig
Page-- Set Prefix -->SetConfig
Page-- Set FineTune -->SetConfig
Page-- Set Rag -->SetConfig

Page-->AddQA[Add Prompt Test]
AddQA-->ViewQA[View Saved QA Tests]
SetConfig-- Get Config For Test-->RunQA
ViewQA-->RunQA[Run Prompt Test]
ViewQA-->UpdateQA[Update Prompt Test]
ViewQA-->DeleteQA[Delete Prompt Test]
```

### Transcript Testing

> Here the user creates transcripts to test out releases or a combination of release components

```mermaid
graph TD;

style Fail1 fill:#FAA,stroke:#f00


Enters[Enters Transcript Test Page]
Enters-->CheckPermission1{Check if User has Test Permission}
CheckPermission1 -->|Success| GetParams[(Get Parameters from URL)]
CheckPermission1 -->|Fail| Fail1([Display No Permission Page])

GetParams-- If no components set --> Page[Transcript Test Page]
GetParams-- If components set -->SetConfig[(Set the Configuration)]
GetParams-- If release set -->SetConfig

Page-- Set Release -->SetConfig

Page-- Set Category -->GetCategoryComponents[Get WorkBench Components using Category]
GetCategoryComponents-- Set Moderators -->SetConfig
GetCategoryComponents-- Set Prefix -->SetConfig
GetCategoryComponents-- Set FineTune -->SetConfig
GetCategoryComponents-- Set Rag -->SetConfig

Page-->ViewTranscript[View Created Transcripts]
ViewTranscript-->ChooseTranscript[Choose Transcript]
ChooseTranscript-- Use Saved Transcript's Config -->SetConfig
ChooseTranscript-->TranscriptItem[(Transcript Item)]

Page-->AddTranscript[Add New Test Transcript]
SetConfig-.->|Use Config for new Transcripts|AddTranscript
AddTranscript-->TranscriptItem

TranscriptItem-->DeleteTranscript[Delete the Transcript]
TranscriptItem-- Use as Chat -->TranscriptItem
```
