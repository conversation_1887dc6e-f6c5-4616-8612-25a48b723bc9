
### WorkBench Outlet

> Before Accessing Anything in the WorkBench, including releases, components testing and consumer tracking, the user must login and have permission related to the workbench.

```mermaid
graph TD;

style DisplayLogin fill:#FAA,stroke:#f00


Enters[Enters WorkBench Outlet]
Enters-->EnsureUser[Ensure User Logged In]

EnsureUser-- Fail -->DisplayLogin([Display Must Log In for WorkBench Usage])
EnsureUser-- Success -->Continue[Continue to the Workbench Page]
```


### WorkBench Index

```mermaid

graph TD;

WorkBenchIndex[Display WorkBench Index Page]

WorkBenchIndex -->CreateWB[Create WorkBench]
WorkBenchIndex -->OwnWorkBench[View Own WorkBenches]
WorkBenchIndex -->SavedWorkBench[View Saved WorkBenches]

CreateWB -- Using Created Doc -->WorkBench[Go to WorkBench Item]
OwnWorkBench -- Choses Doc -->WorkBench
SavedWorkBench -- Choses Doc -->WorkBench

```


### WorkBench Item


```mermaid

graph LR;

WorkBench --> EditWB[Edit WorkBench]
EditWB -->UpdateName[Update Title/Description/Picture]
EditWB -->DeleteWB[Delete WorkBench]

EditWB --> Payment[Set User that pays for WorkBench and Release Usage]
EditWB --> Permissions[Edit Permissions for WorkBench]
EditWB --> NotificationConfig[Edit Notification Configuration for All Releases of WorkBench]

WorkBench --> ReleaseCreation[Build Release Components and Releases themselves]

ReleaseCreation --> WBReleases[Handle WorkBench Releases]
ReleaseCreation --> WBNotification[Handle Notification Components]
ReleaseCreation --> WBModeration[Handle Moderation Components]
ReleaseCreation --> WBPrefix[Handle Prefix Components]
ReleaseCreation --> WBFineTune[Handle Fine Tuning Components]
ReleaseCreation --> WBRAG[Handle RAG Components]

WorkBench --> Testing[Run Tests on Releases or Components]
Testing --> WBQuality[Use Prompts for Testing]
Testing --> WBTestTranscript[Use Chats for Testing]

WorkBench --> Consumer[View Consumer Related Data]

Consumer --> Threads[View Threads using a Release from this WorkBench]
Consumer --> Notifications[View Notifications of Releases from this WorkBench]

```