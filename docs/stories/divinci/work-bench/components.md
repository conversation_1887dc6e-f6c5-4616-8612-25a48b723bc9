
### Component Versioning

> When a Component Is being used by a release, We may want to track the versions of the Component and Increment the minor version of the Release.

```mermaid
---
title: Update Version Before Component Update
---

graph TD

style FailToUpdate fill:#FAA,stroke:#f00

InitializeUpdate[About to Update A Component]
InitializeUpdate-->CheckReleases{Check if the Component is currently Used In active Releases}
CheckReleases-- Yes -->CheckIfAllowsUpdates{Check If any of the Releases Don't allow Updates}
CheckReleases-- No -->Update[Update the Component]
CheckIfAllowsUpdates-- At Least One Release Doesn't Allow Updates -->FailToUpdate([throw Cannot update Component, Must be Forked First])
CheckIfAllowsUpdates-- All Allow Updates -->UpdateVersions[Increment the Version of the Component<br />Increment Each Releases Minor Version]-->Update

```

```mermaid
---
title: Ensure Version, Component Update, Finalize Version
---

graph TD

style ReleaseFail fill:#FAA,stroke:#f00
style FailUpdate fill:#FAA,stroke:#f00

InitializeUpdate[About to Update A Component]
InitializeUpdate-->CheckReleases{Check if the Component is currently Used In active Releases}
CheckReleases-- No -->TryUpdate{Try to Update the Component}

CheckReleases-- Yes -->CheckIfAllowsUpdates{Check If any of the Releases Don't allow Updates}
TryUpdate-- Fail -->FailUpdate([Fail to Update])

CheckIfAllowsUpdates-- At Least One Release Doesn't Allow Updates -->ReleaseFail([throw Cannot update Component, Must be Forked First])

CheckIfAllowsUpdates-- All Allow Updates -->TryUpdate2{Try to Update the Component}

TryUpdate2-- Fail --> FailUpdate([Fail to Update])
TryUpdate2-- Success -->UpdateVersions[Increment the Version of the Component<br />Increment Each Releases Minor Version]


```


### Notification Triggers

> With Notifications, we run the prompt or response through a classifier (Most likely a LLM that should return true or false). From there, triggered notifications can be viewed within the Consumer

```mermaid
graph TD;

style Fail1 fill:#FAA,stroke:#f00
style FailTest fill:#FAA,stroke:#f00
style FailUpdate fill:#FAA,stroke:#f00


Enters[Enters Moderator Page]
Enters -->CheckPermission1{Check if User has Notification Permission}
CheckPermission1 -->|Success| Page[Notification Page]
CheckPermission1 -->|Fail| Fail1([Display No Permission Page])

Page-->Create[Create Notification]
Create-- Handle Created Notification -->Item[Notification Item]
Page-->ViewItems[View Created Notifications]
ViewItems--Choose a Notification-->Item
Item-->UpdateInfo[Update the Notification title/description]
Item-->InitiateUpdatePrompt[Initiate Update the Notification Component Prompt]
InitiateUpdatePrompt-->UpdateVersion{Run Component Version Update}
UpdateVersion
UpdateVersion-- Success -->UpdatePrompt[Update the Notification Component Prompt]
UpdateVersion-- Fail -->FailUpdate([Fail Update])

Item-->Test[Try out the Notification with prompts]

Test-->PayInput{Try to Pay for Notification and Test Prompt}
PayInput-- Fail --> FailTest(["Throw Payment Error"])
PayInput-- Success -->RunPrompt[Run Notification Prompt]
RunPrompt-->PayOutput[Pay for the Notification Result]
RunPrompt-->ShowResult[Show the Notification Result]

Item-->Delete[Delete Notification]
```




### Moderation

> Similar to Notification, the Moderators act as a prompt classifier. The difference is that instead of triggering a notification for the workbench owners to checkout, a flagged prompt would be prevented from continueing. This way we can prevent bad actors, offtopic or prompts that hold sensitive information.

```mermaid

graph TD;

style Fail1 fill:#FAA,stroke:#f00
style FailTest fill:#FAA,stroke:#f00
style FailUpdate fill:#FAA,stroke:#f00

Enters[Enters Moderator Page]
Enters -->CheckPermission1{Check if User has Moderator Permission}
CheckPermission1 -->|Success| ModeratorPage[Moderator Page]
CheckPermission1 -->|Fail| Fail1([Display No Permission Page])

ModeratorPage-->Create[Create Moderator]
Create-- Handle Created Moderator -->Item[Moderator Item]
ModeratorPage-->ViewItems[View Created Moderators]
ViewItems--Choose a Moderator-->Item
Item-->UpdateInfo[Update the Moderators title/description]
Item-->InitiateUpdatePrompt[Initiate Update the Moderator Component Prompt]
InitiateUpdatePrompt-->UpdateVersion{Run Component Version Update}
UpdateVersion-- Fail -->FailUpdate([Fail Update])
UpdateVersion-- Success -->UpdatePrompt[Update the Moderator Component Prompt]

Item-->Test[Try out the moderator with prompts]

Test-->PayInput{Try to Pay for Moderator and Test Prompt}
PayInput-- Fail --> FailTest(["Throw Payment Error"])
PayInput-- Success -->RunPrompt[Run Moderator Prompt]
RunPrompt-->PayOutput[Pay for the Moderator Result]
RunPrompt-->ShowResult[Show the Moderator Result]

Item-->Delete[Delete Moderator]
```

### Pinned Prefix

> Pinned Prefix acts as prompts to give the LLM some context before any of the messages start.

```mermaid
graph TD;

style Fail1 fill:#FAA,stroke:#f00
style FailTest fill:#FAA,stroke:#f00
style FailUpdate fill:#FAA,stroke:#f00


Enters[Enters Prefix Page]
Enters -->CheckPermission1{Check if User has Prefix Permission}
CheckPermission1 -->|Success| Page[Prefix Page]
CheckPermission1 -->|Fail| Fail1([Display No Permission Page])

Page-->Create[Create Prefix]
Create-- Handle Created Prefix -->Item[Handle Prefix Item]
Page-->ViewItems[View Created Prefix]
ViewItems--Choose a Prefix-->Item

Item-->UpdateInfo[Update the Prefix title/description]
Item-->InitiateUpdatePrompt[Initiate to Update the Pinned Prefix Component Prompts]
InitiateUpdatePrompt-->UpdateVersion{Run Component Version Update}
UpdateVersion--Failed-->FailUpdate([Fail to Update])
UpdateVersion--Success-->UpdatePrompt[Update the Pinned Prefix Component Prompts]



Item-->Test[Try out the prefix with prompts]
Test-->PayInput{Try to Pay for Prefix and Test Prompt}
PayInput-- Fail --> FailTest(["Throw Payment Error"])
PayInput-- Success -->RunPrompt[Run Prefix and Prompt]
RunPrompt-->PayOutput[Pay for the Prefix Result]
RunPrompt-->ShowResult[Show the Prefix Result]


Item-->Delete[Delete Prefix]
```


### FineTune

> FineTuned models provide information and expected response formats for the LLM to use

```mermaid
graph TD;

style FailPermission fill:#FAA,stroke:#f00
style FailVersion fill:#FAA,stroke:#f00
style FailReserve fill:#FAA,stroke:#f00
style FailAddFail fill:#FAA,stroke:#f00

Enters[Enters FineTune Page]
Enters -->CheckPermission1{Check if User has Prefix Permission}
CheckPermission1 -->|Success| Page[FineTune Page]
CheckPermission1 -->|Fail| FailPermission([Display No Permission Page])

Page-->Create[Create FineTune]
Create-- Handle Created FineTune -->Item[Handle FineTune Item]
Page-->ViewItems[View Created FineTune]
ViewItems--Choose a FineTune-->Item

Item-->UpdateInfo[Update the fineTune title/description]
Item-->Delete[Delete FineTune]
Item-->InitiateAddFile[Start Add Prompt/Response to FineTune]

Page-->UploadFile[Upload Raw Prompt/Response Array]
Page-->ConvertCSV[Upload and edit CSV]
ConvertCSV-- Convert to Prompt/Response Array -->UploadFile
UploadFile-.->InitiateAddFile

InitiateAddFile-->EnsureVersion{Ensure Can Increment Version}
EnsureVersion--Fail-->FailVersion([Fail FineTune Use File])
EnsureVersion--Success-->ReservePay{Try to Reserve an amount}
ReservePay-- Fail --> FailReserve([Throw Payment Error])
ReservePay-- Success -->RunAddFile{Add Prompt/Response to FineTune}
RunAddFile-- Fail -->Refund[Try to Refund the amount reserved]
Refund-->FailAddFail([Throw Run Add File])
RunAddFile-- Success -->TruePayment[Pay/Refund the difference between Reserve ane True Cost]
TruePayment-->UpdateVersion[Run Component Version Update]

```


### RAG

> FineTuned models have a limit for the amount of information it can hold. However, with Retrieval Augmentation we can add a larger amount of information that will be added to each message on demand without having to add it to the finetune.

```mermaid
graph TD;

style FailPermission fill:#FAA,stroke:#f00
style FailVersion fill:#FAA,stroke:#F00
style FailReserve fill:#FAA,stroke:#f00

Enters[Enters Rag Page]
Enters -->CheckPermission1{Check if User has Rag Permission}
CheckPermission1 -->|Success| Page[Rag Page]
CheckPermission1 -->|Fail| FailPermission([Display No Permission Page])

Page-->Create[Create Rag]
Create-- Handle Created Rag -->Item[Handle Rag Item]
Page-->ViewItems[View Created Rag]
ViewItems--Choose a Rag-->Item

Item-->UpdateInfo[Update the Rag title/description]
Item-->Delete[Delete Rag]
Item-->TryAddFile[Try to Add File Chunks to RAG]

Page-->UploadFile[Upload Raw Text Extractable File]
UploadFile-->EditChunks[Edit Text Chunks]
EditChunks-.->TryAddFile

TryAddFile-->EnsureVersion{Ensure Can Increment Version}
EnsureVersion--Fail-->FailVersion([Fail RAG Use File])
EnsureVersion--Success-->ReservePay{Try to Reserve an amount}
ReservePay-- Fail --> FailReserve([Throw Payment Error])
ReservePay-- Success -->RunAddFile[Add File Chunks to Rag]
RunAddFile-->TruePayment[Pay/Refund the difference between Reserve and True Cost]
TruePayment-->UpdateVersion[Run Component Version Update]

```