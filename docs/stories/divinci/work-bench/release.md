
### Release Index

```mermaid

graph TD;

style Fail1 fill:#FAA,stroke:#f00


Enters[Enters Release Page]
Enters -->CheckPermission1[Check if User has Release Permission]
CheckPermission1 -->|Success| ReleasePage[Release Page]
CheckPermission1 -->|Fail| Fail1([Display No Permission Page])

ReleasePage-->CreateRelease["Create Release"]
CreateRelease -->ReleaseForm["Release Form"]
ReleasePage-->ViewReleases["View Available Releases"]
ViewReleases -->|Choose Draft Release| ReleaseForm
ViewReleases -->|Choose Published Release| ViewRelease["Active Release"]
ViewReleases -->|Choose Deprecated Release| ViewRelease

```

### Release Form


```mermaid

graph LR;

style FailFinalize fill:#FAA,stroke:#f00

ReleaseForm[Release Form]

ReleaseForm-- Finalize Release -->AddVersions[Make sure components are given versions]
AddVersions-->StartFinalize[Try to Finalize Release]
StartFinalize-->CheckComponents{Check If any components are updating}
CheckComponents-- Components are Updating -->FailFinalize([throw Can't Finalize Release with Updating Components]) 
CheckComponents-- No Components are Updating-->FinalizeRelease[Publish Release for public use]
FinalizeRelease-->ViewRelease["View Release"]

ReleaseForm -->Fork[Fork The Current Release]
ReleaseForm -->UserConfig[Set Title/Description]
ReleaseForm -->AllowsUpdates[Set Release allows Components to Update]
ReleaseForm -->AnonConfig[Set Anonymous Configuration]
ReleaseForm -->SaveRelease[Save Release Configuration]

ReleaseForm -->ViewModerators[View Available Moderators]
ViewModerators-->|With Confguration| AddModeration[Add Moderator to Release]
AddModeration -->|With Configuration| UpdateModerator[Update Moderator in Release]
AddModeration-->RemoveModerator[Remove Moderator from Release]

ReleaseForm -->|With Category| ViewPrefix["View Available Prefixes of Category"]
ViewPrefix-->SetPrefix[Set Prefix]
SetPrefix --> RemovePrefix[Remove Prefix]
SetPrefix --> SetPrefix

ReleaseForm -->|With Category| ViewFineTune[View Available Base Models and FineTunes of Category]
ViewFineTune-->SetFineTune[Set Fine Tune Messenger]
SetFineTune --> RemoveFineTune[Remove Fine Tune]
SetFineTune --> SetFineTune

ReleaseForm -->|With Category| ViewRags[View Available RAGs of Category]
ViewRags-->SetRAG[Set RAG]
SetRAG --> RemoveRAG[Remove RAG]
SetRAG --> SetRAG


```


### Active Release

```mermaid

graph LR;

ViewRelease["View Release"]
ViewRelease -- Deprecate Release -->Deprecate[Release and Chats that use the Release can no longer be used]
ViewRelease -->UserConfig[Set Title/Description]
ViewRelease -->AllowsUpdates[Set Release allows Components to Update]
ViewRelease -->AnonConfig[Set Anonymous Configuration]

ViewRelease-->ViewThreads["View Threads using Release"]
ViewThreads-->ViewThread[View Individual Thread]
ViewRelease-->ViewNotifications["View Notifications of Release"]
ViewNotifications-- View Thread causing Notification -->ViewThread
ViewNotifications-- Toggle Notification -->TurnOff[Turn Notification  Finished] 

```

### Deprecated Release

```mermaid

graph TD;

Deprecate[Deprecate Release]
Deprecate -->UserConfig[Set Title/Description]

Deprecate-->ViewThreads["View Threads using Release"]
ViewThreads-->ViewThread[View Individual Thread]


```