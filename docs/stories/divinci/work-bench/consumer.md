
### Consumer Threads (Can Specify Release)

Thread Columns
- release used
- whether release is active or deprecated
- Create Date
- Last Usage Date
- Number of Posts
- Number of Message Attempts Moderated
  - indicates the user may be a Bad Actor
- Number of Communications Resolved
  - past communications can give us feedback on how to better the AI


```mermaid

graph TD;

style Fail1 fill:#FAA,stroke:#f00

Enters[Enters Release Consumer Threads Page]
Enters -->CheckPermission1{Check if User has Consumer Permission}
CheckPermission1 -->|Success| ReleaseSpecified[Release can be specified in URL]
CheckPermission1 -->|Fail| Fail1([Display No Permission Page])

Page[Consumer Threads Page]

GetAllThreads ~~~ GetReleaseThreads


ReleaseSpecified-- Yes-->GetReleaseThreads[Retrieve Threads using Speciified Release] -->Page
ReleaseSpecified-- No -->GetAllThreads[Retrieve All Threads belonging to WorkBench] -->Page


Page-- Remove Release -->GetAllThreads
Page-- Specify Release -->GetReleaseThreads


Page-->ViewThreads[View threads as table]
ViewThreads-->SortThreads[Sort Threads by column]
ViewThreads-->FilterThreads[Filter Threads by column, Tag or Communicate Status]
ViewThreads-->TagThread[Toggle Tag on Thread]

ViewThreads-->ViewThread[Look at Individual Thread]
```

### Consumer Thread

```mermaid
graph LR;

style Fail1 fill:#FAA,stroke:#f00

Enters[Enters Release Consumer Thread Page]
Enters -->CheckPermission1{Check if User has Consumer Permission}
CheckPermission1 -->|Fail| Fail1([Display No Permission Page])
CheckPermission1 -->|Success| ViewThread[View Specified Thread]


ViewThread-->ViewMessageNotification[For Each Message, It tells the viewer what Notifications it has triggered]

ViewThread-->TryCommunicate[Try to Communicate on thread]
TryCommunicate-->CheckRequested1[Check if User Requested Communication]
CheckRequested1-- Not Requested -->ThrowCommunicate1[Throw User doesn't want communication]
CheckRequested1-- Requested -->AllowCommunication[Allow the User to communicate with consumer]

ViewThread-->TryToUseAI[Try to Communicate with AI]
TryToUseAI-->CheckRequested3[Check if User Requested Communication]
CheckRequested3-- Not Requested -->ThrowCommunicate3[Throw User doesn't want communication]
CheckRequested3-- Requested -->AllowAIUse[Allow the User to communicate with the AI on Thread]

ViewThread-->TryFinishCommunicate[Try to Finish Communication on Thread]
TryFinishCommunicate-->CheckRequested2[Check if User Requestsd Communication]
CheckRequested2-- Not Requested -->ThrowCommunicate2[Can't finish non existant communication]
CheckRequested2-- Requested -->AllowFinish[Allow the User to resolve the conversation]
```

### Consumer Notifications (Can Specify Release)

Notification Columns
- release used
- whether release is active or deprecated
- Create Date
- Notification Title

```mermaid
graph TD;

style Fail1 fill:#FAA,stroke:#f00


Enters[Enters Release Consumer Threads Page]
Enters -->CheckPermission1{Check if User has Consumer Permission}
CheckPermission1 -->|Success| ReleaseSpecified[Release can be specified in URL]
CheckPermission1 -->|Fail| Fail1([Display No Permission Page])

Page[Consumer Notifications Page]

ReleaseSpecified-- No -->GetAllThreads[Retrieve All Threads belonging to WorkBench]
ReleaseSpecified-- Yes-->GetReleaseThreads[Retrieve Threads using Speciified Release]

GetAllThreads-->Page
Page-- Remove Release -->GetAllThreads
GetReleaseThreads-->Page
Page-- Specify Release -->GetReleaseThreads


Page-->ViewTable[View Notification as table]
ViewTable-->Sort[Sort Notifications by column]
ViewTable-->Filter[Filter Notifications by column or Tag]
ViewTable-->Tag[Toggle Tag on Notification]

ViewTable-->ViewItem[Look at Thread that Triggered Notification]


```