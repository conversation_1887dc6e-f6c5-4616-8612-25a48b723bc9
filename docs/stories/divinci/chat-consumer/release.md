
### Release Item

Release Statistics
- Total Up/Down votes
- Number Total Users
- Number Total Threads

Threads Table Columns
- Owner
- Title
- Total Messages
- Up/Down votes

```mermaid
graph TD;

style DraftFail fill:#FAA,stroke:#F00
style VoteFail fill:#FAA,stroke:#F00
style CreateFail fill:#FAA,stroke:#F00

Enters([Enters Release Page])

Page[Release Page]

Enters-->CheckNotDraft{Make Sure Release is not a Draft}
CheckNotDraft -->|Not Draft| DraftFail([Display Not Found Page])
CheckNotDraft -- Draft --> Page


Page-->ViewStatistics[View Vote and Usage Statistics]

Page-- Up/Down vote Action -->EnsureUser1{Ensure User is Logged in}
EnsureUser1--Fail-->VoteFail([Throw Vote Fail])
EnsureUser1--Success--> Vote[Run Up/Down Vote]

Page-- Create Chat Action -->EnsureUser2{Ensure User is Logged in}
EnsureUser2-->|Failure| CreateFail([Throw Create Chat Fail])
EnsureUser2-->|Success| CreateChat[Start Chat Using the Release]

Page-->ThreadsTable[View Public Chats using Release]
ThreadsTable-->ThreadItem[Go to Chat Page]
```

