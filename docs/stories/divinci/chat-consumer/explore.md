
# Explore Pages

```mermaid
graph LR;

style ChatFail fill:#FAA,stroke:#F00
style ReleaseFail fill:#FAA,stroke:#F00

Page[Explore Page]

Page --> ExploreChats[Explore Trending/Top Rated Public Chats]
ExploreChats -->|Choose Chat| ChatPage[Go to Chat Page]

Page --> ExploreReleases[Explore Trending/Top Rated Active Releases]
ExploreReleases -->|Choose Release| ReleasePage[Go to Release Page]

Page --> SearchReleases[Search Active Release Titles and Descriptions]
SearchReleases -- Choose Release --> ReleasePage[Go to Release Page]

Page --> StartChat[Start a Chat]
StartChat --> EnsureUser1[Ensure User is Logged In]
EnsureUser1 -->|Failure| ChatFail([Throw Chat Fail])
EnsureUser1 -->|Success| NewChatPage[Go to New Chat Page]

Page --> StartRelease[Start a Release]
StartRelease --> EnsureUser2[Ensure User is Logged In]
EnsureUser2 -->|Failure| ReleaseFail([Throw Release Fail])
EnsureUser2 -->|Success| NewReleasePage[Go to WorkBanch Page]
```
