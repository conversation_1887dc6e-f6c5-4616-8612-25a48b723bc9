
```mermaid
graph TD;

style DisplayFail fill:#FAA,stroke:#F00
style PermissionFail fill:#FAA,stroke:#F00
style VoteFail fill:#FAA,stroke:#F00
style ForkFail fill:#FAA,stroke:#F00

style AddMessageFail fill:#FAA,stroke:#F00
style ModerationFail fill:#FAA,stroke:#F00
style PaymentFail fill:#FAA,stroke:#F00

Enters[Enters Chat Page]
Enters -->CheckPermission1{Check if Chat is Public or User has View Permission}
CheckPermission1 -->|Fail| DisplayFail([Display No Permission Page])
CheckPermission1 -->|Success| ChatPage[Chat Page]

ChatPage-- Up/Down vote -->CheckPermissionVote{Check User has View Permission}
CheckPermissionVote -- Failure --> VoteFail([Throw Fail to Vote])
CheckPermissionVote -- Success --> Vote[Set Up/Down vote]

ChatPage-->|Choose Permissions| CheckPermission3{Check If User has 'Permission' Permission}
CheckPermission3 -->|Fail| PermissionFail([Display No Permission Page])
CheckPermission3 -->|Success| PermissionPage[Go to Permission Page]

ChatPage-- ForkChat --> CheckPermissionFork{Check User has View Permission}
CheckPermissionFork --Fail--> ForkFail([Throw Fail to Fork])
CheckPermissionFork --Success--> ForkChat[Forks the Current chat for you to use]

ChatPage-- View Release Chat Uses -->ReleasePage[Go to Release Page]

ChatPage-->|Choose Message User| ProfilePage[Go to User Profile Page]
ChatPage-->|Choose Owner| ProfilePage[Go to User Profile Page]


ChatPage -- Choose Category --> MessageConfig[("'Add Message' Configuration")]
ChatPage -- Choose AI Assistant --> MessageConfig
ChatPage -- Choose ReplyTo --> MessageConfig

ChatPage-->|Add Message| CheckPermission2{Check if User has 'Add Message' Permission}
CheckPermission2 -->|Success| TryAddMessage["Try Add Message to Transcript"]
CheckPermission2 -->|Fail| AddMessageFail([throw No Message Add Permission])

MessageConfig -.->|Get Message Config| TryAddMessage

TryAddMessage --> Moderation{Check if Message Passes Moderation}
Moderation-->|Fail| ModerationFail([throw Moderation Fail])
Moderation-->|Success| Payment{Check if User has Enough Balance}
Payment-->|Fail| PaymentFail[throw Payment Fail]
Payment-->|Success| AddMessage([Add Message to Transcript])



```