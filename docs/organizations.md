
A person within an organization may have different permissions than another given a peice of data

When attempting to do an action to a peice of data
- check if user has permission to do that action

- If the user is not logged in, check if the "anonymous user" can do the action

- User can be given a role
  - A role may have one or more permissions


```typescript

hasPermissionTo("organization-permission-type")

function hasPermissionTo(permissionType){
  return (req, res, next)=>{
    const user = req.user;
    const data = retrieveData(req.params.data)
    const organization = retrieveOrganization(data.organization)
    organization.canDo(user, permissionType)
  }
}

type Permission = {
  organization: string,
  targetType: "user" | "role" | "anonymous",
  targetValue: string,
  itemId?: string,
  type: string,
  canDo: boolean
}

type Role = {
  organization: string,
  name: string,
  users: Array<string>,
}

function organizationCanDo(organization, user, permissionType, itemId){
  const itemUserPermission = Permission.findOne({
    organization: organization,
    targetType: "user",
    targetValue: user._id,

    itemId: itemId,
    type: permissionType
  });

  if(itemUserPermission !== null){
    return itemUserPermission.canDo;
  }

  const itemRoles = Role.find({
    organization: organization,
    users: { $elemMatch: user._id },
  })

  if(itemRoles.length > 0){
    const roleIds = itemRoles.map((role)=>(role._id));
    const itemRolePermissions = Permission.find({
      organization: organization,
      targetType: "role",
      targetValue: { $in: roleIds },

      itemId: itemId,
      type: permissionType
    });
    const itemRolePermission = itemRolePermissions.filter((permission)=>{
      if(permission === null) return false;
      return true;
    })[0];

    if(typeof itemRolePermission !== "undefined"){
      return itemRolePermission.canDo;
    }
  }

  const itemAnonymousPermission = Permission.findOne({
    organization: organization,
    targetType: "anonymous",
    targetValue: "",

    itemId: itemId,
    type: permissionType
  });

  if(itemAnonymousPermission !== null){
    return itemAnonymousPermission.canDo;
  }


  const orgUserPermission = Permission.findOne({
    organization: organization,
    targetType: "user",
    targetValue: user._id,

    itemId: void 0,
    type: permissionType
  });

  if(orgUserPermission !== null){
    return orgUserPermission.canDo;
  }

  const orgRoles = Role.find({
    organization: organization,
    users: { $elemMatch: user._id },
  })

  if(itemRoles.length > 0){
    const roleIds = itemRoles.map((role)=>(role._id));
    const itemRolePermissions = Permission.find({
      organization: organization,
      targetType: "role",
      targetValue: { $in: roleIds },

      itemId: void 0,
      type: permissionType
    });
    const itemRolePermission = itemRolePermissions.filter((permission)=>{
      if(permission === null) return false;
      return true;
    })[0];

    if(typeof itemRolePermission !== "undefined"){
      return itemRolePermission.canDo;
    }
  }

  const orgAnonymousPermission = Permission.findOne({
    organization: organization,
    targetType: "anonymous",
    targetValue: "",

    itemId: void 0,
    type: permissionType
  });

  if(orgAnonymousPermission !== null){
    return orgAnonymousPermission.canDo;
  }

  return false;

}

```
