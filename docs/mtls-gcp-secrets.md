# Setting up mTLS with GCP Secrets

This document explains how to set up mutual TLS (mTLS) authentication using GCP Secrets for certificate management.

## Overview

Instead of embedding certificates directly in the Docker image, we now use GCP Secrets to securely store and mount the certificates into the container at runtime. This approach has several advantages:

1. **Security**: Certificates are not stored in the Docker image
2. **Flexibility**: Certificates can be rotated without rebuilding the image
3. **Environment-specific**: Different environments can use different certificates

## Prerequisites

- Google Cloud SDK installed and configured
- Proper permissions to create and manage GCP Secrets
- Valid mTLS certificates for your environment (from Cloudflare)

## Certificate Requirements

You need the following certificate files in PEM format:

- `server.crt`: Server certificate (must start with `-----BEGIN CERTIFICATE-----`)
- `server.key`: Server private key (must start with `-----BEGIN PRIVATE KEY-----`)
- `client.crt`: Client certificate for verification (must start with `-----BEGIN CERTIFICATE-----`)

### Certificate Format

Certificates must be in PEM format. A valid PEM certificate looks like this:

```
-----BEGIN CERTIFICATE-----
MIIDFTCCAf2gAwIBAgIUMeKJCJpqjrP5x4hLe8QA1OW5dogwDQYJKoZIhvcNAQEL
BQAwMzESMBAGA1UEAwwJbG9jYWxob3N0MRAwDgYDVQQKDAdEaXZpbmNpMQswCQYD
...
(base64-encoded content)
...
hUohT4xWrqzzCsOk+ZtNMo7Kt1F2bRgIcw==
-----END CERTIFICATE-----
```

If your certificate is just a base64-encoded string without the `BEGIN` and `END` markers, you need to add them to create a valid PEM file.

## Certificate Storage

We use two complementary approaches for certificate management:

1. **Development/Local**: Certificates are stored in the `private-keys/${ENVIRONMENT}/certs/mtls/` directory
2. **Production/GCP**: Certificates are mounted from GCP Secrets to the same path

This dual approach ensures consistency across environments while maintaining security.

## Setting Up GCP Secrets

### Important: Secret Names

The service is configured to use two secrets named `server-crt` and `server-key`. Make sure these secrets exist in your GCP project.

### Option 1: Using the Scripts

We provide scripts to automate the creation and verification of GCP Secrets:

```bash
# Create the individual certificate and key secrets
./deploy/scripts/create-individual-cert-secrets.sh
```

### Option 2: Manual Setup

1. Create the individual secrets in GCP:

```bash
# Create the server.crt secret
gcloud secrets create server-crt --data-file=./private-keys/staging/certs/mtls/server.crt.pem --replication-policy="automatic"

# Create the server.key secret
gcloud secrets create server-key --data-file=./private-keys/staging/certs/mtls/server.key.pem --replication-policy="automatic"
```

2. Configure the secrets in GCP Cloud Run:
   - Go to GCP Console > Cloud Run > Your Service > Configuration > Secrets
   - Add the first secret volume:
     - Name: `mtls-certs`
     - Mount path: `/etc/ssl/certs`
     - Secret: `server-crt`
   - Add the second secret volume:
     - Name: `mtls-key`
     - Mount path: `/etc/ssl/private`
     - Secret: `server-key`

## Service Configuration

The service template (`deploy/service-template.yaml`) has been updated to include the necessary volume mounts for the mTLS certificates. The key changes are:

1. Added volume mounts to the container:
```yaml
volumeMounts:
  - name: mtls-certs
    mountPath: /etc/ssl/certs
  - name: mtls-key
    mountPath: /etc/ssl/private
```

2. Added volumes to the pod spec:
```yaml
volumes:
  - name: mtls-certs
    secret:
      secretName: server-crt
  - name: mtls-key
    secret:
      secretName: server-key
```

## Environment Variables

Make sure the following environment variables are set in your service:

```
ENABLE_MTLS=1
MTLS_CERT_DIR=/etc/ssl
```

## Troubleshooting

If you encounter issues with mTLS:

1. **Check certificate permissions**: Make sure the container has read access to the mounted certificates
2. **Verify certificate paths**: Ensure the paths in the volume mount match what the application expects
3. **Check certificate validity**: Verify that the certificates are valid and not expired
4. **Check logs**: Look for any certificate-related errors in the application logs

### Common Errors

#### PEM Format Error

If you see an error like this:

```
Error: error:0480006C:PEM routines::no start line
```

This means your certificate is not in the proper PEM format. Make sure your certificate:

1. Starts with `-----BEGIN CERTIFICATE-----`
2. Ends with `-----END CERTIFICATE-----`
3. Contains the base64-encoded certificate data between these markers

You can use the provided script to update the GCP Secret with a properly formatted certificate:

```bash
./deploy/scripts/update-gcp-cert-secret.sh private-keys/staging/certs/mtls/server.crt.pem
```

## Certificate Rotation

To rotate certificates:

1. Update the certificates in the `private-keys/${ENVIRONMENT}/certs/mtls/` directory
2. Create new versions of the GCP Secrets with the updated certificates
3. The changes will be automatically picked up on the next container restart

## GitHub Actions Integration

The `private-keys/` directory is available during the GitHub Actions GCP deployment process, so we can use it to copy the certificates into the Docker image. However, for production deployments, we still recommend using GCP Secrets to mount the certificates at runtime for better security and flexibility.
