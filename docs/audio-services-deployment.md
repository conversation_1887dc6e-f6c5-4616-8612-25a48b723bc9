# Audio Services Deployment

This document explains how to deploy the audio processing services (pyannote and FFmpeg) to the various environments.

## Services Overview

### Pyannote Speaker Diarization

The Pyannote service runs a speaker diarization model that identifies who is speaking in audio files. It's built using Python 3.11 with the pyannote.audio library and runs as a Flask application.

- **Source**: `workspace/workers/audio-speaker-diarization@pyannote`
- **Docker**: `deploy/docker/ci/pyannote.ci.Dockerfile`
- **Port**: 8085

### FFmpeg Audio Processor

The FFmpeg service provides audio processing utilities like audio duration calculation, MP3 conversion, and audio slicing. It's built using Node.js 22 with TypeScript and uses FFmpeg under the hood.

- **Source**: `workspace/workers/audio-splitter@ffmpeg`
- **Docker**: `deploy/docker/ci/audio-splitter.ci.Dockerfile`
- **Port**: 8086

## Deployment Commands

You can deploy these services using deployment comments in GitHub Pull Requests.

### Manual Deployment 

Add a comment to your PR with one of the following commands:

```
# Deploy Pyannote only
@github-actions [deploy:develop:pyannote]
@github-actions [deploy:stage:pyannote]
@github-actions [deploy:prod:pyannote]

# Deploy FFmpeg only
@github-actions [deploy:develop:ffmpeg]
@github-actions [deploy:stage:ffmpeg]
@github-actions [deploy:prod:ffmpeg]

# Deploy both services
@github-actions [deploy:develop:pyannote:ffmpeg]
@github-actions [deploy:stage:pyannote:ffmpeg]
@github-actions [deploy:prod:pyannote:ffmpeg]
```

### Using Helper Scripts

For convenience, you can use the helper scripts to trigger deployments:

```bash
# Deploy Pyannote
./.github/scripts/pyannote-deploy.sh develop
./.github/scripts/pyannote-deploy.sh stage
./.github/scripts/pyannote-deploy.sh prod

# Deploy FFmpeg
./.github/scripts/ffmpeg-deploy.sh develop
./.github/scripts/ffmpeg-deploy.sh stage
./.github/scripts/ffmpeg-deploy.sh prod

# Deploy both services
./.github/scripts/audio-services-deploy.sh develop
./.github/scripts/audio-services-deploy.sh stage
./.github/scripts/audio-services-deploy.sh prod
```

## Deployment Process

1. When a deployment command is detected, the CI/CD pipeline will:
   - Build the Docker image(s) for the specified service(s)
   - Push the image(s) to Google Artifact Registry
   - Deploy the service(s) to Google Cloud Run
   - Set up the necessary environment variables and resources

2. The deployment typically takes about 5-10 minutes to complete.

3. You can check the status of the deployment in the GitHub Actions tab of the repository.

## Troubleshooting

If the deployment fails, check the GitHub Actions logs for details. Common issues include:

- Missing environment variables or secrets
- Failed Docker builds due to dependency issues
- Cloud Run deployment failures due to resource constraints

## Resource Requirements

Both services have the following default resource allocations:

- **CPU**: 2000m (2 vCPU)
- **Memory**: 2Gi

These can be adjusted in the `deploy/config.json` file if needed.