# Divinci AI - Whitelabel Todo

- Manage Whitelabels
  - Create
  - Use a single one (when click thread, go to ai studio for that Whitelabel)
  - Delete
- Use AI Studio
  - Need to hide "rating system"
  - Only one user at a time chating (for training purposes)
    - Reason: User of the "prefix" can't use/access different entry (always replies to latest message)
- QA feedback loop
  - Make changes to the "prefix" (InContext Learning)
  - See how the QA prompts respond via running them through "chat tests"
- Release
  - Once published, instances using a version of that thread are subject to change based on the current publioshed version of the Prefix AI
    - This is becasue the Prefix AI will only have a single API endpoint to call on.
  - Users (including self) can use your "Whitelabel" as a base/prefix (if enabled by the owner)
  - Owners of a Whitelabel can view chats that use that Whitelabel (if the owner has this as a hard requirment, otherwise we default to privacy)(helps with)
- Deprecate
  - Users can no longer use that Whitelabel (so that the creator no longer has to pay)
  

## Controllers
- X - Create Whitelabel
- X - Get own Whitelabel list
- X - Get single Whitelabel
- X - Delete Whitelabel
  - and Deprecate at the same time
- X - update Whitelabel
  - title
  - description
  - Release/deprecate

- X - Add message to Whitelabel Transcript
  - while adding, prevent other interactions
- X - Strip messages from Whitelabel Transcript

- X - Add qa prompt for Whitelabel
- X - Run a single qa prompt for Whitelabel
- X - Run all qa prompts for Whitelabel
- X - Delete qa prompt for Whitelabel

- Get all threads that use a Whitelabel
- Use a Whitelabel as a base


## Model ✅ 🙌
- AI Owner
- AI Version
- AI Description
- Status - development, released, deprecated
- awaitingResponse: boolean
  - this is used to prevent multiple messags from happening at the same time
- Privacy Options
  - User can have offline/private chats or not with AI
  - User can publically publish threads or not
  - User can keep using deprecated version of the AI or not
- Optional Social/Ratings
  - How many threads created with this AI 
  - How many users using this AI
  - 5 star user rating of this AI
- Messages
  - From - User/System/Assistant
  - text - a string
    - If from user/system, its the input prompt
    - If from assistant, its the responding markdown
  
  

#3 Versioning
- They work on versions in private
- They release versions
  - Arguably they can release patches for a specific version
    - Existing threads use the version that they started with
    - New Threads use the latest version
- They can deprecate versions
  - This prevents use of that version



### Later
- Payment
  - Stripe monthly/yearly
  - People can pay for "usage ownership" of deprecated AI (They would have to pay for all the tokens they use)
- Notifcations
  - Catching key phrases/convos to look out for
  - Helps with re-training the AI
- Organizations (Multiple people have access to a single Whitelabel) 💯👍🏻