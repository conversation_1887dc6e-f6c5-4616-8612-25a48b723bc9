networks:
  local-network:
    driver: bridge

services:
  migrate:
    user: "node"
    image: node:22.2-slim
    command: pnpm run transcript-focus
    working_dir: "/home/<USER>/app/migration"
    volumes:
      - ../workspace:/home/<USER>/app
      - ../private-keys/local:/home/<USER>/app/migration/env
    networks:
      - local-network
    depends_on:
      - local-mongodb

  local-mongodb:
    extends:
      file: base.yml
      service: base-mongodb
    env_file:
      - ../private-keys/local/mongodb.env
    volumes:
      - ../hidden/mongodb:/data/db
    networks:
      - local-network
    ports:
      - "27017:27017" # This line maps port 27017 in the container to port 27017 on your host machine

  mongo-express:
    image: mongo-express
    networks:
      - local-network
    links:
      - local-mongodb
    environment:
      - ME_CONFIG_MONGODB_SERVER=local-mongodb
      - ME_CONFIG_MONGODB_AUTH_USERNAME=root
      - ME_CONFIG_MONGODB_AUTH_PASSWORD=example
    ports:
      - 8091:8081 # This line maps port 27017 in the container to port 27017 on your host machine

  local-redis:
    extends:
      file: base.yml
      service: base-redis
    env_file:
      - ../private-keys/local/redis.env
    volumes:
      - ../hidden/redis:/data
    networks:
      - local-network

  # redis-viewer:
  #   image: marian/rebrow
  #   networks:
  #     - local-network
  #   links:
  #     - local-redis
  #   ports:
  #     - 8092:5001  # This line maps port 27017 in the container to port 27017 on your host machine
