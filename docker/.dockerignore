#📓 https://docs.docker.com/build/building/context/#dockerignore-files

# Ignore node modules and other unwanted files
#node_modules
#**/node_modules

# Ignore system files
.DS_Store
Thumbs.db

# Ignore logs
*.log

# Ignore environment files
.env
.env.*

# Ignore version control
.git
.gitignore
.gitattributes

# Ignore temporary files and directories
tmp/
temp/
*.tmp
*.swp

# Ignore build directories
dist/
build/
coverage/
output/
.vscode
.idea

# Ignore other specific files
private-keys
workers
