networks:
  e2e-network:
    driver: bridge

services:
  e2e-web-client:
    extends:
      file: base.yml
      service: base-client
    volumes:
      - ../private-keys/e2e:/home/<USER>/app/clients/web/env
    networks:
      - e2e-network
    environment:
      - HTTP_PORT=18080
    ports:
      - 18080:18080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18080/"]

  e2e-api:
    extends:
      file: base.yml
      service: base-api
    volumes:
      - ../private-keys/e2e:/home/<USER>/app/servers/public-api/env
    networks:
      - e2e-network
    ports:
      - 18081:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18081/"]
    links:
      - e2e-mongodb
      - e2e-redis
    depends_on:
      - e2e-mongodb
      - e2e-redis

  e2e-api-live:
    extends:
      file: base.yml
      service: base-api-live
    volumes:
      - ../private-keys/e2e:/home/<USER>/app/servers/public-api-live/env
    networks:
      - e2e-network
    ports:
      - 18082:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18082/"]
    links:
      - e2e-mongodb
      - e2e-redis
    depends_on:
      - e2e-mongodb
      - e2e-redis

  e2e-api-webhook:
    extends:
      file: base.yml
      service: base-api-webhook
    volumes:
      - ../private-keys/e2e:/home/<USER>/app/servers/public-api-webhook/env
    networks:
      - e2e-network
    ports:
      - 18083:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18083/"]
    links:
      - e2e-mongodb
      - e2e-redis
    depends_on:
      - e2e-mongodb
      - e2e-redis

  e2e-mongodb:
    extends:
      file: base.yml
      service: base-mongodb
    env_file:
      - ../private-keys/e2e/mongodb.env
    volumes:
      - ../hidden/mongodb-e2e:/data/db
    networks:
      - e2e-network
    ports:
      - "37017:27017" # This line maps port 27017 in the container to port 37017 on your host machine

  e2e-redis:
    extends:
      file: base.yml
      service: base-redis
    env_file:
      - ../private-keys/e2e/redis.env
    volumes:
      - ../hidden/redis-e2e:/data/db
    networks:
      - e2e-network
