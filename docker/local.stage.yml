networks:
  local-network:
    driver: bridge

services:
  # ========================================================================
  # CLIENT Servers (1, web)
  # ========================================================================
  local-web-client:
    extends:
      file: base.yml
      service: base-client
    working_dir: "/home/<USER>/app/clients/web"
    environment:
      ENV_FOLDER: "/home/<USER>/app/clients/web/env"
      HTTP_PORT: 8080
    volumes:
      - ../private-keys/local-staging:/home/<USER>/app/clients/web/env
    networks:
      - local-network
    ports:
      - 8080:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]

  local-embed-client:
    extends:
      file: base.yml
      service: base-client
    working_dir: "/home/<USER>/app/clients/embed"
    environment:
      ENV_FOLDER: "/home/<USER>/app/clients/embed/env"
      HTTP_PORT: 8081
      STATIC_PORT: 8082
    volumes:
      - ../private-keys/local-staging:/home/<USER>/app/clients/embed/env
    networks:
      - local-network
    ports:
      - 8081:8081
      - 8082:8082
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/"]

  # ========================================================================
  # API Servers (3, api, live, webhook)
  # ========================================================================

  local-api:
    extends:
      file: base.yml
      service: base-api
    working_dir: "/home/<USER>/app/servers/public-api"
    environment:
      ENV_FOLDER: "/home/<USER>/app/servers/public-api/env"
    volumes:
      - ../private-keys/local-staging:/home/<USER>/app/servers/public-api/env
    networks:
      - local-network
    ports:
      - 9080:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9080/"]
    links:
      - local-d1-rag
      - local-mongodb
      - local-redis
    depends_on:
      - local-d1-rag
      - local-mongodb
      - local-redis

  local-api-live:
    extends:
      file: base.yml
      service: base-api
    working_dir: "/home/<USER>/app/servers/public-api-live"
    environment:
      ENV_FOLDER: "/home/<USER>/app/servers/public-api-live/env"
    volumes:
      - ../private-keys/local-staging:/home/<USER>/app/servers/public-api-live/env
    networks:
      - local-network
    ports:
      - 9081:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9081/"]
    links:
      - local-mongodb
      - local-redis
    depends_on:
      - local-mongodb
      - local-redis

  local-api-webhook:
    extends:
      file: base.yml
      service: base-api
    working_dir: "/home/<USER>/app/servers/public-api-webhook"
    environment:
      ENV_FOLDER: "/home/<USER>/app/servers/public-api-webhook/env"
    volumes:
      - ../private-keys/local-staging:/home/<USER>/app/servers/public-api-webhook/env
    networks:
      - local-network
    ports:
      - 9082:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9082/"]
    links:
      - local-mongodb
      - local-redis
    depends_on:
      - local-mongodb
      - local-redis

  # ========================================================================
  # WORKER (1, d1-rag)
  # ========================================================================

  local-d1-rag:
    extends:
      file: base.yml
      service: base-cloudflare-worker
    volumes:
      - ../workspace/workers/d1-doc-elements:/app
      - ../private-keys/local-staging/cloudflare.env:/app/.dev.vars
    networks:
      - local-network
    ports:
      - 8787:8787

  # ========================================================================
  # AI DATABASE (1, qdrant)
  # ========================================================================
  local-qdrant:
    image: qdrant/qdrant
    volumes:
      - ../hidden/qdrant:/qdrant/storage
    networks:
      - local-network
    ports:
      - 6333:6333
      - 6334:6334

  # sqlite-web:
  #   image: coleifer/sqlite-web
  #   container_name: sqlite-web
  #   environment:
  #     - SQLITE_DATABASE=dfo-chunks-1.db
  #   volumes:
  #     - ../workspace/workers/d1-doc-elements/dfo-chunks-1.db:/data/dfo-chunks-1.db
  #   ports:
  #     - 8888:8080
  #   restart: unless-stopped

  # ========================================================================
  # DATABASE (2, mongodb, redis)
  # ========================================================================
  local-mongodb:
    extends:
      file: base.yml
      service: base-mongodb
    env_file:
      - ../private-keys/local-staging/mongodb.env
    volumes:
      - ../hidden/mongodb:/data/db
    networks:
      - local-network
    ports:
      - "27017:27017" # This line maps port 27017 in the container to port 27017 on your host machine

  # mongo-express:
  #   image: mongo-express
  #   networks:
  #     - local-network
  #   links:
  #     - local-mongodb
  #   env_file:
  #     - ../private-keys/local-staging/mongodb.env
  #   ports:
  #     - 8091:8081

  local-redis:
    extends:
      file: base.yml
      service: base-redis
    env_file:
      - ../private-keys/local-staging/redis.env
    volumes:
      - ../hidden/redis:/data
    networks:
      - local-network

  # redis-viewer:
  #   image: marian/rebrow
  #   networks:
  #     - local-network
  #   links:
  #     - local-redis
  #   ports:
  #     - 8092:5001

  # tunnel:
  #   image: ngrok/ngrok:latest
  #   restart: unless-stopped
  #   command: start webhook --config /etc/ngrok.yml
  #   volumes:
  #     - ../private-keys/local-staging/ngrok.yml:/etc/ngrok.yml
  #   links:
  #     - api-webhook
  #   ports:
  #     - 4040:4040
