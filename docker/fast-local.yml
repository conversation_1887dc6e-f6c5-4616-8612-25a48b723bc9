name: divinci-local-fast

networks:
  local-network:
    driver: bridge

services:
  local-web-client:
    extends:
      file: base-fast.yml
      service: base-client
    working_dir: "/home/<USER>/app/workspace/clients/web"
    command: bash -c "cd /home/<USER>/app/workspace/clients/web && npm run start:dev"
    environment:
      HTTP_PORT: 8080
      HTTPS: "true"
      SSL_CERT_PATH: "/workspaces/server/private-keys/local-fast/server.crt"
      SSL_KEY_PATH: "/workspaces/server/private-keys/local-fast/server.key"
      ENVIRONMENT: "dev"
      PATH: "/home/<USER>/app/node_modules/.bin:$PATH"
    networks:
      - local-network
    ports:
      - 8080:8080
    healthcheck:
      test: ["CMD", "curl", "-kf", "https://localhost:8080/"]
      interval: 10s
      timeout: 5s
      retries: 3
    volumes:
      - /workspaces:/workspaces

  public-api-live:
    extends:
      file: base-fast.yml
      service: base-api
    working_dir: "/home/<USER>/app/workspace/servers/public-api-live"
    command: bash -c "cd /home/<USER>/app/workspace/servers/public-api-live && npm run start:dev"
    environment:
      LOG_DEBUG: "1"
      USE_CHUNK_WORKFLOW: "true"
      PATH: "/home/<USER>/app/node_modules/.bin:$PATH"
      ENVIRONMENT: "dev"
      REDIS_DOMAIN_HOSTNAME: "redis.dev.divinci.app"
      REDIS_PORT: "6379"
      REDIS_USERNAME: ""
      REDIS_PASSWORD: ""
      MONGO_INITDB_ROOT_USERNAME: "divinciUser1"
      MONGO_INITDB_ROOT_PASSWORD: "divinciPassword1"
      MONGO_DOMAIN_HOSTNAME: "serverlessinstance0.c4pobzg.mongodb.net"
      MONGO_IS_SRV: "1"
      MONGO_DATABASE_NAME: "divinci-development"
      CLOUDFLARE_WORKER_X_AUTH_DEV: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"
      HTTPS: "true"
      SSL_CERT_PATH: "/workspaces/server/private-keys/local-fast/server.crt"
      SSL_KEY_PATH: "/workspaces/server/private-keys/local-fast/server.key"
      ENABLE_MTLS: "true"
      MTLS_CERT_DIR: "/workspaces/server/private-keys/local-fast"
    networks:
      local-network:
        aliases:
          - public-api-live.divinci.local
    ports:
      - 9080:8080
    healthcheck:
      test: ["CMD", "curl", "-kf", "https://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 3
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
    volumes:
      - /workspaces:/workspaces

  public-api-webhook:
    extends:
      file: base-fast.yml
      service: base-api
    working_dir: "/home/<USER>/app/workspace/servers/public-api-webhook"
    command: bash -c "cd /home/<USER>/app/workspace/servers/public-api-webhook && npm run start:dev"
    environment:
      LOG_DEBUG: "1"
      USE_CHUNK_WORKFLOW: "true"
      PATH: "/home/<USER>/app/node_modules/.bin:$PATH"
      ENVIRONMENT: "dev"
      REDIS_DOMAIN_HOSTNAME: "redis.dev.divinci.app"
      REDIS_PORT: "6379"
      REDIS_USERNAME: ""
      REDIS_PASSWORD: ""
      MONGO_INITDB_ROOT_USERNAME: "divinciUser1"
      MONGO_INITDB_ROOT_PASSWORD: "divinciPassword1"
      MONGO_DOMAIN_HOSTNAME: "serverlessinstance0.c4pobzg.mongodb.net"
      MONGO_IS_SRV: "1"
      MONGO_DATABASE_NAME: "divinci-development"
      CLOUDFLARE_WORKER_X_AUTH_DEV: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"
      HTTPS: "true"
      SSL_CERT_PATH: "/workspaces/server/private-keys/local-fast/server.crt"
      SSL_KEY_PATH: "/workspaces/server/private-keys/local-fast/server.key"
      ENABLE_MTLS: "true"
      MTLS_CERT_DIR: "/workspaces/server/private-keys/local-fast"
    networks:
      local-network:
        aliases:
          - public-api-webhook.divinci.local
    ports:
      - 8082:8080
    healthcheck:
      test: ["CMD", "curl", "-kf", "https://localhost:8080/health"]
      interval: 10s  
      timeout: 5s
      retries: 3
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "localhost:127.0.0.1"
    volumes:
      - /workspaces:/workspaces
