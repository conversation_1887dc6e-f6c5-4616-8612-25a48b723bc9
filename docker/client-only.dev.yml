networks:
  local-network:
    driver: bridge

services:
  # ========================================================================
  # CLIENT Servers (1, web)
  # ========================================================================
  local-web-client:
    extends:
      file: base.yml
      service: base-client
    working_dir: "/home/<USER>/app/clients/web"
    environment:
      ENV_FOLDER: "/home/<USER>/app/clients/web/env"
      HTTP_PORT: 8080
    volumes:
      - ../private-keys/client-only.dev:/home/<USER>/app/clients/web/env
    networks:
      - local-network
    ports:
      - 8080:8080
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]

  local-embed-client:
    extends:
      file: base.yml
      service: base-client
    working_dir: "/home/<USER>/app/clients/embed"
    environment:
      ENV_FOLDER: "/home/<USER>/app/clients/embed/env"
      HTTP_PORT: 8081
      STATIC_PORT: 8082
    volumes:
      - ../private-keys/client-only.dev:/home/<USER>/app/clients/embed/env
    networks:
      - local-network
    ports:
      - 8081:8081
      - 8082:8082
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/"]

