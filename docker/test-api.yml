name: divinci-test-api

networks:
  test-network:
    driver: bridge

services:
  # ========================================================================
  # API Servers (2, api, test)
  # ========================================================================

  api:
    extends:
      file: base.yml
      service: base-api
    working_dir: "/home/<USER>/app/servers/public-api"
    environment:
      ENV_FOLDER: "/home/<USER>/app/servers/public-api/env"
    volumes:
      - ../private-keys/test-api-local:/home/<USER>/app/servers/public-api/env
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - test-network
    ports:
      - 18081:8080
    depends_on:
      - d1-rag
      - mongodb
      - redis
      - audio-splitter-ffmpeg
      - audio-speak-dia-pyannote
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 15s

  api-test:
    extends:
      file: base.yml
      service: base-api
    working_dir: "/home/<USER>/app/servers/test-api"
    environment:
      ENV_FOLDER: "/home/<USER>/app/servers/test-api/env"
    volumes:
      - ../private-keys/test-api-local:/home/<USER>/app/servers/test-api/env
    networks:
      - test-network
    ports:
      - 18084:8080
    depends_on:
      - d1-rag
      - mongodb
      - redis

  # ========================================================================
  # WORKER (d1-rag, pdf-to-chunks)
  # ========================================================================

  d1-rag:
    extends:
      file: base.yml
      service: base-cloudflare-worker
    networks:
      - test-network
    volumes:
      - ../workspace/workers/d1-doc-elements:/app
      - ../private-keys/test-api-local/cloudflare.env:/app/.dev.vars

  open-parse:
    build:
      context: ../workspace/workers/open-parse
      dockerfile: open-parse.Dockerfile
    volumes:
      - ../hidden/servers/open-parse:/openparse-app
    environment:
      HTTP_PORT: 5000
    networks:
      - test-network
    ports:
      - 19100:5000
  audio-speak-dia-pyannote:
    build:
      context: ../workspace/workers/audio-speaker-diarization@pyannote
      dockerfile: python.Dockerfile
    volumes:
      # Private Keys
      - ../private-keys/test-api-local:/home/<USER>/app/env
    environment:
      HTTP_PORT: 5000
      ENV_FOLDER: "/home/<USER>/app/env"
    networks:
      - test-network

 # ========================================================================
 # RAG WORKFLOW WORKER
 # ========================================================================
  chunks-vectorized:
    extends:
      file: base.yml
      service: base-cloudflare-worker
    volumes:
      - ../workspace/workers/chunks-workflow:/app
      - ../private-keys/local/cloudflare.worker.env:/app/.dev.vars
    command: |
      /bin/bash -c 'echo "Running initial commands as root to install certificates..." &&
      apt-get update && apt-get install -y ca-certificates && update-ca-certificates &&
      echo "Switching to node user for remaining operations..." &&
      su node -c "cd /app &&
        mkdir -p /home/<USER>/.wrangler/config &&
        set -a && source /app/.dev.vars && set +a &&
        echo \"api_token = \\\"$$CLOUDFLARE_API_TOKEN\\\"\" > /home/<USER>/.wrangler/config/default.toml &&
        echo \"account_id = \\\"$$CLOUDFLARE_ACCOUNT_ID\\\"\" >> /home/<USER>/.wrangler/config/default.toml &&
        rm -rf node_modules package-lock.json &&
        npm cache clean --force &&
        npm install --no-package-lock &&
        npx wrangler@4.16.1 dev --env local --ip 0.0.0.0 --port 8791 --inspector-port 8792"'
    environment:
      NODE_ENV: development
      NODE_EXTRA_CA_CERTS: /etc/ssl/certs/ca-certificates.crt
      WRANGLER_SEND_METRICS: "false"
      HOST: 0.0.0.0
      PORT: "8791"
      OPENPARSE_API_URL: http://open-parse:5000
      API_HOST: http://api:8080
      CLOUDFLARE_ACCOUNT_ID: 4a6fa23390363382f378b5bd4a0f849
      CLOUDFLARE_D1_API_URL: http://d1-rag:8787
    user: root
    networks:
      - test-network
    ports:
      - 8891:8791
      - 8892:8792
    depends_on:
      - d1-rag
      - open-parse

  audio-splitter-ffmpeg:
    build:
      context: ../workspace/workers/audio-splitter@ffmpeg
      dockerfile: typescript.Dockerfile
    volumes:
      # Private Keys
      - ../private-keys/test-api-local:/home/<USER>/env
    environment:
      HTTP_PORT: 5000
      ENV_FOLDER: "/home/<USER>/env"
    networks:
      - test-network


  # ========================================================================
  # AI DATABASE (1, qdrant)
  # ========================================================================
  vector-qdrant:
    image: qdrant/qdrant
    networks:
      - test-network

  # ========================================================================
  # DATABASE (2, mongodb, redis)
  # ========================================================================

  mongodb:
    extends:
      file: base.yml
      service: base-mongodb
    networks:
      - test-network
    env_file:
      - ../private-keys/test-api-local/mongodb.env

  # mongo-viewer:
  #   image: mongo-express
  #   networks:
  #     - test-network
  #   links:
  #     - mongodb
  #   env_file:
  #     - ../private-keys/test-api-local/mongodb.env
  #   ports:
  #     - 28081:8081 # This line maps port 27017 in the container to port 27017 on your host machine

  redis:
    extends:
      file: base.yml
      service: base-redis
    networks:
      - test-network
    env_file:
      - ../private-keys/test-api-local/redis.env
