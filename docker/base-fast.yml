services:
  base-client:
    user: "node"
    image: node:22-bookworm-slim
    environment:
      - DEBUG="app"
      - HTTP_PORT=8080
      - PATH="/home/<USER>/app/node_modules/.bin:$PATH"
    healthcheck:
      interval: 10s
      timeout: 5s
      retries: 3

  base-api:
    user: "node"
    image: node:22-bookworm-slim
    environment:
      - DEBUG="app"
      - LOG_DEBUG=1
      - HTTP_PORT=8080
      - PATH="/home/<USER>/app/node_modules/.bin:$PATH"
    healthcheck:
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G
