services:
  base-client:
    user: "node"
    image: node:22-bookworm-slim
    command: npm run start:dev
    volumes:
      - ../workspace:/home/<USER>/app
      - ../node_modules:/home/<USER>/node_modules
    environment:
      - DEBUG="app"
      - HTTP_PORT=8080
      - PATH="/home/<USER>/app/node_modules/.bin:$PATH"
    healthcheck:
      interval: 10s
      timeout: 5s
      retries: 3

  base-api:
    user: "node"
    image: node:22-bookworm-slim
    command: npm run start:dev
    volumes:
      - ../workspace:/home/<USER>/app
      - ../node_modules:/home/<USER>/node_modules
    environment:
      - DEBUG="app"
      - LOG_DEBUG=1
      - HTTP_PORT=8080
      - PATH="/home/<USER>/app/node_modules/.bin:$PATH"
    healthcheck:
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G

  base-cloudflare-worker:
    user: "node"
    image: node:22-bookworm
    working_dir: /app

  base-mongodb:
    image: mongo
    command: mongod --quiet --logpath /dev/null
    restart: always
    deploy:
      restart_policy:
        condition: on-failure
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3

  base-redis:
    image: redis
    restart: always
    command: redis-server --save 20 1 --loglevel warning
    deploy:
      restart_policy:
        condition: on-failure
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  base-postgres:
    image: postgres
    restart: always
    deploy:
      restart_policy:
        condition: on-failure
    shm_size: 128mb # or set shared memory limit when deploy via swarm stack
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres-user"]
      interval: 10s
      timeout: 5s
      retries: 5

  # tunnel:
  #   image: ngrok/ngrok:latest
  #   restart: unless-stopped
  #   command: start webhook --config /etc/ngrok.yml
  #   volumes:
  #     - ../private-keys/local/ngrok.yml:/etc/ngrok.yml
  #   links:
  #     - api-webhook
  #   ports:
  #     - 4040:4040
