version: '3.8'

services:
  # Public API service
  public-api:
    build:
      context: ..
      dockerfile: deploy/docker/ci/api-bundle.ci.Dockerfile
    image: divinci/public-api:latest
    container_name: public-api
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
      - PORT=8000
    volumes:
      # Mount mTLS certificates
      - ../private-keys/mtls/services/public-api/ca.crt:/etc/ssl/certs/ca.crt:ro
      - ../private-keys/mtls/services/public-api/client.crt:/etc/ssl/certs/client.crt:ro
      - ../private-keys/mtls/services/public-api/client.key:/etc/ssl/private/client.key:ro
    entrypoint: ["/app/deploy/docker/ci/api-mtls-entrypoint.sh"]
    networks:
      - divinci-network

  # Web client service
  web-client:
    build:
      context: ..
      dockerfile: deploy/docker/ci/client-public.ci.Dockerfile
    image: divinci/web-client:latest
    container_name: web-client
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "8443:8443"
    environment:
      - WEB_CLIENT_HOST=localhost
    volumes:
      # Mount SSL certificates (not mTLS, just regular TLS)
      - ../private-keys/ssl/server.crt:/etc/ssl/certs/server.crt:ro
      - ../private-keys/ssl/server.key:/etc/ssl/private/server.key:ro
    entrypoint: ["/app/deploy/docker/ci/client-docker-entrypoint-updated.sh"]
    networks:
      - divinci-network

  # FFmpeg service
  ffmpeg:
    build:
      context: ..
      dockerfile: deploy/docker/ci/ffmpeg.ci.Dockerfile
    image: divinci/ffmpeg:latest
    container_name: ffmpeg
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=8001
    volumes:
      # Mount mTLS certificates
      - ../private-keys/mtls/services/ffmpeg/ca.crt:/etc/ssl/certs/ca.crt:ro
      - ../private-keys/mtls/services/ffmpeg/server.crt:/etc/ssl/certs/server.crt:ro
      - ../private-keys/mtls/services/ffmpeg/server.key:/etc/ssl/private/server.key:ro
    entrypoint: ["/app/deploy/docker/ci/service-mtls-entrypoint.sh", "ffmpeg"]
    networks:
      - divinci-network

  # Open-Parse service
  open-parse:
    build:
      context: ..
      dockerfile: deploy/docker/ci/open-parse.ci.Dockerfile
    image: divinci/open-parse:latest
    container_name: open-parse
    restart: unless-stopped
    environment:
      - PORT=8002
      - THREADS=50
    volumes:
      # Mount mTLS certificates
      - ../private-keys/mtls/services/open-parse/ca.crt:/etc/ssl/certs/ca.crt:ro
      - ../private-keys/mtls/services/open-parse/server.crt:/etc/ssl/certs/server.crt:ro
      - ../private-keys/mtls/services/open-parse/server.key:/etc/ssl/private/server.key:ro
    entrypoint: ["/app/deploy/docker/ci/service-mtls-entrypoint.sh", "open-parse"]
    networks:
      - divinci-network

  # Pyannote service
  pyannote:
    build:
      context: ..
      dockerfile: deploy/docker/ci/pyannote.ci.Dockerfile
    image: divinci/pyannote:latest
    container_name: pyannote
    restart: unless-stopped
    environment:
      - PORT=8003
      - THREADS=8
    volumes:
      # Mount mTLS certificates
      - ../private-keys/mtls/services/pyannote/ca.crt:/etc/ssl/certs/ca.crt:ro
      - ../private-keys/mtls/services/pyannote/server.crt:/etc/ssl/certs/server.crt:ro
      - ../private-keys/mtls/services/pyannote/server.key:/etc/ssl/private/server.key:ro
    entrypoint: ["/app/deploy/docker/ci/service-mtls-entrypoint.sh", "pyannote"]
    networks:
      - divinci-network

networks:
  divinci-network:
    driver: bridge
