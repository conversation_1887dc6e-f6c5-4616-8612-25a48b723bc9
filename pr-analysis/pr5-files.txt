Analyzing files for PR 5: Docker and Environment Configuration...
Files matching pattern: docker|Dockerfile|\.env|docker-compose
----------------------------------------
.env.runner
.env.runner.new
.github/act/secrets.env
.github/runners/build-deploy/.env.example
.github/runners/build-deploy/.env.runner.example
.github/runners/build-deploy/Dockerfile
.github/runners/build-deploy/build-deploy_docker-compose.yml
.github/runners/build-deploy/docker-compose-arm64.yml
deploy/docker/ci/api-bundle.ci.Dockerfile
deploy/docker/ci/api-mtls-entrypoint.sh
deploy/docker/ci/client-docker-entrypoint-updated.sh
deploy/docker/ci/client-docker-entrypoint.sh
deploy/docker/ci/client-public.ci.Dockerfile
deploy/docker/ci/ffmpeg-docker-entrypoint.sh
deploy/docker/ci/ffmpeg.ci.Dockerfile
deploy/docker/ci/open-parse.ci.Dockerfile
deploy/docker/ci/pyannote.ci.Dockerfile
deploy/docker/ci/service-mtls-entrypoint.sh
deploy/scripts/tests/cloudflare-credentials.env
deploy/scripts/tests/cloudflare.env.template
deploy/steps/2.docker-build-no-buildx.sh
deploy/steps/2.docker-build.sh
docker/local.yml
docker/mtls-example.yml
docker/mtls-local.yml
docker/nginx-mtls.conf
docker/test-api.yml
workspace/clients/web/.env.local
workspace/deploy/docker/ci/client-docker-entrypoint.sh
workspace/servers/open-parse/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/servers/public-api/deploy/docker/ci/api-mtls-entrypoint.sh
workspace/workers/audio-speaker-diarization@pyannote/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/workers/audio-splitter@ffmpeg/deploy/docker/ci/service-mtls-entrypoint.sh

Total files:       33
----------------------------------------

