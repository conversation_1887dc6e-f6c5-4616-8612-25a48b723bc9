----------------------------------------
.claude/settings.local.json
.gitmodules
.husky/pre-push
.srl
.vscode/settings.json
.zed/settings.json
CLAUDE.md
PR-Split-PLAN.md
PR-Split-README.md
PR-Split-TODOs.md
analyze-pr-files.sh
aws-sig-v4-curl.py
cf_trace_response.json
change-cloudflare-ssl-mode.sh
check-certificate-details.sh
check-cloudflare-ssl-mode.sh
check-cloudflare-status.sh
client.crt
client_pkcs8.key
cloudflare-config-verification.sh
cloudflare-options-mtls-guide.md
cloudflare-output/firewall_rules.json
cloudflare-output/zone_info.json
copy-r2-assets.js
cors-test.html
create-split-branches.sh
current-policy.json
deploy/config.json
deploy/kubernetes/mtls-certificates.yaml
deploy/mTLS-CF-Rule
deploy/scripts/add-ca-to-certificates.sh
deploy/scripts/check-server-cert-secret.sh
deploy/scripts/copy-cert-files.sh
deploy/scripts/create-ca-cert-secret.sh
deploy/scripts/create-client-cert-secrets.sh
deploy/scripts/create-individual-cert-secrets.sh
deploy/scripts/create-mtls-certs-secret.sh
deploy/scripts/create-mtls-secret.sh
deploy/scripts/create-server-cert-secret.sh
deploy/scripts/download-cloudflare-ca-certs.sh
deploy/scripts/extract-gcp-certs.sh
deploy/scripts/extract-mtls-certs.sh
deploy/scripts/fix-cloudflare-error-526.sh
deploy/scripts/generate-mtls-certs.sh
deploy/scripts/install-cloudflare-origin-cert.sh
deploy/scripts/monitor-cert-expiration.sh
deploy/scripts/origin_ca_ecc_root.pem
deploy/scripts/origin_ca_rsa_root.pem
deploy/scripts/rotate-mtls-certs.sh
deploy/scripts/test-cloudflare-mtls.sh
deploy/scripts/test-local-mtls.sh
deploy/scripts/test-mtls-connection.sh
deploy/scripts/test-mtls-performance.sh
deploy/scripts/tests/.gitignore
deploy/scripts/tests/README.md
deploy/scripts/tests/analysis/existing-tests-analysis.md
deploy/scripts/tests/analysis/mtls-implementation-plan.md
deploy/scripts/tests/analysis/mtls-implementation-progress.md
deploy/scripts/tests/analysis/mtls-implementation-summary.md
deploy/scripts/tests/certificate-chain-verification.sh
deploy/scripts/tests/certificate-issue-analysis.md
deploy/scripts/tests/certificate-presentation-test.sh
deploy/scripts/tests/cf-access-testing-readme.md
deploy/scripts/tests/cf-advanced-debug.sh
deploy/scripts/tests/cf-cert-test.sh
deploy/scripts/tests/cf-curl-test.sh
deploy/scripts/tests/cf-direct-test.sh
deploy/scripts/tests/cf-e2e-test.js
deploy/scripts/tests/cf-trace-debug.sh
deploy/scripts/tests/cf_trace_response.json
deploy/scripts/tests/check-certificate-mounts.sh
deploy/scripts/tests/check-cloudflare-ssl-mode.sh
deploy/scripts/tests/check-options-errors.sh
deploy/scripts/tests/cloudflare-access-bypass-test.sh
deploy/scripts/tests/cloudflare-access-test.sh
deploy/scripts/tests/cloudflare-api-trace-test.sh
deploy/scripts/tests/cloudflare-rules-analyzer.sh
deploy/scripts/tests/cloudflare-rules-fixer.sh
deploy/scripts/tests/cloudflare-test-fix-report.md
deploy/scripts/tests/common-functions.sh
deploy/scripts/tests/cors-fixes-implementation-guide-final.md
deploy/scripts/tests/cors-fixes-implementation-guide-updated.md
deploy/scripts/tests/cors-fixes-implementation-guide.md
deploy/scripts/tests/cors-mtls-test-fixed.sh
deploy/scripts/tests/cors-mtls-test.sh
deploy/scripts/tests/cors-worker.js
deploy/scripts/tests/curl-tls-analysis-wrapper.sh
deploy/scripts/tests/curl-tls-analysis.sh
deploy/scripts/tests/curl_output_1.log
deploy/scripts/tests/docs/TLS-Testing-Framework.md
deploy/scripts/tests/docs/Technical-Specification.md
deploy/scripts/tests/docs/curl-tls-analysis-implementation.md
deploy/scripts/tests/docs/curl-tls-integration.md
deploy/scripts/tests/docs/fallback-mechanism-specification.md
deploy/scripts/tests/fix-certificate-mounts.sh
deploy/scripts/tests/fix-cloudflare-cors-worker.sh
deploy/scripts/tests/fix-cloudflare-options-rule.sh
deploy/scripts/tests/fix-mtls-enable-value.sh
deploy/scripts/tests/gcp-cloud-run-health-check.sh
deploy/scripts/tests/gcp-cloud-run-network-test.sh
deploy/scripts/tests/gcp-export-yaml.sh
deploy/scripts/tests/generate-client-cert-key.sh
deploy/scripts/tests/manual-cloudflare-fixes-detailed.md
deploy/scripts/tests/mtls-comprehensive-test-wrapper.sh
deploy/scripts/tests/mtls-comprehensive-test.sh
deploy/scripts/tests/mtls-connection-test-wrapper.sh
deploy/scripts/tests/mtls-connection-test.sh
deploy/scripts/tests/mtls-cors-fix-implementation-plan.md
deploy/scripts/tests/mtls-cors-implementation-guide.md
deploy/scripts/tests/mtls-diagnostics.sh
deploy/scripts/tests/mtls-framework/README.md
deploy/scripts/tests/mtls-framework/certificate-manager.sh
deploy/scripts/tests/mtls-framework/common.sh
deploy/scripts/tests/mtls-framework/run-tests.sh
deploy/scripts/tests/mtls-testing-guide.md
deploy/scripts/tests/mtls-troubleshooting.md
deploy/scripts/tests/nmap-ssl-scan.sh
deploy/scripts/tests/run-all-cf-tests.sh
deploy/scripts/tests/run-cloudflare-test-fix-cycle.sh
deploy/scripts/tests/run-comprehensive-tests.sh
deploy/scripts/tests/run-mtls-cors-fixes.sh
deploy/scripts/tests/run-network-diagnostics.sh
deploy/scripts/tests/set-cloudflare-ssl-mode.sh
deploy/scripts/tests/sslyze-scan.sh
deploy/scripts/tests/sudo_helper.sh
deploy/scripts/tests/tcpdump-tls-capture.sh
deploy/scripts/tests/test-config.sh
deploy/scripts/tests/test-cors-fixes-trending.sh
deploy/scripts/tests/test-cors-fixes.sh
deploy/scripts/tests/test-redirect.sh
deploy/scripts/tests/tls-testing/README.md
deploy/scripts/tests/tls-testing/bin/curl-tls-analysis.sh
deploy/scripts/tests/tls-testing/bin/run-tls-tests.sh
deploy/scripts/tests/tls-testing/docs/CONTRIBUTING.md
deploy/scripts/tests/tls-testing/docs/ROADMAP.md
deploy/scripts/tests/tls-testing/docs/curl-tls-analysis-developer-guide.md
deploy/scripts/tests/tls-testing/docs/curl-tls-analysis-user-guide.md
deploy/scripts/tests/tls-testing/docs/quick-start-guide.md
deploy/scripts/tests/tls-testing/lib/curl_tls_parser.sh
deploy/scripts/tests/tls-testing/lib/reporting.sh
deploy/scripts/tests/tls-testing/lib/tls_utils.sh
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/curl_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/nmap_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/openssl_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/report.json
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_20250426_120821/summary.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_curl_output.txt
deploy/scripts/tests/tls-testing/results/api.stage.divinci.app_443_headers.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/curl_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/nmap_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/openssl_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/report.json
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_20250426_000922/summary.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_curl_output.txt
deploy/scripts/tests/tls-testing/results/chat.stage.divinci.app_443_headers.txt
deploy/scripts/tests/trace_response.json
deploy/scripts/tests/tshark-tls-analysis.sh
deploy/scripts/tests/verify-cors-fixes.sh
deploy/scripts/update-gcp-cert-secret.sh
deploy/scripts/update-k8s-mtls.sh
deploy/scripts/verify-cert-before-deploy.sh
deploy/scripts/verify-cloudflare-mtls.sh
deploy/service-template.yaml
deploy/steps/3.deploy-new.sh
deploy/util/env.js
deploy/util/gcp-cloud-run-generate-yaml.sh
deploy/util/minio-entrypoint.sh
deploy/util/setup-minio.sh
docs/cloudflare-error-526-fix.md
docs/gcp-cloud-run-config.md
docs/mtls-gcp-secrets.md
docs/mtls-setup.md
fix-cloudflare-cors.sh
fix-cloudflare-options-only-norules.sh
fix-cloudflare-options-only.sh
fix-cloudflare-transform-rules.sh
fix-options-server.js
generate-cloudflare-origin-cert.sh
mTLS-PLAN.md
mTLS-README.md
mTLS-SECURITY.md
mTLS-TODO.md
manual-cloudflare-fixes.md
mtls-options-fix-readme.md
mtls-options-fix-summary.md
options_response.txt
pnpm-lock.yaml
resolve-mtls-options-issue.md
scripts/create-test-triggers.ts
scripts/generate-mtls-keys.sh
scripts/merge-coverage.js
scripts/trigger-tests.ts
server.crt
server_tls_policy.yaml
target_proxy.yaml
test-cors-fixes.sh
test-cors-options-updated.sh
test-cors-options.sh
test-cors.js
test-file.txt
test-redirect.sh
tools/mtls-testing/mtls-testing-README.md
tools/mtls-testing/test-mtls-connections.sh
tools/mtls-testing/test_mtls.py
tools/mtls-testing/test_mtls_advanced.py
trust_config.yaml
url-map.yaml
vitest.config.mjs
workspace/resources/models/package.json
workspace/resources/models/src/white-label/Tool/index.ts
workspace/resources/python-utils/mtls_utils.py
workspace/resources/server-globals/src/certificates/README.md
workspace/resources/server-globals/src/certificates/paths.ts
workspace/resources/server-globals/src/cors/domains-from-env.ts
workspace/resources/server-globals/src/cors/index.ts
workspace/resources/server-globals/tests/cors/domains-from-env.test.ts
workspace/resources/server-globals/tests/cors/headers.test.ts
workspace/resources/server-globals/tests/unit/certificates/paths.test.ts
workspace/resources/server-models/src/white-label/fine-tune/file/statics/addFile/index.ts
workspace/resources/server-models/src/white-label/rag/file/methods/finalize/textchunk-to-d1chunk.ts
workspace/resources/server-models/src/white-label/rag/file/methods/manage-chunks/lifecycle/addChunk.ts
workspace/resources/server-tools/src/ai-assistant/generators/image/dall-e-base.ts
workspace/resources/server-tools/src/rag/index.ts
workspace/resources/server-tools/src/rag/vector/cloudflare/constants.ts
workspace/resources/server-tools/src/rag/vector/cloudflare/index.ts
workspace/resources/server-utils/src/certificates/README.md
workspace/resources/server-utils/src/certificates/validation.ts
workspace/resources/server-utils/src/env.ts
workspace/resources/server-utils/src/http-request/__tests__/middleware.test.ts
workspace/resources/server-utils/src/http-request/index.ts
workspace/resources/server-utils/src/http-request/middleware.ts
workspace/resources/server-utils/src/http-request/mtls-agent.ts
workspace/resources/server-utils/src/ws/reject.ts
workspace/resources/server-utils/tests/unit/certificates/README.md
workspace/resources/server-utils/tests/unit/certificates/cert-chain.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-domain-simple.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-domain.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-expiration.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-info.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-key-match.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-validate-simple.test.ts
workspace/resources/server-utils/tests/unit/certificates/cert-validate.test.ts
workspace/resources/server-utils/tests/unit/certificates/validation.test.ts
workspace/servers/public-api-live/src/middleware/README.md
workspace/servers/public-api-live/src/middleware/cors-debug.ts
workspace/servers/public-api-live/src/middleware/mtls-debug.ts
workspace/servers/public-api-live/src/setup/http/index.ts
workspace/servers/public-api-live/tests/unit/src/setup/database/setupDBs.test.skip.ts
workspace/servers/public-api-live/tsconfig.json
workspace/servers/public-api-webhook/tsconfig.json
workspace/servers/public-api/jest.config.js
workspace/servers/public-api/jest.setup.js
workspace/servers/public-api/src/app.ts
workspace/servers/public-api/src/env.ts
workspace/servers/public-api/src/middleware/local-cors.ts
workspace/servers/public-api/src/routes/ai-chat/mock-test-trigger.ts
workspace/servers/public-api/src/routes/finetune/mock-test-trigger.ts
workspace/servers/public-api/src/routes/message/mock-test-trigger.ts
workspace/servers/public-api/src/routes/moderation/mock-test-trigger.ts
workspace/servers/public-api/src/routes/rag/mock-test-trigger.ts
workspace/servers/public-api/src/routes/thread/mock-test-trigger.ts
workspace/servers/public-api/src/routes/whitelabel/mock-test-trigger.ts
workspace/servers/public-api/src/routes/workspace/mock-test-trigger.ts
workspace/servers/public-api/src/setup/http/index.ts
workspace/servers/public-api/src/setup/server.ts
workspace/servers/public-api/src/ux/ai-chat/router/index.ts
workspace/servers/public-api/src/ux/system/health.ts
workspace/servers/public-api/src/ux/system/index.ts
workspace/servers/public-api/src/ux/user/router/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/fine-tuning/csv-editor/index.ts
workspace/servers/public-api/src/ux/whitelabel/router/message-prefix/create-prefix.ts
workspace/servers/public-api/src/ux/whitelabel/router/notifications.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/custom-rag/item/rag-file/pending-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/custom-rag/item/rag-file/success-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/create-file-record.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/update-status.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/util/ensure-valid-file.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/text-chunks/handlers/add-chunks.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/text-chunks/handlers/finalize.ts
workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/text-chunks/handlers/update-chunk.ts
workspace/servers/public-api/src/ux/whitelabel/router/util.ts
workspace/servers/public-api/tests/unit/src/ux/user/router/phone-number/subscribeToChatWithPhoneNumber.test.skip.ts
workspace/servers/public-api/tests/unit/src/ux/whitelabel/add-chunks.test.ts
workspace/servers/public-api/tests/unit/src/ux/whitelabel/pending-file.test.ts
workspace/servers/public-api/tsconfig.json
workspace/servers/public-api/tsconfig.tests.json
workspace/workers/create-cf-email-destination/create-cf-email-destination_tail/package.json
workspace/workers/d1-doc-elements/d1-doc-elements_tail/package.json
workspace/workers/d1-doc-elements/src/stream/retrieve.ts
workspace/workers/d1-doc-elements/src/stream/upsert.ts
workspace/workers/d1-doc-elements/wrangler.toml
workspace/workers/divinci-send-notification-email-d011/divinci-send-notification-email-d011n_tail/package.json
workspace/workers/worker-ai-text-categorization/worker-ai-text-categorization_tail/package.json
