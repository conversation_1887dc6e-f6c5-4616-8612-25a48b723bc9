Analyzing files for PR 4: Open-Parse Tool and RAG Workflow...
Files matching pattern: open-parse|RAG|workflow|chunking
----------------------------------------
.github/act/.github/workflows/reusable/reusable
.github/act/events/workflow-dispatch-event.json
.github/act/mock-workflows/build-deploy-changed-services.yml
.github/act/test-new-workflow.sh
.github/act/test-refactored-workflow.yml
.github/workflows/build-deploy-changed-services.yml
.github/workflows/clear-caches.yml
.github/workflows/comment-actions.yml
deploy/docker/ci/open-parse.ci.Dockerfile
workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/NewFileForm/submit-add-file-workflow.ts
workspace/resources/mtls/examples/open-parse-client.py
workspace/resources/mtls/examples/open-parse-server.py
workspace/servers/open-parse/app.py
workspace/servers/open-parse/deploy/docker/ci/service-mtls-entrypoint.sh
workspace/servers/open-parse/mtls_utils.py
workspace/servers/public-api/src/ux/whitelabel/router/data-source/audio-transcript/util/workflow-client.ts
workspace/workers/chunks-workflow/README.md
workspace/workers/chunks-workflow/chunks-workflow_tail/package.json
workspace/workers/chunks-workflow/chunks-workflow_tail/src/index.js
workspace/workers/chunks-workflow/chunks-workflow_tail/test/index.spec.js
workspace/workers/chunks-workflow/package.json
workspace/workers/chunks-workflow/run-tests.sh
workspace/workers/chunks-workflow/src/constants.ts
workspace/workers/chunks-workflow/src/index.ts
workspace/workers/chunks-workflow/src/processors/openparse.ts
workspace/workers/chunks-workflow/src/processors/types.ts
workspace/workers/chunks-workflow/src/processors/unstructured.ts
workspace/workers/chunks-workflow/src/types.ts
workspace/workers/chunks-workflow/src/utils.ts
workspace/workers/chunks-workflow/src/utils/aws-sig-v4.ts
workspace/workers/chunks-workflow/src/utils/d1-api.ts
workspace/workers/chunks-workflow/src/utils/evaluate-relevance.ts
workspace/workers/chunks-workflow/src/utils/retry.ts
workspace/workers/chunks-workflow/src/utils/storage-client.ts
workspace/workers/chunks-workflow/src/utils/vectorize-api.ts
workspace/workers/chunks-workflow/src/workflows/chunks-vectorized.ts
workspace/workers/chunks-workflow/src/workflows/chunks-vectorized_old-working.ts
workspace/workers/chunks-workflow/src/workflows/steps/add-chunks-to-file-record.ts
workspace/workers/chunks-workflow/src/workflows/steps/ensure-d1-table.ts
workspace/workers/chunks-workflow/src/workflows/steps/ensure-file-record.ts
workspace/workers/chunks-workflow/src/workflows/steps/filter-chunks.ts
workspace/workers/chunks-workflow/src/workflows/steps/initialize-processing.ts
workspace/workers/chunks-workflow/src/workflows/steps/initialize-workflow.ts
workspace/workers/chunks-workflow/src/workflows/steps/link-file-to-rag.ts
workspace/workers/chunks-workflow/src/workflows/steps/process-batch.ts
workspace/workers/chunks-workflow/src/workflows/steps/store-chunks-in-d1.ts
workspace/workers/chunks-workflow/src/workflows/steps/store-chunks-in-r2.ts
workspace/workers/chunks-workflow/src/workflows/steps/types.ts
workspace/workers/chunks-workflow/src/workflows/steps/update-file-status.ts
workspace/workers/chunks-workflow/src/workflows/steps/upload-to-r2.ts
workspace/workers/chunks-workflow/src/workflows/steps/upsert-workflow-metadata.ts
workspace/workers/chunks-workflow/src/workflows/steps/validate-and-get-r2-file.ts
workspace/workers/chunks-workflow/src/workflows/steps/vectorize-chunks.ts
workspace/workers/chunks-workflow/test/README.md
workspace/workers/chunks-workflow/test/__snapshots__/snapshot.test.ts.snap
workspace/workers/chunks-workflow/test/basic-processor-functionality.test.js
workspace/workers/chunks-workflow/test/boundary.test.ts
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.comprehensive.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.fixed.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.mock.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/edge-cases.test.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.comprehensive.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.fixed.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.mock.test.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/backup/workflow-boundaries.test.skip.js.bak
workspace/workers/chunks-workflow/test/boundary/edge-cases.fixed.mock.test.js
workspace/workers/chunks-workflow/test/boundary/edge-cases.test.js
workspace/workers/chunks-workflow/test/boundary/skip-original-tests.test.js
workspace/workers/chunks-workflow/test/boundary/workflow-boundaries.fixed.mock.test.js
workspace/workers/chunks-workflow/test/boundary/workflow-boundaries.test.js
workspace/workers/chunks-workflow/test/cloudflare-workers.test.js
workspace/workers/chunks-workflow/test/concurrency.test.ts
workspace/workers/chunks-workflow/test/concurrency/parallel-processing.test.js
workspace/workers/chunks-workflow/test/concurrency/resource-contention.test.js
workspace/workers/chunks-workflow/test/error-recovery/retry-logic.test.ts
workspace/workers/chunks-workflow/test/import.test.js
workspace/workers/chunks-workflow/test/index.test.js
workspace/workers/chunks-workflow/test/index.test.ts
workspace/workers/chunks-workflow/test/integration/end-to-end.test.ts
workspace/workers/chunks-workflow/test/mocks/aws-sig-v4-mock.ts
workspace/workers/chunks-workflow/test/mocks/aws-sig-v4.js
workspace/workers/chunks-workflow/test/mocks/chunks-vectorized.mock.js
workspace/workers/chunks-workflow/test/mocks/cloudflare-workers.js
workspace/workers/chunks-workflow/test/mocks/openparse.mock.js
workspace/workers/chunks-workflow/test/mocks/openparse.mock.ts
workspace/workers/chunks-workflow/test/mocks/unstructured-processor.js
workspace/workers/chunks-workflow/test/parameterized.test.ts
workspace/workers/chunks-workflow/test/parameterized/processor-parameterized.test.js
workspace/workers/chunks-workflow/test/parameterized/workflow-parameterized.test.js
workspace/workers/chunks-workflow/test/performance.test.ts
workspace/workers/chunks-workflow/test/performance/chunking-performance.test.js
workspace/workers/chunks-workflow/test/performance/workflow-performance.test.js
workspace/workers/chunks-workflow/test/processors/openparse.mock.test.ts
workspace/workers/chunks-workflow/test/processors/openparse.smoke.test.ts
workspace/workers/chunks-workflow/test/processors/unstructured.test.js
workspace/workers/chunks-workflow/test/processors/unstructured.test.ts
workspace/workers/chunks-workflow/test/property-based.test.ts
workspace/workers/chunks-workflow/test/property-based/chunking.skip.js
workspace/workers/chunks-workflow/test/property-based/chunking.test.js
workspace/workers/chunks-workflow/test/property-based/chunking.test.skip.js
workspace/workers/chunks-workflow/test/security.test.ts
workspace/workers/chunks-workflow/test/security/security.test.js
workspace/workers/chunks-workflow/test/setup-comprehensive.js
workspace/workers/chunks-workflow/test/setup-new.js
workspace/workers/chunks-workflow/test/setup.js
workspace/workers/chunks-workflow/test/setup.ts
workspace/workers/chunks-workflow/test/skip-original-tests.js
workspace/workers/chunks-workflow/test/snapshot.test.ts
workspace/workers/chunks-workflow/test/snapshot/__snapshots__/processor-output.test.js.snap
workspace/workers/chunks-workflow/test/snapshot/__snapshots__/workflow-output.test.js.snap
workspace/workers/chunks-workflow/test/snapshot/processor-output.test.js
workspace/workers/chunks-workflow/test/snapshot/workflow-output.test.js
workspace/workers/chunks-workflow/test/types.test.ts
workspace/workers/chunks-workflow/test/utils.test.ts
workspace/workers/chunks-workflow/test/utils/aws-sig-v4.test.js
workspace/workers/chunks-workflow/test/utils/aws-sig-v4.test.ts
workspace/workers/chunks-workflow/test/utils/storage-client.test.js
workspace/workers/chunks-workflow/test/utils/storage-client.test.ts
workspace/workers/chunks-workflow/test/utils/vectorize-api.test.js
workspace/workers/chunks-workflow/test/workflows/chunks-vectorized.test.js
workspace/workers/chunks-workflow/test/workflows/chunks-vectorized.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/add-chunks-to-file-record.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/filter-chunks.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/initialize-workflow.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/link-file-to-rag.test.ts
workspace/workers/chunks-workflow/test/workflows/steps/update-file-status.test.ts
workspace/workers/chunks-workflow/tsconfig.json
workspace/workers/chunks-workflow/tsconfig.test.json
workspace/workers/chunks-workflow/vitest.config.js
workspace/workers/chunks-workflow/vitest.config.ts
workspace/workers/chunks-workflow/worker-configuration.d.ts
workspace/workers/chunks-workflow/wrangler.toml

Total files:      134
----------------------------------------

