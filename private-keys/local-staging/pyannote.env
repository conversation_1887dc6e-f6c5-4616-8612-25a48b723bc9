# Configuration for the pyannote speaker diarization service
DIVINCI_AUDIO_DIARIZER_PYANNOTE_HTTP_PORT=8085
PYANNOTE_APIKEY=sk_bf8f111e03b14a7591adfbfed0d7a009

# Hugging Face token for accessing pyannote models
HUGGING_FACE_ACCESS_TOKEN=*************************************

# Service URL for other services to access this one
DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL=https://pyannote.dev.divinci.app

# Logging configuration
LOG_LEVEL=INFO

# mTLS configuration (disabled by default)
MTLS_ENABLED=false

# Cache directories
HOME=/home/<USER>
MPLCONFIGDIR=/home/<USER>/.cache/matplotlib
HF_HOME=/home/<USER>/.cache/huggingface
XDG_CACHE_HOME=/home/<USER>/.cache
