rules: []
rulesConfigs: []
pages: []
resourceServers:
  - name: Chat Stage Divinci App
    identifier: chat.stage.divinci.app:8080
    allow_offline_access: true
    enforce_policies: false
    scopes:
      - value: read:users
        description: Can read users
      - value: read:user_idp_tokens
        description: Can read users
      - value: read:users_app_metadata
        description: Can read users
    signing_alg: RS256
    skip_consent_for_verifiable_first_party_clients: false
    token_dialect: access_token
    token_lifetime: 86400
    token_lifetime_for_web: 7200
  - name: Test API
    identifier: http://localhost:8081
    allow_offline_access: true
    enforce_policies: false
    scopes:
      - value: read:users
        description: Can read users
      - value: read:user_idp_tokens
        description: Can read users
      - value: read:users_app_metadata
        description: Can read users
    signing_alg: RS256
    skip_consent_for_verifiable_first_party_clients: true
    token_dialect: access_token
    token_lifetime: 86400
    token_lifetime_for_web: 7200
clients:
  - name: API Explorer Application
    app_type: non_interactive
    cross_origin_auth: false
    cross_origin_authentication: true
    custom_login_page_on: true
    grant_types:
      - client_credentials
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    oidc_conformant: true
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 31557600
      idle_token_lifetime: 2592000
      rotation_type: non-rotating
    sso_disabled: false
    token_endpoint_auth_method: client_secret_post
  - name: Auth0 Management API 2 (Test Application)
    app_type: non_interactive
    cross_origin_auth: false
    cross_origin_authentication: false
    custom_login_page_on: true
    grant_types:
      - client_credentials
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    oidc_conformant: true
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 31557600
      idle_token_lifetime: 2592000
      rotation_type: non-rotating
    sso_disabled: false
    token_endpoint_auth_method: client_secret_post
  - name: Chat Stage Divinci App (Test Application)
    allowed_clients: []
    allowed_logout_urls:
      - http://localhost
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
    allowed_origins:
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
    app_type: non_interactive
    callbacks:
      - http://localhost
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
    client_aliases: []
    cross_origin_auth: false
    cross_origin_authentication: true
    cross_origin_loc: https://chat.divinci.app/cross-origin-fallback.html
    custom_login_page_on: true
    grant_types:
      - client_credentials
    initiate_login_uri: https://login.stage.divinci.app
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    native_social_login:
      apple:
        enabled: false
      facebook:
        enabled: false
    oidc_conformant: true
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 31557600
      idle_token_lifetime: 2592000
      rotation_type: non-rotating
    sso_disabled: false
    token_endpoint_auth_method: client_secret_post
    web_origins:
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
  - name: Default App
    callbacks: []
    cross_origin_auth: false
    custom_login_page_on: true
    grant_types:
      - authorization_code
      - implicit
      - refresh_token
      - client_credentials
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    oidc_conformant: true
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 2592000
      idle_token_lifetime: 1296000
      rotation_type: non-rotating
    sso_disabled: false
  - name: Divinci Stage Server-to-Server
    allowed_clients: []
    allowed_logout_urls:
      - http://localhost
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
    allowed_origins:
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
    app_type: non_interactive
    callbacks:
      - http://localhost
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
    client_aliases: []
    cross_origin_auth: false
    cross_origin_authentication: true
    cross_origin_loc: https://chat.stage.divinci.app/cross-origin-fallback.html
    custom_login_page_on: true
    grant_types:
      - client_credentials
    initiate_login_uri: https://login.stage.divinci.app
    is_first_party: true
    is_token_endpoint_ip_header_trusted: true
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    logo_uri: >-
      https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
    native_social_login:
      apple:
        enabled: false
      facebook:
        enabled: false
    oidc_conformant: true
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 31557600
      idle_token_lifetime: 2592000
      rotation_type: non-rotating
    sso_disabled: false
    token_endpoint_auth_method: client_secret_post
    web_origins:
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - http://localhost:8080
  - name: Divinci-Mobile-App
    allowed_clients: []
    allowed_logout_urls:
      - >-
        com.mdm.divinci-ai://dev-46tiys6hnb6vbg17.us.auth0.com/ios/com.mdm.divinci-ai/callback
    app_type: native
    callbacks:
      - >-
        com.mdm.divinci-ai://dev-46tiys6hnb6vbg17.us.auth0.com/ios/com.mdm.divinci-ai/callback
    client_aliases: []
    cross_origin_auth: false
    cross_origin_authentication: true
    custom_login_page_on: true
    grant_types:
      - authorization_code
      - implicit
      - refresh_token
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    logo_uri: >-
      https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
    native_social_login:
      apple:
        enabled: false
      facebook:
        enabled: false
    oidc_conformant: true
    organization_require_behavior: no_prompt
    organization_usage: allow
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 2592000
      idle_token_lifetime: 1296000
      rotation_type: non-rotating
    sso_disabled: false
    token_endpoint_auth_method: none
    web_origins:
      - >-
        com.mdm.divinci-ai://dev-46tiys6hnb6vbg17.us.auth0.com/ios/com.mdm.divinci-ai/
  - name: Divinci-Web-Client-Local
    allowed_clients: []
    allowed_logout_urls:
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
    allowed_origins:
      - https://chat.divinci.app
    app_type: spa
    callbacks:
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
    client_aliases: []
    cross_origin_auth: false
    cross_origin_authentication: false
    cross_origin_loc: https://chat.divinci.app/
    custom_login_page_on: true
    description: Local development web client
    grant_types:
      - authorization_code
      - implicit
      - refresh_token
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    logo_uri: >-
      https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
    native_social_login:
      apple:
        enabled: false
      facebook:
        enabled: false
    oidc_conformant: true
    refresh_token:
      expiration_type: expiring
      leeway: 0
      token_lifetime: 2592000
      idle_token_lifetime: 1296000
      infinite_token_lifetime: false
      infinite_idle_token_lifetime: false
      rotation_type: rotating
    sso_disabled: false
    token_endpoint_auth_method: none
    web_origins:
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
  - name: Divinci-Web-Client-Local-Stage
    allowed_clients: []
    allowed_logout_urls:
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:8081
      - https://*.stage.divinci.app:443
      - https://************
      - https://************:80
      - https://************:8080
      - https://************:443
      - https://*.dev.divinci.app
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
    allowed_origins:
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:8081
      - https://*.stage.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
    app_type: spa
    callbacks:
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:8081
      - https://*.stage.divinci.app:443
      - https://************
      - https://************:80
      - https://************:8080
      - https://************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
    client_aliases: []
    cross_origin_auth: false
    cross_origin_authentication: true
    cross_origin_loc: https://chat.stage.divinci.app/cross-origin-fallback.html
    custom_login_page_on: true
    grant_types:
      - authorization_code
      - implicit
      - refresh_token
    initiate_login_uri: https://login.stage.divinci.app
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    logo_uri: >-
      https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
    native_social_login:
      apple:
        enabled: false
      facebook:
        enabled: false
    oidc_conformant: true
    organization_require_behavior: no_prompt
    organization_usage: deny
    refresh_token:
      expiration_type: expiring
      leeway: 0
      token_lifetime: 2592000
      idle_token_lifetime: 1296000
      infinite_token_lifetime: false
      infinite_idle_token_lifetime: false
      rotation_type: rotating
    sso_disabled: false
    token_endpoint_auth_method: none
    web_origins:
      - https://*.stage.divinci.app
      - https://*.stage.divinci.app:80
      - https://*.stage.divinci.app:8080
      - https://*.stage.divinci.app:8081
      - https://*.stage.divinci.app:443
      - https://************
      - https://************:80
      - https://************:8080
      - https://************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
databases:
  - name: Username-Password-Authentication
    strategy: auth0
    enabled_clients:
      - API Explorer Application
      - Auth0 Management API 2 (Test Application)
      - Chat Stage Divinci App (Test Application)
      - Default App
      - Divinci Stage Server-to-Server
      - Divinci-Mobile-App
      - Divinci-Web-Client-Local
      - Divinci-Web-Client-Local-Stage
    is_domain_connection: false
    options:
      mfa:
        active: true
        return_enroll_settings: true
      passwordPolicy: good
      passkey_options:
        challenge_ui: both
        local_enrollment_enabled: true
        progressive_enrollment_enabled: true
      strategy_version: 2
      authentication_methods:
        passkey:
          enabled: false
        password:
          enabled: true
      brute_force_protection: true
    realms:
      - Username-Password-Authentication
connections:
  - name: apple
    strategy: apple
    enabled_clients:
      - API Explorer Application
      - Auth0 Management API 2 (Test Application)
      - Chat Stage Divinci App (Test Application)
      - Default App
      - Divinci Stage Server-to-Server
      - Divinci-Mobile-App
      - Divinci-Web-Client-Local
      - Divinci-Web-Client-Local-Stage
    is_domain_connection: false
    options: {}
  - name: facebook
    strategy: facebook
    enabled_clients:
      - API Explorer Application
      - Auth0 Management API 2 (Test Application)
      - Chat Stage Divinci App (Test Application)
      - Default App
      - Divinci Stage Server-to-Server
      - Divinci-Mobile-App
      - Divinci-Web-Client-Local
      - Divinci-Web-Client-Local-Stage
    is_domain_connection: false
    options:
      scope: ''
  - name: google-oauth2
    strategy: google-oauth2
    enabled_clients:
      - API Explorer Application
      - Auth0 Management API 2 (Test Application)
      - Chat Stage Divinci App (Test Application)
      - Default App
      - Divinci Stage Server-to-Server
      - Divinci-Mobile-App
      - Divinci-Web-Client-Local
      - Divinci-Web-Client-Local-Stage
    is_domain_connection: false
    options:
      email: true
      gmail: false
      orkut: false
      scope:
        - email
        - profile
      sites: false
      tasks: false
      blogger: false
      profile: true
      youtube: false
      calendar: false
      contacts: false
      analytics: false
      client_id: 150038457816-t3otpkvm6ivc4nn44uqlnc77k3llcn80.apps.googleusercontent.com
      moderator: false
      coordinate: false
      picasa_web: false
      google_plus: false
      google_books: false
      google_drive: false
      spreadsheets: false
      client_secret: 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
      document_list: false
      latitude_best: false
      latitude_city: false
      url_shortener: false
      webmaster_tools: false
      chrome_web_store: false
      allowed_audiences: ''
      adsense_management: false
      google_drive_files: false
      coordinate_readonly: false
      google_cloud_storage: false
      content_api_for_shopping: false
      google_affiliate_network: false
  - name: twitter
    strategy: twitter
    enabled_clients:
      - API Explorer Application
      - Auth0 Management API 2 (Test Application)
      - Chat Stage Divinci App (Test Application)
      - Default App
      - Divinci Stage Server-to-Server
      - Divinci-Mobile-App
      - Divinci-Web-Client-Local-Stage
    is_domain_connection: false
    options: {}
tenant:
  allowed_logout_urls:
    - https://chat.stage.divinci.app/
  customize_mfa_in_postlogin_action: true
  enabled_locales:
    - en
  flags:
    enable_custom_domain_in_emails: true
    revoke_refresh_token_grant: false
    mfa_show_factor_list_on_enrollment: true
    disable_clickjack_protection_headers: false
  friendly_name: Divinci AI
  oidc_logout:
    rp_logout_end_session_endpoint_discovery: false
  picture_url: >-
    https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
  sandbox_version: '18'
  sessions:
    oidc_logout_prompt_enabled: true
  support_email: <EMAIL>
  support_url: https://divinci.app/support
  universal_login:
    colors:
      page_background: '#000000'
      primary: '#635dff'
    passwordless:
      allow_magiclink_verify_without_session: true
emailProvider: {}
emailTemplates: []
clientGrants:
  - client_id: API Explorer Application
    audience: http://localhost:8081
    scope:
      - read:users
      - read:user_idp_tokens
      - read:users_app_metadata
  - client_id: API Explorer Application
    audience: chat.stage.divinci.app:8080
    scope:
      - read:users
      - read:user_idp_tokens
      - read:users_app_metadata
  - client_id: API Explorer Application
    audience: https://divinci-staging.us.auth0.com/api/v2/
    scope:
      - read:client_grants
      - create:client_grants
      - delete:client_grants
      - update:client_grants
      - read:users
      - update:users
      - delete:users
      - create:users
      - read:users_app_metadata
      - update:users_app_metadata
      - delete:users_app_metadata
      - create:users_app_metadata
      - read:user_custom_blocks
      - create:user_custom_blocks
      - delete:user_custom_blocks
      - create:user_tickets
      - read:clients
      - update:clients
      - delete:clients
      - create:clients
      - read:client_keys
      - update:client_keys
      - delete:client_keys
      - create:client_keys
      - read:connections
      - update:connections
      - delete:connections
      - create:connections
      - read:resource_servers
      - update:resource_servers
      - delete:resource_servers
      - create:resource_servers
      - read:device_credentials
      - update:device_credentials
      - delete:device_credentials
      - create:device_credentials
      - read:rules
      - update:rules
      - delete:rules
      - create:rules
      - read:rules_configs
      - update:rules_configs
      - delete:rules_configs
      - read:hooks
      - update:hooks
      - delete:hooks
      - create:hooks
      - read:actions
      - update:actions
      - delete:actions
      - create:actions
      - read:email_provider
      - update:email_provider
      - delete:email_provider
      - create:email_provider
      - blacklist:tokens
      - read:stats
      - read:insights
      - read:tenant_settings
      - update:tenant_settings
      - read:logs
      - read:logs_users
      - read:shields
      - create:shields
      - update:shields
      - delete:shields
      - read:anomaly_blocks
      - delete:anomaly_blocks
      - update:triggers
      - read:triggers
      - read:grants
      - delete:grants
      - read:guardian_factors
      - update:guardian_factors
      - read:guardian_enrollments
      - delete:guardian_enrollments
      - create:guardian_enrollment_tickets
      - read:user_idp_tokens
      - create:passwords_checking_job
      - delete:passwords_checking_job
      - read:custom_domains
      - delete:custom_domains
      - create:custom_domains
      - update:custom_domains
      - read:email_templates
      - create:email_templates
      - update:email_templates
      - read:mfa_policies
      - update:mfa_policies
      - read:roles
      - create:roles
      - delete:roles
      - update:roles
      - read:prompts
      - update:prompts
      - read:branding
      - update:branding
      - delete:branding
      - read:log_streams
      - create:log_streams
      - delete:log_streams
      - update:log_streams
      - create:signing_keys
      - read:signing_keys
      - update:signing_keys
      - read:limits
      - update:limits
      - create:role_members
      - read:role_members
      - delete:role_members
      - read:entitlements
      - read:attack_protection
      - update:attack_protection
      - read:organizations_summary
      - create:authentication_methods
      - read:authentication_methods
      - update:authentication_methods
      - delete:authentication_methods
      - read:organizations
      - update:organizations
      - create:organizations
      - delete:organizations
      - create:organization_members
      - read:organization_members
      - delete:organization_members
      - create:organization_connections
      - read:organization_connections
      - update:organization_connections
      - delete:organization_connections
      - create:organization_member_roles
      - read:organization_member_roles
      - delete:organization_member_roles
      - create:organization_invitations
      - read:organization_invitations
      - delete:organization_invitations
      - read:scim_config
      - create:scim_config
      - update:scim_config
      - delete:scim_config
      - create:scim_token
      - read:scim_token
      - delete:scim_token
      - delete:phone_providers
      - create:phone_providers
      - read:phone_providers
      - update:phone_providers
      - delete:phone_templates
      - create:phone_templates
      - read:phone_templates
      - update:phone_templates
      - create:encryption_keys
      - read:encryption_keys
      - update:encryption_keys
      - delete:encryption_keys
      - read:sessions
      - delete:sessions
      - read:refresh_tokens
      - delete:refresh_tokens
      - create:self_service_profiles
      - read:self_service_profiles
      - update:self_service_profiles
      - delete:self_service_profiles
      - create:sso_access_tickets
      - delete:sso_access_tickets
      - read:forms
      - update:forms
      - delete:forms
      - create:forms
      - read:flows
      - update:flows
      - delete:flows
      - create:flows
      - read:flows_vault
      - read:flows_vault_connections
      - update:flows_vault_connections
      - delete:flows_vault_connections
      - create:flows_vault_connections
      - read:flows_executions
      - delete:flows_executions
      - read:connections_options
      - update:connections_options
      - read:self_service_profile_custom_texts
      - update:self_service_profile_custom_texts
      - read:client_credentials
      - create:client_credentials
      - update:client_credentials
      - delete:client_credentials
      - read:organization_client_grants
      - create:organization_client_grants
      - delete:organization_client_grants
  - client_id: Auth0 Management API 2 (Test Application)
    audience: https://divinci-staging.us.auth0.com/api/v2/
    scope:
      - read:client_grants
      - create:client_grants
      - delete:client_grants
      - update:client_grants
      - read:users
      - update:users
      - delete:users
      - create:users
      - read:users_app_metadata
      - update:users_app_metadata
      - delete:users_app_metadata
      - create:users_app_metadata
      - read:user_custom_blocks
      - create:user_custom_blocks
      - delete:user_custom_blocks
      - create:user_tickets
      - read:clients
      - update:clients
      - delete:clients
      - create:clients
      - read:client_keys
      - update:client_keys
      - delete:client_keys
      - create:client_keys
      - read:connections
      - update:connections
      - delete:connections
      - create:connections
      - read:resource_servers
      - update:resource_servers
      - delete:resource_servers
      - create:resource_servers
      - read:device_credentials
      - update:device_credentials
      - delete:device_credentials
      - create:device_credentials
      - read:rules
      - update:rules
      - delete:rules
      - create:rules
      - read:rules_configs
      - update:rules_configs
      - delete:rules_configs
      - read:hooks
      - update:hooks
      - delete:hooks
      - create:hooks
      - read:actions
      - update:actions
      - delete:actions
      - create:actions
      - read:email_provider
      - update:email_provider
      - delete:email_provider
      - create:email_provider
      - blacklist:tokens
      - read:stats
      - read:insights
      - read:tenant_settings
      - update:tenant_settings
      - read:logs
      - read:logs_users
      - read:shields
      - create:shields
      - update:shields
      - delete:shields
      - read:anomaly_blocks
      - delete:anomaly_blocks
      - update:triggers
      - read:triggers
      - read:grants
      - delete:grants
      - read:guardian_factors
      - update:guardian_factors
      - read:guardian_enrollments
      - delete:guardian_enrollments
      - create:guardian_enrollment_tickets
      - read:user_idp_tokens
      - create:passwords_checking_job
      - delete:passwords_checking_job
      - read:custom_domains
      - delete:custom_domains
      - create:custom_domains
      - update:custom_domains
      - read:email_templates
      - create:email_templates
      - update:email_templates
      - read:mfa_policies
      - update:mfa_policies
      - read:roles
      - create:roles
      - delete:roles
      - update:roles
      - read:prompts
      - update:prompts
      - read:branding
      - update:branding
      - delete:branding
      - read:log_streams
      - create:log_streams
      - delete:log_streams
      - update:log_streams
      - create:signing_keys
      - read:signing_keys
      - update:signing_keys
      - read:limits
      - update:limits
      - create:role_members
      - read:role_members
      - delete:role_members
      - read:entitlements
      - read:attack_protection
      - update:attack_protection
      - read:organizations_summary
      - create:authentication_methods
      - read:authentication_methods
      - update:authentication_methods
      - delete:authentication_methods
      - read:organizations
      - update:organizations
      - create:organizations
      - delete:organizations
      - create:organization_members
      - read:organization_members
      - delete:organization_members
      - create:organization_connections
      - read:organization_connections
      - update:organization_connections
      - delete:organization_connections
      - create:organization_member_roles
      - read:organization_member_roles
      - delete:organization_member_roles
      - create:organization_invitations
      - read:organization_invitations
      - delete:organization_invitations
      - read:scim_config
      - create:scim_config
      - update:scim_config
      - delete:scim_config
      - create:scim_token
      - read:scim_token
      - delete:scim_token
      - delete:phone_providers
      - create:phone_providers
      - read:phone_providers
      - update:phone_providers
      - delete:phone_templates
      - create:phone_templates
      - read:phone_templates
      - update:phone_templates
      - create:encryption_keys
      - read:encryption_keys
      - update:encryption_keys
      - delete:encryption_keys
      - read:sessions
      - delete:sessions
      - read:refresh_tokens
      - delete:refresh_tokens
      - create:self_service_profiles
      - read:self_service_profiles
      - update:self_service_profiles
      - delete:self_service_profiles
      - create:sso_access_tickets
      - delete:sso_access_tickets
      - read:forms
      - update:forms
      - delete:forms
      - create:forms
      - read:flows
      - update:flows
      - delete:flows
      - create:flows
      - read:flows_vault
      - read:flows_vault_connections
      - update:flows_vault_connections
      - delete:flows_vault_connections
      - create:flows_vault_connections
      - read:flows_executions
      - delete:flows_executions
      - read:connections_options
      - update:connections_options
      - read:self_service_profile_custom_texts
      - update:self_service_profile_custom_texts
      - read:client_credentials
      - create:client_credentials
      - update:client_credentials
      - delete:client_credentials
      - read:organization_client_grants
      - create:organization_client_grants
      - delete:organization_client_grants
  - client_id: Chat Stage Divinci App (Test Application)
    audience: http://localhost:8081
    scope:
      - read:users
      - read:user_idp_tokens
      - read:users_app_metadata
  - client_id: Chat Stage Divinci App (Test Application)
    audience: chat.stage.divinci.app:8080
    scope:
      - read:users
      - read:user_idp_tokens
      - read:users_app_metadata
  - client_id: Chat Stage Divinci App (Test Application)
    audience: https://divinci-staging.us.auth0.com/api/v2/
    scope:
      - read:client_grants
      - create:client_grants
      - delete:client_grants
      - update:client_grants
      - read:users
      - update:users
      - delete:users
      - create:users
      - read:users_app_metadata
      - update:users_app_metadata
      - delete:users_app_metadata
      - create:users_app_metadata
      - read:user_custom_blocks
      - create:user_custom_blocks
      - delete:user_custom_blocks
      - create:user_tickets
      - read:clients
      - update:clients
      - delete:clients
      - create:clients
      - read:client_keys
      - update:client_keys
      - delete:client_keys
      - create:client_keys
      - read:connections
      - update:connections
      - delete:connections
      - create:connections
      - read:resource_servers
      - update:resource_servers
      - delete:resource_servers
      - create:resource_servers
      - read:device_credentials
      - update:device_credentials
      - delete:device_credentials
      - create:device_credentials
      - read:rules
      - update:rules
      - delete:rules
      - create:rules
      - read:rules_configs
      - update:rules_configs
      - delete:rules_configs
      - read:hooks
      - update:hooks
      - delete:hooks
      - create:hooks
      - read:actions
      - update:actions
      - delete:actions
      - create:actions
      - read:email_provider
      - update:email_provider
      - delete:email_provider
      - create:email_provider
      - blacklist:tokens
      - read:stats
      - read:insights
      - read:tenant_settings
      - update:tenant_settings
      - read:logs
      - read:logs_users
      - read:shields
      - create:shields
      - update:shields
      - delete:shields
      - read:anomaly_blocks
      - delete:anomaly_blocks
      - update:triggers
      - read:triggers
      - read:grants
      - delete:grants
      - read:guardian_factors
      - update:guardian_factors
      - read:guardian_enrollments
      - delete:guardian_enrollments
      - create:guardian_enrollment_tickets
      - read:user_idp_tokens
      - create:passwords_checking_job
      - delete:passwords_checking_job
      - read:custom_domains
      - delete:custom_domains
      - create:custom_domains
      - update:custom_domains
      - read:email_templates
      - create:email_templates
      - update:email_templates
      - read:mfa_policies
      - update:mfa_policies
      - read:roles
      - create:roles
      - delete:roles
      - update:roles
      - read:prompts
      - update:prompts
      - read:branding
      - update:branding
      - delete:branding
      - read:log_streams
      - create:log_streams
      - delete:log_streams
      - update:log_streams
      - create:signing_keys
      - read:signing_keys
      - update:signing_keys
      - read:limits
      - update:limits
      - create:role_members
      - read:role_members
      - delete:role_members
      - read:entitlements
      - read:attack_protection
      - update:attack_protection
      - read:organizations_summary
      - create:authentication_methods
      - read:authentication_methods
      - update:authentication_methods
      - delete:authentication_methods
      - read:organizations
      - update:organizations
      - create:organizations
      - delete:organizations
      - create:organization_members
      - read:organization_members
      - delete:organization_members
      - create:organization_connections
      - read:organization_connections
      - update:organization_connections
      - delete:organization_connections
      - create:organization_member_roles
      - read:organization_member_roles
      - delete:organization_member_roles
      - create:organization_invitations
      - read:organization_invitations
      - delete:organization_invitations
      - read:scim_config
      - create:scim_config
      - update:scim_config
      - delete:scim_config
      - create:scim_token
      - read:scim_token
      - delete:scim_token
      - delete:phone_providers
      - create:phone_providers
      - read:phone_providers
      - update:phone_providers
      - delete:phone_templates
      - create:phone_templates
      - read:phone_templates
      - update:phone_templates
      - create:encryption_keys
      - read:encryption_keys
      - update:encryption_keys
      - delete:encryption_keys
      - read:sessions
      - delete:sessions
      - read:refresh_tokens
      - delete:refresh_tokens
      - create:self_service_profiles
      - read:self_service_profiles
      - update:self_service_profiles
      - delete:self_service_profiles
      - create:sso_access_tickets
      - delete:sso_access_tickets
      - read:forms
      - update:forms
      - delete:forms
      - create:forms
      - read:flows
      - update:flows
      - delete:flows
      - create:flows
      - read:flows_vault
      - read:flows_vault_connections
      - update:flows_vault_connections
      - delete:flows_vault_connections
      - create:flows_vault_connections
      - read:flows_executions
      - delete:flows_executions
      - read:connections_options
      - update:connections_options
      - read:self_service_profile_custom_texts
      - update:self_service_profile_custom_texts
      - read:client_credentials
      - create:client_credentials
      - update:client_credentials
      - delete:client_credentials
      - read:organization_client_grants
      - create:organization_client_grants
      - delete:organization_client_grants
  - client_id: Divinci Stage Server-to-Server
    audience: chat.stage.divinci.app:8080
    scope:
      - read:users
      - read:user_idp_tokens
      - read:users_app_metadata
  - client_id: Divinci Stage Server-to-Server
    audience: https://divinci-staging.us.auth0.com/api/v2/
    scope:
      - read:client_grants
      - create:client_grants
      - delete:client_grants
      - update:client_grants
      - read:users
      - update:users
      - delete:users
      - create:users
      - read:users_app_metadata
      - update:users_app_metadata
      - delete:users_app_metadata
      - create:users_app_metadata
      - read:user_custom_blocks
      - create:user_custom_blocks
      - delete:user_custom_blocks
      - create:user_tickets
      - read:clients
      - update:clients
      - delete:clients
      - create:clients
      - read:client_keys
      - update:client_keys
      - delete:client_keys
      - create:client_keys
      - read:connections
      - update:connections
      - delete:connections
      - create:connections
      - read:resource_servers
      - update:resource_servers
      - delete:resource_servers
      - create:resource_servers
      - read:device_credentials
      - update:device_credentials
      - delete:device_credentials
      - create:device_credentials
      - read:rules
      - update:rules
      - delete:rules
      - create:rules
      - read:rules_configs
      - update:rules_configs
      - delete:rules_configs
      - read:hooks
      - update:hooks
      - delete:hooks
      - create:hooks
      - read:actions
      - update:actions
      - delete:actions
      - create:actions
      - read:email_provider
      - update:email_provider
      - delete:email_provider
      - create:email_provider
      - blacklist:tokens
      - read:stats
      - read:insights
      - read:tenant_settings
      - update:tenant_settings
      - read:logs
      - read:logs_users
      - read:shields
      - create:shields
      - update:shields
      - delete:shields
      - read:anomaly_blocks
      - delete:anomaly_blocks
      - update:triggers
      - read:triggers
      - read:grants
      - delete:grants
      - read:guardian_factors
      - update:guardian_factors
      - read:guardian_enrollments
      - delete:guardian_enrollments
      - create:guardian_enrollment_tickets
      - read:user_idp_tokens
      - create:passwords_checking_job
      - delete:passwords_checking_job
      - read:custom_domains
      - delete:custom_domains
      - create:custom_domains
      - update:custom_domains
      - read:email_templates
      - create:email_templates
      - update:email_templates
      - read:mfa_policies
      - update:mfa_policies
      - read:roles
      - create:roles
      - delete:roles
      - update:roles
      - read:prompts
      - update:prompts
      - read:branding
      - update:branding
      - delete:branding
      - read:log_streams
      - create:log_streams
      - delete:log_streams
      - update:log_streams
      - create:signing_keys
      - read:signing_keys
      - update:signing_keys
      - read:limits
      - update:limits
      - create:role_members
      - read:role_members
      - delete:role_members
      - read:entitlements
      - read:attack_protection
      - update:attack_protection
      - read:organizations_summary
      - create:authentication_methods
      - read:authentication_methods
      - update:authentication_methods
      - delete:authentication_methods
      - read:organizations
      - update:organizations
      - create:organizations
      - delete:organizations
      - create:organization_members
      - read:organization_members
      - delete:organization_members
      - create:organization_connections
      - read:organization_connections
      - update:organization_connections
      - delete:organization_connections
      - create:organization_member_roles
      - read:organization_member_roles
      - delete:organization_member_roles
      - create:organization_invitations
      - read:organization_invitations
      - delete:organization_invitations
      - read:scim_config
      - create:scim_config
      - update:scim_config
      - delete:scim_config
      - create:scim_token
      - read:scim_token
      - delete:scim_token
      - delete:phone_providers
      - create:phone_providers
      - read:phone_providers
      - update:phone_providers
      - delete:phone_templates
      - create:phone_templates
      - read:phone_templates
      - update:phone_templates
      - create:encryption_keys
      - read:encryption_keys
      - update:encryption_keys
      - delete:encryption_keys
      - read:sessions
      - delete:sessions
      - read:refresh_tokens
      - delete:refresh_tokens
      - create:self_service_profiles
      - read:self_service_profiles
      - update:self_service_profiles
      - delete:self_service_profiles
      - create:sso_access_tickets
      - delete:sso_access_tickets
      - read:forms
      - update:forms
      - delete:forms
      - create:forms
      - read:flows
      - update:flows
      - delete:flows
      - create:flows
      - read:flows_vault
      - read:flows_vault_connections
      - update:flows_vault_connections
      - delete:flows_vault_connections
      - create:flows_vault_connections
      - read:flows_executions
      - delete:flows_executions
      - read:connections_options
      - update:connections_options
      - read:self_service_profile_custom_texts
      - update:self_service_profile_custom_texts
      - read:client_credentials
      - create:client_credentials
      - update:client_credentials
      - delete:client_credentials
      - read:organization_client_grants
      - create:organization_client_grants
      - delete:organization_client_grants
  - client_id: Divinci Stage Server-to-Server
    audience: http://localhost:8081
    scope:
      - read:users
      - read:user_idp_tokens
      - read:users_app_metadata
guardianFactors:
  - name: duo
    enabled: false
  - name: email
    enabled: true
  - name: otp
    enabled: true
  - name: push-notification
    enabled: false
  - name: recovery-code
    enabled: true
  - name: sms
    enabled: true
  - name: webauthn-platform
    enabled: false
  - name: webauthn-roaming
    enabled: false
guardianFactorProviders: []
guardianFactorTemplates: []
guardianPolicies:
  policies:
    - confidence-score
guardianPhoneFactorSelectedProvider:
  provider: auth0
guardianPhoneFactorMessageTypes:
  message_types:
    - sms
roles: []
branding:
  colors:
    page_background: '#000000'
    primary: '#635dff'
  logo_url: >-
    https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
  templates: []
prompts:
  customText: {}
  identifier_first: false
  partials: {}
  universal_login_experience: new
actions: []
triggers: {}
organizations:
  - name: divinci
    branding:
      logo_url: >-
        https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
      colors:
        page_background: '#000000'
        primary: '#001c42'
    client_grants: []
    connections:
      - name: google-oauth2
        assign_membership_on_login: true
        show_as_button: true
    display_name: Divinci
attackProtection:
  breachedPasswordDetection:
    enabled: false
    shields: []
    admin_notification_frequency: []
    method: standard
    stage:
      pre-user-registration:
        shields: []
  bruteForceProtection:
    enabled: true
    shields:
      - block
      - user_notification
    mode: count_per_identifier_and_ip
    allowlist: []
    max_attempts: 10
  suspiciousIpThrottling:
    enabled: true
    shields:
      - admin_notification
      - block
    allowlist: []
    stage:
      pre-login:
        max_attempts: 100
        rate: 864000
      pre-user-registration:
        max_attempts: 50
        rate: 1200
logStreams:
  - name: Datadog
    isPriority: false
    sink:
      datadogApiKey: _VALUE_NOT_SHOWN_
      datadogRegion: us
    status: active
    type: datadog
customDomains:
  - domain: login.stage.divinci.app
    primary: true
    status: ready
    tls_policy: recommended
    type: auth0_managed_certs
    verification:
      methods:
        - name: CNAME
          record: divinci-staging-cd-x1esbmuf1ccg9rxn.edge.tenants.us.auth0.com
          domain: login.stage.divinci.app
themes:
  - borders:
      button_border_weight: 1
      buttons_style: rounded
      button_border_radius: 3
      input_border_weight: 1
      inputs_style: rounded
      input_border_radius: 3
      widget_corner_radius: 5
      widget_border_weight: 0
      show_widget_shadow: true
    colors:
      primary_button: '#635dff'
      primary_button_label: '#ffffff'
      secondary_button_border: '#c9cace'
      secondary_button_label: '#1e212a'
      base_focus_color: '#635dff'
      base_hover_color: '#000000'
      links_focused_components: '#635dff'
      header: '#1e212a'
      body_text: '#1e212a'
      widget_background: '#ffffff'
      widget_border: '#c9cace'
      input_labels_placeholders: '#65676e'
      input_filled_text: '#000000'
      input_border: '#c9cace'
      input_background: '#ffffff'
      icons: '#65676e'
      error: '#d03c38'
      success: '#13a688'
    displayName: Unnamed Theme
    fonts:
      font_url: ''
      reference_text_size: 16
      title:
        bold: false
        size: 150
      subtitle:
        bold: false
        size: 87.5
      body_text:
        bold: false
        size: 87.5
      buttons_text:
        bold: false
        size: 100
      input_labels:
        bold: false
        size: 100
      links:
        bold: true
        size: 87.5
      links_style: normal
    page_background:
      page_layout: center
      background_color: '#000000'
      background_image_url: ''
    widget:
      logo_position: center
      logo_url: ''
      logo_height: 52
      header_text_alignment: center
      social_buttons_layout: bottom
forms: []
flows: []
flowVaultConnections: []
selfServiceProfiles: []
