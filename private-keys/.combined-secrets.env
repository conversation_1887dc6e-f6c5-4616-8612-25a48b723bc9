MONGO_DOMAIN_HOSTNAME=serverlessinstance0.c4pobzg.mongodb.net/?retryWrites=true&w=majority&appName=ServerlessInstance0
MONGO_INITDB_ROOT_USERNAME=divinciUser1
MONGO_INITDB_ROOT_PASSWORD=GYy2zwO9pJJtts0s
MONGO_IS_SRV=1


CORS_SUBDOMAINS="chat,api,live,webhook"
CORS_SUBDOMAIN_ENVS="stage"
CORS_IP_ADDRESSES="************,*************,*************"
CORS_PORTS="8080,8081,8082,8083,8084,9080,9081,8787,8788,8789,8790"
CORS_FULL_ORIGINS="login.stage.divinci.app,api.slack.com,divinci-staging.us.auth0.com"


STRIPE_PRODUCT_BASIC_TOKEN_SUBSCRIPTION=price_1PKTITIzNdoxvIKm0Wh6MHn3

NODE_ENV=staging

AUTH0_BASE_URL=https://divinci-staging.us.auth0.com
AUTH0_AUDIENCE=chat.divinci.app:8081

AUTH0_S2S_BASE_URL=https://divinci-staging.us.auth0.com
AUTH0_S2S_AUDIENCE=https://divinci-staging.us.auth0.com/api/v2/
AUTH0_S2S_CLIENT_ID=2GlkXIeQghHaaml596CtTbV4jU1PkkXk
AUTH0_S2S_CLIENT_SECRET=****************************************************************

OPENAI_API_KEY=***************************************************
OPENAI_ORGANIZATION=org-hnPNG30gqq7KYSfaJ8v92w1i

GOOGLE_CLOUD_STORAGE_PROJECT_ID=openai-api-4375643
GOOGLE_CLOUD_STORAGE_BUCKET_NAME=divinci-aichat-response-bucket-development
GOOGLE_CLOUD_DEFAULT_REGION=us-central1

UNSTRUCTURED_API_KEY=IPRqjRNj3xere00NgUmN6CqoeCQQRr
UNSTRUCTURED_WORKER_URL=https://api.unstructuredapp.io/general/v0/general

STRIPE_API_SECRET_KEY=sk_test_51NkdNpIzNdoxvIKmOxGK0eGgON9EG1mocTwAWDminaygiFMVfBlZUGJkbEzvjDeCA6yncOWQQWXPt9h8WrilgX25002zyBz5ZC
STRIPE_API_PUBLIC_KEY=pk_test_51NkdNpIzNdoxvIKmyRJDOyY1yEv2rB5VsscNvsai5UrEoIBrQGSr9fbLnGjfBRtuwax27yAEUYgc9xqmgA8fZopG00Qh7KG0Py

TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=55ff28265d0a1c0a6f4daf9da395fa87
TWILIO_PHONE_NUMBER=***********

JSON_WEBTOKEN_SECRET="NOT_VERY_SECRET"

REDIS_USERNAME=default
REDIS_PASSWORD=kAOe2Xa1u7DDY93Rr0D1Y2CVDISwUZjk
REDIS_DOMAIN_HOSTNAME=redis-15121.c259.us-central1-2.gce.redns.redis-cloud.com
REDIS_PORT=15121

AUTH0_CLIENT_ID=i3mzTidClOWxYPdjo8wd4cdMbwmKt6BJ
AUTH0_CLIENT_DOMAIN=divinci-staging.us.auth0.com
AUTH0_AUDIENCE=chat.divinci.app:8081

SPEECHLY_APP_ID=39580c4d-c98a-4924-ba0c-153809a8648d

DD_API_KEY=pubba62e7aeff00ed649dff480bfc4093e4
DD_API_ID=b5f8affa-cb8b-4d69-bc04-2b18b12b9fb5

#⚠️ These are used for web deployments and deployments only use what they need.
#⚠️ i.e., a web client only change will not load the `cloudflare.env` env vars as it's mostly for backend APIs.
CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4
CLOUDFLARE_ACCOUNT_ID=14a6fa23390363382f378b5bd4a0f849
CLOUDFLARE_ZONE_ID=9b26e2c415f36b0f656204133c8ab87c
CLOUDFLARE_API_TOKEN=****************************************
CLOUDFLARE_PUBLIC_WEB_APP_BUCKET=public-assets
CLOUDFLARE_PUBLIC_WEB_APP_HOST=chat.assets.divinci.app
CLOUDFLARE_PUBLIC_WEB_APP_ACCESS_KEY_ID=30a88e14196735b4ba4eb3250d2f73b9
CLOUDFLARE_PUBLIC_WEB_APP_ACCESS_KEY_SECRET=****************************************************************

TEST_ENV=stage
ENVIRONMENT=staging

AUTH0_TEST_USER_EMAIL=<EMAIL>
AUTH0_TEST_USER_PASSWORD=(abc123ABC)

DIVINCI_TEST_ENVIRONMENT=false
DIVINCI_TEST_PROCESS_DELAY=500

CF_ACCESS_CLIENT_ID=f13e5d39e1997a5ddb674362e73199c5.access
CF_ACCESS_CLIENT_SECRET=****************************************************************

WEB_TEST_CLIENT_IS_SECURE=0
WEB_TEST_CLIENT_PORT=8080
WEB_TEST_CLIENT_HOST=localhost


QDRANT__SERVICE__API_KEY=3DjrdSrh8Kngs3Io5fzqtZyFQJjkZqztYpkBVC86Nb5nziuT8dWy7Q
QDRANT_SECURE=1
QDRANT_HOSTNAME=https://********-c7bd-406d-a1f5-bb83aa96da83.us-east4-0.gcp.cloud.qdrant.io
QDRANT_PORT=6333

CLOUDFLARE_ACCOUNT_ID=14a6fa23390363382f378b5bd4a0f849
CLOUDFLARE_API_KEY=****************************************
CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4

CLOUDFLARE_ACCESS_KEY_ID=df025ff46814fc015c3b2e207ee62b3a
CLOUDFLARE_ACCESS_KEY_SECRET=****************************************************************

CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_ID=18b5c471e5dd78cef1fd1af202b61851
CLOUDFLARE_WHITELABEL_VECTOR_ACCESS_KEY_SECRET=d191b05cf2fae5872909955e79598b9adc85f656f71ca408c6a469342dfe4151

# Cloudflare D1 Worker Secret Header
CLOUDFLARE_WORKER_X_AUTH_DEV=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2
CLOUDFLARE_D1_WORKER_BASE_URL=https://d1-doc-elements-dev.divinci-ai.workers.dev

CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_KEY_ID=7a4b9ed4e9f2d02de1d6484e6b433f02
CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_KEY_SECRET=8f28017d6a8e9135a027cd28117015c7805dd6b663d6788af56bb44196fae7a3
CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_BUCKET=public-files
CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_PUBLIC_HOST=chat-uploads.divinci.app

# Cloudflare D1 Worker Secret Header
CLOUDFLARE_EMAIL_WORKER_BASE_URL=https://email.divinci.app/send-notification
# CLOUDFLARE_EMAIL_WORKER_BASE_URL=https://divinci-send-notification-email-d011.divinci-ai.workers.dev

CLOUDFLARE_FINE_TUNE_TOKEN=****************************************
CLOUDFLARE_FINE_TUNE_ACCESS_KEY=ec82dc7efdfb3a638ea9559900cd323b
CLOUDFLARE_FINE_TUNE_SECRET_ACCESS_KEY=678b8632b6e4704edfd57a491882a752ce85cc61cc33af691a8706b2f8a05cce
CLOUDFLARE_FINE_TUNE_S3=https://14a6fa23390363382f378b5bd4a0f849.r2.cloudflarestorage.com


RAW_CLIENT_IS_SECURE=1
RAW_CLIENT_HOST=stage.divinci.app

WEB_CLIENT_PORT=8080
WEB_CLIENT_IS_SECURE=1
WEB_CLIENT_HOST=chat.stage.divinci.app

EMBED_CLIENT_IS_SECURE=1
EMBED_CLIENT_HOST=embed.stage.divinci.app


API_IS_SECURE=1
API_HOST=api.stage.divinci.app

API_LIVE_IS_SECURE=1
API_LIVE_HOST=live.stage.divinci.app

API_TEST_IS_SECURE=0
API_TEST_PORT=""
API_TEST_HOST=localhost:18084

DIVINCI_GH_PAT_3=****************************************
