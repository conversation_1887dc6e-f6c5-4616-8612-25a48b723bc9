rules: []
rulesConfigs: []
hooks: []
pages: []
resourceServers:
  - name: <PERSON><PERSON> App 2
    identifier: chat.divinci.app:8081
    allow_offline_access: true
    enforce_policies: false
    scopes:
      - value: read:users
        description: Can read users
      - value: "read:user_idp_tokens\t"
        description: Can read users
      - value: "read:users_app_metadata\t"
        description: Can read users
    signing_alg: RS256
    skip_consent_for_verifiable_first_party_clients: true
    token_dialect: access_token
    token_lifetime: 86400
    token_lifetime_for_web: 7200
  - name: Test API
    identifier: http://localhost:8081
    allow_offline_access: true
    enforce_policies: false
    scopes:
      - value: read:users
        description: Can read users
      - value: read:user_idp_tokens
        description: Can read users
      - value: read:users_app_metadata
        description: Can read users
    signing_alg: RS256
    skip_consent_for_verifiable_first_party_clients: true
    token_dialect: access_token
    token_lifetime: 86400
    token_lifetime_for_web: 7200
clients:
  - name: API Explorer Application
    app_type: non_interactive
    cross_origin_auth: false
    cross_origin_authentication: true
    custom_login_page_on: true
    grant_types:
      - client_credentials
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    oidc_conformant: true
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 31557600
      idle_token_lifetime: 2592000
      rotation_type: non-rotating
    sso_disabled: false
    token_endpoint_auth_method: client_secret_post
  - name: Auth0 Management API 2 (Test Application)
    app_type: non_interactive
    cross_origin_auth: false
    cross_origin_authentication: false
    custom_login_page_on: true
    grant_types:
      - client_credentials
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    oidc_conformant: true
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 31557600
      idle_token_lifetime: 2592000
      rotation_type: non-rotating
    sso_disabled: false
    token_endpoint_auth_method: client_secret_post
  - name: Chat Divinci App (Test Application)
    allowed_clients: []
    allowed_logout_urls:
      - http://localhost
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
    allowed_origins:
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
    app_type: non_interactive
    callbacks:
      - http://localhost
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
    client_aliases: []
    cross_origin_auth: false
    cross_origin_authentication: true
    cross_origin_loc: https://chat.divinci.app/cross-origin-fallback.html
    custom_login_page_on: true
    grant_types:
      - client_credentials
    initiate_login_uri: https://login.divinci.app
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    native_social_login:
      apple:
        enabled: false
      facebook:
        enabled: false
    oidc_conformant: true
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 31557600
      idle_token_lifetime: 2592000
      rotation_type: non-rotating
    sso_disabled: false
    token_endpoint_auth_method: client_secret_post
    web_origins:
      - http://localhost:8080
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
  - name: Default App
    allowed_logout_urls: []
    app_type: native
    callbacks: []
    cross_origin_auth: false
    cross_origin_authentication: true
    custom_login_page_on: true
    grant_types:
      - authorization_code
      - implicit
      - refresh_token
      - client_credentials
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    oidc_conformant: true
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 2592000
      idle_token_lifetime: 1296000
      rotation_type: non-rotating
    sso_disabled: false
    web_origins: []
  - name: Divinci Server-to-Server
    allowed_clients: []
    allowed_logout_urls:
      - http://localhost
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
    allowed_origins:
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
    app_type: non_interactive
    callbacks:
      - http://localhost
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
    client_aliases: []
    cross_origin_auth: false
    cross_origin_authentication: true
    cross_origin_loc: https://chat.divinci.app/cross-origin-fallback.html
    custom_login_page_on: true
    grant_types:
      - client_credentials
    initiate_login_uri: https://login.divinci.app
    is_first_party: true
    is_token_endpoint_ip_header_trusted: true
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    logo_uri: >-
      https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
    native_social_login:
      apple:
        enabled: false
      facebook:
        enabled: false
    oidc_conformant: true
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 31557600
      idle_token_lifetime: 2592000
      rotation_type: non-rotating
    sso_disabled: false
    token_endpoint_auth_method: client_secret_post
    web_origins:
      - http://localhost:8080
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
  - name: Divinci-Mobile-App
    allowed_clients: []
    allowed_logout_urls:
      - >-
        com.mdm.divinci-ai://dev-46tiys6hnb6vbg17.us.auth0.com/ios/com.mdm.divinci-ai/callback
    app_type: native
    callbacks:
      - >-
        com.mdm.divinci-ai://dev-46tiys6hnb6vbg17.us.auth0.com/ios/com.mdm.divinci-ai/callback
    client_aliases: []
    cross_origin_auth: false
    cross_origin_authentication: true
    custom_login_page_on: true
    grant_types:
      - authorization_code
      - implicit
      - refresh_token
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    logo_uri: >-
      https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
    native_social_login:
      apple:
        enabled: false
      facebook:
        enabled: false
    oidc_conformant: true
    organization_require_behavior: no_prompt
    organization_usage: allow
    refresh_token:
      expiration_type: non-expiring
      leeway: 0
      infinite_token_lifetime: true
      infinite_idle_token_lifetime: true
      token_lifetime: 2592000
      idle_token_lifetime: 1296000
      rotation_type: non-rotating
    sso_disabled: false
    token_endpoint_auth_method: none
    web_origins:
      - >-
        com.mdm.divinci-ai://dev-46tiys6hnb6vbg17.us.auth0.com/ios/com.mdm.divinci-ai/
  - name: Divinci-Web-Client-Local
    allowed_clients: []
    allowed_logout_urls:
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
    allowed_origins:
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
    app_type: spa
    callbacks:
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
    client_aliases: []
    cross_origin_auth: false
    cross_origin_authentication: true
    cross_origin_loc: https://chat.divinci.app/
    custom_login_page_on: true
    description: Local development web client
    grant_types:
      - authorization_code
      - implicit
      - refresh_token
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    logo_uri: >-
      https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
    native_social_login:
      apple:
        enabled: false
      facebook:
        enabled: false
    oidc_conformant: true
    refresh_token:
      expiration_type: expiring
      leeway: 0
      token_lifetime: 2592000
      idle_token_lifetime: 1296000
      infinite_token_lifetime: false
      infinite_idle_token_lifetime: false
      rotation_type: rotating
    sso_disabled: false
    token_endpoint_auth_method: none
    web_origins:
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
  - name: Divinci-Web-Client-Local-Dev
    allowed_clients: []
    allowed_logout_urls:
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://************
      - https://************:80
      - https://************:8080
      - https://************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
    allowed_origins:
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - https://*************
      - https://*************:80
      - https://*************:8080
      - https://*************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
    app_type: spa
    callbacks:
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://************
      - https://************:80
      - https://************:8080
      - https://************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:9091
      - http://localhost:8080/v1
    client_aliases: []
    cross_origin_auth: false
    cross_origin_authentication: true
    cross_origin_loc: https://chat.divinci.app/cross-origin-fallback.html
    custom_login_page_on: true
    description: For API-Tests and chat.dev.divinci.app
    grant_types:
      - authorization_code
      - implicit
      - refresh_token
    initiate_login_uri: https://login.divinci.app
    is_first_party: true
    is_token_endpoint_ip_header_trusted: false
    jwt_configuration:
      alg: RS256
      lifetime_in_seconds: 36000
      secret_encoded: false
    logo_uri: >-
      https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
    native_social_login:
      apple:
        enabled: false
      facebook:
        enabled: false
    oidc_conformant: true
    organization_require_behavior: no_prompt
    organization_usage: deny
    refresh_token:
      expiration_type: expiring
      leeway: 0
      token_lifetime: 2592000
      idle_token_lifetime: 1296000
      infinite_token_lifetime: false
      infinite_idle_token_lifetime: false
      rotation_type: rotating
    sso_disabled: false
    token_endpoint_auth_method: none
    web_origins:
      - https://*.dev.divinci.app
      - https://*.dev.divinci.app:80
      - https://*.dev.divinci.app:8080
      - https://*.dev.divinci.app:443
      - https://************
      - https://************:80
      - https://************:8080
      - https://************:443
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:18080
      - http://localhost:18081
      - http://localhost:18084
      - http://localhost:9091
      - http://localhost:8080/v1
databases:
  - name: Username-Password-Authentication
    strategy: auth0
    enabled_clients:
      - API Explorer Application
      - Auth0 Management API 2 (Test Application)
      - Chat Divinci App (Test Application)
      - Default App
      - Divinci Server-to-Server
      - Divinci-Mobile-App
      - Divinci-Web-Client-Local
      - Divinci-Web-Client-Local-Dev
    is_domain_connection: false
    options:
      mfa:
        active: true
        return_enroll_settings: true
      passwordPolicy: good
      passkey_options:
        challenge_ui: both
        local_enrollment_enabled: true
        progressive_enrollment_enabled: true
      strategy_version: 2
      authentication_methods:
        passkey:
          enabled: false
        password:
          enabled: true
      brute_force_protection: true
    realms:
      - Username-Password-Authentication
connections:
  - name: apple
    strategy: apple
    enabled_clients:
      - API Explorer Application
      - Auth0 Management API 2 (Test Application)
      - Chat Divinci App (Test Application)
      - Default App
      - Divinci Server-to-Server
      - Divinci-Mobile-App
      - Divinci-Web-Client-Local
      - Divinci-Web-Client-Local-Dev
    is_domain_connection: false
    options: {}
  - name: facebook
    strategy: facebook
    enabled_clients:
      - API Explorer Application
      - Auth0 Management API 2 (Test Application)
      - Chat Divinci App (Test Application)
      - Default App
      - Divinci Server-to-Server
      - Divinci-Mobile-App
      - Divinci-Web-Client-Local
      - Divinci-Web-Client-Local-Dev
    is_domain_connection: false
    options:
      scope: ''
  - name: google-oauth2
    strategy: google-oauth2
    enabled_clients:
      - API Explorer Application
      - Auth0 Management API 2 (Test Application)
      - Chat Divinci App (Test Application)
      - Default App
      - Divinci Server-to-Server
      - Divinci-Mobile-App
      - Divinci-Web-Client-Local
      - Divinci-Web-Client-Local-Dev
    is_domain_connection: false
    options:
      email: true
      gmail: false
      orkut: false
      scope:
        - email
        - profile
      sites: false
      tasks: false
      blogger: false
      profile: true
      youtube: false
      calendar: false
      contacts: false
      analytics: false
      client_id: 150038457816-t3otpkvm6ivc4nn44uqlnc77k3llcn80.apps.googleusercontent.com
      moderator: false
      coordinate: false
      picasa_web: false
      google_plus: false
      google_books: false
      google_drive: false
      spreadsheets: false
      client_secret: 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
      document_list: false
      latitude_best: false
      latitude_city: false
      url_shortener: false
      webmaster_tools: false
      chrome_web_store: false
      allowed_audiences: ''
      adsense_management: false
      google_drive_files: false
      coordinate_readonly: false
      google_cloud_storage: false
      content_api_for_shopping: false
      google_affiliate_network: false
  - name: twitter
    strategy: twitter
    enabled_clients:
      - API Explorer Application
      - Auth0 Management API 2 (Test Application)
      - Chat Divinci App (Test Application)
      - Default App
      - Divinci Server-to-Server
      - Divinci-Mobile-App
      - Divinci-Web-Client-Local
      - Divinci-Web-Client-Local-Dev
    is_domain_connection: false
    options: {}
tenant:
  allowed_logout_urls:
    - https://chat.divinci.app/
  enabled_locales:
    - en
  flags:
    enable_custom_domain_in_emails: true
    revoke_refresh_token_grant: false
    disable_clickjack_protection_headers: false
  friendly_name: Divinci AI
  picture_url: >-
    https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
  sandbox_version: '18'
  sessions:
    oidc_logout_prompt_enabled: true
  support_email: <EMAIL>
  support_url: https://divinci.app/support
  universal_login:
    colors:
      page_background: '#000000'
      primary: '#635dff'
    passwordless:
      allow_magiclink_verify_without_session: true
emailProvider: {}
emailTemplates: []
clientGrants:
  - client_id: Chat Divinci App (Test Application)
    audience: chat.divinci.app:8081
    scope: []
  - client_id: Chat Divinci App (Test Application)
    audience: http://localhost:8081
    scope: []
  - client_id: Chat Divinci App (Test Application)
    audience: https://dev-46tiys6hnb6vbg17.us.auth0.com/api/v2/
    scope: []
  - client_id: Divinci Server-to-Server
    audience: https://dev-46tiys6hnb6vbg17.us.auth0.com/api/v2/
    scope:
      - read:users
      - read:users_app_metadata
      - read:user_idp_tokens
  - client_id: Divinci Server-to-Server
    audience: chat.divinci.app:8081
    scope: []
  - client_id: Divinci Server-to-Server
    audience: http://localhost:8081
    scope:
      - read:users
      - read:user_idp_tokens
      - read:users_app_metadata
guardianFactors:
  - name: duo
    enabled: false
  - name: email
    enabled: true
  - name: otp
    enabled: true
  - name: push-notification
    enabled: false
  - name: recovery-code
    enabled: true
  - name: sms
    enabled: true
  - name: webauthn-platform
    enabled: false
  - name: webauthn-roaming
    enabled: false
guardianFactorProviders: []
guardianFactorTemplates: []
guardianPolicies:
  policies: []
guardianPhoneFactorSelectedProvider:
  provider: auth0
guardianPhoneFactorMessageTypes:
  message_types:
    - sms
roles: []
branding:
  colors:
    page_background: '#000000'
    primary: '#635dff'
  logo_url: >-
    https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
  templates: []
prompts:
  customText: {}
  identifier_first: false
  partials: {}
  universal_login_experience: new
actions: []
triggers: {}
organizations:
  - name: divinci
    branding:
      logo_url: >-
        https://storage.googleapis.com/divinci-bucket/Screenshot%202023-03-15%20at%204.59.07%20AM.png
      colors:
        page_background: '#000000'
        primary: '#001c42'
    client_grants: []
    connections:
      - name: google-oauth2
        assign_membership_on_login: true
        show_as_button: true
    display_name: Divinci
attackProtection:
  breachedPasswordDetection:
    enabled: false
    shields: []
    admin_notification_frequency: []
    method: standard
    stage:
      pre-user-registration:
        shields: []
  bruteForceProtection:
    enabled: true
    shields:
      - block
      - user_notification
    mode: count_per_identifier_and_ip
    allowlist: []
    max_attempts: 10
  suspiciousIpThrottling:
    enabled: true
    shields:
      - admin_notification
      - block
    allowlist: []
    stage:
      pre-login:
        max_attempts: 100
        rate: 864000
      pre-user-registration:
        max_attempts: 50
        rate: 1200
logStreams: []
customDomains:
  - domain: login.divinci.app
    primary: true
    status: ready
    tls_policy: recommended
    type: auth0_managed_certs
    verification:
      methods:
        - name: cname
          record: dev-46tiys6hnb6vbg17-cd-3xy0mq4eixfo4k3u.edge.tenants.us.auth0.com
themes:
  - borders:
      button_border_weight: 1
      buttons_style: rounded
      button_border_radius: 3
      input_border_weight: 1
      inputs_style: rounded
      input_border_radius: 3
      widget_corner_radius: 5
      widget_border_weight: 0
      show_widget_shadow: true
    colors:
      primary_button: '#635dff'
      primary_button_label: '#ffffff'
      secondary_button_border: '#c9cace'
      secondary_button_label: '#1e212a'
      base_focus_color: '#635dff'
      base_hover_color: '#000000'
      links_focused_components: '#635dff'
      header: '#1e212a'
      body_text: '#1e212a'
      widget_background: '#ffffff'
      widget_border: '#c9cace'
      input_labels_placeholders: '#65676e'
      input_filled_text: '#000000'
      input_border: '#c9cace'
      input_background: '#ffffff'
      icons: '#65676e'
      error: '#d03c38'
      success: '#13a688'
    displayName: Unnamed Theme
    fonts:
      font_url: ''
      reference_text_size: 16
      title:
        bold: false
        size: 150
      subtitle:
        bold: false
        size: 87.5
      body_text:
        bold: false
        size: 87.5
      buttons_text:
        bold: false
        size: 100
      input_labels:
        bold: false
        size: 100
      links:
        bold: true
        size: 87.5
      links_style: normal
    page_background:
      page_layout: center
      background_color: '#000000'
      background_image_url: ''
    widget:
      logo_position: center
      logo_url: ''
      logo_height: 52
      header_text_alignment: center
      social_buttons_layout: bottom
forms: []
flows: []
flowVaultConnections: []
selfServiceProfiles: []
