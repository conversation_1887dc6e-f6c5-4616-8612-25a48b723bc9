# LOG_DEBUG=1
ENVIRONMENT=local

AUTH0_CLIENT_ID=waO2KMbVwo3xXu1wqZyUeLA1cTinVpXx
AUTH0_CLIENT_DOMAIN=dev-46tiys6hnb6vbg17.us.auth0.com
AUTH0_AUDIENCE=http://localhost:8081

SPEECHLY_APP_ID=39580c4d-c98a-4924-ba0c-153809a8648d

API_IS_SECURE=0
API_HOST=localhost:18081
API_LIVE_IS_SECURE=0
API_LIVE_HOST=localhost:18082

#⚠️ These are used for web deployments and deployments only use what they need.
#⚠️ i.e., a web client only change will not load the `cloudflare.env` env vars as it's mostly for backend APIs.
CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4
CLOUDFLARE_ACCOUNT_ID=14a6fa23390363382f378b5bd4a0f849
CLOUDFLARE_ZONE_ID=9b26e2c415f36b0f656204133c8ab87c
CLOUDFLARE_API_TOKEN=****************************************
CLOUDFLARE_PUBLIC_WEB_APP_BUCKET=public-assets
CLOUDFLARE_PUBLIC_WEB_APP_HOST=assets.test.divinci.app
CLOUDFLARE_PUBLIC_WEB_APP_ACCESS_KEY_ID=30a88e14196735b4ba4eb3250d2f73b9
CLOUDFLARE_PUBLIC_WEB_APP_ACCESS_KEY_SECRET=****************************************************************
