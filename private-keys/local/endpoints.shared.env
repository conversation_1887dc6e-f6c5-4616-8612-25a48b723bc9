
RAW_CLIENT_IS_SECURE=0
RAW_CLIENT_HOST=localhost

WEB_CLIENT_PORT=8080
WEB_CLIENT_IS_SECURE=0
WEB_CLIENT_HOST=localhost:8080

EMBED_CLIENT_IS_SECURE=0
EMBED_CLIENT_HOST=localhost:8081

API_IS_SECURE=0
API_HOST=localhost:9080

API_LIVE_IS_SECURE=0
API_LIVE_HOST=localhost:9081

API_TEST_IS_SECURE=0
API_TEST_PORT=18084
API_TEST_HOST=localhost:18084

# Webhook server configuration
# WEBHOOK_BASE_URL=http://localhost:9082  # Disabled for local development (Pyannote API rejects localhost URLs)

# Test webhook URL for local development (replace with your webhook.site URL)
# Replace 'your-unique-id' with your actual webhook.site ID
TEST_WEBHOOK_URL=https://webhook.site/your-unique-id
