# Configuration for the pyannote speaker diarization service
DIVINCI_AUDIO_DIARIZER_PYANNOTE_HTTP_PORT=8085
PYANNOTE_APIKEY=sk_f9c6c510b4ee45099506d7c615689fbb

# Hugging Face token for accessing pyannote models
HUGGING_FACE_ACCESS_TOKEN=*************************************

# Service URL for other services to access this one
# Use Docker service name for local development
DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL=http://audio-speak-dia-pyannote:5001

# Logging configuration
LOG_LEVEL=INFO

# mTLS configuration (disabled by default)
MTLS_ENABLED=false

# Cache directories
HOME=/home/<USER>
MPLCONFIGDIR=/home/<USER>/.cache/matplotlib
HF_HOME=/home/<USER>/.cache/huggingface
XDG_CACHE_HOME=/home/<USER>/.cache
