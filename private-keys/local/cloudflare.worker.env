
CLOUDFLARE_ACCOUNT_ID=14a6fa23390363382f378b5bd4a0f849
CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4
#CLOUDFLARE_API_KEY=****************************************
CLOUDFLARE_API_KEY=****************************************
CLOUDFLARE_API_TOKEN=****************************************
CLOUDFLARE_WORKER_X_AUTH_DEV=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2

# Environment configuration
ENVIRONMENT=local

CHUNKS_VECTORIZED_WORKFLOW_URL=http://local-chunks-vectorized:8791
CLOUDFLARE_D1_API_URL=http://local-d1-rag:8787

# R2 Storage configuration
R2_BUCKET_URL=http://local-minio:9000
R2_ACCESS_KEY_ID=minioadmin
R2_SECRET_ACCESS_KEY=minioadmin
# Ensure these variables are used and not overridden elsewhere

# Force R2 storage for E2E testing with external APIs
# Set to false for local development to use MinIO instead of R2
FORCE_R2_STORAGE=false

# R2 credentials for direct R2 client (same as in cloudflare.env)
#R2_ACCESS_KEY_ID=8cadbca7d973a3442581c875c6725c2c
#R2_SECRET_ACCESS_KEY=df8d58584f5373255cba114d2c5fd603f86dd7fdbcf15b292a3d347ff62e3ebc
CLOUDFLARE_AUDIO_ACCESS_KEY=8cadbca7d973a3442581c875c6725c2c
CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY=df8d58584f5373255cba114d2c5fd603f86dd7fdbcf15b292a3d347ff62e3ebc
