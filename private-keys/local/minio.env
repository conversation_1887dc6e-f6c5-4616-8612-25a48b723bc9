MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin
# MINIO_ACCESS_KEY and MINIO_SECRET_KEY are deprecated, use MINIO_ROOT_USER and MINIO_ROOT_PASSWORD
# MINIO_ENDPOINT and MINIO_HOST are set in docker-compose.yml for container-to-host communication
# Use host.docker.internal for accessing host MinIO service from containers
# MINIO_ENDPOINT=http://host.docker.internal:9000
# MINIO_HOST=host.docker.internal
MINIO_PORT=9000

# Cloudflare Audio S3 configuration - using host.docker.internal for container-to-host communication
CLOUDFLARE_AUDIO_S3=http://host.docker.internal:9000
CLOUDFLARE_AUDIO_ACCESS_KEY=minioadmin
CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY=minioadmin
CLOUDFLARE_AUDIO_PUBLIC_URL=http://host.docker.internal:9000
LOCAL_AUDIO_PUBLIC_URL=http://host.docker.internal:9000

# R2 configuration for local development - using host.docker.internal for container-to-host communication
R2_BUCKET_URL=http://host.docker.internal:9000
R2_ACCESS_KEY_ID=minioadmin
R2_SECRET_ACCESS_KEY=minioadmin

# S3 configuration
AWS_S3_FORCE_PATH_STYLE=true