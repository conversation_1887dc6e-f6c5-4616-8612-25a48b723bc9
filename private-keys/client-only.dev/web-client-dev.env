ENVIRONMENT=local
LOG_DEBUG=1

AUTH0_CLIENT_ID=aPi4fs185lbPe8ZtQuQlG2w4lTVem0Gl
AUTH0_CLIENT_DOMAIN=dev-46tiys6hnb6vbg17.us.auth0.com
AUTH0_AUDIENCE=http://localhost:8081

SPEECHLY_APP_ID=39580c4d-c98a-4924-ba0c-153809a8648d

CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4
CLOUDFLARE_WORKER_X_AUTH_DEV=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2

DD_API_KEY=pubba62e7aeff00ed649dff480bfc4093e4
DD_API_ID=b5f8affa-cb8b-4d69-bc04-2b18b12b9fb5

#⚠️ These are used for web deployments and deployments only use what they need.
#⚠️ i.e., a web client only change will not load the `cloudflare.env` env vars as it's mostly for backend APIs.
CLOUDFLARE_API_URL=https://api.cloudflare.com/client/v4
CLOUDFLARE_ACCOUNT_ID=14a6fa23390363382f378b5bd4a0f849
CLOUDFLARE_ZONE_ID=9b26e2c415f36b0f656204133c8ab87c
CLOUDFLARE_API_TOKEN=****************************************
CLOUDFLARE_PUBLIC_WEB_APP_BUCKET=public-assets
CLOUDFLARE_PUBLIC_WEB_APP_HOST=assets.local.divinci.app
CLOUDFLARE_PUBLIC_WEB_APP_ACCESS_KEY_ID=30a88e14196735b4ba4eb3250d2f73b9
CLOUDFLARE_PUBLIC_WEB_APP_ACCESS_KEY_SECRET=36cff2235c19f69ea1d124bd50dac32e641c9b90300eb44e01621382cb0403df

CHUNKS_VECTORIZED_WORKFLOW_URL=https://chunks-workflow-dev.divinci-ai.workers.dev