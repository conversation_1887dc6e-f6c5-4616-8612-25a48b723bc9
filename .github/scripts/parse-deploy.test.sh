#!/bin/bash

# ----------------------------------------
# 🧪 Deploy Command Parser Tests
# ----------------------------------------
# Purpose: Test suite for parse-deploy.sh
# Author: Claude & Team
# Version: 1.0.0
# ----------------------------------------

source "./parse-deploy.sh"

test_parser() {
  local -a test_cases=(
    # Basic commands with different environments
    "[deploy:develop:all]"
    "[deploy:stage:all:fast]"
    "[deploy:prod:web]"
    "[deploy:develop:web:fast]"
    "[deploy:stage:web:api]"
    "[deploy:prod:web:api:fast]"

    # API variants with different environments
    "[deploy:develop:api:all]"
    "[deploy:stage:api:live]"
    "[deploy:prod:api:webhook]"
    "[deploy:develop:api]"
    "[deploy:stage:api:fast]"

    # Resource commands with different environments
    "[deploy:prod:resources]"
    "[deploy:develop:resources:fast]"
    "[deploy:stage:web:resources]"
    "[deploy:prod:api:resources]"
    "[deploy:develop:web:api:resources]"

    # Open-Parse test cases
    "[deploy:develop:open-parse]"
    "[deploy:stage:open-parse:fast]"
    "[deploy:prod:open-parse]"
    "[deploy:develop:api:open-parse]"
    "[deploy:stage:open-parse:api:fast]"
    "@github-actions [deploy:develop:open-parse]"
    "@github-actions [deploy:stage:open-parse:fast]"

    # Open-Parse combined with other services
    "[deploy:develop:web:open-parse]"
    "[deploy:stage:web:api:open-parse]"
    "[deploy:prod:all:open-parse]"

    # Pyannote test cases
    "[deploy:develop:pyannote]"
    "[deploy:stage:pyannote:fast]"
    "[deploy:prod:pyannote]"
    "[deploy:develop:api:pyannote]"
    "[deploy:stage:pyannote:api:fast]"
    "@github-actions [deploy:develop:pyannote]"
    "@github-actions [deploy:stage:pyannote:fast]"

    # FFmpeg test cases
    "[deploy:develop:ffmpeg]"
    "[deploy:stage:ffmpeg:fast]"
    "[deploy:prod:ffmpeg]"
    "[deploy:develop:api:ffmpeg]"
    "[deploy:stage:ffmpeg:api:fast]"
    "@github-actions [deploy:develop:ffmpeg]"
    "@github-actions [deploy:stage:ffmpeg:fast]"

    # Combined worker services
    "[deploy:develop:pyannote:ffmpeg]"
    "[deploy:stage:pyannote:ffmpeg:fast]"
    "[deploy:prod:pyannote:ffmpeg]"
    "[deploy:develop:web:api:pyannote:ffmpeg]"

    # Base services commands with different environments
    "[deploy:develop:base]"
    "[deploy:stage:base:fast]"
    "[deploy:prod:base]"
    "@github-actions [deploy:develop:base]"
    "@github-actions [deploy:stage:base:fast]"

    # Simple commands with different environments
    "[deploy:stage]"
    "[deploy:prod:fast]"
    "[deploy:develop:keep]"
    
    # Keep flag test cases
    "[deploy:stage:keep]"
    "[deploy:prod:keep]"
    "[deploy:develop:all:keep]"
    "[deploy:stage:web:keep]"
    "[deploy:prod:api:keep]"
    "[deploy:develop:web:api:keep]"
    "[deploy:stage:resources:keep]"
    "[deploy:prod:api:fast:keep]"
    "[deploy:develop:base:keep]"
    "[deploy:stage:all:fast:keep]"
    "@github-actions [deploy:develop:web:keep]"
    "@github-actions [deploy:stage:api:keep]"
    "@github-actions [deploy:prod:web:api:keep]"

    # GitHub Actions format
    "@github-actions [deploy:develop:all]"
    "@github-actions [deploy:stage:web:api]"
    "@github-actions [deploy:prod:resources:fast]"

    # Invalid cases
    "[deploy:invalid:all]"
    "[deploy]"
    "[deploy:develop]"
    "[deploy:stage:invalid]"
  )

  for test in "${test_cases[@]}"; do
    echo "Testing: $test"
    GITHUB_OUTPUT=$(mktemp)
    parse_deploy_command "$test"
    echo "Output:"
    cat "$GITHUB_OUTPUT"
    rm "$GITHUB_OUTPUT"
    echo "---"
  done
}

# Run tests
test_parser
