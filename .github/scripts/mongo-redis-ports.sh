#!/bin/bash

# ----------------------------------------
# Port Scanner for MongoDB and Redis
# ----------------------------------------
#
# Description:
#   Scans for available ports for MongoDB and Redis services.
#   Uses <PERSON><PERSON>'s host networking to check port availability.
#
# Usage:
#   ./mongo-redis-ports.sh [--debug]
#
# Options:
#   --debug    Enable debug output
#
# Local Testing:
#   1. Make script executable: chmod +x mongo-redis-ports.sh
#   2. Run with debug: ./mongo-redis-ports.sh --debug
#   3. Check output: echo $MONGO_PORT; echo $REDIS_PORT
#
# ----------------------------------------

# Enable debug mode if requested
LOG_DEBUG=0
if [[ "$1" == "--debug" ]]; then
    LOG_DEBUG=1
fi

# Debug function
debug() {
    if [[ $LOG_DEBUG -eq 1 ]]; then
    echo "🔍 LOG_DEBUG: $1" >&2
    fi
}

# Function to find an available port using Docker
find_available_port() {
    local start_port=$1
    local end_port=$2
    local service=$3

    debug "Scanning for $service port between $start_port and $end_port"

    for port in $(seq $start_port $end_port); do
        debug "Checking port $port..."

        # Try to start a temporary container with host networking
        if docker run --rm --network host alpine:3.14 nc -z localhost $port 2>/dev/null; then
            debug "❌ Port $port is in use"
        else
            debug "✅ Port $port is available for $service"
            echo $port
            return 0
        fi
    done

    debug "❌ No available ports found for $service between $start_port and $end_port"
    return 1
}

# Define port ranges with higher alternatives
MONGO_PORT_RANGE_START=37017  # Using 37xxx range instead of 27xxx
MONGO_PORT_RANGE_END=37117
REDIS_PORT_RANGE_START=16379  # Using 16xxx range instead of 6xxx
REDIS_PORT_RANGE_END=16479

debug "Starting port scan..."

# Find available ports
MONGO_PORT=$(find_available_port $MONGO_PORT_RANGE_START $MONGO_PORT_RANGE_END "MongoDB")
REDIS_PORT=$(find_available_port $REDIS_PORT_RANGE_START $REDIS_PORT_RANGE_END "Redis")

# Verify ports were found
if [[ -z "$MONGO_PORT" || -z "$REDIS_PORT" ]]; then
    echo "❌ Error: Could not find available ports" >&2
    exit 1
fi

# Export ports to environment
if [[ -n "$GITHUB_ENV" ]]; then
    # Running in GitHub Actions
    echo "MONGO_PORT=$MONGO_PORT" >> $GITHUB_ENV
    echo "REDIS_PORT=$REDIS_PORT" >> $GITHUB_ENV
    debug "Ports exported to GITHUB_ENV"
else
    # Running locally
    export MONGO_PORT=$MONGO_PORT
    export REDIS_PORT=$REDIS_PORT
    debug "Ports exported to local environment"
fi

# Always output the results
echo "📊 Port Assignment Results:"
echo " - MongoDB Port: $MONGO_PORT"
echo " - Redis Port: $REDIS_PORT"

debug "Script completed successfully."
