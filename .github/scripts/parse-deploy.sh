#!/bin/bash

# ----------------------------------------
# 🎯 Deploy Command Parser
# ----------------------------------------
# Purpose: Parses GitHub Actions deployment commands for CI/CD pipeline
# Author: Claude & Team
# Version: 1.4.0
# ----------------------------------------
#
# 📝 Description:
# Flexible parser that handles various deployment command combinations for
# targeting specific services and deployment modes.
#
# 🔄 Improvements:
# - Enhanced comment cleaning to handle leading/trailing whitespace and newlines
# - More flexible pattern matching for deployment commands
# - Better handling of different comment formats
# - Support for comments with extra spaces between parts
# - Improved error handling and debugging output
#
# 🎮 Usage:
#   ./parse-deploy.sh "[deploy:stage:web:api]"
#   ./parse-deploy.sh "@github-actions [deploy:prod:all:fail-tests]"
#   ./parse-deploy.sh "[deploy:develop:api]"
#
# 🎯 Supported Commands:
#   - Environments:  develop, stage, prod (required)
#   - Basic:        [deploy:env] [deploy:env:fail-tests] [deploy:env:keep]
#   - All:          [deploy:env:all] [deploy:env:all:fail-tests] [deploy:env:all:keep]
#   - Base:         [deploy:env:base] [deploy:env:base:fail-tests] [deploy:env:base:keep] (resources, web, api, live, webhook)
#   - Web:          [deploy:env:web] [deploy:env:web:fail-tests]
#   - API:          [deploy:env:api] [deploy:env:api:fail-tests]
#                   [deploy:env:api:all] [deploy:env:api:live] [deploy:env:api:webhook]
#   - Workers:      [deploy:env:pyannote] [deploy:env:ffmpeg]
#                   [deploy:env:open-parse]
#   - Resources:    [deploy:env:resources] [deploy:env:resources:fail-tests]
#   - Workers:      [deploy:env:work-pyannote] [deploy:env:work-ffmpeg]
#   - Combinations: [deploy:env:web:api] [deploy:env:web:api:fail-tests]
#                   [deploy:env:web:resources] [deploy:env:api:resources]
#                   [deploy:env:web:api:resources]
#                   [deploy:env:pyannote:ffmpeg]
#
# 📤 Outputs:
#   - is_fast_deploy: Boolean flag to skip tests (renamed from 'fast' to 'fail-tests')
#   - changed_folders: Comma-separated list of folders to deploy
#   - target_branch: Target environment branch
#   - keep_branch: Boolean flag to skip elevated branch cleanup
#
# ----------------------------------------

# Constants for folder paths
readonly RESOURCES_PATH="workspace/resources/server-models"
readonly WEB_PATH="workspace/clients/web"
# ⚠️ Don't add embed yet, it breaks the builds currently!
# readonly EMBED_PATH="workspace/clients/embed"
readonly API_PATH="workspace/servers/public-api"
readonly API_LIVE_PATH="workspace/servers/public-api-live"
readonly API_WEBHOOK_PATH="workspace/servers/public-api-webhook"
readonly API_OPEN_PARSE_PATH="workspace/workers/open-parse"
readonly API_PYANNOTE_PATH="workspace/workers/audio-speaker-diarization@pyannote"
readonly API_FFMPEG_PATH="workspace/workers/audio-splitter@ffmpeg"
readonly WORKER_AUDIO_PYANNOTE_PATH="workspace/workers/audio-speaker-diarization@pyannote"
readonly WORKER_AUDIO_FFMPEG_PATH="workspace/workers/audio-splitter@ffmpeg"

# Base services (core services only)
readonly BASE_SERVICES="${RESOURCES_PATH},${WEB_PATH},${API_PATH},${API_LIVE_PATH},${API_WEBHOOK_PATH}"

# All possible service combinations
readonly ALL_SERVICES_ARRAY=(
  "$API_PATH"
  "$API_LIVE_PATH"
  "$API_WEBHOOK_PATH"

  "$API_OPEN_PARSE_PATH"
  "$WORKER_AUDIO_PYANNOTE_PATH"
  "$WORKER_AUDIO_FFMPEG_PATH"

  "$WEB_PATH"

  "$RESOURCES_PATH"
)

# Join the array into a single comma-separated string
readonly ALL_SERVICES=$(IFS=, ; echo "${ALL_SERVICES_ARRAY[*]}")

parse_deploy_command() {
  local comment="$1"
  local is_github_action=false
  local command=""

  # Trim leading/trailing whitespace and remove carriage returns from comment
  comment=$(echo "$comment" | tr -d '\r' | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//')

  # Check if command starts with @github-actions
  if [[ $comment == "@github-actions"* ]]; then
    is_github_action=true
    command=${comment#"@github-actions "}
  else
    command=$comment
  fi

  # Initialize default values
  local is_fast_deploy=false
  local changed_folders=""
  local target_branch="stage"  # Default to stage
  local keep_branch=false  # New flag to skip elevated branch cleanup
  local services=()

  # Clean and extract just the deployment command
  echo "🔍 Extracting deployment command from: '$command'"

  # First try to extract command between brackets
  if [[ $command =~ \[(deploy:[^]]*)\] ]]; then
    command="${BASH_REMATCH[1]}"
    echo "  ✅ Matched standard bracket pattern: $command"
  elif [[ $command =~ \s*\[(deploy:[^]]*)\]\s* ]]; then
    # Try with optional whitespace around brackets
    command="${BASH_REMATCH[1]}"
    echo "  ✅ Matched pattern with whitespace around brackets: $command"
  elif [[ $command =~ \[\s*(deploy:[^]]*)\s*\] ]]; then
    # Try with optional whitespace inside brackets
    command="${BASH_REMATCH[1]}"
    echo "  ✅ Matched pattern with whitespace inside brackets: $command"
  elif [[ $command =~ deploy:[^[:space:][:punct:]]* ]]; then
    # Try without brackets
    command=$(echo "$command" | grep -o 'deploy:[^[:space:][:punct:]]*')
    echo "  ✅ Matched pattern without brackets: $command"
  elif [[ $command =~ deploy:[[:alnum:]:_-]+ ]]; then
    # Try a more general pattern
    command=$(echo "$command" | grep -o 'deploy:[[:alnum:]:_-]\+')
    echo "  ✅ Matched general deploy pattern: $command"
  else
    echo "  ⚠️ No deployment command pattern matched"
  fi

  # Split at first space to separate command from description
  command=$(echo "$command" | cut -d' ' -f1)
  echo "Cleaned command: $command" >&2

  # Split command into parts
  IFS=':' read -ra parts <<< "$command"

  # Count parts using a more compatible method
  parts_count=0
  for part in "${parts[@]}"; do
    parts_count=$((parts_count + 1))
  done

  echo "📊 Command parts ($parts_count total):" >&2

  # Print parts with indices using a more compatible method
  part_index=0
  for part in "${parts[@]}"; do
    echo "  Part $part_index: $part" >&2
    part_index=$((part_index + 1))
  done

  # Base command must be "deploy"
  if [[ ${parts[0]} != "deploy" ]]; then
    echo "❌ Invalid command format: Expected 'deploy' as first part, got '${parts[0]}'" >&2
    return 1
  fi

  echo "✅ Valid deploy command format detected" >&2

  # Process each part
  echo "🔄 Processing command parts..." >&2
  for part in "${parts[@]}"; do
    echo "  🔹 Processing part: '$part'" >&2
    case "$part" in
      "all")
        echo "    ✅ Found all flag - adding all services" >&2
        services=("$ALL_SERVICES")
        ;;
      "base")
        echo "    ✅ Found base flag - adding core services (resources, web, api, live, webhook)" >&2
        services=("$BASE_SERVICES")
        ;;
      "fail-tests"|"fast")
        if [[ "$part" == "fast" ]]; then
          echo "    ⚠️ Found deprecated 'fast' flag - please use 'fail-tests' instead" >&2
        else
          echo "    ✅ Found fail-tests flag - tests will be skipped" >&2
        fi
        is_fast_deploy=true
        ;;
      "keep")
        echo "    ✅ Found keep flag - branch will not be cleaned up" >&2
        keep_branch=true
        ;;
      # Workers
      "work-pyannote") echo "    🔧 Found audio-diarization worker flag" >&2; services+=("$WORKER_AUDIO_PYANNOTE_PATH") ;;
      "work-ffmpeg") echo "    🔧 Found audio-splitter worker flag" >&2; services+=("$WORKER_AUDIO_FFMPEG_PATH") ;;

      # API
      "api") echo "    🚀 Found api flag" >&2; services+=("$API_PATH") ;;
      "live") echo "    🚀 Found live flag" >&2; services+=("$API_LIVE_PATH") ;;
      "webhook") echo "    🚀 Found webhook flag" >&2; services+=("$API_WEBHOOK_PATH") ;;
      "open-parse") echo "    🚀 Found open-parse flag" >&2; services+=("$API_OPEN_PARSE_PATH") ;;
      "pyannote") echo "    🚀 Found pyannote flag" >&2; services+=("$API_PYANNOTE_PATH") ;;
      "ffmpeg") echo "    🚀 Found ffmpeg flag" >&2; services+=("$API_FFMPEG_PATH") ;;

      # Clients
      "web") echo "    🌐 Found web flag" >&2; services+=("$WEB_PATH") ;;

      # Resources
      "resources") echo "    📦 Found resources flag" >&2; services+=("$RESOURCES_PATH") ;;

      # Environment
      "develop"|"stage"|"prod")
        echo "    🌍 Found environment flag: $part" >&2
        target_branch="$part"
        ;;
      "deploy")
        # Skip the base command
        echo "    ➡️ Skipping base command part" >&2
        ;;
      *)
        echo "    ⚠️ Ignoring unknown part: $part" >&2
        ;;
    esac
  done

  # Combine selected services
  # Count services using a more compatible method
  services_count=0
  for service in "${services[@]}"; do
    services_count=$((services_count + 1))
  done

  if [ $services_count -gt 0 ]; then
    changed_folders=$(IFS=,; echo "${services[*]}")
  fi

  # If this is a comment-triggered deployment with specific services but no changed folders,
  # ensure we still have a valid changed_folders value
  if [[ "$is_github_action" == "true" && -z "$changed_folders" ]]; then
    # Check if we have specific service flags in the command
    if [[ "$command" =~ (pyannote|ffmpeg|open-parse) ]]; then
      echo "⚠️ Comment-triggered deployment with specific services but no changed folders detected" >&2
      # Extract the specific services from the command
      if [[ "$command" =~ pyannote ]]; then
        services+=("$API_PYANNOTE_PATH")
        echo "✅ Adding pyannote service path" >&2
      fi
      if [[ "$command" =~ ffmpeg ]]; then
        services+=("$API_FFMPEG_PATH")
        echo "✅ Adding ffmpeg service path" >&2
      fi
      if [[ "$command" =~ open-parse ]]; then
        services+=("$API_OPEN_PARSE_PATH")
        echo "✅ Adding open-parse service path" >&2
      fi
      # Recombine services
      changed_folders=$(IFS=,; echo "${services[*]}")
    fi
  fi

  # Debug final values
  echo "🏁 Final deployment configuration:" >&2
  echo "  🧪 is_fast_deploy (skip tests): $is_fast_deploy" >&2
  echo "  🗂️ changed_folders: $changed_folders" >&2
  echo "  🎋 target_branch: $target_branch" >&2
  echo "  🔒 keep_branch: $keep_branch" >&2

  # Set outputs
  {
    echo "is_fast_deploy=$is_fast_deploy"
    echo "changed_folders=$changed_folders"
    echo "target_branch=$target_branch"
    echo "keep_branch=$keep_branch"
  } >> "$GITHUB_OUTPUT"

  # Debug what was written to GITHUB_OUTPUT
  echo "📝 Written to GITHUB_OUTPUT:" >&2
  echo "----------------------------------------" >&2
  cat "$GITHUB_OUTPUT" >&2
  echo "----------------------------------------" >&2

  echo "✅ Deployment command parsing complete!" >&2
}

# Main execution
parse_deploy_command "$1"
