#!/bin/bash
# github-cli-fallback.sh
# This script provides fallback mechanisms for GitHub CLI (gh) operations
# Usage: source github-cli-fallback.sh

# GitHub CLI installation with fallback
install_github_cli() {
  # Skip if already installed
  if command -v gh &> /dev/null; then
    echo "GitHub CLI is already installed: $(which gh)"
    return 0
  fi

  echo "GitHub CLI not found, attempting installation..."
  
  # Determine OS
  if [[ "$(uname)" == "Darwin" ]]; then
    echo "Running on macOS, attempting installation..."
    
    # Try local installation without requiring sudo
    LOCAL_BIN_DIR="$HOME/bin"
    mkdir -p "$LOCAL_BIN_DIR"
    
    # Create a temporary directory
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"

    # Download the latest release
    echo "Downloading GitHub CLI..."
    curl -L https://github.com/cli/cli/releases/download/v2.40.1/gh_2.40.1_macOS_amd64.tar.gz -o gh.tar.gz
    
    # Extract the tarball
    echo "Extracting..."
    tar -xzf gh.tar.gz
    
    # First try without sudo for user-local installation
    echo "Installing to $LOCAL_BIN_DIR (no sudo required)..."
    cp gh_*/bin/gh "$LOCAL_BIN_DIR/"
    chmod +x "$LOCAL_BIN_DIR/gh"
    
    # Add to PATH
    export PATH="$LOCAL_BIN_DIR:$PATH"
    
    # Clean up
    cd -
    rm -rf "$TEMP_DIR"
    
    # Check if successful
    if command -v gh &> /dev/null; then
      echo "GitHub CLI successfully installed to $LOCAL_BIN_DIR"
    else
      echo "Local installation failed, trying alternative locations..."
      
      # Try to use sudo if available, but don't fail if it's not
      if command -v sudo &> /dev/null && sudo -n true 2>/dev/null; then
        echo "Sudo access available, installing to /usr/local/bin..."
        TEMP_DIR=$(mktemp -d)
        cd "$TEMP_DIR"
        curl -L https://github.com/cli/cli/releases/download/v2.40.1/gh_2.40.1_macOS_amd64.tar.gz -o gh.tar.gz
        tar -xzf gh.tar.gz
        sudo mkdir -p /usr/local/bin
        sudo cp gh_*/bin/gh /usr/local/bin/
        cd -
        rm -rf "$TEMP_DIR"
        
        # Add to PATH if needed
        export PATH="/usr/local/bin:$PATH"
      else
        echo "No sudo access available, falling back to scripted approach"
      fi
    fi
  elif [[ "$(uname)" == "Linux" ]]; then
    echo "Running on Linux, attempting installation..."
    
    # Try local installation first (without sudo)
    LOCAL_BIN_DIR="$HOME/bin"
    mkdir -p "$LOCAL_BIN_DIR"
    
    # Direct download approach
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    echo "Downloading GitHub CLI..."
    curl -L https://github.com/cli/cli/releases/download/v2.40.1/gh_2.40.1_linux_amd64.tar.gz -o gh.tar.gz
    
    echo "Extracting..."
    tar -xzf gh.tar.gz
    
    echo "Installing to $LOCAL_BIN_DIR (no sudo required)..."
    cp gh_*/bin/gh "$LOCAL_BIN_DIR/"
    chmod +x "$LOCAL_BIN_DIR/gh"
    
    # Add to PATH
    export PATH="$LOCAL_BIN_DIR:$PATH"
    
    # Clean up
    cd -
    rm -rf "$TEMP_DIR"
    
    # Check if successful
    if command -v gh &> /dev/null; then
      echo "GitHub CLI successfully installed to $LOCAL_BIN_DIR"
    else
      echo "Local installation failed, trying system-wide installation..."
      
      # Try system-wide installation with sudo if available
      if command -v sudo &> /dev/null && sudo -n true 2>/dev/null; then
        echo "Sudo access available, trying system installation..."
        
        # Try apt installation first if available
        if command -v apt-get &> /dev/null; then
          echo "Installing via apt..."
          curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg 2>/dev/null
          echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
          sudo apt-get update -qq
          sudo apt-get install -qq -y gh
        else
          echo "apt-get not found, using direct download..."
          TEMP_DIR=$(mktemp -d)
          cd "$TEMP_DIR"
          
          curl -L https://github.com/cli/cli/releases/download/v2.40.1/gh_2.40.1_linux_amd64.tar.gz -o gh.tar.gz
          tar -xzf gh.tar.gz
          
          sudo mkdir -p /usr/local/bin
          sudo cp gh_*/bin/gh /usr/local/bin/
          
          cd -
          rm -rf "$TEMP_DIR"
          
          # Add to PATH if needed
          export PATH="/usr/local/bin:$PATH"
        fi
      else
        echo "No sudo access available, falling back to scripted approach"
      fi
    fi
  else
    echo "Unsupported OS: $(uname)"
    return 1
  fi

  # Final verification
  if ! command -v gh &> /dev/null; then
    echo "GitHub CLI installation attempts failed"
    echo "PATH: $PATH"
    echo "Current working directory: $(pwd)"
    echo "HOME directory: $HOME"
    echo "Debug - contents of common binary directories:"
    echo "* $HOME/bin: $(ls -la "$HOME/bin" 2>/dev/null || echo 'Not accessible')"
    echo "* /usr/local/bin: $(ls -la /usr/local/bin 2>/dev/null || echo 'Not accessible')"
    echo "* /usr/bin: $(ls -la /usr/bin/gh 2>/dev/null || echo 'gh not found')"
    
    # Don't exit with error, just set up fallback functions
    echo "Setting up fallback functions instead..."
    setup_gh_fallback_functions # Setup fallbacks since installation failed
    return 0  # Return success so the script continues
  else
    echo "GitHub CLI successfully installed: $(which gh)"
    gh --version
    return 0
  fi
}

# Setup fallback functions for common GitHub CLI operations
setup_gh_fallback_functions() {
  echo "Setting up GitHub CLI fallback functions..."
  
  # Function to view PR details
  gh_pr_view() {
    local pr_number="$1"
    local repo="${GITHUB_REPOSITORY}"
    local option="$2"
    local query="$3"
    
    if [ "$option" == "--json" ]; then
      # Get PR data via API and apply jq query if provided
      curl -s -H "Authorization: token ${GITHUB_TOKEN}" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/repos/${repo}/pulls/${pr_number}" | \
        jq "${query}"
    else
      # Basic info
      curl -s -H "Authorization: token ${GITHUB_TOKEN}" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/repos/${repo}/pulls/${pr_number}"
    fi
  }
  
  # Function to list PRs
  gh_pr_list() {
    local repo="${GITHUB_REPOSITORY}"
    local state="open"
    local base=""
    local head=""
    local json=""
    local query=""
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
      case "$1" in
        --state)
          state="$2"
          shift 2
          ;;
        --base)
          base="$2"
          shift 2
          ;;
        --head)
          head="$2"
          shift 2
          ;;
        --json)
          json="$2"
          shift 2
          ;;
        -q|--jq)
          query="$2"
          shift 2
          ;;
        *)
          shift
          ;;
      esac
    done
    
    # Build API URL with filters
    local api_url="https://api.github.com/repos/${repo}/pulls?state=${state}"
    if [ -n "$base" ]; then
      api_url="${api_url}&base=${base}"
    fi
    if [ -n "$head" ]; then
      api_url="${api_url}&head=${head}"
    fi
    
    # Make API call
    local result=$(curl -s -H "Authorization: token ${GITHUB_TOKEN}" \
      -H "Accept: application/vnd.github.v3+json" \
      "${api_url}")
    
    # Apply jq query if provided
    if [ -n "$query" ]; then
      echo "$result" | jq "$query"
    else
      echo "$result"
    fi
  }
  
  # Function to create a PR
  gh_pr_create() {
    local repo="${GITHUB_REPOSITORY}"
    local base=""
    local head=""
    local title=""
    local body=""
    local labels=""
    local assignees=""
    local reviewers=""
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
      case "$1" in
        --base)
          base="$2"
          shift 2
          ;;
        --head)
          head="$2"
          shift 2
          ;;
        --title)
          title="$2"
          shift 2
          ;;
        --body)
          body="$2"
          shift 2
          ;;
        --label)
          if [ -z "$labels" ]; then
            labels="$2"
          else
            labels="$labels,$2"
          fi
          shift 2
          ;;
        --assignee)
          if [ -z "$assignees" ]; then
            assignees="$2"
          else
            assignees="$assignees,$2"
          fi
          shift 2
          ;;
        --reviewer)
          if [ -z "$reviewers" ]; then
            reviewers="$2"
          else
            reviewers="$reviewers,$2"
          fi
          shift 2
          ;;
        *)
          shift
          ;;
      esac
    done
    
    # Create JSON payload
    local payload=$(cat <<EOF
{
  "head": "${head}",
  "base": "${base}",
  "title": "${title}",
  "body": "${body}"
EOF
)

    # Add optional fields if present
    if [ -n "$labels" ]; then
      payload="${payload},\"labels\": [\"${labels//,/\",\"}\"]"
    fi
    
    if [ -n "$assignees" ]; then
      payload="${payload},\"assignees\": [\"${assignees//,/\",\"}\"]"
    fi
    
    # Close JSON object
    payload="${payload}}"
    
    # Create PR
    local result=$(curl -s -X POST \
      -H "Authorization: token ${GITHUB_TOKEN}" \
      -H "Accept: application/vnd.github.v3+json" \
      -H "Content-Type: application/json" \
      -d "${payload}" \
      "https://api.github.com/repos/${repo}/pulls")
    
    # Extract PR number
    local pr_number=$(echo "$result" | jq -r '.number')
    
    # Add reviewers in a separate request if present
    if [ -n "$reviewers" ]; then
      # Convert comma-separated list to JSON array
      local reviewers_json=$(echo "${reviewers}" | awk -F',' '{for(i=1;i<=NF;i++) printf "\"%s\"%s", $i, (i==NF?"":", ")}')
      
      curl -s -X POST \
        -H "Authorization: token ${GITHUB_TOKEN}" \
        -H "Accept: application/vnd.github.v3+json" \
        -H "Content-Type: application/json" \
        -d "{\"reviewers\": [${reviewers_json}]}" \
        "https://api.github.com/repos/${repo}/pulls/${pr_number}/requested_reviewers" > /dev/null
    fi
    
    # Return PR URL
    echo "https://github.com/${repo}/pull/${pr_number}"
  }
  
  # Function to comment on a PR
  gh_pr_comment() {
    local pr_number="$1"
    local body="$3"  # Assuming --body is the 3rd argument
    local repo="${GITHUB_REPOSITORY}"
    
    # Create comment
    curl -s -X POST \
      -H "Authorization: token ${GITHUB_TOKEN}" \
      -H "Accept: application/vnd.github.v3+json" \
      -H "Content-Type: application/json" \
      -d "{\"body\": \"${body}\"}" \
      "https://api.github.com/repos/${repo}/issues/${pr_number}/comments"
  }
  
  # Function to run a workflow
  gh_workflow_run() {
    local repo="${GITHUB_REPOSITORY}"
    local workflow_file="$1"
    local ref=""
    local parameters="{}"
    
    # Parse arguments
    shift
    while [[ $# -gt 0 ]]; do
      case "$1" in
        --ref|-r)
          ref="$2"
          shift 2
          ;;
        -f)
          local key="$2"
          local value="$3"
          # Update parameters JSON
          parameters=$(echo "$parameters" | jq --arg key "$key" --arg value "$value" '. + {($key): $value}')
          shift 3
          ;;
        *)
          shift
          ;;
      esac
    done
    
    # Create JSON payload
    local payload="{\"ref\":\"${ref:-master}\",\"inputs\":${parameters}}"
    
    # Run workflow
    curl -s -X POST \
      -H "Authorization: token ${GITHUB_TOKEN}" \
      -H "Accept: application/vnd.github.v3+json" \
      -H "Content-Type: application/json" \
      -d "${payload}" \
      "https://api.github.com/repos/${repo}/actions/workflows/${workflow_file}/dispatches"
  }
  
  # Function to get workflow runs
  gh_run_list() {
    local repo="${GITHUB_REPOSITORY}"
    local query=""
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
      case "$1" in
        --json)
          local fields="$2"
          shift 2
          ;;
        --jq)
          query="$2"
          shift 2
          ;;
        *)
          shift
          ;;
      esac
    done
    
    # Get workflow runs
    local result=$(curl -s \
      -H "Authorization: token ${GITHUB_TOKEN}" \
      -H "Accept: application/vnd.github.v3+json" \
      "https://api.github.com/repos/${repo}/actions/runs")
    
    # Apply jq query if provided
    if [ -n "$query" ]; then
      echo "$result" | jq "$query"
    else
      echo "$result"
    fi
  }
  
  # Function for GitHub auth login
  gh_auth_login() {
    # Just save the token for later use
    export GITHUB_TOKEN_SAVED="${GITHUB_TOKEN}"
    echo "GitHub token saved for API calls"
  }
  
  # Function for GitHub auth status
  gh_auth_status() {
    if [ -n "${GITHUB_TOKEN_SAVED}" ]; then
      echo "GitHub CLI auth status: Logged in with token"
    else
      echo "GitHub CLI auth status: Not logged in"
    fi
  }
  
  # Export all functions for use in the shell
  export -f gh_pr_view
  export -f gh_pr_list
  export -f gh_pr_create
  export -f gh_pr_comment
  export -f gh_workflow_run
  export -f gh_run_list
  export -f gh_auth_login
  export -f gh_auth_status
  
  # Create aliases to the real gh command
  alias gh_pr_view="gh_pr_view"
  alias gh_pr_list="gh_pr_list"
  alias gh_pr_create="gh_pr_create"
  alias gh_pr_comment="gh_pr_comment"
  alias gh_workflow_run="gh_workflow_run"
  alias gh_run_list="gh_run_list"
  alias gh="gh_wrapper"
  
  # Wrapper function to route gh commands to appropriate functions
  gh_wrapper() {
    local cmd="$1"
    shift
    
    case "$cmd" in
      pr)
        local subcmd="$1"
        shift
        case "$subcmd" in
          view) gh_pr_view "$@" ;;
          list) gh_pr_list "$@" ;;
          create) gh_pr_create "$@" ;;
          comment) gh_pr_comment "$@" ;;
          *) echo "Unsupported gh pr subcommand: $subcmd" >&2; return 1 ;;
        esac
        ;;
      workflow)
        local subcmd="$1"
        shift
        case "$subcmd" in
          run) gh_workflow_run "$@" ;;
          *) echo "Unsupported gh workflow subcommand: $subcmd" >&2; return 1 ;;
        esac
        ;;
      run)
        local subcmd="$1"
        shift
        case "$subcmd" in
          list) gh_run_list "$@" ;;
          *) echo "Unsupported gh run subcommand: $subcmd" >&2; return 1 ;;
        esac
        ;;
      auth)
        local subcmd="$1"
        shift
        case "$subcmd" in
          login) gh_auth_login "$@" ;;
          status) gh_auth_status "$@" ;;
          setup-git) echo "Git setup skipped in fallback mode" ;;
          *) echo "Unsupported gh auth subcommand: $subcmd" >&2; return 1 ;;
        esac
        ;;
      *)
        echo "Unsupported gh command: $cmd" >&2
        return 1
        ;;
    esac
  }
  
  echo "GitHub CLI fallback functions set up successfully"
}

# Try to install GitHub CLI
install_github_cli

# Verify if GitHub CLI is available
if ! command -v gh &> /dev/null; then
  echo "Using GitHub CLI fallback functions instead"
  setup_gh_fallback_functions
else 
  # Configure the real GitHub CLI
  echo "$GITHUB_TOKEN" | gh auth login --with-token
  gh auth setup-git
  gh auth status
fi