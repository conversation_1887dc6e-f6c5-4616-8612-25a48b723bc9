# GitHub Actions Scripts

This directory contains helper scripts for GitHub Actions workflows.

## GitHub CLI Fallback Script (`github-cli-fallback.sh`)

This script provides fallback mechanisms for GitHub CLI (gh) operations in GitHub Actions workflows. It attempts to install the GitHub CLI if not available, and if installation fails, it sets up shell functions that emulate the GitHub CLI using direct API calls.

### Usage

Add this to your workflow:

```yaml
- name: Setup GitHub CLI with fallback
  env:
    GITHUB_TOKEN: ${{ secrets.YOUR_GITHUB_TOKEN }}
  run: |
    chmod +x .github/scripts/github-cli-fallback.sh
    source .github/scripts/github-cli-fallback.sh
```

### Features

1. **Installation Attempt**: First tries to install the GitHub CLI using the appropriate method for the OS (apt on Linux, direct download on macOS).

2. **Fallback Functions**: If installation fails, sets up bash functions to emulate common GitHub CLI commands:
   - `gh pr view`
   - `gh pr list`
   - `gh pr create`
   - `gh pr comment`
   - `gh workflow run`
   - `gh run list`
   - `gh auth login`
   - `gh auth status`

3. **Transparent Usage**: After sourcing the script, you can use `gh` commands as usual, regardless of whether the actual CLI is installed or the fallback functions are being used.

### Requirements

- Bash shell
- `curl` and `jq` commands
- GitHub token with appropriate permissions set in `GITHUB_TOKEN` environment variable

### Example

```bash
# Source the script
source .github/scripts/github-cli-fallback.sh

# Use GitHub CLI commands as usual
gh pr view 123 --json number,title,body
gh pr create --base main --head feature-branch --title "My PR" --body "Description"
gh pr comment 123 --body "Adding a comment"
```

### Maintenance

If the GitHub API changes or new `gh` commands are needed, update the fallback functions in the script accordingly.