#!/bin/bash

# 🎯 Environment Loader for GitHub Actions
#
# This script loads environment variables from .env files in the private-keys directory
# following the same patterns as the test client:
# - Loads shared .env files
# - Loads non-client specific .env files
# - Skips client-specific files unless they're shared
#
# Usage: ./load-env.sh <environment-directory>

# Get script location and repository root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

copy_credentials() {
  local src_dir="$1"
  local dest_dir="$2"

  echo "🔐 Copying credentials..."

  # Check if source credentials exist
  if [ ! -d "$src_dir/credentials" ]; then
    echo "❌ No credentials directory found in source: $src_dir/credentials"
    return 1
  fi

  # Create destination directories
  local credentials_dest="$dest_dir/env/credentials"
  echo "📂 Creating credentials directory: $credentials_dest"
  mkdir -p "$credentials_dest"

  # Copy credentials
  echo "📋 Copying credentials files..."
  cp -r "$src_dir/credentials/"* "$credentials_dest/"

  # Verify copy
  if [ $? -eq 0 ]; then
    echo "✅ Credentials copied successfully"
    echo "📑 Copied credentials files:"
    ls -la "$credentials_dest"
  else
    echo "❌ Failed to copy credentials"
    return 1
  fi
}

main() {
  echo "🚀 Starting environment loader..."
  echo "📂 Script directory: $SCRIPT_DIR"
  echo "📁 Repository root: $REPO_ROOT"

  # Handle input path
  local INPUT_PATH="$1"
  local ENV_DIR
  if [ -z "$INPUT_PATH" ]; then
    ENV_DIR="$REPO_ROOT/private-keys/api-test"
    echo "📝 No directory specified, using default: $ENV_DIR"
  else
    ENV_DIR="$REPO_ROOT/$INPUT_PATH"
    echo "📝 Using specified directory: $ENV_DIR"
  fi

  # Check if directory exists
  if [ ! -d "$ENV_DIR" ]; then
    echo "❌ Environment directory not found: $ENV_DIR"
    echo "📁 Repository root contents:"
    ls -la "$REPO_ROOT"
    echo "🔍 Checking for private-keys directory:"
    ls -la "$REPO_ROOT/private-keys" || echo "private-keys directory not found"
    exit 1
  fi

  echo "✅ Found environment directory: $ENV_DIR"
  echo "📑 Found environment files:"
  ls -la "$ENV_DIR"/*.env 2>/dev/null || echo "No .env files found"

  # Process each .env file
  for file in "$ENV_DIR"/*.env; do
    if [ -f "$file" ]; then
      filename=$(basename "$file")

      echo "📄 Loading environment from: $filename"
      while IFS= read -r line || [ -n "$line" ]; do
        # Skip comments and empty lines
        if [[ ! "$line" =~ ^#.*$ ]] && [ ! -z "$line" ]; then
          echo "$line" >> $GITHUB_ENV
          echo "✨ Added variable from $filename"
        fi
      done < "$file"
    fi
  done

  # Copy credentials to test-api server
  local TEST_API_DIR="$REPO_ROOT/workspace/servers/test-api"
  echo "🔄 Setting up credentials for test-api server..."
  copy_credentials "$ENV_DIR" "$TEST_API_DIR"

  echo "✅ Environment and credentials setup complete!"
}

# Run main function with all arguments
main "$@"
