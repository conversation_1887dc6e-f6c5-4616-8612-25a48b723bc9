# 📓 Test locally with act:
#### For regular systems
# #  act workflow_run -W .github/workflows/api-tests.yml --secret-file private-keys/.combined-secrets.env --var ACT=true

#### For MacOS
# #  act workflow_run -W .github/workflows/api-tests.yml --secret-file private-keys/.combined-secrets.env --var ACT=true --container-architecture linux/amd64

name: API Tests

on:
  workflow_run:
    workflows: [Deploy to Google Cloud Run]
    types:
      - completed
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to run tests against"
        type: string
        default: "api-test"
        required: true

concurrency:
  group: deploy-${{ github.base_ref }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  api-tests:
    if: ${{
      vars.ACT == 'true' ||
      github.event.workflow_run.conclusion == 'success'
      }}
    # runs-on: ubuntu-latest
    runs-on: self-hosted
    timeout-minutes: 44
    environment: ${{
      github.base_ref == 'main' && 'production' ||
      github.base_ref == 'stage' && 'staging' ||
      'develop' }}

    env:
      CI: true
      NODE_ENV: ${{
        github.base_ref == 'main' && 'production' ||
        github.base_ref == 'stage' && 'staging' ||
        github.base_ref == 'develop' && 'development' ||
        'development' }}

      ENVIRONMENT: ${{
        github.base_ref == 'main' && 'production' ||
        github.base_ref == 'stage' && 'staging' ||
        'develop' }}

      API_IS_SECURE: 1
      API_HOST: ${{
        github.base_ref == 'stage' && 'api.stage.divinci.app' ||
        github.base_ref == 'main' && 'api.divinci.app' ||
        'api.dev.divinci.app' }}

      API_LIVE_IS_SECURE: 1
      API_LIVE_HOST: ${{
        github.base_ref == 'stage' && 'live.stage.divinci.app' ||
        github.base_ref == 'main' && 'live.divinci.app' ||
        'live.dev.divinci.app' }}

      WEB_CLIENT_IS_SECURE: 1
      WEB_CLIENT_HOST: ${{
        github.base_ref == 'stage' && 'chat.stage.divinci.app' ||
        github.base_ref == 'main' && 'chat.divinci.app' ||
        'chat.dev.divinci.app' }}

      EMBED_CLIENT_IS_SECURE: 1
      EMBED_CLIENT_HOST: ${{
        github.base_ref == 'stage' && 'embed.stage.divinci.app' ||
        github.base_ref == 'main' && 'embed.divinci.app' ||
        'embed.dev.divinci.app' }}

      API_TEST_IS_SECURE: 1
      API_TEST_HOST: localhost:18084
      API_TEST_PORT: 18084
      RAW_CLIENT_IS_SECURE: 1
      RAW_CLIENT_HOST: divinci.app
      WEB_CLIENT_PORT: 8080

      AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}
      AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
      AUTH0_CLIENT_DOMAIN: ${{ secrets.AUTH0_CLIENT_DOMAIN }}
      AUTH0_AUDIENCE: ${{ secrets.AUTH0_AUDIENCE }}

      CF_ACCESS_CLIENT_ID: ${{ secrets.CF_ACCESS_CLIENT_ID }}
      CF_ACCESS_CLIENT_SECRET: ${{ secrets.CF_ACCESS_CLIENT_SECRET }}

    strategy:
      matrix:
        node-version: ["20.x"]

    services:
      redis:
        image: redis
        ports:
          - "127.0.0.1:6379:6379"
        env:
          REDIS_USERNAME: ""
          REDIS_PASSWORD: ""
          REDIS_DOMAIN_HOSTNAME: redis
          REDIS_PORT: 6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      mongodb:
        image: mongo
        ports:
          - 27017:27017
        env:
          MONGO_DOMAIN_HOSTNAME: mongodb
          MONGO_INITDB_ROOT_USERNAME: root
          MONGO_INITDB_ROOT_PASSWORD: example
          MONGO_IS_SRV: 0
          MONGO_INITDB_DATABASE: divinci-local
        options: >-
          --health-cmd "mongosh --eval 'db.runCommand({ ping: 1 })' --username root --password example --authenticationDatabase admin"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Prevent runner cleanup
        run: |
          echo "ACTIONS_RUNNER_HOOK_JOB_STARTED=true" >> $GITHUB_ENV
          echo "ACTIONS_RUNNER_HOOK_JOB_COMPLETED=true" >> $GITHUB_ENV

      - name: Start Redis Container
        run: |
          docker stop redis || true
          docker rm redis || true
          docker run -d --name redis -p 127.0.0.1:6379:6379 redis
          echo "Waiting for Redis to be ready..."
          for i in $(seq 1 30); do
            if docker exec redis redis-cli ping > /dev/null 2>&1; then
              echo "Redis is ready!"
              break
            fi
            if [ $i -eq 30 ]; then
              echo "Redis failed to start"
              exit 1
            fi
            sleep 1
          done

      - name: Start MongoDB Container
        run: |
          docker stop mongo || true
          docker rm mongo || true
          docker run -d --name mongo \  # Changed from mongodb to mongo
            -p 127.0.0.1:27018:27017 \
            -e MONGO_INITDB_ROOT_USERNAME=root \
            -e MONGO_INITDB_ROOT_PASSWORD=example \
            -e MONGO_INITDB_DATABASE=divinci-local \
            -v mongo_data:/data/db \
            --command "mongod --quiet --logpath /dev/null" \
            mongo  # Using mongo image name consistently
          echo "Waiting for MongoDB to be ready..."
          for i in $(seq 1 30); do
            if docker exec mongo mongosh --eval "db.runCommand({ ping: 1 })" --quiet \  # Changed from mongodb to mongo
              --username root --password example --authenticationDatabase admin > /dev/null 2>&1; then
              echo "MongoDB is ready!"
              break
            fi
            if [ $i -eq 30 ]; then
              echo "MongoDB failed to start"
              exit 1
            fi
            sleep 1
          done

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2 # Only fetch the last 2 commits
          token: ${{ secrets.DIVINCI_GH_PAT_3 }}
          submodules: true
          ref: ${{ github.event.pull_request.head.sha }} # Checkout the PR branch
          # Fetch the base branch
          fetch-tags: false
          fetch-branches: |
            +refs/heads/${{ github.base_ref }}:refs/remotes/origin/${{ github.base_ref }}

      # Node.js setup and npm cache
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"
          cache-dependency-path: |
            **/package-lock.json

      # Cache shared resources (models, utils, etc.)
      - name: Cache shared resources
        uses: actions/cache@v3
        id: cache-resources
        with:
          path: |
            workspace/resources/**/dist
            workspace/resources/**/node_modules
          key: ${{ runner.os }}-resources-${{ hashFiles('workspace/resources/**/package-lock.json', 'workspace/resources/**/tsconfig.json') }}
          restore-keys: |
            ${{ runner.os }}-resources-

      # Cache for all node_modules
      - name: Cache node modules
        uses: actions/cache@v3
        id: cache-deps
        with:
          path: |
            **/node_modules
            ~/.npm
            ~/.cache
            !**/temp
          key: ${{ runner.os }}-modules-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**/tsconfig.json') }}
          restore-keys: |
            ${{ runner.os }}-modules-${{ hashFiles('**/package-lock.json') }}-
            ${{ runner.os }}-modules-

      # Cache TypeScript incremental builds
      - name: Cache TypeScript incremental builds
        uses: actions/cache@v3
        id: cache-tsbuildinfo
        with:
          path: |
            **/*.tsbuildinfo
            **/tsconfig.tsbuildinfo
          key: ${{ runner.os }}-tsbuildinfo-${{ hashFiles('**/tsconfig.json') }}
          restore-keys: |
            ${{ runner.os }}-tsbuildinfo-

      # Install dependencies if cache miss
      - name: Install dependencies
        if: steps.cache-deps.outputs.cache-hit != 'true'
        working-directory: workspace/clients/tests
        env:
          NODE_ENV: development
        run: |
          npm install --ignore-scripts
          npm run prepare:ci

      # Cache test client build output
      - name: Cache test client build
        uses: actions/cache@v3
        id: cache-test-client
        with:
          path: |
            workspace/clients/tests/dist
            workspace/clients/tests/build
          key: ${{ runner.os }}-test-client-${{ hashFiles('workspace/clients/tests/package-lock.json', 'workspace/clients/tests/tsconfig.json') }}
          restore-keys: |
            ${{ runner.os }}-test-client-

      # Cache Playwright browsers
      - name: Cache Playwright browsers
        uses: actions/cache@v3
        id: playwright-cache
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-playwright-

      - name: Install Playwright Browsers
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        run: |
          npx playwright install --with-deps chromium

      # Cache environment files
      - name: Cache environment files
        uses: actions/cache@v3
        id: cache-env
        with:
          path: |
            workspace/servers/test-api/env
            workspace/clients/tests/env
          key: ${{ runner.os }}-env-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-env-

      - name: Setup test-api environment
        if: steps.cache-env.outputs.cache-hit != 'true'
        run: |
          mkdir -p workspace/servers/test-api/env/credentials
          chmod -R 755 workspace/servers/test-api/env

      - name: Load environment variables and credentials
        run: |
          chmod +x .github/scripts/load-env.sh
          .github/scripts/load-env.sh private-keys/api-test

      # Cache test-api build
      - name: Cache test-api build
        uses: actions/cache@v3
        id: cache-test-api
        with:
          path: |
            workspace/servers/test-api/dist
            workspace/servers/test-api/node_modules
          key: ${{ runner.os }}-test-api-${{ hashFiles('workspace/servers/test-api/package-lock.json', 'workspace/servers/test-api/tsconfig.json') }}
          restore-keys: |
            ${{ runner.os }}-test-api-

      - name: Install test-api server dependencies
        if: steps.cache-test-api.outputs.cache-hit != 'true'
        working-directory: workspace/servers/test-api
        run: |
          npm install --ignore-scripts
          npm run build

      - name: Start test-api server
        working-directory: workspace/servers/test-api
        env:
          PORT: 18084
          REDIS_DOMAIN_HOSTNAME: 127.0.0.1
          REDIS_PORT: 6379
          REDIS_USERNAME: ""
          REDIS_PASSWORD: ""
          MONGO_DOMAIN_HOSTNAME: 127.0.0.1
          MONGO_PORT: 27017
          MONGO_INITDB_ROOT_USERNAME: root
          MONGO_INITDB_ROOT_PASSWORD: example
          MONGO_DATABASE_NAME: divinci-local
          MONGO_IS_SRV: 0
        run: |
          echo "✨ Starting test-api server..."
          npm run start:dev &
          SERVER_PID=$!

          # Wait for server to start
          echo "⏳ Waiting for test-api server to start... "
          for i in $(seq 1 30); do
            if curl -s http://127.0.0.1:18084/health > /dev/null; then
              echo "Server is ready!"
              break
            fi
            if [ $i -eq 30 ]; then
              echo "Server failed to start"
              exit 1
            fi
            sleep 1
          done

      - name: Run API tests
        working-directory: workspace/clients/tests
        run: npm run start:dev

      - name: Upload Test Artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-artifacts
          path: |
            workspace/clients/tests/test-screenshots/*.png
          retention-days: 5

      - name: Rollback Cloud Run Deployment on Staging Test Failure
        if: failure() && github.event.workflow_run.conclusion == 'success' && contains(github.ref, 'stage')
        env:
          GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
          GOOGLE_CREDENTIALS: ${{ secrets.GOOGLE_CREDENTIALS }}
        run: |
          echo "🔄 Rolling back staging deployment due to test failures..."
          # [Rest of the rollback script]

      # Create PR for environment elevation only if:
      # 1. Tests passed
      # 2. We're on develop branch
      # 3. Not running against staging or main
      - name: Create Environment Elevation PR (Develop to Stage)
        if: |
          success() &&
          github.ref == 'refs/heads/develop'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.DIVINCI_GH_PAT_3 }}
          commit-message: "chore: elevate changes from develop to stage"
          title: "🔼 Elevate changes from develop to stage"
          body: |
            This PR was automatically created after successful API tests in the develop environment.

            🔄 Source branch: `develop`
            🎯 Target branch: `stage`

            ### Changes included:
            - Automated environment elevation
            - All API tests passed successfully in develop environment

            Please review the changes and merge to trigger staging deployment.
          branch: elevation/develop-to-stage
          base: stage
          labels: |
            automated-pr
            environment-elevation
          draft: false

      - name: Report test results
        if: always()
        run: |
          if [ ${{ job.status }} == 'success' ]; then
            echo "✅ API tests passed successfully!"
          else
            echo "❌ API tests failed!"
            exit 1
          fi

      - name: Cleanup Containers and Volumes
        if: always()
        run: |
          docker stop redis mongo || true
          docker rm redis mongo || true
          docker volume rm mongo_data || true
