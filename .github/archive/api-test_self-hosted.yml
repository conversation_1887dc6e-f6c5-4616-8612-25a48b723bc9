# 📓 Test locally with act:
#### For regular systems
# #  act workflow_run -W .github/workflows/api-tests_self-hosted.yml --secret-file private-keys/.combined-secrets.env --var ACT=true

#### For MacOS
# #  act workflow_run -W .github/workflows/api-tests_self-hosted.yml --secret-file private-keys/.combined-secrets.env --var ACT=true --container-architecture linux/amd64

name: API Tests - Self-Hosted

on:
  workflow_run:
    workflows: [Deploy to Google Cloud Run]
    types:
      - completed
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to run tests against"
        type: string
        default: "api-test"
        required: true
      clear_cache:
        description: "Clear all caches before running"
        type: boolean
        default: false
      skip_tests:
        description: "Skip running tests (for debugging)"
        type: boolean
        default: false
      debug_mode:
        description: "Enable extra debugging output"
        type: boolean
        default: false
      custom_branch:
        description: "Specify a branch to test against"
        type: string
        required: false

concurrency:
  group: deploy-${{ github.base_ref }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  api-tests_self-hosted:
    if: ${{
      vars.ACT == 'true' ||
      github.event.workflow_run.conclusion == 'success'
      }}
    runs-on: self-hosted
    timeout-minutes: 342
    environment: ${{
      github.base_ref == 'main' && 'production' ||
      github.base_ref == 'stage' && 'staging' ||
      'develop' }}

    env:
      CI: "1"
      PRIVATE_KEYS_FOLDER: "api-test"
      NODE_ENV: ${{
        github.base_ref == 'main' && 'production' ||
        github.base_ref == 'stage' && 'staging' ||
        github.base_ref == 'develop' && 'development' ||
        'development' }}

      ENVIRONMENT: ${{
        github.base_ref == 'main' && 'production' ||
        github.base_ref == 'stage' && 'staging' ||
        'develop' }}

      API_IS_SECURE: 1
      API_HOST: ${{
        github.base_ref == 'stage' && 'api.stage.divinci.app' ||
        github.base_ref == 'main' && 'api.divinci.app' ||
        'api.dev.divinci.app' }}

      API_LIVE_IS_SECURE: 1
      API_LIVE_HOST: ${{
        github.base_ref == 'stage' && 'live.stage.divinci.app' ||
        github.base_ref == 'main' && 'live.divinci.app' ||
        'live.dev.divinci.app' }}

      WEB_CLIENT_IS_SECURE: 1
      WEB_CLIENT_HOST: ${{
        github.base_ref == 'stage' && 'chat.stage.divinci.app' ||
        github.base_ref == 'main' && 'chat.divinci.app' ||
        'chat.dev.divinci.app' }}

      EMBED_CLIENT_IS_SECURE: 1
      EMBED_CLIENT_HOST: ${{
        github.base_ref == 'stage' && 'embed.stage.divinci.app' ||
        github.base_ref == 'main' && 'embed.divinci.app' ||
        'embed.dev.divinci.app' }}

      AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}
      AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
      AUTH0_CLIENT_DOMAIN: ${{ secrets.AUTH0_CLIENT_DOMAIN }}
      AUTH0_AUDIENCE: ${{ secrets.AUTH0_AUDIENCE }}

      CF_ACCESS_CLIENT_ID: ${{ secrets.CF_ACCESS_CLIENT_ID }}
      CF_ACCESS_CLIENT_SECRET: ${{ secrets.CF_ACCESS_CLIENT_SECRET }}

    strategy:
      matrix:
        node-version: ["20.x"]

    steps:
      # - name: Cleanup previous processes
      # if: always()
      # run: |
      #   echo "Cleaning up previous processes..."
      #   if command -v ss &> /dev/null; then
      #     ss -ltnp | grep ':18080' | awk '{print $6}' | cut -d',' -f2 | xargs -r kill -9
      #   else
      #     echo "ss command not found and netstat is required but missing, installing now..."
      #     apt-get update && apt-get install -y net-tools lsof
      #     netstat -ltnp | grep ':18080' | awk '{print $7}' | cut -d'/' -f1 | xargs -r kill -9
      #   fi
      #   echo "Port 18080 status after cleanup:"
      #   if command -v ss &> /dev/null; then
      #     ss -ltnp | grep ':18080'
      #   else
      #     netstat -ltn
      #   fi
      #

      # - name: Setup job
      #   id: setup
      #   run: |
      #     echo "BRANCH_NAME=${GITHUB_REF##*/}" >> $GITHUB_ENV
      #     echo "PULL_REQUEST_SHA=$GITHUB_SHA" >> $GITHUB_ENV

      # 🔍 Validate deployment target
      - name: Validate Base Ref
        run: |
          if [ "${ACT:-false}" = "true" ]; then
            echo "Running locally with act - setting default base ref to develop"
            echo "BASE_REF=develop" >> $GITHUB_ENV
            echo "ENVIRONMENT=develop" >> $GITHUB_ENV
            echo "✅ Base ref validation skipped for local act run"
            exit 0
          fi

          echo "Validating base ref..."
          echo "github.event.pull_request.base.ref: ${{ github.event.pull_request.base.ref }}"
          echo "github.event.pull_request.base.ref: ${{ github.event.pull_request.base.ref }}"
          echo "ENVIRONMENT: ${{ env.ENVIRONMENT }}"

          BASE_REF="${{ github.event.pull_request.base.ref || github.event.pull_request.base.ref }}"
          echo "Using BASE_REF: $BASE_REF"

          if [[ "$BASE_REF" != "develop" && "$BASE_REF" != "stage" && "$BASE_REF" != "main" ]]; then
            echo "❌ Error: Invalid base ref: $BASE_REF"
            exit 1
          fi
          echo "✅ Base ref validation passed"

      - name: Setup debug logging
        if: inputs.debug_mode
        run: |
          echo "ACTIONS_STEP_DEBUG=true" >> $GITHUB_ENV
          echo "Debug mode enabled"

      - name: Clear caches if requested
        if: ${{ inputs.clear_cache }}
        run: |
          gh extension install actions/gh-actions-cache

          REPO=${{ github.repository }}
          BRANCH=${{ github.ref }}

          cacheKeys=$(gh actions-cache list -R $REPO -B $BRANCH | cut -f 1)

          set +e
          for cacheKey in $cacheKeys
          do
              gh actions-cache delete $cacheKey -R $REPO -B $BRANCH --confirm
          done
          set -e
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Prevent runner cleanup
        run: |
          echo "ACTIONS_RUNNER_HOOK_JOB_STARTED=true" >> $GITHUB_ENV
          echo "ACTIONS_RUNNER_HOOK_JOB_COMPLETED=true" >> $GITHUB_ENV

      - name: Create Docker network
        run: |
          docker network create test-network || true

      - name: Start Redis Container
        run: |
          docker stop redis || true
          docker rm redis || true
          docker run -d --name redis \
            --network test-network \
            -p 6379:6379 \
            redis
          echo "Waiting for Redis to be ready..."
          for i in $(seq 1 30); do
            if docker exec redis redis-cli ping > /dev/null 2>&1; then
              echo "Redis is ready!"
              break
            fi
            if [ $i -eq 30 ]; then
              echo "Redis failed to start"
              exit 1
            fi
            sleep 1
          done

      - name: Start MongoDB Container
        run: |
          docker stop mongo || true
          docker rm mongo || true
          docker run -d --name mongo \
            --network test-network \
            -p 27017:27017 \
            -e MONGO_INITDB_ROOT_USERNAME=root \
            -e MONGO_INITDB_ROOT_PASSWORD=example \
            -e MONGO_INITDB_DATABASE=divinci-local \
            mongo
          echo "Waiting for MongoDB to be ready..."
          for i in $(seq 1 30); do
            if docker exec mongo mongosh --eval "db.runCommand({ ping: 1 })" --quiet \
              --username root --password example --authenticationDatabase admin > /dev/null 2>&1; then
              echo "MongoDB is ready!"
              break
            fi
            if [ $i -eq 30 ]; then
              echo "MongoDB failed to start"
              exit 1
            fi
            sleep 1
          done

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2 # Only fetch the last 2 commits
          token: ${{ secrets.DIVINCI_GH_PAT_3 }}
          submodules: true
          # - **Purpose**: Specifically identifies the commit SHA of the head of a pull request.
          # - **Description**:
          #   - Directly points to the SHA-1 hash of the commit that forms the head of the branch associated with the pull request in review.
          # - **Use Case**: When you need precise identification of the specific commit being tested or compared against within a pull request. Often used when you directly want to check out the exact change set under review.
          ref: ${{ github.event.pull_request.head.sha }} # Checkout the PR branch
          # Fetch the base branch
          fetch-tags: false

      # Node.js setup and npm cache
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"
          cache-dependency-path: |
            **/package-lock.json

      # Cache shared resources (models, utils, etc.)
      - name: Cache shared resources
        uses: actions/cache@v3
        id: cache-resources
        with:
          path: |
            workspace/resources/**/dist
            workspace/resources/**/node_modules
          key: ${{ runner.os }}-resources-${{ hashFiles('workspace/resources/**/package-lock.json', 'workspace/resources/**/tsconfig.json') }}
          restore-keys: |
            ${{ runner.os }}-resources-

      # Cache for all node_modules
      - name: Cache node modules
        uses: actions/cache@v3
        id: cache-deps
        with:
          path: |
            **/node_modules
            ~/.npm
            ~/.cache
            !**/temp
          key: ${{ runner.os }}-modules-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**/tsconfig.json') }}
          restore-keys: |
            ${{ runner.os }}-modules-${{ hashFiles('**/package-lock.json') }}-
            ${{ runner.os }}-modules-

      # Cache TypeScript incremental builds
      - name: Cache TypeScript incremental builds
        uses: actions/cache@v3
        id: cache-tsbuildinfo
        with:
          path: |
            **/*.tsbuildinfo
            **/tsconfig.tsbuildinfo
          key: ${{ runner.os }}-tsbuildinfo-${{ hashFiles('**/tsconfig.json') }}
          restore-keys: |
            ${{ runner.os }}-tsbuildinfo-

      # Install dependencies if cache miss
      - name: Install dependencies
        if: steps.cache-deps.outputs.cache-hit != 'true'
        working-directory: workspace/clients/tests
        env:
          NODE_ENV: development
        run: |
          npm install --ignore-scripts
          npm run prepare

      # Cache test client build output
      - name: Cache test client build
        uses: actions/cache@v3
        id: cache-test-client
        with:
          path: |
            workspace/clients/tests/dist
            workspace/clients/tests/build
          key: ${{ runner.os }}-test-client-${{ hashFiles('workspace/clients/tests/package-lock.json', 'workspace/clients/tests/tsconfig.json') }}
          restore-keys: |
            ${{ runner.os }}-test-client-

      # Cache Playwright browsers
      - name: Cache Playwright browsers
        uses: actions/cache@v3
        id: playwright-cache
        with:
          path: ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-playwright-

      # Only install if not already present
      - name: Install Playwright Browsers
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        run: |
          if ! command -v playwright &> /dev/null; then
            npx playwright install --with-deps chromium
          else
            echo "🎭 Playwright already installed, skipping installation."
          fi

      # Cache environment files
      - name: Cache environment files
        uses: actions/cache@v3
        id: cache-env
        with:
          path: |
            workspace/servers/test-api/env
            workspace/clients/tests/env
          key: ${{ runner.os }}-env-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-env-

      - name: Setup test-api environment
        if: steps.cache-env.outputs.cache-hit != 'true'
        run: |
          mkdir -p workspace/servers/test-api/env/credentials
          chmod -R 755 workspace/servers/test-api/env

      - name: Load environment variables and credentials
        run: |
          chmod +x .github/scripts/load-env.sh
          .github/scripts/load-env.sh private-keys/api-test

      # Cache test-api build
      - name: Cache test-api build
        uses: actions/cache@v3
        id: cache-test-api
        with:
          path: |
            workspace/servers/test-api/dist
            workspace/servers/test-api/node_modules
          key: ${{ runner.os }}-test-api-${{ hashFiles('workspace/servers/test-api/package-lock.json', 'workspace/servers/test-api/tsconfig.json') }}
          restore-keys: |
            ${{ runner.os }}-test-api-

      - name: Install test-api server dependencies
        if: steps.cache-test-api.outputs.cache-hit != 'true'
        working-directory: workspace/servers/test-api
        run: |
          npm install --ignore-scripts
          npm run build

      - name: Prepare log directory
        working-directory: workspace/servers/test-api
        run: |
          mkdir -p logs
          touch logs/app.log

      - name: Start test client
        working-directory: workspace/clients/tests
        run: |
          docker network connect test-network $(hostname) || true

          echo "✨ Starting test client..."
          npm run start:dev

      - name: Upload Test Screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-artifacts
          path: |
            workspace/clients/tests/test-screenshots/*.png
          retention-days: 5

      - name: Rollback Cloud Run Deployment on Staging Test Failure
        if: failure() && github.base_ref == 'stage'
        env:
          GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
          GOOGLE_CREDENTIALS: ${{ secrets.GOOGLE_CREDENTIALS }}
        run: |
          echo "🔄 Rolling back staging deployment due to test failures..."
          # [Rest of the rollback script]

      - name: Report test results
        if: always()
        run: |
          if [ ${{ job.status }} == 'success' ]; then
            echo "✅ API tests passed successfully!"
          else
            echo "❌ API tests failed!"
            exit 1
          fi

      # Conditionally checkout stage branch if api-tests pass
      # Check for needed fixes or modifications
      # - name: Check for Modifications
      #   id: check-modifications
      # if: success() && github.event.pull_request.base.ref == 'develop'
      #   run: |
      #     # Fetch the base branch to ensure it's up to date
      #     git fetch origin stage

      #     # Compare the latest commit on 'stage' with changes on the PR head
      #     if ! git diff origin/stage...${{ github.event.pull_request.head.ref }} --quiet; then
      #       echo "modifications-detected=true" >> $GITHUB_ENV
      #     else
      #       echo "modifications-detected=false" >> $GITHUB_ENV
      #     fi

      # Create and push dynamic branch name if merge is successful without conflicts
      - name: Create and Push Dynamic Branch
        id: dynamic-branch-create
        # if: success() && !env.MERGE_CONFLICT_DETECTED
        # if: steps.check-modifications.outputs.modifications-needed == '1'
        if: success() && github.event.pull_request.base.ref == 'develop'
        run: |
          # Determine the effective branch name
          # Assume original branch name is stored in an environment variable (or available in ref context)
          BRANCH_NAME="${{ github.event.pull_request.head.ref || github.event.workflow_run.head_branch || github.head_ref || github.ref }}"

          # Ensure there's a valid branch reference
          if [ -z "$BRANCH_NAME" ]; then
            echo "Could not determine a valid branch name! Exiting."
            exit 1
          fi

          # Sanitize to match valid Git branch naming conventions
          SAFE_BRANCH_NAME=$(echo "$BRANCH_NAME" | tr -d '[:space:]' | tr / -)
          echo "SAFE_BRANCH_NAME=$SAFE_BRANCH_NAME" >> $GITHUB_ENV

          # Prepare dynamic branch name
          DYNAMIC_BRANCH_NAME="elevated-${SAFE_BRANCH_NAME}-$(date +%Y%m%d%H%M%S)"
          echo "DYNAMIC_BRANCH_NAME=$DYNAMIC_BRANCH_NAME" >> $GITHUB_ENV

      - name: Configure Git User
        if: success() && github.event.pull_request.base.ref == 'develop'
        run: |
          git config --local user.name "GitHub Elevate-Action"
          git config --local user.email "<EMAIL>"
          git remote set-url origin "https://${{ secrets.DIVINCI_GH_PAT_3 }}@github.com/Divinci-AI/server.git"

      - name: Create New Branch from PR Head
        if: success() && github.event.pull_request.base.ref == 'develop'
        run: |
          git checkout -b ${{ env.DYNAMIC_BRANCH_NAME }}
          git push origin ${{ env.DYNAMIC_BRANCH_NAME }}

      - name: Check for Conflicts with Stage
        if: ${{ success() && env.BASE_REF == 'develop' }}
        run: |
          git fetch origin stage
          if ! git merge --no-commit --no-ff origin/stage; then
            echo "MERGE_CONFLICT_DETECTED=true" >> $GITHUB_ENV
          else
            echo "MERGE_CONFLICT_DETECTED=false" >> $GITHUB_ENV
            git merge --abort
          fi
        continue-on-error: true

      - name: Merge Stage into New Branch if Conflicts Detected
        # if: env.MERGE_CONFLICT_DETECTED == 'true' && github.event.pull_request.base.ref == 'develop'
        if: ${{ success() && env.BASE_REF == 'develop' }}
        run: |
          git merge origin/stage -s ours --allow-unrelated-histories
          git push origin ${{ env.DYNAMIC_BRANCH_NAME }} --force

      # Create pull request for elevation post successful test
      - name: Create Pull Request
        id: create-pr
        # if: success() && !env.MERGE_CONFLICT_DETECTED || steps.check-modifications.outputs.modifications-needed == '1'
        # if: steps.check-modifications.outputs.modifications-needed == '1'
        if: ${{ success() && env.BASE_REF == 'develop' }}
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.ELEVATE_PR_PAT }}
          branch: ${{ env.DYNAMIC_BRANCH_NAME }} # ${{ github.event.pull_request.head.ref }}
          base: stage
          commit-message: "✅ Tests passed - ⬆️ Elevating changes from ${{ env.DYNAMIC_BRANCH_NAME }} to stage."
          title: "Elevate changes from ${{ env.DYNAMIC_BRANCH_NAME }} to stage."
          body: "This PR is generated automatically after successful deployment and tests."
          labels:
            automated-pr, elevated-pr

            # Check the outcome of PR creation
      - name: Check PR Creation Outcome
        if: always()
        run: |
          if [ "${{ steps.create-pr.outcome }}" == "failure" ]; then
            echo "PR_CREATION_FAILED=true" >> $GITHUB_ENV
          fi

      # Slack Notification for Manual Review
      # - name: Notify Slack of Manual Resolution Need
      #   if: steps.check-modifications.outputs.modifications-needed == '1' && env.PR_CREATION_FAILED == 'true'
      #   env:
      #     SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      #   run: |
      #     ERR_MSG="${{ steps.capture-pr-errors.outputs.captured-error }}"
      #     if [ -z "$ERR_MSG" ]; then
      #       ERR_MSG="No detailed errors captured. Check logs manually for investigation."
      #     fi

      #     MESSAGE=$(cat <<EOF
      #     {
      #       "text": "🚨 Elevation Action Required: `develop` -> `stage` ",
      #       "blocks": [
      #         {
      #           "type": "section",
      #           "text": {
      #             "type": "mrkdwn",
      #             "text": ":warning: *Conflicts Detected During Pull Request Creation or Errors Encountered!*"
      #           }
      #         },
      #         {
      #           "type": "section",
      #           "text": {
      #             "type": "mrkdwn",
      #             "text": "The automated CI/CD process encountered issues:\n\n*Error Details:* \n\`\`\`${{ env.ERR_MSG }}\`\`\`\n\nPlease review and resolve the conflict in the <https://github.com/${{ github.repository }}/pull/${{ env.SAFE_BRANCH_NAME }}|Pull Request>.\n\nIf resolved, you can manually <https://github.com/${{ github.repository }}/actions|trigger the workflow again>."
      #           }
      #         },
      #         {
      #           "type": "divider"
      #         },
      #         {
      #           "type": "context",
      #           "elements": [
      #             {
      #               "type": "mrkdwn",
      #               "text": "If you have questions, please reach out to the support team (@Mike 😅)."
      #             }
      #           ]
      #         }
      #       ]
      #     }
      #     EOF
      #     )

      #     curl -X POST -H 'Content-type: application/json' --data "$MESSAGE" "$SLACK_WEBHOOK_URL"

      # Enable Pull Request Automerge if all tests pass and base branch is 'develop'
      # - name: Enable Auto-Merge
      #   if: success() && github.event.pull_request.base.ref == 'develop'
      #   uses: peter-evans/enable-pull-request-automerge@v3
      #   with:
      #     token: ${{ secrets.PAT }} # Ensure this is a Personal Access Token with appropriate permissions
      #     pull-request-number: ${{ github.event.pull_request.number }}
      #     merge-method: squash # Options: merge, squash, or rebase

      - name: Cleanup
        if: always()
        run: |
          echo "Cleaning up processes..."
          lsof -i :18080 | awk 'NR!=1 {print $2}' | xargs -r kill -9 || true

          echo "Cleaning up Docker resources..."
          docker stop redis mongo || true
          docker rm redis mongo || true
          docker volume rm mongo_data || true

          echo "Cleaning up Docker network..."
          docker network disconnect test-network $(hostname) || true
          docker network rm test-network || true

          echo "Final port status check:"
          lsof -i :18080 || true

          echo "Final Docker status:"
          docker ps -a
          docker network ls
