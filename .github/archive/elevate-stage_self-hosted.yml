# 📓 Test locally with act:
#### For regular systems
# act workflow_run -W .github/workflows/elevate-stage_self-hosted.yml --secret-file private-keys/.combined-secrets.env --var ACT=true

#### For MacOS
# act workflow_run -W .github/workflows/elevate-stage_self-hosted.yml --secret-file private-keys/.combined-secrets.env --var ACT=true --container-architecture linux/amd64

name: Evelate to Stage - Self-Hosted

on:
  workflow_run:
    workflows: [API Tests - Self-Hosted]
    types:
      - completed

  # Manually trigger this workflow
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to run against"
        type: string
        default: "api-test"
        required: true
      clear_cache:
        description: "Clear all caches before running"
        type: boolean
        default: false
      debug_mode:
        description: "Enable extra debugging output"
        type: boolean
        default: false
      custom_branch:
        description: "Specify a branch to run against"
        type: string
        required: false

concurrency:
  group: deploy-${{ github.base_ref }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  elevate-to-stage:
    if: ${{
      vars.ACT == 'true' ||
      ( github.event.workflow_run.conclusion == 'success' &&
      github.event.workflow_run.head_branch == 'develop' )
      }}
    runs-on: self-hosted
    timeout-minutes: 42
    environment: ${{
      github.base_ref == 'main' && 'production' ||
      github.base_ref == 'stage' && 'staging' ||
      'develop' }}

    env:
      CI: "1"
      ENVIRONMENT: ${{
        github.base_ref == 'main' && 'production' ||
        github.base_ref == 'stage' && 'staging' ||
        'develop' }}

    strategy:
      matrix:
        node-version: ["20.x"]

    steps:
      # Enable debugging if requested
      - name: Setup debug logging
        if: inputs.debug_mode
        run: |
          echo "ACTIONS_STEP_DEBUG=true" >> $GITHUB_ENV
          echo "Debug mode enabled"

      # Clear action caches if requested
      - name: Clear caches if requested
        if: ${{ inputs.clear_cache }}
        run: |
          gh extension install actions/gh-actions-cache

          REPO=${{ github.repository }}
          BRANCH=${{ github.ref }}

          cacheKeys=$(gh actions-cache list -R $REPO -B $BRANCH | cut -f 1)

          set +e
          for cacheKey in $cacheKeys
          do
              gh actions-cache delete $cacheKey -R $REPO -B $BRANCH --confirm
          done
          set -e
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Prevent GitHub Actions runner from automatic cleanup
      - name: Prevent runner cleanup
        run: |
          echo "ACTIONS_RUNNER_HOOK_JOB_STARTED=true" >> $GITHUB_ENV
          echo "ACTIONS_RUNNER_HOOK_JOB_COMPLETED=true" >> $GITHUB_ENV

      # Check out latest code
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          token: ${{ secrets.DIVINCI_GH_PAT_3 }}
          # Pull the specific changes from the PR branch
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-tags: false
          fetch-branches: |
            +refs/heads/${{ github.base_ref }}:refs/remotes/origin/${{ github.base_ref }}
            +refs/heads/stage:refs/remotes/origin/stage

      # Run script or command to check for needed fixes or modifications, similar to autopep8
      # - name: Check for Modifications
      #   id: check-modifications
      #   run: |
      #     # Fetch the base branch to ensure it's up to date
      #     git fetch origin stage

      #     # Compare the latest commit on 'stage' with changes on the PR head
      #     if ! git diff origin/stage...${{ github.event.pull_request.head.ref }} --quiet; then
      #       echo "modifications-detected=true" >> $GITHUB_ENV
      #     else
      #       echo "modifications-detected=false" >> $GITHUB_ENV
      #     fi

      # Attempt to merge with "ours" strategy to resolve conflicts
      # - name: Attempt to Merge with Ours Strategy
      #   id: merge-attempt
      #   run: |
      #     git checkout -B stage origin/stage
      #     # Merge with 'ours' strategy
      #     if ! git merge -s ours ${{ github.event.pull_request.head.sha }}; then
      #       echo "MERGE_CONFLICT_DETECTED=true" >> $GITHUB_ENV
      #     else
      #       # Push changes cautiously; ensure the operation is clearly understood
      #       git push origin stage
      #     fi
      #   continue-on-error: true

      # Create a PR if merge resulted in conflicts
      # - name: Create PR if Merge Conflict Occurred
      #   if: failure() || env.MERGE_CONFLICT_DETECTED == 'true'
      #   run: |
      #     echo "Detected conflicts, creating a PR for manual resolution"
      #     # Use gh CLI to create pull request
      #     gh pr create --base stage --head ${{ github.event.pull_request.head.sha }} \
      #       --title "Resolve Conflicts: PR #${{ inputs.pr_number }}" \
      #       --body "This PR is opened to manually resolve conflicts between your branch and stage."
      #   env:
      #     GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Create and push dynamic branch name if merge is successful without conflicts
      - name: Create and Push Dynamic Branch
        id: dynamic-branch-create
        # if: success() && !env.MERGE_CONFLICT_DETECTED
        run: |
          # Determine the effective branch name
          # Assume original branch name is stored in an environment variable (or available in ref context)
          BRANCH_NAME="${{ github.event.pull_request.head || github.event.workflow_run.head_branch || github.head_ref || github.ref }}"

          # Ensure there's a valid branch reference
          if [ -z "$BRANCH_NAME" ]; then
            echo "Could not determine a valid branch name! Exiting."
            exit 1
          fi

          # Sanitize to match valid Git branch naming conventions
          SAFE_BRANCH_NAME=$(echo "$BRANCH_NAME" | tr -d '[:space:]' | tr / -)
          echo "SAFE_BRANCH_NAME=$SAFE_BRANCH_NAME" >> $GITHUB_ENV

          # Prepare dynamic branch name
          DYNAMIC_BRANCH_NAME="elevated-${SAFE_BRANCH_NAME}-$(date +%Y%m%d%H%M%S)"
          echo "DYNAMIC_BRANCH_NAME=$DYNAMIC_BRANCH_NAME" >> $GITHUB_ENV

      - name: Configure Git User
        run: |
          git config --local user.name "GitHub Elevate-Action"
          git config --local user.email "<EMAIL>"
          git remote set-url origin "https://${{ secrets.DIVINCI_GH_PAT_3 }}@github.com/Divinci-AI/server.git"

      - name: Create Branch for Merge
        run: |
          git checkout -b $DYNAMIC_BRANCH_NAME

          # Fetch and merge from stage, using ours strategy
          git fetch origin stage
          if ! git merge origin/stage -s ours --allow-unrelated-histories; then
            echo "MERGE_CONFLICT_DETECTED=true" >> $GITHUB_ENV
          else
            echo "MERGE_CONFLICT_DETECTED=false" >> $GITHUB_ENV
            git push origin $DYNAMIC_BRANCH_NAME --force
          fi
        env:
          GH_TOKEN: ${{ secrets.DIVINCI_GH_PAT_3 }}
        continue-on-error: true

      # Create pull request for elevation post successful test
      - name: Create Pull Request
        id: create-pr
        # if: success() && !env.MERGE_CONFLICT_DETECTED || steps.check-modifications.outputs.modifications-needed == '1'
        # if: success()
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.ELEVATE_PR_PAT }}
          branch: ${{ env.DYNAMIC_BRANCH_NAME }} # ${{ github.event.pull_request.head.ref }}
          base: stage
          commit-message: "✅ Tests passed - ⬆️ Elevating changes from ${{ env.DYNAMIC_BRANCH_NAME }} to stage."
          title: "Elevate changes from ${{ env.DYNAMIC_BRANCH_NAME }} to stage."
          body: "This PR is generated automatically after successful deployment and tests."
          labels: automated-pr, elevated-pr

      # Capture and log error output if failure occurs
      # - name: Capture Error Output
      #   id: capture-error
      #   if: steps.check-modifications.outputs.modifications-needed == '1'
      #   run: |
      #     if [ -f pr-errors.log ]; then
      #       ERR_MSG=$(cat pr-errors.log)
      #       echo "captured-error=$ERR_MSG" >> $GITHUB_ENV
      #       echo "::set-output name=captured-error::${ERR_MSG}"
      #     else
      #       echo "No error to report"
      #     fi

      # Check the outcome of PR creation
      - name: Check PR Creation Outcome
        if: always()
        run: |
          if [ "${{ steps.create-pr.outcome }}" == "failure" ]; then
            echo "PR_CREATION_FAILED=true" >> $GITHUB_ENV
          fi

      # - name: Get Pull Request Info
      #   run: |
      #     echo "PR_NUMBER from previous workflow: ${{ github.event.inputs.pr_number }}"
      #     echo "PR_NUMBER=${{ github.event.inputs.pr_number }}" >> $GITHUB_ENV

      # Comment on PR with further instructions if PR creation fails
      # Add comment on original PR if there's a need for manual review and merge
      # - name: Comment for Manual Review and Merge
      #   if: env.MERGE_CONFLICT_DETECTED || env.PR_CREATION_FAILED || steps.check-modifications.outputs.modifications-needed == '1'
      #   uses: peter-evans/create-or-update-comment@v4
      #   with:
      #     token: ${{ secrets.GITHUB_TOKEN }}
      #     repository: ${{ github.repository }}
      #     issue-number: ${{ env.SAFE_BRANCH_NAME }}
      #     body: |
      #       :wave: Conflicts detected during PR creation. Please resolve them manually.
      #       PR is here: [PR Link](https://github.com/${{ github.repository }}/pull/${{ env.SAFE_BRANCH_NAME }})
      #   continue-on-error: true

      # Slack Notification for Manual Review
      # - name: Notify Slack of Manual Resolution Need
      #   if: env.MERGE_CONFLICT_DETECTED == 'true' || env.PR_CREATION_FAILED == 'true' || env.ERR_MSG || steps.check-modifications.outputs.modifications-needed == '1'
      #   env:
      #     SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      #   run: |
      #     ERR_MSG="${{ steps.capture-pr-errors.outputs.captured-error }}"
      #     if [ -z "$ERR_MSG" ]; then
      #       ERR_MSG="No detailed errors captured. Check logs manually for investigation."
      #     fi

      #     MESSAGE=$(cat <<EOF
      #     {
      #       "text": "🚨 Elevation Action Required: `develop` -> `stage` ",
      #       "blocks": [
      #         {
      #           "type": "section",
      #           "text": {
      #             "type": "mrkdwn",
      #             "text": ":warning: *Conflicts Detected During Pull Request Creation or Errors Encountered!*"
      #           }
      #         },
      #         {
      #           "type": "section",
      #           "text": {
      #             "type": "mrkdwn",
      #             "text": "The automated CI/CD process encountered issues:\n\n*Error Details:* \n\`\`\`${{ env.ERR_MSG }}\`\`\`\n\nPlease review and resolve the conflict in the <https://github.com/${{ github.repository }}/pull/${{ env.SAFE_BRANCH_NAME }}|Pull Request>.\n\nIf resolved, you can manually <https://github.com/${{ github.repository }}/actions|trigger the workflow again>."
      #           }
      #         },
      #         {
      #           "type": "divider"
      #         },
      #         {
      #           "type": "context",
      #           "elements": [
      #             {
      #               "type": "mrkdwn",
      #               "text": "If you have questions, please reach out to the support team."
      #             }
      #           ]
      #         }
      #       ]
      #     }
      #     EOF
      #     )

      #     curl -X POST -H 'Content-type: application/json' --data "$MESSAGE" "$SLACK_WEBHOOK_URL"
