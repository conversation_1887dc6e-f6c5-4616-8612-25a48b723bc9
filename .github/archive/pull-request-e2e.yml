# name: Playwright E2E Testing

# on:
#   pull_request:
#     branches:
#       - develop
#     paths:
#       - 'workspace/**'
#     # types: [opened]

# env:
#   E2E_URL: https://chat.divinci.app
#   CI: true

# jobs:
#   build:
#     timeout-minutes: 25  # ✏️ We'll increase thise number as needed as the tests/app grows.
#     runs-on: ubuntu-latest
#     steps:
#     - name: Checkout code
#       uses: actions/checkout@v4
#       with:
#         fetch-depth: 0  # fetches all history so git commands can access any commit
#         submodules: true  # checks out submodules
#         token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

#     - name: Execute install script
#       run: |
#         cd workspace
#         chmod +x install.sh
#         ./install.sh ${{ steps.changed_folders.outputs.folders }} ci 2>&1   # Passing 'ci' as an additional argument
#         cd -

#     - name: Set up Docker
#       uses: docker/setup-buildx-action@v3

#     - name: Run E2E Script
#       env:
#         SERVICE_ADDRESS: web-client # localhost
#         E2E_URL: https://chat.divinci.app
#         CI: true
#       run: |
#         ./scripts/e2e-docker-compose.sh ${{ env.SERVICE_ADDRESS }} local
