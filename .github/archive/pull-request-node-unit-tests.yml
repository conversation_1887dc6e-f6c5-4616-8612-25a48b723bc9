# name: Node.js CI

# on:
#   pull_request:
#     branches: [ main, master, develop, stage, production ]
#     paths:
#       - 'workspace/**'

# jobs:
#   build:
#     timeout-minutes: 8  # ✏️ We'll increase thise number as needed as the tests/app grows.
#     runs-on: ubuntu-latest
#     steps:
#     - name: Checkout code
#       uses: actions/checkout@v4
#       with:
#         fetch-depth: 0  # fetches all history so git commands can access any commit
#         submodules: true  # checks out submodules
#         token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

#     - name: Use Node.js 18.x
#       uses: actions/setup-node@v3.5.1
#       with:
#         node-version: 18.x

#     - name: Check for full-test-run label
#       id: check_label
#       env:
#         GH_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}  # Use your new secret here
#       run: |
#         export GH_TOKEN=${{ secrets.PERSONAL_ACCESS_TOKEN }}
#         LABELS=$(gh pr view ${{ github.event.pull_request.number }} --json labels -q '.labels[].name')
#         if [[ $LABELS == *"full-test-run"* ]]; then
#           echo "FULL_TEST_RUN=true" >> $GITHUB_ENV
#         fi    

      # - name: Determine Changed Folders
      #   run: |
      #     chmod +x ./scripts/determine_changed_folders.sh
      #     ./scripts/determine_changed_folders.sh ${{ github.event.before }} ${{ github.event.after }}
        
#     - name: Execute install script
#       run: |
#         cd workspace
#         chmod +x install.sh
#         ./install.sh ${{ steps.changed_folders.outputs.folders }} ci 2>&1   # Passing 'ci' as an additional argument
#         cd -
          
#     - name: Run tests
#       run: |
#         if [[ "${{ env.FULL_TEST_RUN }}" == "true" ]]; then
#           echo "🔄 Running tests in all directories"
#           chmod +x scripts/run-jest-tests.sh
#           scripts/run-jest-tests.sh  # Execute your script to run all tests
#         else
#           IFS=',' read -r -a changed_folders <<< "${{ env.folders }}"
#           # echo "🔍 changed_folders=${changed_folders[@]}"
#           for folder in "${changed_folders[@]}"
#           do
#             echo "📘 Checking $folder for package.json"
#             if [ -e "$folder/package.json" ]; then
#               echo "🔄 Running tests in $folder"
#               cd $folder
#               echo "⬇️ npm install rimraf"
#               npm install rimraf
#               echo "⬇️ npm ci"
#               npm ci
#               echo "⬇️ npm run test"
#               npm run test
#               cd -
#             # else
#               # echo "🙅🏻‍♂️ No package.json in $folder"
#             fi
#           done
#         fi
