#!/bin/bash

# Function to verify repository URL format and accessibility
verify_repository_url() {
    echo "Verifying repository URL format and accessibility..."
    
    if [ -z "${REPO_URL}" ]; then
        echo "❌ ERROR: REPO_URL is not set"
        return 1
    fi
    
    # Check URL format
    if [[ ! "${REPO_URL}" =~ ^https://github.com/.+/.+ ]]; then
        echo "❌ ERROR: Invalid repository URL format: ${REPO_URL}"
        echo "   Expected format: https://github.com/owner/repo"
        return 1
    fi
    
    # Extract owner and repo
    REPO_FULL_NAME=$(echo "${REPO_URL}" | sed 's/.*github.com\///g')
    
    # Check if extraction worked correctly
    if [[ ! "${REPO_FULL_NAME}" =~ .+/.+ ]]; then
        echo "❌ ERROR: Failed to extract owner/repo from URL: ${REPO_URL}"
        echo "   Extracted value: ${REPO_FULL_NAME}"
        echo "   Expected format: owner/repo"
        return 1
    fi
    
    echo "✓ Repository URL format is valid: ${REPO_URL}"
    echo "✓ Extracted owner/repo: ${REPO_FULL_NAME}"
    
    # Check repository accessibility
    http_code=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "Authorization: token ${GITHUB_PAT}" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/repos/${REPO_FULL_NAME}")
    
    if [ "$http_code" = "200" ]; then
        echo "✅ Repository is accessible: ${REPO_FULL_NAME}"
        return 0
    elif [ "$http_code" = "404" ]; then
        echo "❌ ERROR: Repository not found: ${REPO_FULL_NAME}"
        echo "   Please check that:"
        echo "   1. The repository exists"
        echo "   2. The GITHUB_PAT has access to this repository"
        echo "   3. The repository URL is correct: ${REPO_URL}"
        return 1
    elif [ "$http_code" = "401" ] || [ "$http_code" = "403" ]; then
        echo "❌ ERROR: Authentication failed for repository: ${REPO_FULL_NAME}"
        echo "   The GITHUB_PAT token lacks required permissions or is invalid."
        return 1
    else
        echo "⚠️ WARNING: Unexpected response code when checking repository: ${http_code}"
        
        # Handle specific HTTP status codes with helpful messages
        if [ "$http_code" = "301" ] || [ "$http_code" = "302" ]; then
            echo "   REDIRECT detected. This usually means:"
            echo "   1. The repository has been renamed or moved"
            echo "   2. There might be a trailing slash issue in the URL"
            echo "   Trying to follow redirect to get the correct URL..."
            
            # Get the redirect location
            redirect_url=$(curl -s -I -H "Authorization: token ${GITHUB_PAT}" \
                "https://api.github.com/repos/${REPO_FULL_NAME}" | grep -i "location:" | awk '{print $2}' | tr -d '\r')
            
            if [ -n "$redirect_url" ]; then
                echo "   Redirect points to: ${redirect_url}"
                echo "   Please update your REPO_URL environment variable accordingly"
                
                # Extract the new repo path from redirect URL
                new_repo_path=$(echo "$redirect_url" | sed 's|https://api.github.com/repos/||g')
                if [ -n "$new_repo_path" ]; then
                    echo "   Suggested new REPO_URL: https://github.com/${new_repo_path}"
                fi
            fi
            
            # Try with redirect following as a fallback
            echo "   Attempting to follow redirect..."
            redirect_code=$(curl -s -o /dev/null -w "%{http_code}" -L \
                -H "Authorization: token ${GITHUB_PAT}" \
                -H "Accept: application/vnd.github.v3+json" \
                "https://api.github.com/repos/${REPO_FULL_NAME}")
                
            if [ "$redirect_code" = "200" ]; then
                echo "   ✅ Repository is accessible when following redirects"
                echo "   You should update your configuration, but we'll proceed for now"
                return 0
            else
                echo "   ❌ Repository still not accessible after following redirects: ${redirect_code}"
            fi
        elif [ "$http_code" = "307" ] || [ "$http_code" = "308" ]; then
            echo "   TEMPORARY REDIRECT detected. This might be a GitHub API routing issue."
            echo "   Attempting to follow redirect..."
            
            # Try with redirect following
            redirect_code=$(curl -s -o /dev/null -w "%{http_code}" -L \
                -H "Authorization: token ${GITHUB_PAT}" \
                -H "Accept: application/vnd.github.v3+json" \
                "https://api.github.com/repos/${REPO_FULL_NAME}")
                
            if [ "$redirect_code" = "200" ]; then
                echo "   ✅ Repository is accessible when following redirects"
                return 0
            fi
        elif [ "$http_code" = "429" ]; then
            echo "   RATE LIMIT EXCEEDED. GitHub API rate limits have been reached."
            echo "   Wait a few minutes before trying again."
        elif [ "$http_code" = "500" ] || [ "$http_code" = "502" ] || [ "$http_code" = "503" ]; then
            echo "   SERVER ERROR. GitHub API might be experiencing issues."
            echo "   Check https://www.githubstatus.com/ for GitHub status."
        fi
    fi
}

# Function to get a runner token using GitHub API with exponential backoff
get_runner_token() {
    if [ -z "${GITHUB_PAT}" ]; then
        echo "❌ Error: GITHUB_PAT (Personal Access Token) is not set."
        echo "   Required permissions for GITHUB_PAT:"
        echo "   - repo (Full control of private repositories)"
        echo "   - admin:org (if using organization repositories)"
        echo "   - admin:repo_hook (for managing repository webhooks)"
        return 1
    fi

    # Extract owner and repo from REPO_URL
    REPO_FULL_NAME=$(echo "${REPO_URL}" | sed 's/.*github.com\///g')
    echo "🔍 DEBUG: Extracted repo path: ${REPO_FULL_NAME}"
    
    # Implement exponential backoff for API calls
    local max_attempts=5
    local attempt=1
    local wait_time=5
    
    while [ $attempt -le $max_attempts ]; do
        echo "Attempting to get runner token (attempt ${attempt}/${max_attempts})..."
        
        # Call GitHub API to get a new runner token with full response capture
        echo "🔍 DEBUG: Calling GitHub API at: https://api.github.com/repos/${REPO_FULL_NAME}/actions/runners/registration-token"
        
        # Get full response with headers for debugging
        full_response=$(curl -v -X POST \
            -H "Authorization: token ${GITHUB_PAT}" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/${REPO_FULL_NAME}/actions/runners/registration-token" 2>&1)
            
        # Capture just the response body for processing
        response=$(curl -s -X POST \
            -H "Authorization: token ${GITHUB_PAT}" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/${REPO_FULL_NAME}/actions/runners/registration-token")
        
        # Get HTTP status code
        http_code=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
            -H "Authorization: token ${GITHUB_PAT}" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/${REPO_FULL_NAME}/actions/runners/registration-token")
            
        echo "🔍 DEBUG: HTTP Status Code: ${http_code}"
        echo "🔍 DEBUG: Response body: ${response}"
        
        # Check if response contains error message
        error_message=$(echo "${response}" | grep -o '"message": "[^"]*' | cut -d'"' -f4)
        if [ -n "${error_message}" ]; then
            echo "🔍 DEBUG: Error message from GitHub: ${error_message}"
        fi

        # Extract token from response
        token=$(echo "${response}" | grep -o '"token": "[^"]*' | cut -d'"' -f4)

        if [ -n "${token}" ]; then
            echo "✅ Successfully obtained runner token"
            echo "${token}"
            return 0
        fi
        
        echo "❌ Failed to get runner token (attempt ${attempt}/${max_attempts})."
        
        # Check for specific error patterns
        if echo "${response}" | grep -q "Resource not accessible by integration"; then
            echo "   ERROR: The GitHub App or PAT doesn't have access to this repository."
            echo "   Please check that the token has the correct repository access."
        elif echo "${response}" | grep -q "Not Found"; then
            echo "   ERROR: Repository not found or incorrect path: ${REPO_FULL_NAME}"
            echo "   Please verify the REPO_URL is correct: ${REPO_URL}"
            echo "   The repository must exist and the token must have access to it."
        elif echo "${response}" | grep -q "Bad credentials"; then
            echo "   ERROR: Invalid credentials. The GITHUB_PAT token is invalid."
            echo "   Please check that the token is correct and not expired."
        fi
        
        # Provide more specific error messages based on HTTP status code
        if [ "$http_code" = "401" ] || [ "$http_code" = "403" ]; then
            echo "   ERROR: Authentication failed. Token lacks required permissions."
            echo "   Required permissions for GITHUB_PAT:"
            echo "   - repo (Full control of private repositories)"
            echo "   - admin:org (if using organization repositories)"
            echo "   - admin:repo_hook (for managing repository webhooks)"
        elif [ "$http_code" = "404" ]; then
            echo "   ERROR: Repository not found. Check REPO_URL: ${REPO_URL}"
            echo "   Extracted repo path: ${REPO_FULL_NAME}"
        elif [ "$http_code" = "429" ]; then
            echo "   ERROR: Rate limit exceeded. Will retry with backoff."
        fi
        
        # Check if we're using the correct API endpoint based on repo type
        if [[ "${REPO_FULL_NAME}" == *"/"* ]]; then
            # Contains a slash, likely a user/org repo
            echo "   INFO: Using repository-level API endpoint"
        else
            echo "   WARNING: Repository path doesn't contain a slash. Expected format: owner/repo"
            echo "   Current value: ${REPO_FULL_NAME}"
            echo "   Original REPO_URL: ${REPO_URL}"
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            echo "   Maximum attempts reached. Cannot continue."
            echo "   FULL CURL DEBUG OUTPUT:"
            echo "${full_response}"
            return 1
        fi
        
        # Exponential backoff
        sleep $wait_time
        wait_time=$((wait_time * 2))
        ((attempt++))
    done

    return 1
}

# Function to configure runner
configure_runner() {
    local token=$1
    local max_attempts=3
    local attempt=1
    local random_suffix=$(cat /dev/urandom | tr -dc 'a-z0-9' | fold -w 8 | head -n 1)
    local final_name="${RUNNER_NAME}-${random_suffix}"

    while [ $attempt -le $max_attempts ]; do
        echo "Configuring runner (attempt ${attempt}/${max_attempts})..."

        ./config.sh \
            --url "${REPO_URL}" \
            --token "${token}" \
            --name "${final_name}" \
            --work "${RUNNER_WORKDIR}" \
            --labels "${LABELS}" \
            --unattended \
            --replace

        if [ $? -eq 0 ]; then
            echo "✅ Runner configuration successful"
            return 0
        fi

        echo "❌ Configuration attempt ${attempt}/${max_attempts} failed. Waiting before retry..."
        sleep 5
        ((attempt++))
    done

    return 1
}

# Function to handle shutdown with better cleanup
cleanup() {
    echo "Cleaning up..."
    if [ -f "/actions-runner/.credentials" ]; then
        cd /actions-runner
        token=$(get_runner_token)
        if [ $? -eq 0 ]; then
            echo "Removing runner from GitHub..."
            ./config.sh remove --token "${token}"
        else
            echo "Failed to get token for cleanup. Attempting unregistration without token..."
            ./config.sh remove --unattended
        fi
    fi
    exit 0
}

# Trap signals
trap cleanup SIGTERM SIGINT

# Function to verify GitHub API connectivity
verify_github_connectivity() {
    echo "Verifying GitHub API connectivity..."
    
    http_code=$(curl -s -o /dev/null -w "%{http_code}" \
        "https://api.github.com/rate_limit")
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "401" ]; then
        echo "✅ GitHub API is accessible"
        return 0
    else
        echo "❌ ERROR: GitHub API is not accessible (HTTP code: ${http_code})"
        return 1
    fi
}

# Function to verify token permissions
verify_token_permissions() {
    echo "Verifying GitHub token permissions..."
    
    if [ -z "${GITHUB_PAT}" ]; then
        echo "❌ ERROR: GITHUB_PAT is not set"
        return 1
    fi
    
    # Check token validity and permissions
    response=$(curl -s \
        -H "Authorization: token ${GITHUB_PAT}" \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/rate_limit")
    
    if echo "${response}" | grep -q "Bad credentials"; then
        echo "❌ ERROR: Invalid GitHub token"
        return 1
    fi
    
    echo "✅ GitHub token appears valid"
    return 0
}

# Main runner logic with automatic reregistration
main() {
    cd /actions-runner
    
    # Verify GitHub connectivity before starting
    verify_github_connectivity || {
        echo "Initial GitHub API connectivity check failed. Will retry in main loop..."
    }
    
    # Verify repository URL format and accessibility
    verify_repository_url
    repo_check_result=$?
    
    if [ $repo_check_result -eq 1 ]; then
        echo "❌ FATAL: Repository verification failed. Cannot continue."
        echo "   Please fix the repository URL or permissions and restart the runner."
        exit 1
    fi
    
    # Verify token permissions early
    verify_token_permissions
    token_check_result=$?
    
    if [ $token_check_result -eq 1 ]; then
        echo "❌ FATAL: GitHub token permission check failed. Cannot continue."
        echo "   Please fix the token permissions and restart the runner."
        exit 1
    fi
    
    while true; do
        # Check if runner needs configuration
        if [ ! -f ".runner" ] || [ ! -f ".credentials" ]; then
            echo "Runner needs configuration... (attempt $((retry_count+1)) of $max_consecutive_failures)"
            
            # Circuit breaker pattern
            if [ $consecutive_failures -ge $max_consecutive_failures ]; then
                echo "⚠️ Too many consecutive failures ($consecutive_failures). Waiting for 5 minutes before retrying..."
                echo "   Possible issues:"
                echo "   - GITHUB_PAT may not have sufficient permissions"
                echo "   - Repository path may be incorrect"
                echo "   - GitHub API may be unavailable or rate limiting"
                sleep 300
                consecutive_failures=0
                
                # Re-verify token permissions after circuit break
                verify_token_permissions
            fi
            
            token=$(get_runner_token)

            if [ $? -ne 0 ]; then
                ((retry_count++))
                ((consecutive_failures++))
                echo "Failed to get runner token. Retrying in 30 seconds... (attempt ${retry_count})"
                sleep 30
                continue
            fi

            configure_runner "${token}"
            if [ $? -ne 0 ]; then
                ((retry_count++))
                ((consecutive_failures++))
                echo "Runner configuration failed. Retrying in 30 seconds... (attempt ${retry_count})"
                sleep 30
                continue
            fi
            
            # Reset failure counter on success
            consecutive_failures=0
            retry_count=0
        fi

        # Start the runner
        echo "Starting runner..."
        ./run.sh &
        runner_pid=$!

        # Monitor runner process
        wait $runner_pid
        exit_code=$?

        echo "Runner exited with code ${exit_code}"

        # If runner exits with specific error codes, remove configuration and retry
        if [ $exit_code -eq 2 ] || [ $exit_code -eq 3 ]; then
            echo "Runner registration appears invalid. Removing configuration..."
            rm -f .runner .credentials
            sleep 10
        else
            # For other errors, wait before retrying
            sleep 30
        fi
    done
}

# Validate required environment variables
if [ -z "${REPO_URL}" ]; then
    echo "❌ Error: REPO_URL is not set."
    exit 1
fi

if [ -z "${RUNNER_NAME}" ]; then
    echo "❌ Error: RUNNER_NAME is not set."
    exit 1
fi

if [ -z "${GITHUB_PAT}" ]; then
    echo "❌ Error: GITHUB_PAT is not set."
    exit 1
fi

# Start main logic
main
