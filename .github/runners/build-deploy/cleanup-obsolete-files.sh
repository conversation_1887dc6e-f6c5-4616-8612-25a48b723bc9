#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

echo -e "${YELLOW}===============================================${NC}"
echo -e "${YELLOW}   Cleaning Up Obsolete Runner Files         ${NC}"
echo -e "${YELLOW}===============================================${NC}"

# List of obsolete files to move to backup
obsolete_files=(
  "${SCRIPT_DIR}/docker-compose-official.yml"
  "${SCRIPT_DIR}/docker-compose-org.yml"
  "${SCRIPT_DIR}/docker-compose-repo.yml"
  "${SCRIPT_DIR}/start-docker-runners.sh"
  "${SCRIPT_DIR}/start-official-runners.sh"
  "${SCRIPT_DIR}/start-repo-runners.sh"
  "${SCRIPT_DIR}/stop-docker-runners.sh"
  "${SCRIPT_DIR}/test-org-runner.sh"
  "${SCRIPT_DIR}/test-repo-runner.sh"
  "${SCRIPT_DIR}/test-runner.sh"
  "${SCRIPT_DIR}/arm64-runner.sh"
  "${SCRIPT_DIR}/official-runner.sh"
  "${SCRIPT_DIR}/fixed-volume-approach.sh"
  "${SCRIPT_DIR}/simple-docker-run.sh"
  "${SCRIPT_DIR}/test-combined-runner.sh"
  "${SCRIPT_DIR}/test-token-var.sh"
  "${SCRIPT_DIR}/try-all-approaches.sh"
  "${SCRIPT_DIR}/../setup-direct-runner.sh"
  "${SCRIPT_DIR}/../setup-direct-runner-automated.sh"
  "${SCRIPT_DIR}/../setup-direct-runner.debug.sh"
  "${SCRIPT_DIR}/../setup-multiple-direct-runners.sh"
  "${SCRIPT_DIR}/../setup-multiple-runners.sh"
)

# Create backup directory
backup_dir="${SCRIPT_DIR}/obsolete-runner-files-backup"
mkdir -p "$backup_dir"

echo -e "${BLUE}Moving obsolete files to backup directory: $backup_dir${NC}"

# Move files to backup directory
for file in "${obsolete_files[@]}"; do
  if [[ -f "$file" ]]; then
    filename=$(basename "$file")
    echo -e "${BLUE}  Moving: $filename${NC}"
    mv "$file" "$backup_dir/$filename"
  fi
done

echo -e "${GREEN}✅ Cleanup completed!${NC}"
echo -e "${BLUE}All obsolete files have been moved to: $backup_dir${NC}"
echo -e "${YELLOW}Note: These files are preserved for reference, but should no longer be used.${NC}"
echo -e "${YELLOW}Use the ARM64 runner scripts (start-arm64-runners.sh and stop-arm64-runners.sh) instead.${NC}"