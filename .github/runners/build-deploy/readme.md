# Docker-based GitHub Actions Runners

This directory contains scripts and configurations for setting up GitHub Actions runners using Docker containers for the Divinci-AI/server repository.

## Prerequisites

- <PERSON>er and Docker Compose V2 installed on your system
- A GitHub Personal Access Token or a GitHub Actions Runner registration token
- For ARM64 runners (Apple Silicon): macOS running on Apple Silicon (M1/M2/M3)

## Setting Up ARM64 Runners (Apple Silicon)

If you're using macOS on Apple Silicon (M1/M2/M3), use the ARM64-specific setup script:

```bash
./start-arm64-runners.sh --runner-token YOUR_RUNNER_TOKEN
```

If you have a GitHub Personal Access Token instead of a runner token, you can use:

```bash
./start-arm64-runners.sh --token YOUR_GITHUB_PAT
```

The script will automatically generate a runner token using the PAT if needed.

### Command Options

The following options are available:

- `-t, --token TOKEN`: GitHub Personal Access Token (optional if runner-token is provided)
- `-r, --runner-token TOKEN`: Direct runner registration token (optional if token is provided)
- `-c, --count COUNT`: Number of runners to create (default: 4)
- `-h, --help`: Show help message

## How to Get a Runner Token

### Option 1: Generate via GitHub UI
1. Go to your GitHub repository
2. Navigate to Settings > Actions > Runners
3. Click "New self-hosted runner"
4. Find the token in the configuration instructions

### Option 2: Generate via GitHub API with PAT
1. Create a GitHub Personal Access Token with `repo` scope
2. Use the `start-arm64-runners.sh` script with the `--token` option
3. The script will automatically generate a runner token

## Stopping Runners

To stop all ARM64 runners:

```bash
cd /path/to/.github/runners/build-deploy/docker-runners
docker compose -f docker-compose-arm64.yml down
```

## Checking Runner Status

To check the status of your ARM64 runners:

```bash
docker compose -f docker-compose-arm64.yml ps
```

Or simply:

```bash
docker ps | grep github-runner-arm64
```

## Viewing Runner Logs

```bash
docker compose -f docker-compose-arm64.yml logs -f
```

## Troubleshooting

1. **404 Not Found errors**: 
   - Ensure your token is valid and has not expired
   - Make sure you're using a repository-specific runner token

2. **Permission Issues**:
   - Repository-level tokens need `repo` scope
   - Ensure the token has the correct permissions

3. **Runners Not Connecting**:
   - Check the logs with `docker compose -f docker-compose-arm64.yml logs -f`
   - Generate a new token if your current one might have expired

## Important Notes

- Runner tokens typically expire after a short period (usually about an hour)
- Each time you stop and restart the runners, you'll need a new registration token
- The runners are configured with labels: `self-hosted,linux,docker,ARM64,build-deploy`
- The runners automatically have access to the host's Docker daemon, allowing them to run Docker commands