#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if token is provided
if [ -z "$1" ]; then
  echo -e "${RED}Error: Please provide a runner token as argument${NC}"
  echo -e "Usage: $0 <RUNNER_TOKEN>"
  exit 1
fi

TOKEN="$1"
ORG_NAME="Divinci-AI"
REPO_NAME="server"

# Detect architecture
ARCH=$(uname -m)
echo -e "${BLUE}Detected architecture: $ARCH${NC}"

# Set image based on architecture
if [ "$ARCH" = "arm64" ] || [ "$ARCH" = "aarch64" ]; then
  # For Apple Silicon / ARM64
  RUNNER_IMAGE="ghcr.io/actions/actions-runner:latest"
  echo -e "${BLUE}Using ARM64 compatible image: $RUNNER_IMAGE${NC}"
else
  # For Intel / AMD64
  RUNNER_IMAGE="myoung34/github-runner:latest"
  echo -e "${BLUE}Using AMD64 compatible image: $RUNNER_IMAGE${NC}"
fi

# Create a unique container name
CONTAINER_NAME="github-runner-$(date +%s)"

# Clean up any old containers
docker rm -f github-runner &>/dev/null || true

# Create directories
mkdir -p ~/actions-runner/$CONTAINER_NAME

echo -e "${BLUE}Starting a runner with architecture detection...${NC}"
echo -e "${BLUE}Token length: ${#TOKEN}${NC}"

# Start the runner with appropriate configuration
docker run -d --restart always --name $CONTAINER_NAME \
  -e REPO_URL="https://github.com/$ORG_NAME/$REPO_NAME" \
  -e RUNNER_NAME="$CONTAINER_NAME" \
  -e RUNNER_TOKEN="$TOKEN" \
  -e LABELS="self-hosted,linux,docker,X64,build-deploy" \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v ~/actions-runner/$CONTAINER_NAME:/actions-runner \
  $RUNNER_IMAGE

echo
echo -e "${GREEN}Runner started with name: $CONTAINER_NAME${NC}"
echo -e "${BLUE}To see logs:${NC}"
echo -e "${BLUE}docker logs -f $CONTAINER_NAME${NC}"