#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

echo -e "${YELLOW}===============================================${NC}"
echo -e "${YELLOW}   Stopping ARM64 GitHub Actions Runners      ${NC}"
echo -e "${YELLOW}===============================================${NC}"

# Stop all ARM64 runners
echo -e "${BLUE}Stopping all ARM64 GitHub Actions runners...${NC}"
docker compose -f "$SCRIPT_DIR/docker-compose-arm64.yml" down

if [[ $? -eq 0 ]]; then
  echo -e "${GREEN}✅ Successfully stopped all ARM64 GitHub Actions runners!${NC}"
else
  echo -e "${RED}Failed to stop GitHub Actions runners.${NC}"
  exit 1
fi

echo -e "${BLUE}Note: You'll need to generate a new runner token the next time you start the runners.${NC}"