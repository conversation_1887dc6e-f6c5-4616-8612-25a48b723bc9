#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
RUNNER_COUNT=4
GITHUB_TOKEN=""
RUNNER_TOKEN=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --token|-t)
      GITHUB_TOKEN="$2"
      shift
      shift
      ;;
    --runner-token|-r)
      RUNNER_TOKEN="$2"
      shift
      shift
      ;;
    --count|-c)
      RUNNER_COUNT="$2"
      shift
      shift
      ;;
    --help|-h)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  -t, --token TOKEN      GitHub Personal Access Token (optional if runner-token is provided)"
      echo "  -r, --runner-token TOKEN  Direct runner registration token (optional if token is provided)"
      echo "  -c, --count COUNT      Number of runners to create (default: 4)"
      echo "  -h, --help             Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Check if either token is provided
if [[ -z "$GITHUB_TOKEN" && -z "$RUNNER_TOKEN" ]]; then
  echo -e "${YELLOW}No tokens provided. Would you like to:${NC}"
  echo "1. Enter a GitHub Personal Access Token"
  echo "2. Enter a Runner Registration Token directly"
  echo "3. Exit"
  read -p "Enter your choice (1-3): " TOKEN_CHOICE

  case $TOKEN_CHOICE in
    1)
      echo -e "${BLUE}Enter your GitHub Personal Access Token:${NC}"
      read -s GITHUB_TOKEN
      echo
      ;;
    2)
      echo -e "${BLUE}Enter your Runner Registration Token:${NC}"
      read -s RUNNER_TOKEN
      echo
      ;;
    *)
      echo -e "${RED}Exiting.${NC}"
      exit 1
      ;;
  esac
fi

echo -e "${YELLOW}===============================================${NC}"
echo -e "${YELLOW}   ARM64 GitHub Actions Runners Setup      ${NC}"
echo -e "${YELLOW}===============================================${NC}"

# Verify we're on ARM64
ARCH=$(uname -m)
if [ "$ARCH" != "arm64" ] && [ "$ARCH" != "aarch64" ]; then
  echo -e "${RED}This script is intended for ARM64 architecture (Apple Silicon).${NC}"
  echo -e "${RED}Your architecture is: $ARCH${NC}"
  echo -e "${YELLOW}If you want to continue anyway, press Enter, otherwise press Ctrl+C to exit.${NC}"
  read -p ""
fi

# Ensure Docker is running and accessible
echo -e "${BLUE}Checking Docker...${NC}"
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}Docker is not running or not installed. Please start Docker and try again.${NC}"
  exit 1
fi
echo -e "${GREEN}✅ Docker is running and accessible${NC}"

# Check for docker compose
echo -e "${BLUE}Checking Docker Compose...${NC}"
if ! docker compose version > /dev/null 2>&1; then
  echo -e "${RED}Docker Compose V2 is not available. Please ensure you have Docker Compose installed.${NC}"
  echo -e "${YELLOW}You might be using an older version of Docker.${NC}"
  exit 1
fi
echo -e "${GREEN}✅ Docker Compose is available${NC}"

# If we only have a GitHub PAT, generate a runner token
if [[ -z "$RUNNER_TOKEN" && -n "$GITHUB_TOKEN" ]]; then
  echo -e "${BLUE}Generating runner registration token using GitHub PAT...${NC}"
  
  # Call GitHub API to get a registration token for the repository
  RESPONSE=$(curl -s -X POST \
    -H "Authorization: token $GITHUB_TOKEN" \
    -H "Accept: application/vnd.github+json" \
    -H "X-GitHub-Api-Version: 2022-11-28" \
    "https://api.github.com/repos/Divinci-AI/server/actions/runners/registration-token")
  
  # Extract token
  RUNNER_TOKEN=$(echo "$RESPONSE" | grep -o '"token"[[:space:]]*:[[:space:]]*"[^"]*' | cut -d'"' -f4)
  
  if [[ -z "$RUNNER_TOKEN" ]]; then
    echo -e "${RED}Failed to get token. API response:${NC}"
    echo "$RESPONSE"
    exit 1
  fi
  
  echo -e "${GREEN}✅ Successfully generated runner token${NC}"
fi

# Create required directories
echo -e "${BLUE}Creating directories for runner data...${NC}"
mkdir -p "$SCRIPT_DIR/runner-arm64/runner1"
mkdir -p "$SCRIPT_DIR/runner-arm64/runner2"
mkdir -p "$SCRIPT_DIR/runner-arm64/runner3"
mkdir -p "$SCRIPT_DIR/runner-arm64/runner4"

# Create .env file for docker-compose
echo -e "${BLUE}Creating .env file with runner token...${NC}"
cat > "$SCRIPT_DIR/.env" << EOF
RUNNER_TOKEN=${RUNNER_TOKEN}
EOF

# Export variables
export RUNNER_TOKEN

# Debug info
echo -e "${GREEN}✅ Environment file created${NC}"
echo -e "${BLUE}Token length: ${#RUNNER_TOKEN}${NC}"

# Clean up any existing runners
echo -e "${BLUE}Cleaning up any existing runners...${NC}"
docker compose -f "$SCRIPT_DIR/docker-compose-arm64.yml" down 2>/dev/null || true

# Start the runners
echo -e "${BLUE}Starting ${RUNNER_COUNT} ARM64 GitHub Actions runners...${NC}"
docker compose --env-file "$SCRIPT_DIR/.env" -f "$SCRIPT_DIR/docker-compose-arm64.yml" up -d

if [[ $? -eq 0 ]]; then
  echo -e "${GREEN}✅ Successfully started ARM64 GitHub Actions runners!${NC}"
  
  # Show logs automatically
  echo -e "${BLUE}Following runner logs (press Ctrl+C to exit logs without stopping runners):${NC}"
  echo
  docker compose -f "$SCRIPT_DIR/docker-compose-arm64.yml" logs -f
else
  echo -e "${RED}Failed to start GitHub Actions runners.${NC}"
  exit 1
fi