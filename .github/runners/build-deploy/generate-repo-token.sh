#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if token is provided
if [ -z "$1" ]; then
  echo -e "${RED}Error: Please provide a GitHub Personal Access Token${NC}"
  echo -e "Usage: $0 <GITHUB_PAT>"
  exit 1
fi

# GitHub variables
GITHUB_PAT="$1"
ORG_NAME="Divinci-AI"
REPO_NAME="server"

echo -e "${BLUE}Generating a runner token for repository $ORG_NAME/$REPO_NAME${NC}"

# Call GitHub API to get a registration token for the repository
RESPONSE=$(curl -s -X POST \
  -H "Authorization: token $GITHUB_PAT" \
  -H "Accept: application/vnd.github+json" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  "https://api.github.com/repos/$ORG_NAME/$REPO_NAME/actions/runners/registration-token")

# Extract token
TOKEN=$(echo "$RESPONSE" | grep -o '"token"[[:space:]]*:[[:space:]]*"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo -e "${RED}Failed to get token. API response:${NC}"
  echo "$RESPONSE"
  exit 1
fi

echo -e "${GREEN}Successfully generated token:${NC}"
echo "$TOKEN"

echo
echo -e "${BLUE}Use this token with the runner scripts:${NC}"
echo "./simple-docker-run.sh $TOKEN"