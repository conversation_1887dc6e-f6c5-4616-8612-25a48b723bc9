version: '3.8'

services:
  runner-1:
    image: myoung34/github-runner:latest
    platform: linux/arm64
    container_name: github-runner-arm64-1
    restart: always
    environment:
      RUNNER_NAME: "runner-arm64-1"
      REPO_URL: "https://github.com/Divinci-AI/server"
      RUNNER_TOKEN: ${RUNNER_TOKEN}
      RUNNER_WORKDIR: "/tmp/runner/work"
      LABELS: "self-hosted,linux,docker,ARM64,build-deploy"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./runner-arm64/runner1:/tmp/runner/work
      
  runner-2:
    image: myoung34/github-runner:latest
    platform: linux/arm64
    container_name: github-runner-arm64-2
    restart: always
    environment:
      RUNNER_NAME: "runner-arm64-2"
      REPO_URL: "https://github.com/Divinci-AI/server"
      RUNNER_TOKEN: ${RUNNER_TOKEN}
      RUNNER_WORKDIR: "/tmp/runner/work"
      LABELS: "self-hosted,linux,docker,ARM64,build-deploy"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./runner-arm64/runner2:/tmp/runner/work
      
  runner-3:
    image: myoung34/github-runner:latest
    platform: linux/arm64
    container_name: github-runner-arm64-3
    restart: always
    environment:
      RUNNER_NAME: "runner-arm64-3"
      REPO_URL: "https://github.com/Divinci-AI/server"
      RUNNER_TOKEN: ${RUNNER_TOKEN}
      RUNNER_WORKDIR: "/tmp/runner/work"
      LABELS: "self-hosted,linux,docker,ARM64,build-deploy"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./runner-arm64/runner3:/tmp/runner/work
      
  runner-4:
    image: myoung34/github-runner:latest
    platform: linux/arm64
    container_name: github-runner-arm64-4
    restart: always
    environment:
      RUNNER_NAME: "runner-arm64-4"
      REPO_URL: "https://github.com/Divinci-AI/server"
      RUNNER_TOKEN: ${RUNNER_TOKEN}
      RUNNER_WORKDIR: "/tmp/runner/work"
      LABELS: "self-hosted,linux,docker,ARM64,build-deploy"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./runner-arm64/runner4:/tmp/runner/work