# .github/workflows/clear-caches.yml
name: Clear All Caches

on:
  workflow_dispatch:
    inputs:
      cache_pattern:
        description: "Pattern to match cache keys (empty for all)"
        required: false
        type: string
        default: ""
      use_self_hosted:
        description: "Use self-hosted runner"
        required: false
        type: boolean
        default: true

permissions:
  actions: write # Add explicit permissions

jobs:
  clear-caches:
    runs-on: ${{ inputs.use_self_hosted && 'self-hosted' || 'ubuntu-latest' }}
    steps:
      # First clear GitHub Actions caches
      - name: Delete GitHub Actions caches
        run: |
          # Source the GitHub CLI fallback script
          chmod +x .github/scripts/github-cli-fallback.sh
          source .github/scripts/github-cli-fallback.sh
          
          # Try to install the GitHub CLI extension
          if command -v gh &> /dev/null && [ ! -f "$HOME/.gh/extensions/gh-actions-cache" ]; then
            gh extension install actions/gh-actions-cache || echo "Extension installation failed, will use API directly"
          fi
          
          PATTERN="${{ inputs.cache_pattern }}"
          echo "Cache pattern: '${PATTERN:-all}'"
          echo "Running on: ${{ inputs.use_self_hosted && 'self-hosted runner' || 'GitHub-hosted runner' }}"
          
          # Function to list caches using API if extension fails
          list_caches_api() {
            curl -s -H "Authorization: token ${GITHUB_TOKEN}" \
              -H "Accept: application/vnd.github.v3+json" \
              "https://api.github.com/repos/${REPO}/actions/caches" | jq -r '.actions_caches[].id'
          }
          
          # Function to delete cache using API if extension fails
          delete_cache_api() {
            local cache_id="$1"
            curl -s -X DELETE -H "Authorization: token ${GITHUB_TOKEN}" \
              -H "Accept: application/vnd.github.v3+json" \
              "https://api.github.com/repos/${REPO}/actions/caches/${cache_id}"
            return $?
          }
          
          echo "Fetching list of cache keys..."
          if command -v gh &> /dev/null && gh extension list | grep -q "actions/gh-actions-cache"; then
            # Use gh extension if available
            if [ -z "$PATTERN" ]; then
              cacheKeys=$(gh actions-cache list -R $REPO | cut -f 1)
            else
              cacheKeys=$(gh actions-cache list -R $REPO | grep "$PATTERN" | cut -f 1)
            fi
          else
            # Fallback to API
            echo "Using API fallback for cache listing"
            cacheKeys=$(list_caches_api)
            if [ -n "$PATTERN" ]; then
              # Filter by pattern (crude implementation)
              cacheKeys=$(echo "$cacheKeys" | grep "$PATTERN" || echo "")
            fi
          fi

          # Check if we found any caches
          if [ -z "$cacheKeys" ]; then
            echo "No GitHub Actions caches found matching pattern: ${PATTERN:-all}"
          else
            # Count caches to be deleted
            count=$(echo "$cacheKeys" | wc -l)
            echo "Found $count cache(s) to delete"

            echo "Deleting GitHub Actions caches..."
            for cacheKey in $cacheKeys
            do
                echo "Deleting cache key: $cacheKey"
                if command -v gh &> /dev/null && gh extension list | grep -q "actions/gh-actions-cache"; then
                  # Use gh extension if available
                  if gh actions-cache delete $cacheKey -R $REPO --confirm; then
                    echo "✅ Successfully deleted: $cacheKey"
                  else
                    echo "❌ Failed to delete: $cacheKey"
                  fi
                else
                  # Fallback to API
                  if delete_cache_api "$cacheKey"; then
                    echo "✅ Successfully deleted using API: $cacheKey"
                  else
                    echo "❌ Failed to delete using API: $cacheKey"
                  fi
                fi
            done
          fi
        env:
          GH_TOKEN: ${{ secrets.DIVINCI_GH_PAT_3 }}
          REPO: ${{ github.repository }}

      # Then clear Playwright browser cache
      - name: Delete Playwright cache
        run: |
          echo "Clearing Playwright browser cache..."

          # Clear Playwright browsers from root cache
          if [ -d "/root/.cache/ms-playwright" ]; then
            echo "Found Playwright cache directory"
            sudo rm -rf /root/.cache/ms-playwright/*
            echo "✅ Cleared Playwright browser cache"
          else
            echo "No Playwright cache directory found in /root/.cache"
          fi

          # Clear from home directory cache
          if [ -d "$HOME/.cache/ms-playwright" ]; then
            echo "Found Playwright cache in home directory"
            rm -rf "$HOME/.cache/ms-playwright"/*
            echo "✅ Cleared Playwright cache from home directory"
          fi

          # Clear from node_modules more safely
          echo "Cleaning Playwright from node_modules..."
          find . -type d -name "node_modules" -exec sh -c '
            for dir in "$0"/playwright* "$0"/ms-playwright*; do
              if [ -d "$dir" ]; then
                echo "Removing $dir"
                rm -rf "$dir"
              fi
            done
          ' {} \;
          echo "✅ Cleared Playwright from node_modules"

      - name: Report cache clearing status
        run: |
          echo "Cache clearing complete!"
          echo "Verifying Playwright caches:"
          echo "Root cache:"
          ls -la /root/.cache/ms-playwright/ 2>/dev/null || echo "Root Playwright cache is empty/gone"
          echo "Home cache:"
          ls -la "$HOME/.cache/ms-playwright/" 2>/dev/null || echo "Home Playwright cache is empty/gone"
