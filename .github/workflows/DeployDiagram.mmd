sequenceDiagram
    participant Dev as Developer
    participant G<PERSON> as GitHub
    participant Runner as Self-Hosted Runner
    participant Cloud as Google Cloud
    participant Test as Test Infrastructure

    %% Initial PR Creation and Comment Trigger
    Dev->>GH: Create PR to develop
    Dev->>GH: Comment "@github-actions [deploy]"

    %% Workflow Initialization
    GH->>Runner: Trigger Workflow

    %% Check PR Status
    Runner->>GH: Check PR Approval Status

    alt PR Approved
        %% Deployment Process
        Runner->>Cloud: Build & Deploy to dev environment
        Cloud-->>Runner: Deployment Complete

        %% Testing Phase
        Runner->>Test: Start Redis & MongoDB
        Runner->>Test: Run Integration Tests
        Test-->>Runner: Test Results

        alt Tests Pass
            Runner->>GH: Create Elevated PR to stage
            GH-->>Runner: Enable Auto-merge
            Runner->>Dev: Notify Success
        else Tests Fail
            Runner->>Dev: Add Test Failure Comment
        end
    else PR Not Approved
        Runner->>Dev: Request Approval Comment
    end

    %% Cleanup
    Runner->>Runner: Cleanup Test Infrastructure
