# 📓 Build-Deploy GitHub Action Workflow ReadMe

[Build Deploy Mermaid Diagram](./DeployDiagram.mmd)

## Develop Branch PRs (Local => Develop)
1. <PERSON>eloper creates PR against `develop`
2. To trigger deployment:
   - Add `[deploy]` tag in commit message
   - Or leave a comment on a PR against `develop` with: `@github-actions [deploy]`
   - PR must have at least one approval
3. System checks:
   - Validates approval status
   - If not approved:
     - Adds comment requesting approval
     - Stops deployment process
4. If approved:
   - Deploys to dev environment (`*.dev.divinci.app`)
   - Runs integration tests
   - If tests fail:
     - Adds test output as PR comment
     - Deployment remains for debugging
   - If tests pass:
     - Creates new "elevated" PR from `develop` to `stage`
     - Adds `elevated-` prefix to branch name

## Stage Branch PRs (Develop => Stage)
1. Automated PR created with `elevated-` prefix
2. Auto-merge enabled automatically
3. Runs integration tests
4. If tests pass:
   - Auto-merges to `stage`
   - Adds comment on original `develop` PR to merge ASAP
   - Cleans up `elevated-` branch
5. If tests fail:
   - Adds test output as PR comment
   - PR remains open for review

## Production Deployment (Stage => Production)
- Currently manual process
- Not handled in this workflow

## Key Features
- Approval checking before elevation
- Automatic PR creation for stage
- Auto-merge capability
- Branch cleanup
- Test result commenting
- Environment-specific deployments
