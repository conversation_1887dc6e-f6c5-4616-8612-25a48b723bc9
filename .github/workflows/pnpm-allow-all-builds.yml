name: PNPM Allow All Builds

on:
  workflow_call:
    # This workflow can be called by other workflows

jobs:
  allow-all-builds:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.11.0'

      - name: Setup PNPM
        uses: pnpm/action-setup@v3
        with:
          version: 10.11.5

      - name: Configure PNPM to allow all builds
        run: |
          # Set CI environment variable
          export CI=true

          # Create or update .npmrc file
          echo "dangerously-allow-all-builds=true" >> .npmrc

          # No need to run approve-builds command as we're using dangerously-allow-all-builds

          # List approved dependencies
          echo "Approved dependencies:"
          pnpm list --depth=0
