# ----------------------------------------
# 🎯 Comment Actions Handler
# ----------------------------------------
# Purpose: Handles comment-based triggers for deployment workflow
# Trigger: When a comment is created on a PR containing '@github-actions [deploy]'
# Requirements:
#   - Must be a pull request comment
#   - PR must be open
#   - Comment must contain exact trigger phrase
# Output: Triggers main deployment workflow if conditions are met
#
# 📝 Best Practices:
# - Comment Triggered actions are faster to trigger (doesn't require the git commit song and dance)
# - But they can sometimes junk up PR conversations. Github PR comments don't have subthreads so it can get busy in there quickly after a code-review and then if you're trying to trigger builds with comments several times, it becomes hard to find the Code Review comments.
#
# 🔄 Comment Parsing:
# - Handles comments with leading/trailing whitespace and newlines
# - Removes carriage returns for consistent parsing
# - Flexible pattern matching for deployment commands
# ----------------------------------------
#

name: Comment Actions Handler

# Add explicit permissions for GitHub token
permissions:
  contents: read
  pull-requests: write
  issues: write
  actions: write

on:
  issue_comment:
    types: [created]
  workflow_dispatch:
    inputs:
      pr_number:
        description: "PR number to deploy"
        required: true
      base_ref:
        description: "Base branch reference"
        required: true
        default: "develop"
      comment:
        description: "Simulated comment text"
        required: false
        default: "@github-actions [deploy]"

concurrency:
  group: ${{
    format(
      'comment-{0}-{1}-{2}',
      github.event.issue.number || inputs.pr_number,
      startsWith(github.event.comment.body || inputs.comment, '[deploy') && 'deploy-command' || 'other',
      github.actor
    )
    }}
  cancel-in-progress: ${{
    !startsWith(github.event.comment.body || inputs.comment, '[deploy') &&
    github.actor != 'mikeumus' &&
    github.actor != 'formula1' &&
    contains(github.actor, 'bot')
    }}

jobs:
  select-runner:
    runs-on: ubuntu-latest
    outputs:
      runner: ${{ steps.select-runner.outputs.runner }}
    steps:
      - uses: actions/checkout@v4 # Add this to get access to healthy.sh
      - id: select-runner
        uses: ./.github/actions/select-runner
        with:
          github-token: ${{ secrets.DIVINCI_GH_PAT_3 }}
          workflow-type: "comment"
        env:
          GITHUB_PAT: ${{ secrets.DIVINCI_GH_PAT_3 }}
          RUNNER_LABELS: "self-hosted,linux,docker,X64"
          LABELS: "self-hosted,linux,docker,X64"

  # ----------------------------------------
  # 🚀 Deploy Command Handler
  # ----------------------------------------
  # Purpose: Processes the [deploy] command and triggers main workflow
  # Conditions:
  #   - Comment must contain '@github-actions [deploy]'
  #   - Must be on a pull request (not an issue)
  # Actions:
  #   1. Fetches PR details
  #   2. Triggers main deployment workflow
  #   3. Adds status comment to PR
  # ----------------------------------------
  handle-deploy-command:
    needs: select-runner # Add dependency on select-runner job
    runs-on: ${{ needs.select-runner.outputs.runner }} # Use selected runner
    if: |
      (github.event_name == 'issue_comment' &&
       contains(github.event.comment.body, '@github-actions [deploy') &&
       github.event.issue.pull_request) ||
      (github.event_name == 'workflow_dispatch' &&
       contains(inputs.comment, '@github-actions [deploy'))

    # Add environment variables at job level
    env:
      RUNNER_LABELS: "self-hosted,linux,docker,X64"
      LABELS: "self-hosted,linux,docker,X64"

    steps:
      - name: Debug Deploy Command Conditions
        run: |
          # Install xxd if not present
          if ! command -v xxd &> /dev/null; then
            echo "Installing xxd..."
            sudo apt-get update -qq
            sudo apt-get install -qq -y vim-common
          fi

          # Get the comment body, escaping any potential command-like content
          COMMENT="${{ github.event.comment.body }}"
          ESCAPED_COMMENT=$(printf '%s' "$COMMENT" | sed 's/[;&|`$()\\]/\\&/g')

          echo "🔍 Checking deployment conditions:"
          echo "================================"
          echo "Event name: ${{ github.event_name }}"
          echo "Original comment body: $COMMENT"
          echo "Escaped comment body: $ESCAPED_COMMENT"
          echo "Has pull request: ${{ github.event.issue.pull_request != '' }}"
          echo "Workflow dispatch comment: ${{ inputs.comment }}"
          echo "================================"

          # Debug each condition separately
          echo "🔬 Detailed condition checks:"
          echo "1. Is issue_comment? [[ ${{ github.event_name }} == 'issue_comment' ]] => $([[ "${{ github.event_name }}" == "issue_comment" ]] && echo "true" || echo "false")"

          # Clean the comment by removing leading/trailing whitespace and newlines
          CLEANED_COMMENT=$(echo "$COMMENT" | tr -d '\r' | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//')

          # Check if the cleaned comment starts with @github-actions
          if [[ "$CLEANED_COMMENT" =~ ^@github-actions ]]; then
            echo "2. Comment starts with @github-actions [deploy? [[ $CLEANED_COMMENT =~ '@github-actions [deploy' ]] => $([[ "$CLEANED_COMMENT" =~ "@github-actions [deploy" ]] && echo "true" || echo "false")"
          else
            echo "2. Skipping comment pattern check (not a deployment command)"
          fi

          echo "3. Has pull request? [[ ${{ github.event.issue.pull_request }} != '' ]] => $([[ "${{ github.event.issue.pull_request }}" != "" ]] && echo "true" || echo "false")"

          echo "4. Raw comment body for regex check:"
          echo "$COMMENT" | xxd

          # Evaluate conditions with simpler pattern matching
          if [[ "${{ github.event_name }}" == "issue_comment" ]]; then
            echo "✅ Event type matches"

            # Clean the comment by removing leading/trailing whitespace and newlines
            CLEANED_COMMENT=$(echo "$COMMENT" | tr -d '\r' | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//')

            # Only check for deploy command if it's not a branch deletion message
            if [[ "$CLEANED_COMMENT" =~ ^@github-actions ]]; then
              if [[ "$CLEANED_COMMENT" =~ "@github-actions [deploy" ]]; then
                echo "✅ Comment pattern matches"
                if [[ "${{ github.event.issue.pull_request }}" != "" ]]; then
                  echo "✅ Has pull request"
                  echo "✅ All conditions met, proceeding with deployment"
                else
                  echo "❌ No pull request found"
                  exit 1
                fi
              else
                echo "❌ Comment pattern doesn't match"
                exit 1
              fi
            else
              echo "⏩ Skipping deployment (not a deployment command)"
              exit 1
            fi
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "Checking workflow dispatch conditions..."

            # Clean the workflow dispatch comment
            CLEANED_COMMENT=$(echo "${{ inputs.comment }}" | tr -d '\r' | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//')

            if [[ "$CLEANED_COMMENT" =~ "@github-actions [deploy" ]]; then
              echo "✅ All workflow dispatch conditions met"
            else
              echo "❌ Workflow dispatch comment doesn't match pattern"
              exit 1
            fi
          else
            echo "❌ Event type not recognized"
            exit 1
          fi

      # ----------------------------------------
      # 🏃 Runner Health Check
      # ----------------------------------------
      # Purpose: Verifies selected runner availability
      # Output: Confirms whether self-hosted or GitHub runner is being used
      # ----------------------------------------
      - name: Check runner availability
        id: check-runner
        continue-on-error: true
        run: |
          if [ "${{ needs.select-runner.outputs.runner }}" = "self-hosted" ]; then
            echo "Using self-hosted runner"
          else
            echo "Using GitHub-hosted runner"
          fi

      # ----------------------------------------
      # 📊 Runner Status Report
      # ----------------------------------------
      # Purpose: Reports final runner selection status
      # Output: Selected runner and health check outcome
      # ----------------------------------------
      - name: Report runner status
        if: always()
        run: |
          echo "Runner: ${{ needs.select-runner.outputs.runner }}"
          echo "Status: ${{ steps.check-runner.outcome }}"

      # ----------------------------------------
      # 🛡️ Runner Protection
      # ----------------------------------------
      # Purpose: Prevents premature runner cleanup
      # Action: Sets environment flags to maintain runner
      # ----------------------------------------
      - name: Prevent runner cleanup
        run: |
          echo "ACTIONS_RUNNER_HOOK_JOB_STARTED=true" >> $GITHUB_ENV
          echo "ACTIONS_RUNNER_HOOK_JOB_COMPLETED=true" >> $GITHUB_ENV

      # ----------------------------------------
      # 🔍 Checkout Repository (Minimal)
      # ----------------------------------------
      # Purpose: Sets up a git repository for GitHub CLI to work
      # ----------------------------------------
      - name: Checkout Repository (Minimal)
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
          token: ${{ secrets.ELEVATE_PR_PAT }}

      # ----------------------------------------
      # 📝 Fetch Pull Request Details
      # ----------------------------------------
      # Purpose: Retrieves necessary PR information
      # Outputs:
      #   - base: Target branch name
      #   - head: Source branch name
      #   - state: PR state (open/closed)
      #   - number: PR number
      # ----------------------------------------
      - name: Get PR Details
        id: pr-details
        env:
          GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
          GITHUB_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
        run: |
          # Debug GitHub repository name
          echo "GitHub repository: ${{ github.repository }}"

          # Debug incoming event data
          echo "Event name: ${{ github.event_name }}"
          echo "Issue number: ${{ github.event.issue.number }}"
          echo "PR number (input): ${{ inputs.pr_number }}"

          # Get PR number based on event type
          if [[ "${{ github.event_name }}" == "issue_comment" ]]; then
            PR_NUMBER="${{ github.event.issue.number }}"
            echo "Using PR number from issue comment: $PR_NUMBER"
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            PR_NUMBER="${{ inputs.pr_number }}"
            echo "Using PR number from workflow dispatch: $PR_NUMBER"
          else
            echo "Error: Unsupported event type"
            exit 1
          fi

          if [[ -z "$PR_NUMBER" ]]; then
            echo "Error: Could not determine PR number"
            exit 1
          fi

          # Create output file
          GITHUB_OUTPUT="${RUNNER_TEMP}/github_output"
          echo "" > "$GITHUB_OUTPUT"

          echo "Fetching details for PR #${PR_NUMBER}..."

          # Use direct API call - the most reliable method
          echo "Using direct API call with PAT"
          set -x  # Enable debug output

          PR_DATA=$(curl -s -L \
            -H "Authorization: token $GH_TOKEN" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/${{ github.repository }}/pulls/${PR_NUMBER}")

          # Check if we got a valid response
          if [[ $(echo "$PR_DATA" | jq -r '.message') == "Not Found" || $(echo "$PR_DATA" | jq -r '.message') == "Bad credentials" ]]; then
            echo "Error with token authorization. API response:"
            echo "$PR_DATA" | jq '.'
            echo "Trying with Bearer format..."

            PR_DATA=$(curl -s -L \
              -H "Authorization: Bearer $GH_TOKEN" \
              -H "Accept: application/vnd.github.v3+json" \
              "https://api.github.com/repos/${{ github.repository }}/pulls/${PR_NUMBER}")
          fi

          # Transform the API response to match the expected format
          if [[ $(echo "$PR_DATA" | jq -r '.message') != "Not Found" && $(echo "$PR_DATA" | jq -r '.message') != "Bad credentials" ]]; then
            PR_DATA=$(echo "$PR_DATA" | jq '{
              baseRefName: .base.ref,
              headRefName: .head.ref,
              state: .state,
              number: .number,
              title: .title,
              headRepository: {
                name: .head.repo.name,
                owner: {
                  login: .head.repo.owner.login
                }
              }
            }')
          fi

          set +x  # Disable debug output

          if [[ -z "$PR_DATA" || "$PR_DATA" == "{}" ]]; then
            echo "Error: Failed to fetch PR data after multiple attempts"
            exit 1
          fi

          # Extract and validate data
          BASE_REF=$(echo "$PR_DATA" | jq -r '.baseRefName // .base.ref // ""')
          HEAD_REF=$(echo "$PR_DATA" | jq -r '.headRefName // .head.ref // ""')
          PR_STATE=$(echo "$PR_DATA" | jq -r '.state // ""')
          PR_NUM=$(echo "$PR_DATA" | jq -r '.number // ""')
          PR_TITLE=$(echo "$PR_DATA" | jq -r '.title // ""')

          # Debug PR data
          echo "Raw PR data:"
          echo "$PR_DATA" | jq '.'

          echo "Extracted values:"
          echo "Base ref: '$BASE_REF'"
          echo "Head ref: '$HEAD_REF'"
          echo "State: '$PR_STATE'"
          echo "Number: '$PR_NUM'"
          echo "Title: '$PR_TITLE'"

          # Validate required values
          if [[ -z "$HEAD_REF" || "$HEAD_REF" == "null" ]]; then
            echo "Error: Invalid HEAD_REF"
            exit 1
          fi

          if [[ -z "$BASE_REF" || "$BASE_REF" == "null" ]]; then
            echo "Error: Invalid BASE_REF"
            exit 1
          fi

          # Set outputs
          {
            echo "base=${BASE_REF}"
            echo "head=${HEAD_REF}"
            echo "state=${PR_STATE}"
            echo "number=${PR_NUM}"
            echo "title=${PR_TITLE}"
          } >> "$GITHUB_OUTPUT"

          # Verify outputs were written
          echo "Output file contents:"
          cat "$GITHUB_OUTPUT"

          # Set outputs using workflow commands as backup
          echo "::set-output name=base::${BASE_REF}"
          echo "::set-output name=head::${HEAD_REF}"
          echo "::set-output name=state::${PR_STATE}"
          echo "::set-output name=number::${PR_NUM}"
          echo "::set-output name=title::${PR_TITLE}"

      # ----------------------------------------
      # 📦 Repository Setup
      # ----------------------------------------
      # Purpose: Checks out repository code
      # Config:
      #   - Uses PR head branch
      #   - Shallow clone for efficiency
      #   - Uses PAT for authentication
      # ----------------------------------------
      - name: Checkout Repository
        env:
          GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
        run: |
          # Debug Step Inputs
          echo "🔍 PR Details Step Outputs:"
          echo "Head ref: '${{ steps.pr-details.outputs.head }}'"
          echo "Base ref: '${{ steps.pr-details.outputs.base }}'"
          echo "State: '${{ steps.pr-details.outputs.state }}'"
          echo "Number: '${{ steps.pr-details.outputs.number }}'"
          echo "Title: '${{ steps.pr-details.outputs.title }}'"

          # Validate required input
          if [[ -z "${{ steps.pr-details.outputs.head }}" ]]; then
            echo "❌ Error: No head ref provided from pr-details step"
            echo "Recent workflow commands:"
            tail -n 20 "$GITHUB_STEP_SUMMARY" || true
            exit 1
          fi

          # Clean workspace
          rm -rf ./* ./.[!.]* ..?*

          # Initialize new repo
          git init

          # Configure git
          git config --global init.defaultBranch main
          git config --global --add safe.directory "${GITHUB_WORKSPACE}"

          # Set up the remote with token in URL (Method 5)
          git remote add origin "https://x-access-token:${GH_TOKEN}@github.com/${GITHUB_REPOSITORY}"

          HEAD_REF="${{ steps.pr-details.outputs.head }}"
          echo "🔍 Using HEAD_REF: $HEAD_REF"

          echo "📥 Fetching branch..."
          # Use --depth=1 to only fetch the latest commit
          if ! git fetch origin "${HEAD_REF}:refs/remotes/origin/${HEAD_REF}" --depth=1; then
            echo "❌ Error: Failed to fetch branch $HEAD_REF"
            echo "Available remote branches:"
            git ls-remote --heads origin
            exit 1
          fi

          echo "📦 Checking out branch..."
          if ! git checkout -b "${HEAD_REF}" "origin/${HEAD_REF}"; then
            echo "❌ Error: Failed to checkout branch $HEAD_REF"
            echo "Local branches:"
            git branch -a
            exit 1
          fi

          echo "✅ Checkout complete:"
          echo "Current branch: $(git rev-parse --abbrev-ref HEAD)"
          echo "Current commit: $(git rev-parse HEAD)"

      # ----------------------------------------
      # ✅ Branch Verification
      # ----------------------------------------
      # Purpose: Confirms correct branch checkout
      # Checks:
      #   - Current branch matches expected
      #   - Deploy parser script exists
      # Output: Debug information about repository state
      # ----------------------------------------
      - name: Verify Branch
        run: |
          echo "Current branch: $(git rev-parse --abbrev-ref HEAD)"
          echo "Expected branch: ${{ steps.pr-details.outputs.head }}"
          echo "Checking for parser script..."
          ls -la .github/scripts/parse-deploy.sh || echo "Script not found!"

      # ----------------------------------------
      # 🔍 Parse Deployment Command
      # ----------------------------------------
      # Purpose: Parses deployment command flags
      # Outputs:
      #   - is_fast_deploy: Whether fast deploy was requested
      #   - changed_folders: Specific folders to deploy
      #   - target_branch: Target environment branch
      # ----------------------------------------
      - name: Parse Deploy Command
        id: parse-command
        env:
          GITHUB_OUTPUT: ${{ runner.temp }}/github_output
        run: |
          # Create the output file
          touch $GITHUB_OUTPUT

          # Get comment based on event type
          COMMENT="${{
            github.event_name == 'issue_comment' && github.event.comment.body ||
            github.event_name == 'workflow_dispatch' && inputs.comment
          }}"

          # Clean the comment by removing leading/trailing whitespace and newlines
          CLEANED_COMMENT=$(echo "$COMMENT" | tr -d '\r' | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//')

          echo "Original command: $COMMENT"
          echo "Cleaned command: $CLEANED_COMMENT"

          # Make the script executable
          chmod +x .github/scripts/parse-deploy.sh

          # Run the parser with the cleaned comment
          ./.github/scripts/parse-deploy.sh "$CLEANED_COMMENT"

          # Debug output
          echo "Parser outputs:"
          cat $GITHUB_OUTPUT

      # ----------------------------------------
      # ⏳ Deployment Queue Check
      # ----------------------------------------
      # Purpose: Checks for concurrent deployments
      # Action: Notifies if deployment needs to wait
      # Condition: Only runs for comment-triggered deployments
      # Output: Adds queue status comment to PR if needed
      # ----------------------------------------
      - name: Check Concurrent Deployments
        if: github.event_name == 'issue_comment'
        env:
          GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
        run: |
          PR_NUMBER="${{
            github.event_name == 'issue_comment' && github.event.issue.number ||
            github.event_name == 'workflow_dispatch' && inputs.pr_number
          }}"

          RUNNING_DEPLOYMENTS=$(gh run list --json status,name,headBranch,conclusion \
            --jq '.[] | select(.status=="in_progress" and .name=="Deploy to Google Cloud Run")')

          if [ -n "$RUNNING_DEPLOYMENTS" ]; then
            gh pr comment $PR_NUMBER --body "⏳ Deployment queued. Waiting for other deployments to complete..."
          fi

      # ----------------------------------------
      # 🚀 Trigger Main Deployment
      # ----------------------------------------
      # Purpose: Triggers the main deployment workflow
      # Condition: Only runs if PR is open
      # Action: Starts build-deploy-changed-services.yml workflow
      # Parameters:
      #   - PR head branch
      #   - PR number
      #   - Comment trigger flag
      # ----------------------------------------
      - name: Trigger Deployment Workflow
        if: steps.pr-details.outputs.state == 'open'
        env:
          GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
        run: |
          # Get original command
          COMMAND="${{
            github.event_name == 'issue_comment' && github.event.comment.body ||
            github.event_name == 'workflow_dispatch' && inputs.comment
          }}"

          # Build base parameters
          BASE_PARAMS=(
            --repo "${GITHUB_REPOSITORY}"
            -r "${{ steps.pr-details.outputs.head }}"
            -f pr_number="${{ steps.pr-details.outputs.number }}"
            -f triggered_by_comment=true
            -f base_ref="${{ steps.pr-details.outputs.base }}"
            -f is_fast_deploy="${{ steps.parse-command.outputs.is_fast_deploy }}"
            -f changed_folders="${{ steps.parse-command.outputs.changed_folders }}"
            -f comment="$COMMAND"
          )

          echo "Triggering workflow with parameters:"
          printf '%s\n' "${BASE_PARAMS[@]}"

          gh workflow run build-deploy-changed-services.yml "${BASE_PARAMS[@]}"

      # ----------------------------------------
      # 💬 Add Status Comment
      # ----------------------------------------
      # Purpose: Provides feedback about the deployment trigger
      # Responses:
      #   - Success: Links to Actions tab for progress
      #   - Failure: Explains why deployment couldn't start
      # ----------------------------------------
      - name: Add Response Comment
        env:
          GH_TOKEN: ${{ secrets.ELEVATE_PR_PAT }}
        run: |
          PR_NUMBER="${{
            github.event_name == 'issue_comment' && github.event.issue.number ||
            github.event_name == 'workflow_dispatch' && inputs.pr_number
          }}"

          if [ "${{ steps.pr-details.outputs.state }}" == "open" ]; then
            # Determine deployment type without using ^ for capitalization
            if [[ "${{ steps.parse-command.outputs.is_fast_deploy }}" == "true" ]]; then
              DEPLOY_TYPE="Fast deployment"
            else
              DEPLOY_TYPE="Deployment"
            fi

            FOLDERS="${{ steps.parse-command.outputs.changed_folders }}"

            if [ -n "$FOLDERS" ]; then
              FOLDER_MSG="\nTargeting folders: \`$FOLDERS\`"
            else
              FOLDER_MSG=""
            fi

            # Create the comment with proper escaping
            COMMENT="🚀 ${DEPLOY_TYPE} triggered by ${{ github.event_name }}.${FOLDER_MSG}\nCheck the [Actions tab](https://github.com/${{ github.repository }}/actions) for progress."

            # Post the comment
            gh pr comment $PR_NUMBER --body "${COMMENT}"
          else
            echo "❌ Cannot trigger deployment - PR #$PR_NUMBER is not open."
          fi