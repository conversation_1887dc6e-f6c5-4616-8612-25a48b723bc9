name: Test Refactored Workflow

on:
  workflow_dispatch:
    inputs:
      pr_number:
        description: "PR number"
        required: true
        default: "123"
      base_ref:
        description: "Base branch reference"
        required: true
        default: "develop"
      triggered_by_comment:
        description: "Whether triggered by comment"
        required: true
        type: boolean
        default: true
      changed_folders:
        description: "Comma-separated list of folders to deploy"
        required: false
        default: "workspace/servers/public-api"
      is_fast_deploy:
        description: "Whether to run fast deployment"
        required: false
        type: boolean
        default: true
      comment:
        description: "Original deployment command."
        required: false
        default: "@github-actions [deploy:develop:fail-tests]"

env:
  RUNNER_LABELS: "self-hosted,linux,docker,X64"
  LABELS: "self-hosted,linux,docker,X64"
  GCP_SA_KEY: "dummy-key-for-testing"
  CI: true
  BASE_REF: "develop"
  WEB_CLIENT_HOST: "chat.dev.divinci.app"
  ENVIRONMENT: "develop"
  NODE_ENV: "develop"
  ACT: "true"

jobs:
  # This job simulates a self-hosted runner for act
  simulate-self-hosted:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Set up self-hosted runner simulation
        run: |
          echo "Setting up self-hosted runner simulation"
          echo "RUNNER_NAME=self-hosted" >> $GITHUB_ENV
          echo "RUNNER_OS=Linux" >> $GITHUB_ENV
          echo "RUNNER_ARCH=X64" >> $GITHUB_ENV
          echo "RUNNER_LABELS=self-hosted,linux,docker,X64" >> $GITHUB_ENV

      - name: Check Deploy Flag
        id: check-deploy-flag
        run: |
          echo "should_deploy=true" >> $GITHUB_OUTPUT
          echo "is_fast_deploy=true" >> $GITHUB_OUTPUT
          echo "changed_folders=workspace/servers/public-api" >> $GITHUB_OUTPUT
          echo "target_branch=develop" >> $GITHUB_OUTPUT

      - name: Determine Services
        id: determine-services
        run: |
          echo "services=workspace/servers/public-api" >> $GITHUB_OUTPUT
          echo "service_count=1" >> $GITHUB_OUTPUT

      - name: Simulate Service Deployment
        run: |
          echo "🚀 Simulating deployment of workspace/servers/public-api to develop environment"
          echo "✅ Deployment successful!"

      - name: Simulate API Tests
        run: |
          echo "🧪 Simulating API tests for workspace/servers/public-api"
          echo "✅ Tests passed!"

      - name: Cleanup Resources
        run: |
          echo "🧹 Simulating cleanup of resources"
          echo "✅ Cleanup successful!"
