name: Mock Build and Deploy Changed Services

on:
  workflow_dispatch:
    inputs:
      pr_number:
        description: 'PR number'
        required: true
      triggered_by_comment:
        description: 'Whether this was triggered by a comment'
        required: false
        default: 'false'
      base_ref:
        description: 'Base branch'
        required: false
        default: 'develop'
      is_fast_deploy:
        description: 'Whether to use fast deploy'
        required: false
        default: 'false'
      changed_folders:
        description: 'Comma-separated list of folders to deploy'
        required: false
      comment:
        description: 'Original comment that triggered the deployment'
        required: false

jobs:
  mock-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Mock Deployment
        run: |
          echo "🚀 Mock deployment triggered with parameters:"
          echo "PR Number: ${{ github.event.inputs.pr_number }}"
          echo "Triggered by comment: ${{ github.event.inputs.triggered_by_comment }}"
          echo "Base ref: ${{ github.event.inputs.base_ref }}"
          echo "Fast deploy: ${{ github.event.inputs.is_fast_deploy }}"
          echo "Changed folders: ${{ github.event.inputs.changed_folders }}"
          printf "Original comment: %b\\n" "$(echo -e "${{ github.event.inputs.comment }}")"
          echo "✅ Mock deployment completed successfully"
