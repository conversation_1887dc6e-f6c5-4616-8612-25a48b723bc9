#!/bin/bash
# Script to test the new Git authentication method

# Load GitHub token from secrets file
source secrets.env

echo "🔍 Testing new Git authentication method..."

# Create a temporary directory
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"
echo "📁 Working in temporary directory: $TEMP_DIR"

# Configure git
echo "⚙️ Configuring git..."
git config --global credential.helper store
echo "https://github.com:${GITHUB_TOKEN}" > ~/.git-credentials
git config --global init.defaultBranch main

# Initialize a new git repository
echo "🔄 Initializing git repository..."
git init

# Set up the remote without embedding token in URL
echo "🔗 Setting up remote..."
git remote add origin "https://github.com/Divinci-AI/server"

# Try to fetch from the repository
echo "📥 Fetching from repository..."
if git fetch origin; then
  echo "✅ Git fetch successful! The new authentication method works."
else
  echo "❌ Git fetch failed. The new authentication method is not working."
  exit 1
fi

# Try to clone the private-keys repository as a submodule
echo "🔑 Testing submodule authentication..."
export GIT_CONFIG_COUNT=1
export GIT_CONFIG_KEY_0="url.https://github.com/.insteadOf"
export GIT_CONFIG_VALUE_0="**************:"

if git -c credential.helper=store submodule add https://github.com/Divinci-AI/private-keys.git private-keys; then
  echo "✅ Submodule add successful! The new authentication method works for submodules."
else
  echo "❌ Submodule add failed. The new authentication method is not working for submodules."
  exit 1
fi

# Clean up
cd -
rm -rf "$TEMP_DIR"
echo "🧹 Cleaned up temporary directory"

echo "✅ All tests passed! The new Git authentication method works correctly."
