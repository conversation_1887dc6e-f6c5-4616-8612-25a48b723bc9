#!/bin/bash
# Simplified script to test the new workflows with act

echo "🚀 Testing deploy-services-act.yml workflow..."
act workflow_dispatch \
  -W ../../.github/workflows/deploy-services-act.yml \
  -e test-event.json \
  --container-architecture linux/amd64 \
  -P ubuntu-latest=nektos/act-environments-ubuntu:18.04 \
  -s GITHUB_TOKEN=dummy-token \
  -s ELEVATE_PR_PAT=dummy-token \
  -s GCP_SA_KEY=dummy-key

echo "✅ Test completed!"
