#!/bin/bash
# <PERSON>ript to test the final Git authentication methods

# Load GitHub token from secrets file
source secrets.env

echo "🔍 Testing final Git authentication methods..."

# Create a temporary directory
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"
echo "📁 Working in temporary directory: $TEMP_DIR"

# Method 5: Using Git with direct URL embedding
echo "🧪 Method 5: Using Git with direct URL embedding"
mkdir method5
cd method5
git init
if git remote add origin "https://x-access-token:${GITHUB_TOKEN}@github.com/Divinci-AI/server" && git fetch --depth 1 origin; then
  echo "✅ Method 5 successful!"
else
  echo "❌ Method 5 failed."
fi
cd ..
rm -rf method5

# Method 7: Using Git with username:token format
echo "🧪 Method 7: Using Git with username:token format"
mkdir method7
cd method7
git init
if git remote add origin "https://${GITHUB_TOKEN}:<EMAIL>/Divinci-AI/server" && git fetch --depth 1 origin; then
  echo "✅ Method 7 successful!"
else
  echo "❌ Method 7 failed."
fi
cd ..
rm -rf method7

# Test submodule cloning with Method 5
echo "🧪 Testing submodule cloning with Method 5"
mkdir submodule5
cd submodule5
git init
git remote add origin "https://x-access-token:${GITHUB_TOKEN}@github.com/Divinci-AI/server"
if git submodule add "https://x-access-token:${GITHUB_TOKEN}@github.com/Divinci-AI/private-keys.git" private-keys; then
  echo "✅ Submodule cloning with Method 5 successful!"
else
  echo "❌ Submodule cloning with Method 5 failed."
fi
cd ..
rm -rf submodule5

# Test submodule cloning with Method 7
echo "🧪 Testing submodule cloning with Method 7"
mkdir submodule7
cd submodule7
git init
git remote add origin "https://${GITHUB_TOKEN}:<EMAIL>/Divinci-AI/server"
if git submodule add "https://${GITHUB_TOKEN}:<EMAIL>/Divinci-AI/private-keys.git" private-keys; then
  echo "✅ Submodule cloning with Method 7 successful!"
else
  echo "❌ Submodule cloning with Method 7 failed."
fi
cd ..
rm -rf submodule7

# Clean up
cd -
rm -rf "$TEMP_DIR"
echo "🧹 Cleaned up temporary directory"

echo "✅ All tests completed!"
