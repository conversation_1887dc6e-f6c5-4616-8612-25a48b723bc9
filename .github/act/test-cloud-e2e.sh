#!/bin/bash
# Enhanced script to test E2E workflows against cloud environments using act
# Usage: ./test-cloud-e2e.sh [environment] [test_type] [services]

set -e

# Default values
ENVIRONMENT=${1:-develop}
TEST_TYPE=${2:-smoke}
SERVICES=${3:-all}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"

echo -e "${BLUE}🚀 Testing E2E workflow against $ENVIRONMENT environment${NC}"
echo -e "${BLUE}🧪 Test type: $TEST_TYPE${NC}"
echo -e "${BLUE}📦 Services: $SERVICES${NC}"

# Validate environment
case $ENVIRONMENT in
    "develop"|"staging"|"production")
        echo -e "${GREEN}✅ Valid environment: $ENVIRONMENT${NC}"
        ;;
    *)
        echo -e "${RED}❌ Invalid environment: $ENVIRONMENT${NC}"
        echo -e "${YELLOW}Supported environments: develop, staging, production${NC}"
        exit 1
        ;;
esac

# Check if act is installed
if ! command -v act &> /dev/null; then
    echo -e "${RED}❌ Error: act is not installed${NC}"
    echo -e "${YELLOW}Please install act: https://github.com/nektos/act#installation${NC}"
    exit 1
fi

# Determine event file based on test type
case $TEST_TYPE in
    "deploy-e2e")
        EVENT_FILE="deploy-${ENVIRONMENT}-with-e2e.json"
        ;;
    "e2e-only")
        EVENT_FILE="e2e-only-${ENVIRONMENT}.json"
        ;;
    *)
        # Default to deploy with E2E
        EVENT_FILE="deploy-${ENVIRONMENT}-with-e2e.json"
        ;;
esac

EVENT_PATH="$SCRIPT_DIR/events/$EVENT_FILE"

# Check if event file exists
if [ ! -f "$EVENT_PATH" ]; then
    echo -e "${RED}❌ Event file not found: $EVENT_PATH${NC}"
    echo -e "${YELLOW}Available event files:${NC}"
    ls -la "$SCRIPT_DIR/events/"
    exit 1
fi

# Determine secrets file
SECRETS_FILE="$SCRIPT_DIR/secrets.env"
ENV_SECRETS_FILE="$SCRIPT_DIR/secrets/${ENVIRONMENT}.env"

if [ -f "$ENV_SECRETS_FILE" ]; then
    SECRETS_FILE="$ENV_SECRETS_FILE"
    echo -e "${GREEN}📋 Using environment-specific secrets: $ENV_SECRETS_FILE${NC}"
else
    echo -e "${YELLOW}⚠️ Using default secrets file: $SECRETS_FILE${NC}"
fi

# Check if secrets file exists
if [ ! -f "$SECRETS_FILE" ]; then
    echo -e "${RED}❌ Secrets file not found: $SECRETS_FILE${NC}"
    exit 1
fi

# Create temporary event file with custom services if specified
TEMP_EVENT_FILE=""
if [ "$SERVICES" != "all" ]; then
    TEMP_EVENT_FILE=$(mktemp)
    
    # Read the original event file and modify the comment
    jq --arg services "$SERVICES" '.comment.body = "@github-actions [deploy:'"$ENVIRONMENT"':e2e:services=" + $services + "]"' "$EVENT_PATH" > "$TEMP_EVENT_FILE"
    
    echo -e "${BLUE}📝 Created temporary event file with custom services${NC}"
    EVENT_PATH="$TEMP_EVENT_FILE"
fi

# Build act command
ACT_CMD="act issue_comment"
ACT_CMD="$ACT_CMD -e $EVENT_PATH"
ACT_CMD="$ACT_CMD --secret-file $SECRETS_FILE"
ACT_CMD="$ACT_CMD --container-architecture linux/amd64"
ACT_CMD="$ACT_CMD -P ubuntu-latest=catthehacker/ubuntu:act-latest"

# Add environment variables
ACT_CMD="$ACT_CMD --env TARGET_ENVIRONMENT=$ENVIRONMENT"
ACT_CMD="$ACT_CMD --env TEST_ENV=$ENVIRONMENT"
ACT_CMD="$ACT_CMD --env CHANGED_FOLDERS=$SERVICES"

# Add specific job if we only want to test E2E
if [ "$TEST_TYPE" = "e2e-only" ]; then
    ACT_CMD="$ACT_CMD -j api-tests"
fi

# Add bind flag for debugging if requested
if [ "$DEBUG" = "true" ]; then
    ACT_CMD="$ACT_CMD --bind"
    echo -e "${YELLOW}🐛 Debug mode enabled - container will be bound to local directory${NC}"
fi

# Change to repository root
cd "$ROOT_DIR"

echo -e "${GREEN}🏃 Running act command:${NC}"
echo -e "${BLUE}$ACT_CMD${NC}"
echo ""

# Run the command
eval "$ACT_CMD"
ACT_EXIT_CODE=$?

# Cleanup temporary files
if [ -n "$TEMP_EVENT_FILE" ] && [ -f "$TEMP_EVENT_FILE" ]; then
    rm "$TEMP_EVENT_FILE"
    echo -e "${GREEN}🧹 Cleaned up temporary event file${NC}"
fi

# Report results
if [ $ACT_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ Act workflow completed successfully!${NC}"
    echo -e "${GREEN}🎉 E2E tests against $ENVIRONMENT environment passed${NC}"
else
    echo -e "${RED}❌ Act workflow failed (exit code: $ACT_EXIT_CODE)${NC}"
    echo -e "${RED}💥 E2E tests against $ENVIRONMENT environment failed${NC}"
fi

echo -e "${BLUE}📊 Test Summary:${NC}"
echo -e "Environment: $ENVIRONMENT"
echo -e "Test Type: $TEST_TYPE"
echo -e "Services: $SERVICES"
echo -e "Exit Code: $ACT_EXIT_CODE"
echo -e "Timestamp: $(date)"

exit $ACT_EXIT_CODE
