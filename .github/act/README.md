# Act Test Files

This directory contains files for testing GitHub Actions workflows locally using [act](https://github.com/nektos/act).

## Directory Structure

- `events/`: Sample event payloads for different GitHub events
  - `pull_request_event.json`: Sample event for pull request triggers
  - `deploy-comment-event.json`: Sample event for deploy command issue comments
  - `workflow-dispatch-event.json`: Sample event for workflow dispatch triggers
- `secrets.env`: Environment variables for testing (contains placeholder tokens)
- `test-deploy-command.sh`: Helper script to test the deploy command workflow

## Usage

To test a workflow locally:

```bash
# Using the helper script
./test-deploy-command.sh --job handle-deploy-command
./test-deploy-command.sh --event workflow_dispatch --file events/workflow-dispatch-event.json

# Or directly with act
act issue_comment -e events/deploy-comment-event.json --secret-file secrets.env --container-architecture linux/amd64 -P ubuntu-latest=catthehacker/ubuntu:act-latest

# Test a specific job
act issue_comment -e events/deploy-comment-event.json --secret-file secrets.env --container-architecture linux/amd64 -j handle-deploy-command -P ubuntu-latest=catthehacker/ubuntu:act-latest
```

## Notes

- Replace the placeholder tokens in `secrets.env` with real tokens when testing
- You may need to modify the event payloads to match your specific testing needs
- For security, do not commit real tokens to this repository
