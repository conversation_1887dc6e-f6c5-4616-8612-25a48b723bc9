name: "Set Environment Variables"
description: "Sets environment variables based on base ref"
author: "Divinci Team"
branding:
  icon: "settings"
  color: "blue"
inputs:
  base_ref:
    description: "Base reference branch"
    required: true
outputs:
  environment:
    description: "The resolved environment (develop, staging, production)"
    value: ${{ steps.set-env.outputs.environment }}
  web_client_host:
    description: "The resolved web client host"
    value: ${{ env.WEB_CLIENT_HOST }}
  api_host:
    description: "The resolved API host"
    value: ${{ env.API_HOST }}
runs:
  using: "composite"
  steps:
    - id: validate-input
      shell: bash
      run: |
        # Initial validation of base_ref
        if [ -z "${{ inputs.base_ref }}" ]; then
          echo "❌ Error: base_ref input is empty"
          exit 1
        fi
        echo "✅ Base ref validation passed: ${{ inputs.base_ref }}"

    - id: set-env
      shell: bash
      run: |
        # Set environment variables based on base_ref
        case "${{ inputs.base_ref }}" in
          "stage")
            echo "WEB_CLIENT_HOST=chat.stage.divinci.app" >> $GITHUB_ENV
            echo "API_HOST=api.stage.divinci.app" >> $GITHUB_ENV
            echo "API_LIVE_HOST=live.stage.divinci.app" >> $GITHUB_ENV
            echo "EMBED_CLIENT_HOST=embed.stage.divinci.app" >> $GITHUB_ENV
            echo "ENVIRONMENT=staging" >> $GITHUB_ENV
            echo "environment=staging" >> $GITHUB_OUTPUT
            ;;
          "develop")
            echo "WEB_CLIENT_HOST=chat.dev.divinci.app" >> $GITHUB_ENV
            echo "API_HOST=api.dev.divinci.app" >> $GITHUB_ENV
            echo "API_LIVE_HOST=live.dev.divinci.app" >> $GITHUB_ENV
            echo "EMBED_CLIENT_HOST=embed.dev.divinci.app" >> $GITHUB_ENV
            echo "ENVIRONMENT=develop" >> $GITHUB_ENV
            echo "environment=develop" >> $GITHUB_OUTPUT
            ;;
          "main"|"master")
            echo "WEB_CLIENT_HOST=chat.divinci.app" >> $GITHUB_ENV
            echo "API_HOST=api.divinci.app" >> $GITHUB_ENV
            echo "API_LIVE_HOST=live.divinci.app" >> $GITHUB_ENV
            echo "EMBED_CLIENT_HOST=embed.divinci.app" >> $GITHUB_ENV
            echo "ENVIRONMENT=production" >> $GITHUB_ENV
            echo "environment=production" >> $GITHUB_OUTPUT
            ;;
          *)
            echo "WEB_CLIENT_HOST=chat.dev.divinci.app" >> $GITHUB_ENV
            echo "API_HOST=api.dev.divinci.app" >> $GITHUB_ENV
            echo "API_LIVE_HOST=live.dev.divinci.app" >> $GITHUB_ENV
            echo "EMBED_CLIENT_HOST=embed.dev.divinci.app" >> $GITHUB_ENV
            echo "ENVIRONMENT=develop" >> $GITHUB_ENV
            echo "environment=develop" >> $GITHUB_OUTPUT
            ;;
        esac

        # Set NODE_ENV
        echo "NODE_ENV=${{ env.ENVIRONMENT }}" >> $GITHUB_ENV

        echo "✅ Environment variables set successfully"

    - id: validate-env
      shell: bash
      run: |
        echo "🔍 Validating environment configuration..."

        # Check required variables
        for var in "WEB_CLIENT_HOST" "API_HOST" "ENVIRONMENT"; do
          if [ -z "${!var}" ]; then
            echo "❌ Error: Required variable $var is not set"
            exit 1
          fi
          echo "✅ $var is set: ${!var}"
        done

        # Validate environment-specific values
        case "${{ env.ENVIRONMENT }}" in
          "production")
            if [ "${{ env.WEB_CLIENT_HOST }}" != "chat.divinci.app" ]; then
              echo "❌ Error: Invalid WEB_CLIENT_HOST for production"
              exit 1
            fi
            ;;
          "staging")
            if [ "${{ env.WEB_CLIENT_HOST }}" != "chat.stage.divinci.app" ]; then
              echo "❌ Error: Invalid WEB_CLIENT_HOST for staging"
              exit 1
            fi
            ;;
          "develop")
            if [ "${{ env.WEB_CLIENT_HOST }}" != "chat.dev.divinci.app" ]; then
              echo "❌ Error: Invalid WEB_CLIENT_HOST for develop"
              exit 1
            fi
            ;;
        esac

        echo "✅ All environment validations passed"
