apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: {{CONTAINER_NAME}}
  namespace: "150038457816"
  labels:
    cloud.googleapis.com/location: us-central1
  annotations:
    serving.knative.dev/creator: <EMAIL>
    run.googleapis.com/launch-stage: {{LAUNCH_STAGE}}
    run.googleapis.com/minScale: {{MIN_SCALE}}
    run.googleapis.com/ingress: internal-and-cloud-load-balancing
    run.googleapis.com/ingress-status: internal-and-cloud-load-balancing
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: {{MIN_SCALE}}
        autoscaling.knative.dev/maxScale: {{MAX_SCALE}}
        run.googleapis.com/vpc-access-connector: projects/openai-api-4375643/locations/us-central1/connectors/divinci-serverless-vpc-1
    spec:
      containerConcurrency: 100
      timeoutSeconds: 3600
      containers:
        - name: {{CONTAINER_NAME}}
          image: us-docker.pkg.dev/openai-api-4375643/gcr.io/{{CONTAINER_NAME}}:{{TAG}}
          ports:
            - name: http1
              containerPort: {{PORT}}
          env: {{ENV_VARS}}
          resources:
            limits:
              cpu: {{CPU_LIMIT}}
              memory: {{MEMORY_LIMIT}}
          volumeMounts:
            - name: server-crt
              mountPath: /etc/ssl/certs
            - name: server-key
              mountPath: /etc/ssl/private
            - name: ca-crt
              mountPath: /etc/ssl/ca
              subPath: ca.crt
            # Client certificate is not needed for server-only TLS
            # - name: client-crt
            #   mountPath: /etc/ssl/client
          startupProbe:
            timeoutSeconds: 240
            periodSeconds: 240
            failureThreshold: 3
            tcpSocket:
              port: {{PORT}}
      volumes:
        - name: server-crt
          secret:
            secretName: server-crt
            items:
              - key: latest
                path: server.crt
        - name: server-key
          secret:
            secretName: server-key
            items:
              - key: latest
                path: server.key
        - name: ca-crt
          secret:
            secretName: ca-crt
            items:
              - key: latest
                path: ca.crt
        # Client certificate is not needed for server-only TLS
        # - name: client-crt
        #   secret:
        #     secretName: client-crt
        #     items:
        #       - key: latest
        #         path: client.crt
  traffic:
    - percent: 100
      latestRevision: true
