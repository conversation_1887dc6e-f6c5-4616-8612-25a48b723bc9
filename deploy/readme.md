

# Deploy Scripts

- `./local-mimic-act.sh` - This is supposed to effectively copy the `/.github/workflows/build-deploy-web-client-develop.yml`
Its noteworthy that some steps from the workflow do not happen in this file. But those steps don't seem to be required.
Another point is that `gcloud` requires authenticatiion. `gcloud auth configure-docker us-docker.pkg.dev` should be enough for the steps using `gcloud` to work fine
- `./steps/` - The Individual steps are in the steps folder.
  - step 1 is expecting 2 arguments
    - `$1` - Before Commit - This can be a hash or something like `HEAD^` or `HEAD~4`
    - `$2` - After Commit - This can be a hash or something like `HEAD^` or `HEAD~4`
  - step 2 and 3 are expecting 2 arguments and will should without them (if they don't that needs fixing)
    - `$1` Changed Folders - A comma seperated paths to the service. You can see examples in `config.json` with the folder param
    - `$2` tag - this is expected to be one of `develop`, `stage` or `prod`
  - step 4 cleans up deployed services that are not in the config.json
    - Currently it isn't in the `./local-mimic-act.sh`
    - It exits after deleting one service so that we don't mess up the whole system
- `./docker/ci/` - contains docker related files
  - `./docker/ci/resources.ci.Dockerfile` is a prefix to all the individual service docker files
    - It copies the resource folder and runs pnpm inside each folder
    - It probably can be used for the web-client although that folder doesn't need it yet
  - `./docker/ci/api-basic.ci.Dockerfile`
    - This file takes in a few args `DESCRIPTION`, `APP_FOLDER`, `ENVIRONMENT` and `PORT`
    - `APP_FOLDER` specifies which folder our app is from. Example `/workspace/servers/public-api`
    - `ENVIRONMENT` specifies which folder our env comes from. Example `/private-keys/develop`
    - `PORT` specifies the port the server should listen on, arguable this can always be port 80 or 8080
    - `DESCRIPTION` isn't very important and can be ignored
