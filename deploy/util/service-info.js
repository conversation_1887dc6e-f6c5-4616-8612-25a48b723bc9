#!/usr/bin/env node

/* eslint-disable no-console */


/**
 * <PERSON><PERSON><PERSON> to retrieve service configuration information from config.json for local builds and deployments.
 * This script replaces the Bash script version, providing more efficient JSON parsing and portability using Node.js.
 *
 * Usage:
 *  - Import in other Node.js scripts:
 *    const { getServiceInfoByFolder, getConfigGlobal, ... } = require('./service-info');
 *
 *  - Run directly from the command line:
 *    node deploy/util/service-info.js <command> <args>
 *
 * Commands:
 *  - getServiceInfoByFolder <folder> <environment>:
 *      Returns the container name, Docker file, port, and priority for the given folder and environment.
 *  - getDescriptionByFolder <folder>:
 *      Returns the description for the specified folder.
 *  - getServiceInfoByName <containerName>:
 *      Returns the base container name, Docker file, port, and priority for the given container name.
 *  - getConfigGlobal <globalName>:
 *      Returns the global configuration value for the specified key.
 *  - getMaximumPriority:
 *      Returns the maximum priority value among all services.
 *  - isCurrentPriority <folder> <priority>:
 *      Checks if the given folder has the specified priority.
 *
 * Example:
 *  - To get service info by folder:
 *    node deploy/util/service-info.js getServiceInfoByFolder workspace/folder-name develop
 *
 * Environment:
 *  - Expects `config.json` to be located in the "deploy/" directory relative to this script.
 *  - `config.json` should contain a "services" object with keys for each service, including `folder`, `docker_file`, `port`, `priority`, and `description` attributes.
 *  - Additionally, `config.json` should contain a "globals" object with global configuration values.
 *
 * Exported Functions:
 *  - getServiceInfoByFolder(folder, environment): Returns an object containing container name, Docker file, port, and priority.
 *  - getDescriptionByFolder(folder): Returns the description for the specified folder.
 *  - getServiceInfoByName(containerName): Returns an object containing container name, Docker file, port, and priority.
 *  - getConfigGlobal(globalName): Returns a global configuration value.
 *  - getMaximumPriority(): Returns the maximum priority among all services.
 *  - isCurrentPriority(folder, priority): Checks if the specified folder has the given priority.
 *
 * Dependencies:
 *  - Node.js
 *  - `fs` and `path` modules (built-in to Node.js)
 *
 * Notes:
 *  - This script eliminates the need for `jq` and can be used within other Node.js scripts or run directly from the command line.
 *  - Ensure this script has execute permissions (`chmod +x service-info.js`) if you want to run it directly.
 */

const fs = require("fs");
const { get } = require("http");
const path = require("path");

// Paths to the config.json file
const SCRIPT_DIR = __dirname;
const ROOT_DIR = path.join(SCRIPT_DIR, "../..");
const CONFIG_JSON_PATH = path.join(ROOT_DIR, "deploy/config.json");

// Read and parse config.json
const configJson = JSON.parse(fs.readFileSync(CONFIG_JSON_PATH, "utf8"));
const services = configJson.services;
const globalConfig = configJson.globals;

// Extract values into arrays
const SERVICE_CONTAINER_NAMES = Object.keys(services);
const SERVICE_CONTAINER_FOLDERS = SERVICE_CONTAINER_NAMES.map(
  (name)=>services[name].folder,
);
const SERVICE_CONTAINER_PUBLIC = SERVICE_CONTAINER_NAMES.map(
  (name)=>!!services[name].public,
);
const SERVICE_CONTAINER_DOCKER_FILES = SERVICE_CONTAINER_NAMES.map(
  (name)=>services[name].docker_file,
);
const SERVICE_CONTAINER_PORTS = SERVICE_CONTAINER_NAMES.map(
  (name)=>services[name].port,
);
const SERVICE_CONTAINER_PRIORITIES = SERVICE_CONTAINER_NAMES.map(
  (name)=>services[name].priority,
);
const SERVICE_CONTAINER_DESCRIPTIONS = SERVICE_CONTAINER_NAMES.map(
  (name)=>services[name].description,
);

function getServiceContainerName(folder, environment){
  const index = SERVICE_CONTAINER_FOLDERS.indexOf(folder);
  if(index === -1) {
    return null;
  }
  const prefix = globalConfig.name_prefix || "";
  if(prefix === ""){
    return `${environment}-${SERVICE_CONTAINER_NAMES[index]}`;
  }
  return `${prefix}-${environment}-${SERVICE_CONTAINER_NAMES[index]}`;
}

// Function to get service info by folder
function getServiceInfoByFolder(folder){
  const index = SERVICE_CONTAINER_FOLDERS.indexOf(folder);
  if(index === -1) {
    return null;
  }
  return {
    containerName: SERVICE_CONTAINER_NAMES[index],
    dockerFile: SERVICE_CONTAINER_DOCKER_FILES[index],
    port: SERVICE_CONTAINER_PORTS[index],
    priority: SERVICE_CONTAINER_PRIORITIES[index],
    public: SERVICE_CONTAINER_PUBLIC[index]
  };
}

// Function to get description by folder
function getDescriptionByFolder(folder){
  const index = SERVICE_CONTAINER_FOLDERS.indexOf(folder);
  if(index === -1) {
    return null;
  }
  return SERVICE_CONTAINER_DESCRIPTIONS[index];
}

// Function to get service info by name
function getServiceInfoByName(containerName){
  const index = SERVICE_CONTAINER_NAMES.indexOf(containerName);
  if(index === -1) {
    return null;
  }
  return {
    containerName: SERVICE_CONTAINER_NAMES[index],
    dockerFile: SERVICE_CONTAINER_DOCKER_FILES[index],
    port: SERVICE_CONTAINER_PORTS[index],
    priority: SERVICE_CONTAINER_PRIORITIES[index],
    public: SERVICE_CONTAINER_PUBLIC[index]
  };
}

function getServiceScalingByFolder(folder){
  const index = SERVICE_CONTAINER_FOLDERS.indexOf(folder);
  if(index === -1) {
    return null;
  }
  const service = services[SERVICE_CONTAINER_NAMES[index]].scale;
  return {
    min: service.min,
    max: service.max
  };
}

// Function to get global config values
function getConfigGlobal(globalName){
  return globalConfig[globalName] || null;
}

// Function to get maximum priority
function getMaximumPriority(){
  return Math.max(...SERVICE_CONTAINER_PRIORITIES);
}

// Function to check if the current folder matches the priority
function isCurrentPriority(folder, priority){
  const serviceInfo = getServiceInfoByFolder(folder, "");
  return serviceInfo && serviceInfo.priority === priority;
}

function requiresJWTSecrets(serviceName){
    console.error(`🧾👀 Checking JWT secrets for service: ${serviceName}`);
    console.error(`🧾 Service config:`, JSON.stringify(configJson.services[serviceName], null, 2));
    const requires = configJson.services[serviceName]?.requiresJWTSecrets === true;
    console.error(`📛 Requires JWT: ${requires}`);
    return requires;
}

/**
 * Get resource limits for a service in a specific environment
 * @param {string} serviceName - Name of the service
 * @param {string} environment - Environment (develop/stage/prod)
 * @returns {Object} CPU and memory limits
 */
function getServiceResources(serviceName, environment){
  // Map 'staging' to 'stage' if needed
  const envKey = environment === "staging" ? "stage" : environment;

  const resources = configJson.resources?.[envKey];
  if(!resources) {
    throw new Error(`No resource configuration found for environment: ${environment} (tried ${envKey})`);
  }

  // Get service-specific resources if they exist, otherwise use default
  const serviceResources = resources[serviceName];
  const defaultResources = resources.default;

  if(!defaultResources) {
    throw new Error(`No default resource configuration found for environment: ${environment}`);
  }

  return {
    cpu: serviceResources?.cpu || defaultResources.cpu,
    memory: serviceResources?.memory || defaultResources.memory
  };
}

/**
 * Get all Docker build related information for a service
 * @param {string} folder - Service folder path
 * @param {string} environment - Environment tag
 * @returns {Object} Docker build configuration
 */
function getDockerBuildInfo(folder, environment){
  const serviceInfo = getServiceInfoByFolder(folder);
  if(!serviceInfo) {
    return null;
  }

  const containerName = getServiceContainerName(folder, environment);
  const dockerOrg = getConfigGlobal("docker_org");
  const description = getDescriptionByFolder(folder);

  if(!containerName || !dockerOrg) {
    console.error("Missing required Docker configuration");
    return null;
  }

  return {
    ...serviceInfo,
    containerName,
    dockerOrg,
    description,
    registryUrl: `us-docker.pkg.dev/${dockerOrg}/gcr.io/${containerName}:${environment}`
  };
}

/**
 * Get all service container folders as a space-separated string
 * @returns {string} Space-separated list of service folders
 */
function getServiceContainerFolders(){
  return SERVICE_CONTAINER_FOLDERS.join(" ");
}

/**
 * Get deployment information for a service
 * @param {string} folder - Service folder path
 * @param {string} environment - Environment tag
 * @returns {Object} Deployment configuration
 */
function getDeploymentInfo(folder, environment) {
  // For now, this is the same as getDockerBuildInfo
  // In the future, this could include additional deployment-specific information
  return getDockerBuildInfo(folder, environment);
}

// Export functions to be used in other scripts
module.exports = {
  SERVICE_CONTAINER_FOLDERS,
  getServiceInfoByFolder,
  getDescriptionByFolder,
  getServiceInfoByName,
  getConfigGlobal,
  getMaximumPriority,
  isCurrentPriority,
  requiresJWTSecrets,
  getServiceResources,
  getDockerBuildInfo,
  getServiceContainerFolders,
  getDeploymentInfo,
};

// Command-line usage
if(require.main === module) {
  const [, , command, ...args] = process.argv;

  switch(command) {
    case "getServiceContainerName":
      console.log(getServiceContainerName(args[0], args[1]));
      break;
    case "getServiceContainerFolders":
      console.log(getServiceContainerFolders());
      break;
    case "getServiceInfoByFolder":
      logServiceToBashArray(getServiceInfoByFolder(args[0], args[1]));
      break;
    case "getDescriptionByFolder":
      console.log(getDescriptionByFolder(args[0]));
      break;
    case "getServiceInfoByName":
      logServiceToBashArray(getServiceInfoByName(args[0]));
      break;
    case "getServiceScalingByFolder":
      logScalingToBashArray(getServiceScalingByFolder(args[0]));
      break;
    case "getConfigGlobal":
      console.log(getConfigGlobal(args[0]));
      break;
    case "getMaximumPriority":
      console.log(getMaximumPriority());
      break;
    case "isCurrentPriority":
      console.log(isCurrentPriority(args[0], parseInt(args[1])));
      break;
    case "requiresJWTSecrets":
      console.log(requiresJWTSecrets(args[0]));
      break;
    case "getServiceResources": {
      const [serviceName, environment] = args;
      const resources = getServiceResources(serviceName, environment);
      console.log(`${resources.cpu}\n${resources.memory}`);
      break;
    }
    case "getDockerBuildInfo": {
      const [folder, env] = args;
      if(!folder || !env) {
        console.error("Missing required arguments: folder and environment");
        process.exit(1);
      }
      const buildInfo = getDockerBuildInfo(folder, env);
      if(buildInfo) {
        console.log(JSON.stringify(buildInfo));
      } else {
        console.error("Failed to generate Docker build info");
        process.exit(1);
      }
      break;
    }
    case "getDeploymentInfo": {
      const [folder, env] = args;
      if(!folder || !env) {
        console.error("Missing required arguments: folder and environment");
        process.exit(1);
      }
      const deployInfo = getDeploymentInfo(folder, env);
      if(deployInfo) {
        console.log(JSON.stringify(deployInfo));
      } else {
        console.error("Failed to generate deployment info");
        process.exit(1);
      }
      break;
    }
    default: {
      console.error("🤷🏻‍♂️ Unknown command.");
      process.exit(1);
    }
  }
}

function logServiceToBashArray(value){
  if(value === null){
    console.error("Item not found");
    process.exit(1);
  }
  console.log(value.containerName);
  console.log(value.dockerFile);
  console.log(value.port);
  console.log(value.priority);
  console.log(value.public);
}

function logScalingToBashArray(value){
  if(value === null){
    console.error("Item not found");
    process.exit(1);
  }
  console.log(value.min);
  console.log(value.max);
}

