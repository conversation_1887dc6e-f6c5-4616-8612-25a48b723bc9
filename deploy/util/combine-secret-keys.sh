#!/bin/bash

# More portable way to get script directory
get_script_dir() {
    local source="${BASH_SOURCE[0]}"
    local dir

    # Resolve $source until the file is no longer a symlink
    while [ -h "$source" ]; do
        dir="$( cd -P "$( dirname "$source" )" && pwd )"
        source="$(readlink "$source")"
        # If $source was a relative symlink, we need to resolve it relative to the path where the symlink file was located
        [[ $source != /* ]] && source="$dir/$source"
    done
    dir="$( cd -P "$( dirname "$source" )" && pwd )"
    echo "$dir"
}

SCRIPT_DIR="$(get_script_dir)"
ROOT_DIR=$(dirname $SCRIPT_DIR)

# shellcheck disable=SC2016
# Is a comment
: '
combine-secrets-keys.sh

Usage:
    ./combine-secrets-keys.sh private-keys/<env-folder>

Description:
    Combines all .env files from the specified environment folder and its subdirectories
    into a single .env file that can be used with the `act` CLI.

Arguments:
    env-folder: The name of the folder inside `private-keys/` containing the .env files
                (e.g., "private-keys/api-test", "private-keys/local", "private-keys/develop", "private-keys/staging", "private-keys/prod").

Example:
    ./combine-secrets-keys.sh local

    This will combine all .env files in the `private-keys/local/` directory and its
    subdirectories into a single file named `.combined-secrets.env`.
'

# Check if the env-folder argument is provided
if [ -z "$1" ]; then
    echo "Error: No environment folder specified."
    echo "Usage: ./combine-secrets-keys.sh private-keys/<env-folder>"
    exit 1
fi

# Set the folder based on the provided argument
env_folder="$1"
output_secrets_file="$2"

echo "ROOT_DIR: $ROOT_DIR"

# Check if the specified folder exists
if [ ! -d "$env_folder" ]; then
    echo "Error: The specified folder '$env_folder' does not exist."
    exit 1
fi

if [ -z "$output_secrets_file" ]; then
    output_secrets_file="$ROOT_DIR/../private-keys/.combined-secrets.env"
fi

# Clear the output file if it already exists
> "$output_secrets_file"

# Use find to recursively locate all .env files and concatenate them
while IFS= read -r -d '' file; do
    echo "Adding $file to $output_secrets_file"
    cat "$file" >> "$output_secrets_file"
    echo "" >> "$output_secrets_file"  # Add a newline between files
done < <(find "$env_folder" -type f -name "*.env" -print0)

echo "Combined secrets file created at $output_secrets_file"
