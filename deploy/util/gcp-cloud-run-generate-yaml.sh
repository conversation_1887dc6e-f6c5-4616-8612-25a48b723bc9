#!/bin/bash
set -e

# Get the directory of the current script
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
ROOT_DIR=$(dirname "$(dirname "$SCRIPT_DIR")")

# Define input parameters
SERVICE_FOLDER=$1
STAGE=$2
OUTPUT_FILE=$3
TEMPLATE_FILE="$SCRIPT_DIR/../service-template.yaml"

# Strip the environment tag from the service name (e.g., divinci-api-webhook-develop -> divinci-api-webhook)
CONTAINER_NAME="$(node "$SCRIPT_DIR/service-info.js" getServiceContainerName "$SERVICE_FOLDER" "$STAGE")"

# Determine the directory for environment files
ENV_DIR="$ROOT_DIR/private-keys/$STAGE"

service_info=($(node "$SCRIPT_DIR/service-info.js" getServiceInfoByFolder "$SERVICE_FOLDER"))
BASE_SERVICE_NAME=${service_info[0]}
PORT=${service_info[2]}
ENV_FILES=($(node "$SCRIPT_DIR/env.js" "$ENV_DIR" "$BASE_SERVICE_NAME"))

# Read environment variables from the specified files
ENV_VARS=""

# Function to add JWT secrets if needed
function addJWTSecrets {
    local service_name=$1
    echo "🧾👀 Checking JWT secrets for service: $service_name"
    local requires_jwt=$(node "$SCRIPT_DIR/service-info.js" requiresJWTSecrets "$service_name")
    echo "🕵🏻‍♂️ requires_jwt value: $requires_jwt"

    if [ "$requires_jwt" = "true" ]; then
        echo "🤫 Adding JWT secrets to environment variables"
        ENV_VARS+="            - name: JWT_PRIVATE_KEY\n"
        ENV_VARS+="              valueFrom:\n"
        ENV_VARS+="                secretKeyRef:\n"
        ENV_VARS+="                  key: latest\n"
        ENV_VARS+="                  name: JWT_PRIVATE_KEY\n"
        ENV_VARS+="            - name: JWT_PUBLIC_KEY\n"
        ENV_VARS+="              valueFrom:\n"
        ENV_VARS+="                secretKeyRef:\n"
        ENV_VARS+="                  key: latest\n"
        ENV_VARS+="                  name: JWT_PUBLIC_KEY\n"
        ENV_VARS+="            - name: JWT_PEM_PASSPHRASE\n"
        ENV_VARS+="              valueFrom:\n"
        ENV_VARS+="                secretKeyRef:\n"
        ENV_VARS+="                  key: latest\n"
        ENV_VARS+="                  name: JWT_PEM_PASSPHRASE\n"
    else
        echo "No JWT secrets needed for this service: $service_name"
    fi
}

# Check if we have any environment files
if [ ${#ENV_FILES[@]} -eq 0 ]; then
  echo "⚠️ No environment files found in $ENV_DIR"
  echo "🔄 This may be expected in CI/CD environments. Continuing with default environment."

  # Add some default environment variables
  ENV_VARS+="            - name: NODE_ENV\n              value: \"$STAGE\"\n"
  ENV_VARS+="            - name: ENVIRONMENT\n              value: \"$STAGE\"\n"
  ENV_VARS+="            - name: MTLS_CERT_DIR\n              value: \"/etc/ssl\"\n"
else
  # Process each environment file
  for ENV_FILE in "${ENV_FILES[@]}"; do
    ENV_FILE="$ENV_DIR/$ENV_FILE"
    if ! [ -f "$ENV_FILE" ]; then
      echo "❌ Environment file not found: $ENV_FILE"
      # Don't exit, just continue with default environment
      echo "⚠️ This may be expected in CI/CD environments. Continuing with default environment."
      continue
    fi
    echo "Environment file found: $ENV_FILE"
    while IFS='=' read -r key value; do
      # Skip comments and empty lines
      [[ "$key" =~ ^#.*$ || -z "$key" ]] && continue

      # Remove leading/trailing quotations
      value="${value%\"}"  # Remove closing quote
      value="${value#\"}"  # Remove opening quote

      # Escape special characters in the value
      value=$(printf '%s' "$value" | sed 's/["\\]/\\&/g')
      # Format environment variables for YAML
      ENV_VARS+="            - name: $key\n              value: \"$value\"\n"
    done < "$ENV_FILE"
  done
fi

echo "BASE_SERVICE_NAME: $BASE_SERVICE_NAME"
# Add JWT secrets if needed
addJWTSecrets "$BASE_SERVICE_NAME"

# Set common parameters for all services
LAUNCH_STAGE="BETA"
SCALING=($(node "$SCRIPT_DIR/../util/service-info.js" getServiceScalingByFolder "$SERVICE_FOLDER"))
MIN_SCALE=${SCALING[0]}
MAX_SCALE=${SCALING[1]}
TAG="$STAGE"

# Get resource limits for the service
RESOURCES=($(node "$SCRIPT_DIR/service-info.js" getServiceResources "$BASE_SERVICE_NAME" "$STAGE"))
CPU_LIMIT=${RESOURCES[0]}
MEMORY_LIMIT=${RESOURCES[1]}

function escapeStr {
  local str="$1"
  echo "${str//&/\\&}"
}

# Replace placeholders in the template file, using | as a delimiter to avoid conflicts with slashes
# IMPORTANT: min scale, max scale, and port are wrapped in quotes to ensure they are treated as strings
# Appently Kuburnetes hates numbers ¯\_(ツ)_/¯

sed -e "s|{\s*{\s*CONTAINER_NAME\s*}\s*}|$(escapeStr "$CONTAINER_NAME")|g" \
    -e "s|{\s*{\s*LAUNCH_STAGE\s*}\s*}|$(escapeStr "$LAUNCH_STAGE")|g" \
    -e "s|{\s*{\s*TAG\s*}\s*}|$(escapeStr "$TAG")|g" \
    -e "s|{\s*{\s*ENV_VARS\s*}\s*}|\n$(escapeStr "$ENV_VARS")|g" \
    -e "s|{\s*{\s*MIN_SCALE\s*}\s*}|\"$(escapeStr "$MIN_SCALE")\"|g" \
    -e "s|{\s*{\s*MAX_SCALE\s*}\s*}|\"$(escapeStr "$MAX_SCALE")\"|g" \
    -e "s|{\s*{\s*PORT\s*}\s*}|$(escapeStr "$PORT")|g" \
    -e "s|{\s*{\s*CPU_LIMIT\s*}\s*}|$(escapeStr "$CPU_LIMIT")|g" \
    -e "s|{\s*{\s*MEMORY_LIMIT\s*}\s*}|$(escapeStr "$MEMORY_LIMIT")|g" \
    "$TEMPLATE_FILE" > "$OUTPUT_FILE"

echo "Generated YAML file: $OUTPUT_FILE"
