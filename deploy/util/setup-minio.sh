#!/bin/sh

# <PERSON><PERSON> should already be ready due to the depends_on condition in docker-compose
echo "Setting up MinIO buckets and policies..."

# We're using the minio/mc image, so mc is already installed

# Get credentials from environment variables or use defaults
MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin}

echo "Using MinIO credentials: $MINIO_ROOT_USER / $MINIO_ROOT_PASSWORD"

# Configure MinIO client with the correct endpoint and credentials
echo "Configuring MinIO client..."
mc alias set local-minio http://local-minio:9000 "$MINIO_ROOT_USER" "$MINIO_ROOT_PASSWORD"

# Verify the connection
echo "Verifying connection to MinIO..."
mc admin info local-minio

# Create buckets
echo "Creating buckets..."
mc mb --ignore-existing local-minio/rag-origin-files-local
mc mb --ignore-existing local-minio/rag-files-local

# Set bucket policies to public read-write for local development
echo "Setting bucket policies..."
mc anonymous set public local-minio/rag-origin-files-local
mc anonymous set public local-minio/rag-files-local

# No need to create whitelabel directories - they'll be created dynamically
echo "Skipping creation of whitelabel directories - they'll be created at runtime"

echo "MinIO setup completed successfully!"
