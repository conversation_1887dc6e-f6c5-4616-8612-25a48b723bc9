#!/bin/bash

# This script configures CORS for all MinIO buckets
# It should be run after Min<PERSON> is started

# MinIO endpoint
MINIO_ENDPOINT="http://localhost:9000"

# MinIO credentials from environment or defaults
# We're using the root user for all operations
MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin}

# Buckets to configure
BUCKETS=(
  "workspace-audio"
  "rag-origin-files-local"
  "rag-files-local"
  "audio-transcript-files"
  "local-audio"
  "private-temporary-uploads"
)

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
CORS_FILE="$SCRIPT_DIR/minio-cors-config.json"

echo "Using CORS configuration from $CORS_FILE"

# Check if mc (MinIO Client) is installed in docker container
if ! command -v /usr/bin/mc &> /dev/null; then
    echo "MinIO Client (mc) not found at /usr/bin/mc, checking in PATH..."

    if ! command -v mc &> /dev/null; then
        echo "MinIO Client (mc) is not installed. Please run this script in the MinIO container."
        exit 1
    else
        MC_CMD="mc"
    fi
else
    MC_CMD="/usr/bin/mc"
fi

# Configure MinIO Client
echo "Configuring MinIO Client..."
$MC_CMD alias set local-minio "$MINIO_ENDPOINT" "$MINIO_ROOT_USER" "$MINIO_ROOT_PASSWORD"

# Apply CORS configuration to all buckets
echo "Applying CORS configuration to all buckets..."
for bucket in "${BUCKETS[@]}"; do
    echo "Setting CORS configuration for bucket: local-minio/$bucket"

    # Check if bucket exists, create if not
    if ! $MC_CMD ls local-minio/$bucket &> /dev/null; then
        echo "Bucket $bucket does not exist. Creating..."
        $MC_CMD mb --ignore-existing local-minio/$bucket
        $MC_CMD anonymous set public local-minio/$bucket
    fi

    # Set CORS configuration
    $MC_CMD admin bucket cors set local-minio/$bucket "$CORS_FILE"

    # Verify CORS configuration
    echo "Verifying CORS configuration for bucket: local-minio/$bucket"
    $MC_CMD admin bucket cors get local-minio/$bucket
done

echo "CORS configuration completed successfully for all buckets!"

# Create test file directories if they don't exist
YEAR=$(date +%Y)
MONTH=$(date +%m)
DAY=$(date +%d)
WHITELABEL_ID="682415a03d653676ebe89b06"

# Create organized directory structure in workspace-audio bucket
echo "Creating directory structure in workspace-audio bucket..."
$MC_CMD cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}/${DAY}/.keep
$MC_CMD cp /dev/null local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}/${DAY}/.keep

# Create test files to verify bucket access
echo "Creating test files to verify MinIO access..."
echo "This is a test audio file" > /tmp/test-audio.txt
echo "This is a test original file" > /tmp/test-original.txt

# Upload test files to verify accessibility
$MC_CMD cp /tmp/test-audio.txt local-minio/workspace-audio/${WHITELABEL_ID}/audio/${YEAR}/${MONTH}/${DAY}/test-audio.txt
$MC_CMD cp /tmp/test-original.txt local-minio/workspace-audio/${WHITELABEL_ID}/original/${YEAR}/${MONTH}/${DAY}/test-original.txt

# List files to verify
echo "Listing files in workspace-audio bucket:"
$MC_CMD ls --recursive local-minio/workspace-audio/${WHITELABEL_ID}

echo "Test files created and uploaded successfully!"
