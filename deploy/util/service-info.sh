#!/bin/bash -x

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
ROOT_DIR=$(dirname "$(dirname "$SCRIPT_DIR")")

CONFIG_JSON=$(cat "$ROOT_DIR/deploy/config.json")
CONFIG_SERVICES="$(echo "$CONFIG_JSON" | jq .services[])"

SERVICE_CONTAINER_NAMES=($(echo $CONFIG_JSON | jq -r '.services | to_entries[] | .key'))
SERVICE_CONTAINER_FOLDERS=($(echo $CONFIG_SERVICES | jq  -r .folder))
SERVICE_CONTAINER_DOCKER_FILES=($(echo $CONFIG_SERVICES | jq  -r .docker_file))
SERVICE_CONTAINER_PORTS=($(echo $CONFIG_SERVICES | jq  -r .port))
SERVICE_CONTAINER_PRIORITYS=($(echo $CONFIG_SERVICES | jq  -r .priority))

IFS=$'\n' read -r -d '' -a SERVICE_CONTAINER_DESCRIPTIONS < <(echo "$CONFIG_SERVICES" | jq -r '.description' && printf '\0')

function getServiceInfoByFolder {
  local folder=$1
  local environment=$2
  local index=0
  for service in "${SERVICE_CONTAINER_FOLDERS[@]}"; do
    if [ "$service" = "$folder" ]; then
      local container_name="${SERVICE_CONTAINER_NAMES[$index]}-$environment"
      echo "$container_name"
      echo "${SERVICE_CONTAINER_DOCKER_FILES[$index]}"
      echo "${SERVICE_CONTAINER_PORTS[$index]}"
      echo "${SERVICE_CONTAINER_PRIORITYS[$index]}"
      return
    fi
    index=$((index+1))
  done
}

function getDescriptionByFolder {
  local folder=$1
  local index=0
  for service in "${SERVICE_CONTAINER_FOLDERS[@]}"; do
    if [ "$service" = "$folder" ]; then
      echo "${SERVICE_CONTAINER_DESCRIPTIONS[$index]}"
      return
    fi
    index=$((index+1))
  done
}

function getServiceInfoByName {
  local container_name=$1
  local index=0
  for service in "${SERVICE_CONTAINER_NAMES[@]}"; do
    if [ "$service" = "${container_name%-*}" ]; then
      echo "${SERVICE_CONTAINER_NAMES[$index]}"
      echo "${SERVICE_CONTAINER_DOCKER_FILES[$index]}"
      echo "${SERVICE_CONTAINER_PORTS[$index]}"
      echo "${SERVICE_CONTAINER_PRIORITYS[$index]}"
      return
    fi
    index=$((index+1))
  done
}

function getConfigGlobal {
  local globalName=$1
  echo "$CONFIG_JSON" | jq -r ".globals.$globalName"
}

export SERVICE_CONTAINER_FOLDERS
export -f getServiceInfoByFolder
export -f getServiceInfoByName
export -f getConfigGlobal

function getMaximumPriority {
  local max=0
  for priority in "${SERVICE_CONTAINER_PRIORITYS[@]}"; do
    if [ "$priority" -gt "$max" ]; then
      max=$priority
    fi
  done
  echo $max
}

function isCurrentPriority {
  local folder=$1
  local priority=$2
  local service_info=($(getServiceInfoByFolder $folder $tag))
  local current_priority=${service_info[3]}
  if [ "$current_priority" -eq "$priority" ]; then
    echo 1
  else
    echo 0
  fi
}

export -f getMaximumPriority
export -f isCurrentPriority
