#!/bin/sh

# Start <PERSON><PERSON> in the background
minio server /data --console-address ':9001' &
MINIO_PID=$!

# Wait for Min<PERSON> to be ready
echo "Waiting for Min<PERSON> to start..."
for i in $(seq 1 30); do
  if curl -s -f -o /dev/null http://localhost:9000/minio/health/live; then
    echo "<PERSON><PERSON> is ready!"
    break
  fi
  echo "Waiting for Min<PERSON> to start... ($i/30)"
  sleep 2
done

# MinIO client (mc) is already included in the minio/minio image
echo "Using pre-installed MinIO client..."

# Get credentials from environment variables or use defaults
MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin}

echo "Using MinIO credentials: $MINIO_ROOT_USER / $MINIO_ROOT_PASSWORD"

# Configure MinIO client
echo "Configuring MinIO client..."
/usr/bin/mc alias set local-minio http://localhost:9000 "$MINIO_ROOT_USER" "$MINIO_ROOT_PASSWORD"

# Verify the connection
echo "Verifying connection to MinIO..."
/usr/bin/mc admin info local-minio

# Create buckets
echo "Creating buckets for RAG files..."
/usr/bin/mc mb --ignore-existing local-minio/rag-origin-files-local
/usr/bin/mc mb --ignore-existing local-minio/rag-files-local

# Create buckets for Audio files
echo "Creating buckets for Audio files..."
/usr/bin/mc mb --ignore-existing local-minio/workspace-audio
/usr/bin/mc mb --ignore-existing local-minio/audio-transcript-files
/usr/bin/mc mb --ignore-existing local-minio/local-audio
/usr/bin/mc mb --ignore-existing local-minio/private-temporary-uploads

# Set bucket policies to public read-write for local development
echo "Setting bucket policies for RAG buckets..."
/usr/bin/mc anonymous set public local-minio/rag-origin-files-local
/usr/bin/mc anonymous set public local-minio/rag-files-local

echo "Setting bucket policies for Audio buckets..."
/usr/bin/mc anonymous set public local-minio/workspace-audio
/usr/bin/mc anonymous set public local-minio/audio-transcript-files
/usr/bin/mc anonymous set public local-minio/local-audio
/usr/bin/mc anonymous set public local-minio/private-temporary-uploads

# No need to create whitelabel directories - they'll be created at runtime
echo "Skipping creation of whitelabel directories - they'll be created at runtime"

echo "MinIO setup completed successfully!"

# Wait for MinIO to exit
wait $MINIO_PID