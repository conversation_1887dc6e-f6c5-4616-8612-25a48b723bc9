const util = require("node:util");
const execCallback = require("node:child_process").exec;
const exec = util.promisify(execCallback);


Promise.resolve().then(async ()=>{
  const [,, serviceName] = process.argv;

  if(!serviceName){
    console.error("Service name is required");
    process.exit(1);
  }

  // gcloud run services get-iam-policy divinci-develop-api-webhook --region="us-central1" --format=json

  const { stdout } = await exec(`gcloud run services get-iam-policy ${serviceName} --region us-central1 --format=json`);

  const policy = JSON.parse(stdout);

  const allUsersCanInvoke = (function(){
    if(!Array.isArray(policy.bindings)) return false;

    for(const binding of policy.bindings){
      if(binding.role !== "roles/run.invoker") continue;
      if(!Array.isArray(binding.members)) continue;
      if(binding.members.includes("allUsers")) return true;
    }

    return false;
  })();

  console.log(allUsersCanInvoke);

}).catch((e)=>{
  console.error(e);
  process.exit(1);
});

