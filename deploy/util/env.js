const { statSync: fsStat, readdirSync: fsReadDir } = require("node:fs");
const { resolve: pathResolve } = require("node:path");

/**
 * 🎯 Environment File Selector
 *
 * This utility helps manage environment variables by selecting appropriate .env files
 * based on service type (client/server) and shared configurations.
 *
 * Usage:
 *   node env.js <env-path> <service-name>
 *
 * Features:
 * 🔍 Validates environment directory existence
 * 📂 Filters files based on service type (client/server)
 * 🤝 Handles shared configuration files
 * ⚡ Supports both client and server-side environments
 *
 * File Patterns:
 * - *-client-*.env: Client-specific environment files
 * - *.shared.env: Shared configuration files
 * - *.env: General environment files
 */

let [, , envPath, service] = process.argv;

const CONFIG = require("../config.json");
if(!(service in CONFIG.services)){
  throw new Error("Service not found in config.json");
}
const serviceEnv = CONFIG.services[service].env;

envPath = pathResolve(process.cwd(), envPath);

getEnvFiles(envPath, serviceEnv === "client").forEach((file)=>(console.log(file)));

function getEnvFiles(envPath, client){
  client = !!client;

  const stat = fsStat(envPath, { throwIfNoEntry: false }); // just checking if folder exists

  if(typeof stat === "undefined") {
    throw new Error("env doesn't exist");
  }

  if(!stat.isDirectory()) {
    throw new Error("env is file not directory");
  }
  const isClient = /.*-client-.*/;
  const isShared = /\.shared\./;
  const isEnv = /.*\.env/;
  const files = fsReadDir(envPath);

  return files.filter((file)=>{
    if(!isEnv.test(file)) return false;

    // If the file is shared, we'll use it for both environments
    if(!isShared.test(file)){
      // If it's a clientside evirnoment, we want clientside files
      if(client && !isClient.test(file)) return false;
      // else, we dont want clientside files
      else if(!client && isClient.test(file)) return false;
    }

    // Just making sure file exists and is not a directory
    const stat = fsStat(pathResolve(envPath, "./" + file), { throwIfNoEntry: false });
    if(typeof stat === "undefined") return false;
    if(stat.isDirectory()) return false;

    return true;
  });
}
