/**
 * Service utilities for the Divinci Deployment CLI
 * 
 * This module provides utilities for working with services,
 * including mapping service names to folders and checking for changes.
 */

const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');
const { SERVICE_MAPPINGS } = require('./parse-args');

// Path to the config.json file
const CONFIG_PATH = path.resolve(__dirname, '../config.json');

/**
 * Get the folder paths for the specified services
 * 
 * @param {string[]} services - Service names
 * @returns {Promise<string[]>} Service folder paths
 */
async function getServiceFolders(services) {
  // Read the config.json file
  const config = JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'));
  
  // Map service names to folder paths
  const folders = [];
  
  for (const service of services) {
    // Map service name to config key
    const configKey = SERVICE_MAPPINGS[service] || service;
    
    // Get the folder path from config
    if (config.services[configKey]) {
      folders.push(config.services[configKey].folder);
    } else if (service === 'resources') {
      // Special case for resources
      folders.push('workspace/resources');
    } else {
      throw new Error(`Service not found in config: ${service}`);
    }
  }
  
  return folders;
}

/**
 * Check if a service has changes
 * 
 * @param {string} folder - Service folder path
 * @returns {Promise<boolean>} True if the service has changes
 */
async function hasServiceChanges(folder) {
  try {
    // Use git to check for changes in the folder
    const output = execSync(`git diff --name-only HEAD~1 HEAD -- ${folder}`).toString().trim();
    return output.length > 0;
  } catch (error) {
    console.warn(`Warning: Could not check for changes in ${folder}: ${error.message}`);
    // Assume changes if we can't check
    return true;
  }
}

/**
 * Check if resources have changes
 * 
 * @returns {Promise<boolean>} True if resources have changes
 */
async function hasResourcesChanges() {
  return hasServiceChanges('workspace/resources');
}

/**
 * Get the Docker image name for a service
 * 
 * @param {string} folder - Service folder path
 * @param {string} environment - Environment (develop, stage, prod)
 * @returns {Promise<string>} Docker image name
 */
async function getDockerImageName(folder, environment) {
  // Read the config.json file
  const config = JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'));
  
  // Get the Docker organization from config
  const dockerOrg = config.globals.docker_org;
  
  // Find the service in config
  let serviceName = null;
  for (const [name, service] of Object.entries(config.services)) {
    if (service.folder === folder) {
      serviceName = name;
      break;
    }
  }
  
  if (!serviceName) {
    throw new Error(`Service not found for folder: ${folder}`);
  }
  
  // Get the container name prefix
  const prefix = config.globals.name_prefix || '';
  
  // Build the container name
  let containerName;
  if (prefix) {
    containerName = `${prefix}-${environment}-${serviceName}`;
  } else {
    containerName = `${environment}-${serviceName}`;
  }
  
  // Return the full image name
  return `us-docker.pkg.dev/${dockerOrg}/gcr.io/${containerName}:${environment}`;
}

/**
 * Get the resources image name
 * 
 * @param {string} environment - Environment (develop, stage, prod)
 * @returns {Promise<string>} Resources image name
 */
async function getResourcesImageName(environment) {
  // Read the config.json file
  const config = JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'));
  
  // Get the Docker organization from config
  const dockerOrg = config.globals.docker_org;
  
  // Return the resources image name
  return `us-docker.pkg.dev/${dockerOrg}/gcr.io/divinci-resources:${environment}`;
}

module.exports = {
  getServiceFolders,
  hasServiceChanges,
  hasResourcesChanges,
  getDockerImageName,
  getResourcesImageName
};
