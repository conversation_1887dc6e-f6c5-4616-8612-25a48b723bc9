#!/usr/bin/env node

/**
 * Divinci Deployment CLI
 *
 * A command-line tool for building and deploying Divinci services to Google Cloud Run.
 * This CLI provides similar functionality to the GitHub Actions workflow but can be run locally.
 *
 * Usage:
 *   divinci deploy <environment> [services] [options]
 *
 * Examples:
 *   divinci deploy stage                       # Deploy all services to staging
 *   divinci deploy stage resources:web:api     # Deploy specific services to staging
 *   divinci deploy stage web:api skip-tests    # Deploy web and API to staging, skipping tests
 *   divinci deploy prod resources:web skip-e2e # Deploy resources and web to production, skipping E2E tests
 *
 * Environment:
 *   develop - Development environment
 *   stage   - Staging environment
 *   prod    - Production environment
 *
 * Services:
 *   resources - Shared resources image
 *   web       - Web client
 *   api       - Public API server
 *   live      - Live API server
 *   webhook   - Webhook API server
 *   pyannote  - Pyannote audio service
 *   ffmpeg    - FFmpeg audio service
 *   open-parse - Open Parse service
 *   all       - All services
 *
 * Options:
 *   skip-tests   - Skip all tests
 *   skip-unit    - Skip unit tests
 *   skip-e2e     - Skip E2E tests
 */

const path = require('path');
const { parseArgs } = require('./parse-args');
const { runTests } = require('./run-tests');
const { buildAndDeploy } = require('./build-deploy');
const { getServiceFolders } = require('./service-utils');

// Main function
async function main() {
  try {
    console.log('🚀 Divinci Deployment CLI');

    // Parse command-line arguments
    const args = parseArgs(process.argv.slice(2));

    // Validate environment
    if (!args.environment) {
      console.error('❌ Error: Environment is required (develop, stage, or prod)');
      process.exit(1);
    }

    console.log(`📋 Deployment Configuration:`);
    console.log(`  🌍 Environment: ${args.environment}`);
    console.log(`  🧪 Skip Tests: ${args.skipTests ? 'Yes' : 'No'}`);
    console.log(`  🧪 Skip Unit Tests: ${args.skipUnitTests ? 'Yes' : 'No'}`);
    console.log(`  🧪 Skip E2E Tests: ${args.skipE2ETests ? 'Yes' : 'No'}`);
    console.log(`  🤖 Auto-confirm: ${args.yes ? 'Yes' : 'No'}`);

    // Get service folders to deploy
    const serviceFolders = await getServiceFolders(args.services);
    console.log(`  🗂️ Services to deploy: ${args.services.join(':')}`);
    console.log(`  📂 Service folders: ${serviceFolders.join(', ')}`);

    // Run tests unless skipped
    if (!args.skipTests) {
      console.log('🧪 Running tests...');
      const testsPassed = await runTests({
        skipUnitTests: args.skipUnitTests,
        skipE2ETests: args.skipE2ETests,
        serviceFolders
      });

      if (!testsPassed) {
        console.error('❌ Tests failed. Deployment aborted.');
        process.exit(1);
      }

      console.log('✅ Tests passed.');
    } else {
      console.log('⏩ Skipping tests.');
    }

    // Build and deploy services
    console.log('🏗️ Building and deploying services...');
    await buildAndDeploy({
      environment: args.environment,
      serviceFolders,
      skipTests: args.skipTests,
      yes: args.yes
    });

    console.log('✅ Deployment completed successfully.');
  } catch (error) {
    console.error(`❌ Deployment failed: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Export the main function for testing
module.exports = { main };

// Run the main function if this is the main module
if (require.main === module) {
  main();
}
