# Divinci Deployment CLI

A command-line tool for building and deploying Divinci services to Google Cloud Run. This CLI provides similar functionality to the GitHub Actions workflow but can be run locally.

## Installation

The CLI is included in the repository and can be run directly from the `deploy` directory:

```bash
./deploy/divinci deploy <environment> [services] [options]
```

For convenience, you can add the `deploy` directory to your PATH:

```bash
export PATH=$PATH:/path/to/repository/deploy
```

Then you can run the CLI from anywhere:

```bash
divinci deploy <environment> [services] [options]
```

## Usage

```
divinci deploy <environment> [services] [options]
```

### Examples

```bash
# Deploy all services to staging
divinci deploy stage

# Deploy specific services to staging
divinci deploy stage resources:web:api

# Deploy web and API to staging, skipping tests
divinci deploy stage web:api skip-tests

# Deploy resources and web to production, skipping E2E tests
divinci deploy prod resources:web skip-e2e
```

### Environment

- `develop` - Development environment
- `stage` - Staging environment
- `prod` - Production environment

### Services

- `resources` - Shared resources image
- `web` - Web client
- `api` - Public API server
- `live` - Live API server
- `webhook` - Webhook API server
- `pyannote` - Pyannote audio service
- `ffmpeg` - FFmpeg audio service
- `open-parse` - Open Parse service
- `all` - All services

### Options

- `skip-tests` - Skip all tests
- `skip-unit` - Skip unit tests
- `skip-e2e` - Skip E2E tests

## How It Works

The CLI follows the same process as the GitHub Actions workflow:

1. **Parse Arguments**: Parse command-line arguments to determine which services to deploy and which options to use.
2. **Run Tests**: Run unit tests and E2E tests for the specified services, unless skipped.
3. **Build Resources**: Build the resources image if needed (if resources are specified or have changes).
4. **Build Services**: Build Docker images for the specified services.
5. **Deploy Services**: Deploy the built images to Google Cloud Run.

## Implementation Details

The CLI is implemented as a Node.js application with the following components:

- `deploy.js` - Main CLI entry point
- `parse-args.js` - Command-line argument parser
- `run-tests.js` - Test runner
- `build-deploy.js` - Build and deploy orchestrator
- `service-utils.js` - Service utilities

It uses the existing build and deploy scripts from the `deploy/steps` directory:

- `2.docker-build-no-buildx.sh` - Build Docker images
- `3.deploy-new.sh` - Deploy to Google Cloud Run

## Requirements

- Node.js 14 or later
- Docker
- Google Cloud SDK
- Authentication to Google Cloud (run `gcloud auth login` and `gcloud auth configure-docker us-docker.pkg.dev`)

## Troubleshooting

### Authentication Issues

If you encounter authentication issues with Google Cloud, make sure you're logged in:

```bash
gcloud auth login
gcloud auth configure-docker us-docker.pkg.dev
```

### Docker Build Failures

If Docker builds fail, check the following:

- Make sure Docker is running
- Check that you have enough disk space
- Verify that you have access to the Google Container Registry

### Deployment Failures

If deployments fail, check the following:

- Verify that you have the necessary permissions in Google Cloud
- Check that the service configuration in `deploy/config.json` is correct
- Ensure that the environment variables in `private-keys/<environment>` are set correctly
