#!/bin/bash

# Divinci Deployment CLI Test Script
# 
# This script tests the Divinci Deployment CLI without actually deploying anything.
# It mocks the build and deploy scripts to simulate the deployment process.

# Get the directory of this script
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
REPO_ROOT=$(cd "$SCRIPT_DIR/../.." && pwd)

# Create temporary directory for mocks
TEMP_DIR=$(mktemp -d)
trap 'rm -rf "$TEMP_DIR"' EXIT

# Create mock scripts
echo "Creating mock scripts..."
cat > "$TEMP_DIR/2.docker-build-no-buildx.sh" << 'EOF'
#!/bin/bash
echo "Mock: Building Docker image for $1 in environment $2"
exit 0
EOF

cat > "$TEMP_DIR/3.deploy-new.sh" << 'EOF'
#!/bin/bash
echo "Mock: Deploying service $1 to environment $2"
exit 0
EOF

# Make mock scripts executable
chmod +x "$TEMP_DIR/2.docker-build-no-buildx.sh"
chmod +x "$TEMP_DIR/3.deploy-new.sh"

# Backup original scripts
if [ -f "$REPO_ROOT/deploy/steps/2.docker-build-no-buildx.sh" ]; then
  cp "$REPO_ROOT/deploy/steps/2.docker-build-no-buildx.sh" "$TEMP_DIR/2.docker-build-no-buildx.sh.bak"
fi

if [ -f "$REPO_ROOT/deploy/steps/3.deploy-new.sh" ]; then
  cp "$REPO_ROOT/deploy/steps/3.deploy-new.sh" "$TEMP_DIR/3.deploy-new.sh.bak"
fi

# Replace original scripts with mocks
cp "$TEMP_DIR/2.docker-build-no-buildx.sh" "$REPO_ROOT/deploy/steps/2.docker-build-no-buildx.sh"
cp "$TEMP_DIR/3.deploy-new.sh" "$REPO_ROOT/deploy/steps/3.deploy-new.sh"

# Run the CLI
echo "Running the CLI..."
"$REPO_ROOT/deploy/divinci" deploy "$@"
CLI_EXIT_CODE=$?

# Restore original scripts
if [ -f "$TEMP_DIR/2.docker-build-no-buildx.sh.bak" ]; then
  cp "$TEMP_DIR/2.docker-build-no-buildx.sh.bak" "$REPO_ROOT/deploy/steps/2.docker-build-no-buildx.sh"
fi

if [ -f "$TEMP_DIR/3.deploy-new.sh.bak" ]; then
  cp "$TEMP_DIR/3.deploy-new.sh.bak" "$REPO_ROOT/deploy/steps/3.deploy-new.sh"
fi

# Exit with the same code as the CLI
exit $CLI_EXIT_CODE
