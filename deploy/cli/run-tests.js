/**
 * Test runner for the Divinci Deployment CLI
 *
 * This module runs unit tests and E2E tests for the specified services.
 */

const path = require('path');
const { execSync } = require('child_process');
const fs = require('fs');

/**
 * Run tests for the specified services
 *
 * @param {Object} options - Test options
 * @param {boolean} options.skipUnitTests - Skip unit tests
 * @param {boolean} options.skipE2ETests - Skip E2E tests
 * @param {string[]} options.serviceFolders - Service folders to test
 * @returns {Promise<boolean>} True if all tests pass
 */
async function runTests(options) {
  const { skipUnitTests, skipE2ETests, serviceFolders } = options;

  // Run unit tests
  if (!skipUnitTests) {
    console.log('🧪 Running unit tests...');
    try {
      // Run unit tests for each service
      for (const folder of serviceFolders) {
        if (shouldRunUnitTests(folder)) {
          console.log(`  🧪 Running unit tests for ${folder}...`);
          runUnitTestsForService(folder);
        } else {
          console.log(`  ⏩ Skipping unit tests for ${folder} (no tests found).`);
        }
      }
      console.log('✅ Unit tests passed.');
    } catch (error) {
      console.error(`❌ Unit tests failed: ${error.message}`);
      return false;
    }
  } else {
    console.log('⏩ Skipping unit tests.');
  }

  // Run E2E tests
  if (!skipE2ETests) {
    console.log('🧪 Running E2E tests...');
    try {
      // Run E2E tests for the affected services
      if (shouldRunE2ETests(serviceFolders)) {
        runE2ETests(serviceFolders);
      } else {
        console.log('  ⏩ No E2E tests needed for the specified services.');
      }
      console.log('✅ E2E tests passed.');
    } catch (error) {
      console.error(`❌ E2E tests failed: ${error.message}`);
      return false;
    }
  } else {
    console.log('⏩ Skipping E2E tests.');
  }

  return true;
}

/**
 * Check if unit tests should be run for a service
 *
 * @param {string} folder - Service folder
 * @returns {boolean} True if unit tests should be run
 */
function shouldRunUnitTests(folder) {
  // Check if the service has unit tests
  const packageJsonPath = path.join(process.cwd(), folder, 'package.json');

  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    return packageJson.scripts && (packageJson.scripts.test || packageJson.scripts['test:unit']);
  }

  return false;
}

/**
 * Run unit tests for a service
 *
 * @param {string} folder - Service folder
 */
function runUnitTestsForService(folder) {
  const packageJsonPath = path.join(process.cwd(), folder, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  // Determine the test script to run
  const testScript = packageJson.scripts['test:unit'] || packageJson.scripts.test;

  if (testScript) {
    // Run the test script
    execSync(`cd ${folder} && npm run ${testScript.includes(':') ? `"${testScript}"` : testScript}`, {
      stdio: 'inherit'
    });
  }
}

/**
 * Check if E2E tests should be run for the specified services
 *
 * @param {string[]} serviceFolders - Service folders
 * @returns {boolean} True if E2E tests should be run
 */
function shouldRunE2ETests(serviceFolders) {
  // Check if any of the services require E2E tests
  const requiresE2E = serviceFolders.some(folder => {
    return folder.includes('web') || folder.includes('api');
  });

  // Check if E2E tests exist
  const e2eTestsPath = path.join(process.cwd(), 'workspace/clients/tests');
  const e2eTestsExist = fs.existsSync(e2eTestsPath);

  return requiresE2E && e2eTestsExist;
}

/**
 * Run E2E tests for the specified services
 *
 * @param {string[]} serviceFolders - Service folders
 */
function runE2ETests(serviceFolders) {
  try {
    // Run E2E tests
    console.log('  🧪 Running E2E tests...');

    // Use the api-test-mapping.ts file to determine which tests to run
    const testMappingPath = path.join(process.cwd(), 'workspace/clients/tests/src/api-test-mapping.ts');

    if (fs.existsSync(testMappingPath)) {
      console.log('  📋 Using api-test-mapping.ts to determine which tests to run.');

      // Run the appropriate E2E tests based on the changed services
      execSync('cd workspace/clients/tests && pnpm test', {
        stdio: 'inherit',
        env: {
          ...process.env,
          CHANGED_FOLDERS: serviceFolders.join(',')
        }
      });
    } else {
      // Run all E2E tests
      console.log('  📋 Running all E2E tests (api-test-mapping.ts not found).');
      execSync('cd workspace/clients/tests && pnpm test', {
        stdio: 'inherit'
      });
    }
  } catch (error) {
    throw new Error(`E2E tests failed: ${error.message}`);
  }
}

module.exports = {
  runTests
};
