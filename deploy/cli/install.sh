#!/bin/bash

# Divinci Deployment CLI Installer
# 
# This script installs the Divinci Deployment CLI by:
# 1. Making the scripts executable
# 2. Creating a symlink to the CLI in /usr/local/bin (or another specified location)

# Get the directory of this script
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
REPO_ROOT=$(cd "$SCRIPT_DIR/../.." && pwd)

# Default installation directory
INSTALL_DIR="/usr/local/bin"

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --dir=*)
      INSTALL_DIR="${1#*=}"
      shift
      ;;
    --help)
      echo "Usage: $0 [--dir=INSTALL_DIR]"
      echo ""
      echo "Options:"
      echo "  --dir=INSTALL_DIR  Install the CLI to INSTALL_DIR (default: /usr/local/bin)"
      echo "  --help             Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Check if the installation directory exists
if [ ! -d "$INSTALL_DIR" ]; then
  echo "Error: Installation directory $INSTALL_DIR does not exist"
  exit 1
fi

# Check if the installation directory is writable
if [ ! -w "$INSTALL_DIR" ]; then
  echo "Error: Installation directory $INSTALL_DIR is not writable"
  echo "Try running with sudo: sudo $0"
  exit 1
fi

# Make the scripts executable
echo "Making scripts executable..."
chmod +x "$SCRIPT_DIR/deploy.js"
chmod +x "$SCRIPT_DIR/divinci-deploy.sh"
chmod +x "$REPO_ROOT/deploy/divinci"

# Create a symlink to the CLI
echo "Creating symlink in $INSTALL_DIR..."
ln -sf "$REPO_ROOT/deploy/divinci" "$INSTALL_DIR/divinci"

echo "Installation complete!"
echo "You can now run the CLI with: divinci deploy <environment> [services] [options]"
