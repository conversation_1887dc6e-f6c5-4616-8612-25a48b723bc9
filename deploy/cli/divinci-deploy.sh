#!/bin/bash

# Divinci Deployment CLI Wrapper
# 
# This script is a simple wrapper around the Node.js CLI to make it easier to use.
# It ensures that the Node.js CLI is executable and passes all arguments to it.

# Get the directory of this script
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")

# Make sure the Node.js CLI is executable
chmod +x "$SCRIPT_DIR/deploy.js"

# Run the Node.js CLI with all arguments
"$SCRIPT_DIR/deploy.js" "$@"
