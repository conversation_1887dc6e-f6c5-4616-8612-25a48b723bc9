{"name": "divinci-deploy-cli", "version": "1.0.0", "description": "Divinci Deployment CLI", "main": "deploy.js", "scripts": {"test": "jest", "test:watch": "jest --watch"}, "bin": {"divinci-deploy": "./deploy.js"}, "dependencies": {}, "devDependencies": {"jest": "^29.7.0"}, "jest": {"testEnvironment": "node", "testMatch": ["**/tests/**/*.test.js"], "collectCoverage": true, "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov"]}}