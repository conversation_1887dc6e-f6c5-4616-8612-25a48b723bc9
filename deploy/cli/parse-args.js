/**
 * Command-line argument parser for the Divinci Deployment CLI
 *
 * This module parses command-line arguments for the deployment CLI,
 * similar to how parse-deploy.sh works for GitHub Actions comments.
 */

const path = require('path');
const fs = require('fs');

// Constants
const VALID_ENVIRONMENTS = ['develop', 'stage', 'prod'];
const VALID_SERVICES = [
  'resources',
  'web',
  'api',
  'live',
  'webhook',
  'pyannote',
  'ffmpeg',
  'open-parse',
  'all',
  'base'
];

// Service mappings (similar to parse-deploy.sh)
const SERVICE_MAPPINGS = {
  'web': 'web-client',
  'api': 'server-api',
  'live': 'server-api-live',
  'webhook': 'api-webhook',
  'pyannote': 'api-pyannote',
  'ffmpeg': 'api-ffmpeg',
  'open-parse': 'api-open-parse'
};

// Base services (similar to parse-deploy.sh)
const BASE_SERVICES = ['resources', 'web', 'api', 'live', 'webhook'];

/**
 * Parse command-line arguments
 *
 * @param {string[]} args - Command-line arguments
 * @returns {Object} Parsed arguments
 */
function parseArgs(args) {
  // Default values
  const result = {
    environment: null,
    services: [],
    skipTests: false,
    skipUnitTests: false,
    skipE2ETests: false,
    yes: false // Auto-confirm all prompts
  };

  // Check for global flags first (like -y or --yes)
  const filteredArgs = [];
  for (const arg of args) {
    if (arg === '-y' || arg === '--yes') {
      result.yes = true;
    } else {
      filteredArgs.push(arg);
    }
  }

  // Must have at least one argument (environment)
  if (filteredArgs.length === 0) {
    throw new Error('No arguments provided. Usage: divinci deploy <environment> [services] [options]');
  }

  // First argument is the environment
  const environment = filteredArgs[0].toLowerCase();
  if (!VALID_ENVIRONMENTS.includes(environment)) {
    throw new Error(`Invalid environment: ${environment}. Must be one of: ${VALID_ENVIRONMENTS.join(', ')}`);
  }
  result.environment = environment;

  // Process remaining arguments
  for (let i = 1; i < filteredArgs.length; i++) {
    const arg = filteredArgs[i].toLowerCase();

    // Check if it's a service list
    if (arg.includes(':')) {
      const services = arg.split(':');

      // Validate each service
      for (const service of services) {
        if (!VALID_SERVICES.includes(service)) {
          throw new Error(`Invalid service: ${service}. Must be one of: ${VALID_SERVICES.join(', ')}`);
        }
      }

      // Handle 'all' service
      if (services.includes('all')) {
        result.services = [...VALID_SERVICES].filter(s => s !== 'all' && s !== 'base');
      }
      // Handle 'base' service group
      else if (services.includes('base')) {
        result.services = ['resources', 'web', 'api', 'live', 'webhook'];
      }
      // Add specified services
      else {
        result.services = [...services];
      }
    }
    // Check if it's an option
    else if (arg.startsWith('skip-')) {
      const option = arg.substring(5);

      switch (option) {
        case 'tests':
          result.skipTests = true;
          break;
        case 'unit':
          result.skipUnitTests = true;
          break;
        case 'e2e':
          result.skipE2ETests = true;
          break;
        default:
          throw new Error(`Invalid option: ${arg}`);
      }
    }
    // Must be a single service
    else if (VALID_SERVICES.includes(arg)) {
      if (arg === 'all') {
        result.services = [...VALID_SERVICES].filter(s => s !== 'all' && s !== 'base');
      } else if (arg === 'base') {
        result.services = ['resources', 'web', 'api', 'live', 'webhook'];
      } else {
        result.services.push(arg);
      }
    }
    // Unknown argument
    else {
      throw new Error(`Unknown argument: ${arg}`);
    }
  }

  // If no services specified, default to all
  if (result.services.length === 0) {
    result.services = [...VALID_SERVICES].filter(s => s !== 'all' && s !== 'base');
  }

  // Remove duplicates
  result.services = [...new Set(result.services)];

  return result;
}

module.exports = {
  parseArgs,
  VALID_ENVIRONMENTS,
  VALID_SERVICES,
  SERVICE_MAPPINGS
};
