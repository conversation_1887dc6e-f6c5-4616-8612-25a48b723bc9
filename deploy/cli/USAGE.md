# Divinci Deployment CLI Usage Guide

This guide provides detailed instructions on how to use the Divinci Deployment CLI.

## Installation

To install the CLI, run the installation script:

```bash
# Install to /usr/local/bin (default)
sudo ./install.sh

# Install to a custom directory
./install.sh --dir=$HOME/bin
```

## Basic Usage

The basic syntax for the CLI is:

```bash
divinci deploy [-y|--yes] <environment> [services] [options]
```

Where:
- `-y|--yes` is optional: Auto-confirm all prompts (useful for CI/CD)
- `<environment>` is required: develop, stage, or prod
- `[services]` is optional: resources, web, api, etc. (defaults to all)
- `[options]` is optional: skip-tests, skip-unit, skip-e2e

## Examples

Here are some common usage examples:

```bash
# Deploy all services to staging
divinci deploy stage

# Deploy specific services to staging
divinci deploy stage resources:web:api

# Deploy web and API to staging, skipping tests
divinci deploy stage web:api skip-tests

# Deploy resources and web to production, skipping E2E tests
divinci deploy prod resources:web skip-e2e

# Deploy web to staging with auto-confirmation (no prompts)
divinci deploy -y stage web

# Deploy web and API to production with auto-confirmation and skipping tests
divinci deploy --yes prod web:api skip-tests
```

## Environments

The CLI supports the following environments:

- `develop` - Development environment
- `stage` - Staging environment
- `prod` - Production environment

## Services

The CLI supports the following services:

- `resources` - Shared resources image
- `web` - Web client
- `api` - Public API server
- `live` - Live API server
- `webhook` - Webhook API server
- `pyannote` - Pyannote audio service
- `ffmpeg` - FFmpeg audio service
- `open-parse` - Open Parse service
- `all` - All services
- `base` - Base services (resources, web, api, live, webhook)

## Options

The CLI supports the following options:

### Global Flags

- `-y, --yes` - Auto-confirm all prompts (useful for CI/CD)

### Service Options

- `skip-tests` - Skip all tests
- `skip-unit` - Skip unit tests
- `skip-e2e` - Skip E2E tests

## How It Works

The CLI follows the same process as the GitHub Actions workflow:

1. **Parse Arguments**: Parse command-line arguments to determine which services to deploy and which options to use.
2. **Run Tests**: Run unit tests and E2E tests for the specified services, unless skipped.
3. **Build Resources**: Build the resources image if needed (if resources are specified or have changes).
4. **Build Services**: Build Docker images for the specified services.
5. **Deploy Services**: Deploy the built images to Google Cloud Run.

## Troubleshooting

### Authentication Issues

If you encounter authentication issues with Google Cloud, make sure you're logged in:

```bash
gcloud auth login
gcloud auth configure-docker us-docker.pkg.dev
```

### Docker Build Failures

If Docker builds fail, check the following:

- Make sure Docker is running
- Check that you have enough disk space
- Verify that you have access to the Google Container Registry

### Deployment Failures

If deployments fail, check the following:

- Verify that you have the necessary permissions in Google Cloud
- Check that the service configuration in `deploy/config.json` is correct
- Ensure that the environment variables in `private-keys/<environment>` are set correctly
