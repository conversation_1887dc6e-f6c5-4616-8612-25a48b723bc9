/**
 * Integration tests for the Divinci Deployment CLI
 *
 * These tests mock the entire CLI to test the full workflow.
 */

const path = require('path');
const fs = require('fs');
const { execSync, spawn } = require('child_process');

// Mock all dependencies
jest.mock('fs', () => {
  const originalFs = jest.requireActual('fs');
  return {
    ...originalFs,
    existsSync: jest.fn().mockReturnValue(true),
    readFileSync: jest.fn()
  };
});

jest.mock('child_process', () => {
  return {
    execSync: jest.fn(),
    spawn: jest.fn()
  };
});

// Mock the modules
jest.mock('../parse-args', () => {
  return {
    parseArgs: jest.fn()
  };
});

jest.mock('../run-tests', () => {
  return {
    runTests: jest.fn()
  };
});

jest.mock('../build-deploy', () => {
  return {
    buildAndDeploy: jest.fn()
  };
});

jest.mock('../service-utils', () => {
  return {
    getServiceFolders: jest.fn()
  };
});

// Import the mocked modules
const { parseArgs } = require('../parse-args');
const { runTests } = require('../run-tests');
const { buildAndDeploy } = require('../build-deploy');
const { getServiceFolders } = require('../service-utils');

// Mock the main function
jest.mock('../deploy', () => {
  return {
    main: jest.fn().mockImplementation(async () => {
      // Simulate the CLI workflow
      const mockParseArgs = require('../parse-args').parseArgs;
      const mockGetServiceFolders = require('../service-utils').getServiceFolders;
      const mockRunTests = require('../run-tests').runTests;
      const mockBuildAndDeploy = require('../build-deploy').buildAndDeploy;

      const args = mockParseArgs(process.argv.slice(2));

      if (!args.environment) {
        console.error('❌ Error: Environment is required (develop, stage, or prod)');
        process.exit(1);
      }

      const serviceFolders = await mockGetServiceFolders(args.services);

      if (!args.skipTests) {
        const testsPassed = await mockRunTests({
          skipUnitTests: args.skipUnitTests,
          skipE2ETests: args.skipE2ETests,
          serviceFolders
        });

        if (!testsPassed) {
          console.error('❌ Tests failed. Deployment aborted.');
          process.exit(1);
          return; // Stop execution here
        }
      }

      await mockBuildAndDeploy({
        environment: args.environment,
        serviceFolders,
        skipTests: args.skipTests
      });
    })
  };
});

describe('CLI Integration', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock config.json
    fs.readFileSync.mockImplementation((filePath, encoding) => {
      if (filePath.endsWith('config.json')) {
        return fs.readFileSync(
          path.join(__dirname, 'mock-data/config.json'),
          'utf8'
        );
      }
      return '';
    });

    // Mock parseArgs
    parseArgs.mockReturnValue({
      environment: 'develop',
      services: ['web', 'api'],
      skipTests: false,
      skipUnitTests: false,
      skipE2ETests: false
    });

    // Mock getServiceFolders
    getServiceFolders.mockResolvedValue([
      'workspace/clients/web',
      'workspace/servers/public-api'
    ]);

    // Mock runTests
    runTests.mockResolvedValue(true);

    // Mock buildAndDeploy
    buildAndDeploy.mockResolvedValue();
  });

  test('should run the full deployment workflow', async () => {
    // Set up process.argv
    process.argv = ['node', 'deploy.js', 'develop', 'web:api'];

    // Run the main function
    const { main } = require('../deploy');
    await main();

    // Check that parseArgs was called with the correct arguments
    expect(parseArgs).toHaveBeenCalledWith(['develop', 'web:api']);

    // Check that getServiceFolders was called with the correct services
    expect(getServiceFolders).toHaveBeenCalledWith(['web', 'api']);

    // Check that runTests was called with the correct options
    expect(runTests).toHaveBeenCalledWith({
      skipUnitTests: false,
      skipE2ETests: false,
      serviceFolders: [
        'workspace/clients/web',
        'workspace/servers/public-api'
      ]
    });

    // Check that buildAndDeploy was called with the correct options
    expect(buildAndDeploy).toHaveBeenCalledWith({
      environment: 'develop',
      serviceFolders: [
        'workspace/clients/web',
        'workspace/servers/public-api'
      ],
      skipTests: false
    });
  });

  test('should skip tests if specified', async () => {
    // Mock parseArgs to return skipTests: true
    parseArgs.mockReturnValue({
      environment: 'develop',
      services: ['web', 'api'],
      skipTests: true,
      skipUnitTests: false,
      skipE2ETests: false
    });

    // Set up process.argv
    process.argv = ['node', 'deploy.js', 'develop', 'web:api', 'skip-tests'];

    // Run the main function
    const { main } = require('../deploy');
    await main();

    // Check that runTests was not called
    expect(runTests).not.toHaveBeenCalled();

    // Check that buildAndDeploy was called with skipTests: true
    expect(buildAndDeploy).toHaveBeenCalledWith({
      environment: 'develop',
      serviceFolders: [
        'workspace/clients/web',
        'workspace/servers/public-api'
      ],
      skipTests: true
    });
  });

  test('should abort if tests fail', async () => {
    // Mock runTests to return false (tests failed)
    runTests.mockResolvedValue(false);

    // Mock process.exit
    const mockExit = jest.spyOn(process, 'exit').mockImplementation(() => {});

    // Set up process.argv
    process.argv = ['node', 'deploy.js', 'develop', 'web:api'];

    // Run the main function
    const { main } = require('../deploy');
    await main();

    // Check that process.exit was called with code 1
    expect(mockExit).toHaveBeenCalledWith(1);

    // Check that buildAndDeploy was not called
    expect(buildAndDeploy).not.toHaveBeenCalled();

    // Restore process.exit
    mockExit.mockRestore();
  });
});
