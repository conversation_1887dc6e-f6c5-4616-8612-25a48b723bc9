/**
 * Tests for the argument parser
 */

const { parseArgs, VALID_ENVIRONMENTS, VALID_SERVICES } = require('../parse-args');

describe('parseArgs', () => {
  test('should parse environment correctly', () => {
    const args = parseArgs(['develop']);
    expect(args.environment).toBe('develop');
  });

  test('should throw error for invalid environment', () => {
    expect(() => parseArgs(['invalid'])).toThrow('Invalid environment');
  });

  test('should default to all services if none specified', () => {
    const args = parseArgs(['develop']);
    expect(args.services).toEqual(expect.arrayContaining(VALID_SERVICES.filter(s => s !== 'all')));
  });

  test('should parse single service correctly', () => {
    const args = parseArgs(['develop', 'web']);
    expect(args.services).toEqual(['web']);
  });

  test('should parse multiple services correctly', () => {
    const args = parseArgs(['develop', 'web:api']);
    expect(args.services).toEqual(['web', 'api']);
  });

  test('should handle "all" service correctly', () => {
    const args = parseArgs(['develop', 'all']);
    expect(args.services).toEqual(expect.arrayContaining(VALID_SERVICES.filter(s => s !== 'all' && s !== 'base')));
  });

  test('should handle "base" service correctly', () => {
    const args = parseArgs(['develop', 'base']);
    expect(args.services).toEqual(['resources', 'web', 'api', 'live', 'webhook']);
  });

  test('should parse skip-tests option correctly', () => {
    const args = parseArgs(['develop', 'skip-tests']);
    expect(args.skipTests).toBe(true);
  });

  test('should parse skip-unit option correctly', () => {
    const args = parseArgs(['develop', 'skip-unit']);
    expect(args.skipUnitTests).toBe(true);
  });

  test('should parse skip-e2e option correctly', () => {
    const args = parseArgs(['develop', 'skip-e2e']);
    expect(args.skipE2ETests).toBe(true);
  });

  test('should throw error for invalid option', () => {
    expect(() => parseArgs(['develop', 'skip-invalid'])).toThrow('Invalid option');
  });

  test('should handle complex command correctly', () => {
    const args = parseArgs(['stage', 'resources:web:api', 'skip-e2e']);
    expect(args.environment).toBe('stage');
    expect(args.services).toEqual(['resources', 'web', 'api']);
    expect(args.skipE2ETests).toBe(true);
  });

  test('should remove duplicate services', () => {
    const args = parseArgs(['develop', 'web:api:web']);
    expect(args.services).toEqual(['web', 'api']);
  });
});
