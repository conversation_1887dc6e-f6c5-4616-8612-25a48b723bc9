/**
 * API Test Mapping
 * 
 * This file maps service folders to test files.
 * It's used to determine which tests to run based on which services have changed.
 */

export const folderToTestMapping = {
  // Web client
  'workspace/clients/web': [
    'web-client.spec.ts',
    'auth.spec.ts'
  ],
  
  // API servers
  'workspace/servers/public-api': [
    'api.spec.ts',
    'auth.spec.ts'
  ],
  'workspace/servers/public-api-live': [
    'api-live.spec.ts',
    'websocket.spec.ts'
  ],
  'workspace/servers/public-api-webhook': [
    'api-webhook.spec.ts'
  ],
  
  // Workers
  'workspace/workers/audio-speaker-diarization@pyannote': [
    'pyannote.spec.ts',
    'audio-processing.spec.ts'
  ],
  'workspace/workers/audio-splitter@ffmpeg': [
    'ffmpeg.spec.ts',
    'audio-processing.spec.ts'
  ]
};

/**
 * Get the test files to run based on the changed folders
 * 
 * @param {string[]} changedFolders - List of changed folders
 * @returns {string[]} List of test files to run
 */
export function getTestsToRun(changedFolders: string[]): string[] {
  const testsToRun = new Set<string>();
  
  for (const folder of changedFolders) {
    const tests = folderToTestMapping[folder];
    if (tests) {
      for (const test of tests) {
        testsToRun.add(test);
      }
    }
  }
  
  return Array.from(testsToRun);
}
