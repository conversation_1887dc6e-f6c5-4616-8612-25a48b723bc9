# Divinci Deployment CLI Tests

This directory contains tests for the Divinci Deployment CLI.

## Test Structure

The tests are organized as follows:

- **Unit Tests**: Test individual modules in isolation
  - `parse-args.test.js`: Tests for the argument parser
  - `service-utils.test.js`: Tests for the service utilities
  - `run-tests.test.js`: Tests for the test runner
  - `build-deploy.test.js`: Tests for the build and deploy module

- **Integration Tests**: Test the entire CLI workflow
  - `integration.test.js`: Tests the full CLI workflow with mocked dependencies

- **Mock Data**: Contains mock data for testing
  - `config.json`: Mock configuration file
  - `web-package.json`: Mock package.json for web client
  - `api-package.json`: Mock package.json for API server
  - `api-test-mapping.ts`: Mock test mapping file

## Running Tests

To run the tests, use the following command from the `deploy/cli` directory:

```bash
npm test
```

To run tests in watch mode:

```bash
npm run test:watch
```

## Test Coverage

The tests are configured to generate coverage reports. After running the tests, you can view the coverage report in the `deploy/cli/coverage` directory.

## Adding New Tests

When adding new tests, follow these guidelines:

1. **Unit Tests**: Create a new test file for each module
2. **Integration Tests**: Add test cases to the existing integration test file
3. **Mock Data**: Add new mock data files as needed

## Mocking Strategy

The tests use Jest's mocking capabilities to mock dependencies:

- **File System**: Mock `fs` to return mock data
- **Child Process**: Mock `execSync` and `spawn` to avoid running actual commands
- **Module Dependencies**: Mock internal modules to isolate components

## Test Scenarios

The tests cover the following scenarios:

1. **Argument Parsing**: Test different command-line arguments
2. **Service Mapping**: Test mapping service names to folders
3. **Test Running**: Test running unit and E2E tests
4. **Build and Deploy**: Test building and deploying services
5. **Error Handling**: Test error scenarios and recovery

## Continuous Integration

These tests are designed to run in a CI environment. They don't require any external dependencies or actual deployment infrastructure.
