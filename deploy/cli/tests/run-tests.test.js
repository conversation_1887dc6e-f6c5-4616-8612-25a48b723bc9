/**
 * Tests for the test runner
 */

const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');
const { runTests } = require('../run-tests');

// Mock fs and child_process
jest.mock('fs', () => {
  const originalFs = jest.requireActual('fs');
  return {
    ...originalFs,
    existsSync: jest.fn(),
    readFileSync: jest.fn()
  };
});

jest.mock('child_process', () => {
  return {
    execSync: jest.fn()
  };
});

describe('run-tests', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock package.json files
    fs.readFileSync.mockImplementation((filePath, encoding) => {
      if (filePath.includes('workspace/clients/web/package.json')) {
        return fs.readFileSync(
          path.join(__dirname, 'mock-data/web-package.json'),
          'utf8'
        );
      } else if (filePath.includes('workspace/servers/public-api/package.json')) {
        return fs.readFileSync(
          path.join(__dirname, 'mock-data/api-package.json'),
          'utf8'
        );
      }
      return '{}';
    });
  });

  describe('runTests', () => {
    test('should run unit tests for services', async () => {
      // Mock file existence
      fs.existsSync.mockImplementation((filePath) => {
        return filePath.includes('package.json');
      });

      // Mock file reading
      fs.readFileSync.mockImplementation((filePath, encoding) => {
        if (filePath.includes('package.json')) {
          return JSON.stringify({
            scripts: {
              'test:unit': 'vitest run'
            }
          });
        }
        return '';
      });

      // Reset execSync mock
      execSync.mockReset();

      const result = await runTests({
        skipUnitTests: false,
        skipE2ETests: true,
        serviceFolders: [
          'workspace/clients/web',
          'workspace/servers/public-api'
        ]
      });

      expect(result).toBe(true);
      expect(execSync).toHaveBeenCalledTimes(2);
      expect(execSync).toHaveBeenCalledWith(
        'cd workspace/clients/web && npm run vitest run',
        expect.any(Object)
      );
      expect(execSync).toHaveBeenCalledWith(
        'cd workspace/servers/public-api && npm run vitest run',
        expect.any(Object)
      );
    });

    test('should skip unit tests if specified', async () => {
      const result = await runTests({
        skipUnitTests: true,
        skipE2ETests: true,
        serviceFolders: [
          'workspace/clients/web',
          'workspace/servers/public-api'
        ]
      });

      expect(result).toBe(true);
      expect(execSync).not.toHaveBeenCalled();
    });

    test('should run E2E tests if needed', async () => {
      // Mock file existence
      fs.existsSync.mockImplementation((filePath) => {
        return true;
      });

      const result = await runTests({
        skipUnitTests: true,
        skipE2ETests: false,
        serviceFolders: [
          'workspace/clients/web',
          'workspace/servers/public-api'
        ]
      });

      expect(result).toBe(true);
      expect(execSync).toHaveBeenCalledWith(
        'cd workspace/clients/tests && pnpm test',
        expect.objectContaining({
          env: expect.objectContaining({
            CHANGED_FOLDERS: 'workspace/clients/web,workspace/servers/public-api'
          })
        })
      );
    });

    test('should skip E2E tests if specified', async () => {
      const result = await runTests({
        skipUnitTests: true,
        skipE2ETests: true,
        serviceFolders: [
          'workspace/clients/web',
          'workspace/servers/public-api'
        ]
      });

      expect(result).toBe(true);
      expect(execSync).not.toHaveBeenCalled();
    });

    test('should return false if unit tests fail', async () => {
      // Mock file existence
      fs.existsSync.mockImplementation((filePath) => {
        return filePath.includes('package.json');
      });

      // Mock file reading
      fs.readFileSync.mockImplementation((filePath, encoding) => {
        if (filePath.includes('package.json')) {
          return JSON.stringify({
            scripts: {
              'test:unit': 'vitest run'
            }
          });
        }
        return '';
      });

      // Reset execSync mock
      execSync.mockReset();

      // Mock test failure for all calls
      execSync.mockImplementation(() => {
        throw new Error('Tests failed');
      });

      const result = await runTests({
        skipUnitTests: false,
        skipE2ETests: true,
        serviceFolders: [
          'workspace/clients/web'
        ]
      });

      expect(result).toBe(false);
    });

    test('should return false if E2E tests fail', async () => {
      // Mock file existence
      fs.existsSync.mockImplementation((filePath) => {
        return true;
      });

      // Reset execSync mock
      execSync.mockReset();

      // Mock test failure for all calls
      execSync.mockImplementation(() => {
        throw new Error('Tests failed');
      });

      const result = await runTests({
        skipUnitTests: true,
        skipE2ETests: false,
        serviceFolders: [
          'workspace/clients/web'
        ]
      });

      expect(result).toBe(false);
    });
  });
});
