/**
 * Tests for the service utilities
 */

const path = require('path');
const fs = require('fs');
const {
  getServiceFolders,
  hasServiceChanges,
  hasResourcesChanges,
  getDockerImageName,
  getResourcesImageName
} = require('../service-utils');

// Mock the config.json file
jest.mock('fs', () => {
  const originalFs = jest.requireActual('fs');
  return {
    ...originalFs,
    readFileSync: jest.fn(),
    existsSync: jest.fn().mockReturnValue(true)
  };
});

// Mock child_process
jest.mock('child_process', () => {
  return {
    execSync: jest.fn()
  };
});

const { execSync } = require('child_process');

describe('service-utils', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock config.json
    const mockConfigPath = path.join(__dirname, 'mock-data/config.json');
    const originalReadFileSync = jest.requireActual('fs').readFileSync;
    const mockConfig = JSON.parse(originalReadFileSync(mockConfigPath, 'utf8'));

    fs.readFileSync.mockImplementation((filePath, encoding) => {
      if (filePath.endsWith('config.json')) {
        return JSON.stringify(mockConfig);
      }
      return '';
    });
  });

  describe('getServiceFolders', () => {
    test('should map service names to folders', async () => {
      const folders = await getServiceFolders(['web', 'api']);
      expect(folders).toEqual([
        'workspace/clients/web',
        'workspace/servers/public-api'
      ]);
    });

    test('should handle resources service', async () => {
      const folders = await getServiceFolders(['resources']);
      expect(folders).toEqual(['workspace/resources']);
    });

    test('should throw error for unknown service', async () => {
      await expect(getServiceFolders(['unknown'])).rejects.toThrow('Service not found');
    });
  });

  describe('hasServiceChanges', () => {
    test('should return true if service has changes', async () => {
      execSync.mockReturnValue('file1.js\nfile2.js');
      const result = await hasServiceChanges('workspace/clients/web');
      expect(result).toBe(true);
      expect(execSync).toHaveBeenCalledWith(
        'git diff --name-only HEAD~1 HEAD -- workspace/clients/web'
      );
    });

    test('should return false if service has no changes', async () => {
      execSync.mockReturnValue('');
      const result = await hasServiceChanges('workspace/clients/web');
      expect(result).toBe(false);
    });

    test('should return true if git command fails', async () => {
      execSync.mockImplementation(() => {
        throw new Error('Git command failed');
      });
      const result = await hasServiceChanges('workspace/clients/web');
      expect(result).toBe(true);
    });
  });

  describe('hasResourcesChanges', () => {
    test('should check for changes in resources folder', async () => {
      execSync.mockReturnValue('file1.js\nfile2.js');
      const result = await hasResourcesChanges();
      expect(result).toBe(true);
      expect(execSync).toHaveBeenCalledWith(
        'git diff --name-only HEAD~1 HEAD -- workspace/resources'
      );
    });
  });

  describe('getDockerImageName', () => {
    test('should return correct image name for service', async () => {
      const imageName = await getDockerImageName('workspace/clients/web', 'develop');
      expect(imageName).toBe('us-docker.pkg.dev/mock-docker-org/gcr.io/divinci-develop-web-client:develop');
    });

    test('should throw error for unknown service', async () => {
      await expect(getDockerImageName('unknown', 'develop')).rejects.toThrow('Service not found');
    });
  });

  describe('getResourcesImageName', () => {
    test('should return correct image name for resources', async () => {
      const imageName = await getResourcesImageName('develop');
      expect(imageName).toBe('us-docker.pkg.dev/mock-docker-org/gcr.io/divinci-resources:develop');
    });
  });
});
