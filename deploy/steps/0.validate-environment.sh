#!/bin/bash -x
set -e

# shellcheck disable=SC2016
: '
<PERSON>ript to validate environment configuration before deployment.

Usage:
  ./0.validate-environment.sh <environment>

Arguments:
  <environment>   The target environment (develop, stage, prod)

Environment Variables Required:
  # - GOOGLE_CREDENTIALS
  - GCP_SA_KEY
  - WEB_CLIENT_HOST

Examples:
  ./0.validate-environment.sh stage
'

ENVIRONMENT=$1

if [[ -z "$ENVIRONMENT" ]]; then
    echo "❌ Error: Environment argument is required"
    exit 1
fi

# Validate required environment variables
required_vars=(
  # "GOOGLE_CREDENTIALS"
  "GCP_SA_KEY"
  "WEB_CLIENT_HOST"
)
for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "❌ Error: Required environment variable $var is not set"
        exit 1
    fi
done

# Stage-specific validations
if [[ "$ENVIRONMENT" == "stage" ]]; then
    # Validate Google Cloud configuration
    if ! gcloud projects describe openai-api-4375643 &>/dev/null; then
        echo "❌ Error: Cannot access GCP project"
        exit 1
    fi

    # Validate required resources exist
    # if ! gcloud run services list --platform managed --region us-central1 | grep -q "divinci-serverless-vpc-1"; then
    #     echo "❌ Error: Required VPC connector not found"
    #     exit 1
    # fi

    # Validate domain configuration
    # if ! host chat.stage.divinci.app &>/dev/null; then
    #     echo "⚠️ Warning: Domain chat.stage.divinci.app may not be properly configured"
    # fi

    # Validate resource quotas
    QUOTA_CHECK=$(gcloud compute project-info describe --format="value(quotas.metric,quotas.limit)")
    if ! echo "$QUOTA_CHECK" | grep -q "CPUS.*100"; then
        echo "⚠️ Warning: CPU quota might be insufficient"
    fi

    # Validate network access
    # if ! curl -s -o /dev/null -w "%{http_code}" https://chat.stage.divinci.app; then
    #     echo "⚠️ Warning: Cannot reach staging endpoint"
    # fi

    # Validate database connection (if applicable)
    # if [[ -n "$DATABASE_URL" ]]; then
    #     if ! pg_isready -d "$DATABASE_URL"; then
    #         echo "❌ Error: Cannot connect to database"
    #         exit 1
    #     fi
    # fi

    # Validate minimum replica configuration
    MIN_REPLICAS=$(yq e '.spec.template.metadata.annotations."autoscaling.knative.dev/minScale"' deploy/service-template.yaml)
    if [[ "$MIN_REPLICAS" -lt 1 ]]; then
        echo "❌ Error: Minimum replicas should be at least 1 for staging"
        exit 1
    fi

    # Check for required staging labels
    # if ! grep -q 'environment: stage' deploy/service-template.yaml; then
    #     echo "❌ Error: Missing required staging labels in service template"
    #     exit 1
    # fi
fi

echo "✅ Environment validation passed for $ENVIRONMENT"
