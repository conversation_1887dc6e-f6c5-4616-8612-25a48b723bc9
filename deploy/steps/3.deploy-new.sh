#!/bin/bash -x
set -e

# shellcheck disable=SC2016
: '
📖 Script to deploy services to Google Cloud Run using YAML files. This script uses `service-info.js`
to obtain necessary service configuration information, including:
  - Container names and base configuration
  - Environment-specific resource limits (CPU and memory)
  - Service visibility settings
  - Priority and deployment order

▶️ Usage:
  ./3.deploy-new.sh <changed_folders_csv> <tag>

🎛️ Arguments:
  <changed_folders_csv>   A comma-separated list of folders with changes.
  <tag>                   The environment tag (e.g., develop, stage, prod). If not provided, defaults to "latest".

🌎 Environment:
  - Requires `service-info.js` located in "deploy/util/" to fetch service details.
  - Uses `gcloud` to deploy services to Google Cloud Run.
  - Expects `gcp-cloud-run-generate-yaml.sh` script for YAML file generation.

🔢 Steps:
  1. **Generate YAML File**: Calls `generate-yaml.sh` to create a YAML file for each service, including:
     - Basic service configuration
     - Environment-specific resource limits
     - IAM and security settings
  2. **Deploy Service**: Uses `gcloud run services replace` to deploy the service using the generated YAML file.
  3. **Configure Access**: Sets public/private access based on service configuration.
  4. **Parallel Processing**: Deploys services in parallel based on their priority.

🔗 Dependencies:
  - Node.js: To execute `service-info.js`.
  - Google Cloud SDK (`gcloud`): To deploy services to Cloud Run.

📓 Notes:
  - Ensure `gcloud` is authenticated with the correct project and region before running this script.
  - Resource limits are automatically configured based on the deployment environment (develop/stage/prod).
  - The script includes parallel processing to deploy services based on their priority.

💻 Examples:
  # Deploy changed services for the "develop" environment
  ./3.deploy-new.sh "workspace/server-api,workspace/web-client" develop
'

echo "3️⃣ 🚚 ENTERING DEPLOY STEP: deploy/steps/3.deploy-new.sh"

CHANGED_FOLDERS_CSV=$1
IFS=',' read -ra CHANGED_FOLDERS <<< "$CHANGED_FOLDERS_CSV"
tag=$2

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
ROOT_DIR=$(dirname "$(dirname "$SCRIPT_DIR")")

# Call service-info.js to get global configuration values
DOCKER_ORG=$(node "$SCRIPT_DIR/../util/service-info.js" getConfigGlobal docker_org)
GOOGLE_REGION=$(node "$SCRIPT_DIR/../util/service-info.js" getConfigGlobal google_region)

CONFIG_OUT_FOLDER="$ROOT_DIR/hidden/google-deploy"
mkdir -p "$CONFIG_OUT_FOLDER"

# Verify certificates before deployment
echo "🔐 Verifying SSL/TLS certificates before deployment..."
VERIFY_SCRIPT="$ROOT_DIR/deploy/scripts/verify-cert-before-deploy.sh"

# Make the script executable if it exists
if [ -f "$VERIFY_SCRIPT" ]; then
  chmod +x "$VERIFY_SCRIPT"

  # Check if running in CI/CD environment
  CI_ENV=${CI:-false}

  # Run certificate verification
  if ! "$VERIFY_SCRIPT" "$tag" "$CI_ENV"; then
    echo "❌ Certificate verification failed. This may cause mTLS connection failures."
    echo "   Please fix the certificate issues before continuing deployment."

    if [ "$CI_ENV" == "true" ]; then
      # In CI/CD environment, continue but log a warning
      echo "⚠️ CI/CD environment detected. Continuing deployment despite certificate issues."
      echo "   This will be reported in the logs, and mTLS connections may fail."
    else
      # In interactive environment, prompt for confirmation
      read -p "Continue deployment despite certificate issues? (y/n): " -n 1 -r
      echo
      if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Aborting deployment due to certificate issues."
        exit 1
      fi

      echo "⚠️ Continuing deployment despite certificate issues. mTLS connections may fail."
    fi
  else
    echo "✅ Certificate verification passed. Proceeding with deployment."
  fi
else
  echo "⚠️ Certificate verification script not found at $VERIFY_SCRIPT"
  echo "   Skipping certificate verification. This may lead to mTLS connection issues."
fi

function deployServiceImage {
  local folder=$1
  local container_name="$(node "$SCRIPT_DIR/../util/service-info.js" getServiceContainerName "$folder" "$tag")"
  local stage=$tag

  GENERATE_YAML_SCRIPT="$SCRIPT_DIR/../util/gcp-cloud-run-generate-yaml.sh"

  if [ ! -x "$GENERATE_YAML_SCRIPT" ]; then
    chmod +x "$GENERATE_YAML_SCRIPT"
  fi

  if [ ! -f "$GENERATE_YAML_SCRIPT" ]; then
    echo "❌ generate-yaml.sh script not found at $GENERATE_YAML_SCRIPT"
    return
  fi

  local output_file="$CONFIG_OUT_FOLDER/${container_name}.yaml"

  "$GENERATE_YAML_SCRIPT" "$folder" "$stage" "$output_file"

  if [ ! -f "$output_file" ]; then
    echo "❌ Failed to generate YAML file. Skipping deployment for $container_name."
    return
  fi

  echo "🔵 Deploying $container_name using YAML file: $output_file..."

  # Deploy the service using the generated YAML file
  gcloud run services replace "$output_file" --region "$GOOGLE_REGION"

  echo "🟢 $container_name deployed successfully using $output_file!"
}

function toggleServicePublicAccess {
  local folder=$1
  local container_name="$(node "$SCRIPT_DIR/../util/service-info.js" getServiceContainerName "$folder" "$tag")"
  local currently_public=$(node "$SCRIPT_DIR/../util/get-iam-policy.js" "$container_name")
  local service_info=($(node "$SCRIPT_DIR/../util/service-info.js" getServiceInfoByFolder "$folder"))
  local config_is_public=${service_info[4]}

  if [ "$currently_public" == "$config_is_public" ]; then
    echo "🟢 $container_name public access already set to $config_is_public"
    return
  elif [ "$config_is_public" == "true" ]; then
    echo "🔵 Ensuring $container_name can be evoked publicly..."
    gcloud run services add-iam-policy-binding "$container_name" \
    --member="allUsers" \
    --role="roles/run.invoker" \
    --region="us-central1"
  else
    echo "🔵 Ensuring $container_name can not be evoked publicly..."
    cloud run services remove-iam-policy-binding "$container_name" \
    --member="allUsers" \
    --role="roles/run.invoker" \
    --region="us-central1"
  fi

  echo "🟢 $container_name public access updated"
}

function updateService {
  local folder=$1
  deployServiceImage "$folder"
  toggleServicePublicAccess "$folder"
}


CHANGED_FOLDERS_LENGTH=${#CHANGED_FOLDERS[@]}
maxPriority=$(node "$SCRIPT_DIR/../util/service-info.js" getMaximumPriority)
for ((currentPriority=0; currentPriority<=maxPriority; currentPriority++)); do
  pids=()
  for ((i=0; i<CHANGED_FOLDERS_LENGTH; i++)); do
    # Check priority using service-info.js
    if [[ "$(node "$SCRIPT_DIR/../util/service-info.js" isCurrentPriority "${CHANGED_FOLDERS[$i]}" "$currentPriority")" == "true" ]]; then
        updateService "${CHANGED_FOLDERS[$i]}" &
        pids+=($!)
    fi
  done
  for pid in ${pids[*]}; do
    wait $pid || { echo "❌ Deployment process with PID $pid failed."; exit 1; }
  done
done
