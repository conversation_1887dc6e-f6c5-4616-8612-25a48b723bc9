#!/bin/bash -x

# shellcheck disable=SC2016
: '
<PERSON><PERSON><PERSON> to identify folders with changes in a Git repository for Pull Requests. This script determines which services
and dependencies need to be rebuilt and redeployed based on the changes in the PR.

Usage:
  ./changed-git-folders.sh

Requirements:
  - GitHub CLI (gh) must be installed and authenticated
  - <PERSON><PERSON><PERSON> must be run in a GitHub repository context
  - Appropriate GitHub permissions to access PR information

Environment:
  - The script calls a Node.js script `service-info.js` to retrieve service container folders
  - Expects `service-info.js` to be located in the "deploy/util/" directory relative to this script
  - Requires `git` and `gh` CLI to be installed
  - The current directory must be a Git repository

Steps:
  1. **Detect PR Context**: Automatically detects if running in a PR context using GitHub CLI
  2. **Get Changed Services**: Determines which services have changed or use a modified dependency by comparing
     the PR base and head commits
  3. **Check Dependencies**: Identifies changes in certain dependencies and associates them with the services that use them
  4. **Filter Changed Folders**: Identifies which folders in the workspace have changes, excluding those where only
     `pnpm-lock.yaml` has changed
  5. **Outputs**: Outputs a comma-separated list of changed service folders to be used in subsequent scripts

Dependencies:
  - `git`: To identify changed files between commits
  - `gh`: GitHub CLI for PR information
  - `node`: To execute `service-info.js` for retrieving service information

Fallback Behavior:
  - If no PR context is found, falls back to comparing the last commit (HEAD^ to HEAD)
  - Can still accept explicit commit references as arguments

Examples:
  # Determine changed folders in current PR context
  ./1.changed-git-folders.sh

  # Determine changed folders between specific commits (fallback usage)
  ./1.changed-git-folders.sh abc123def456 HEAD
'

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
ROOT_DIR=$(dirname "$(dirname "$SCRIPT_DIR")")

# Function to check if gh CLI is installed
check_gh_cli() {
    if ! command -v gh &> /dev/null; then
        echo "⚠️ GitHub CLI (gh) is not installed. Falling back to default commit comparison."
        BEFORE_COMMIT="HEAD^"
        AFTER_COMMIT="HEAD"
        return 1
    fi
    return 0
}

# Function to get PR base and head commits
get_pr_commits() {
    # First check if this is a workflow dispatch event
    if [ "$GITHUB_EVENT_NAME" = "workflow_dispatch" ]; then
        if [ -n "$INPUT_PR_NUMBER" ]; then
            PR_NUMBER="$INPUT_PR_NUMBER"
            echo "Using PR number from workflow dispatch: $PR_NUMBER"
        else
            echo "No PR number provided in workflow dispatch. Using default comparison..."
            BEFORE_COMMIT="HEAD^"
            AFTER_COMMIT="HEAD"
            return
        fi
    # Then check if we're in a PR event context
    elif [ -n "$GITHUB_EVENT_PATH" ] && [ -f "$GITHUB_EVENT_PATH" ]; then
        PR_NUMBER=$(jq -r ".pull_request.number // empty" "$GITHUB_EVENT_PATH")
        if [ -z "$PR_NUMBER" ]; then
            echo "No PR number found in event path. Using default comparison..."
            BEFORE_COMMIT="HEAD^"
            AFTER_COMMIT="HEAD"
            return
        fi
    else
        # Try to detect PR number from current branch
        PR_NUMBER=$(gh pr view --json number -q .number 2>/dev/null || true)
    fi

    if [ -z "$PR_NUMBER" ]; then
        echo "No pull request context found. Using default comparison..."
        BEFORE_COMMIT="HEAD^"
        AFTER_COMMIT="HEAD"
        return
    fi

    echo "Processing Pull Request #$PR_NUMBER"

    # Get base and head commits from PR
    if ! BASE_COMMIT=$(gh pr view "$PR_NUMBER" --json baseRefOid --jq .baseRefOid 2>/dev/null); then
        echo "Failed to get base commit. Using default comparison..."
        BEFORE_COMMIT="HEAD^"
        AFTER_COMMIT="HEAD"
        return
    fi

    if ! HEAD_COMMIT=$(gh pr view "$PR_NUMBER" --json headRefOid --jq .headRefOid 2>/dev/null); then
        echo "Failed to get head commit. Using default comparison..."
        BEFORE_COMMIT="HEAD^"
        AFTER_COMMIT="HEAD"
        return
    fi

    BEFORE_COMMIT="$BASE_COMMIT"
    AFTER_COMMIT="$HEAD_COMMIT"
}

# Main execution starts here
check_gh_cli || true
get_pr_commits

# Rest of your existing script remains the same, but use these variables:
# - $BEFORE_COMMIT: The base commit of the PR
# - $AFTER_COMMIT: The head commit of the PR

# Ensure we have valid commits to compare
if [ -z "$BEFORE_COMMIT" ] || [ -z "$AFTER_COMMIT" ]; then
    echo "⚠️ Could not determine commits to compare. Using default comparison..."
    BEFORE_COMMIT="HEAD^"
    AFTER_COMMIT="HEAD"
fi

echo "🔍 Comparing commits: $BEFORE_COMMIT..$AFTER_COMMIT"

# Call service-info.js to get service container folders
SERVICE_CONTAINER_FOLDERS_STR=$(node "$SCRIPT_DIR/../util/service-info.js" getServiceContainerFolders)
if [[ -z "$SERVICE_CONTAINER_FOLDERS_STR" ]]; then
  echo "No Services to update."
  exit 1
fi

# Convert SERVICE_CONTAINER_FOLDERS to an array
# shellcheck disable=SC2206
SERVICE_CONTAINER_FOLDERS_ARRAY=($SERVICE_CONTAINER_FOLDERS_STR)

CHANGED_RESOURCE_FOLDERS=()
CHANGED_SERVICE_FOLDERS=()

# ======================== Check if a resource is used by a service changed ========================
# If the resource has changed, then the service should be updated as well
function hasDependency {
  local dependencyName=$1
  local filepath=$2

  if [ ! -f "$filepath" ]; then
    echo 0
    return
  fi

  # Search each line for the two patterns
  while IFS= read -r line; do
    if [[ $line == *"@divinci-ai/$dependencyName"* && $line == *"../../resources/$dependencyName"* ]]; then
      echo "1"
      return 0
    fi
    if [[ $line == *"@divinci-ai/$dependencyName"* && $line == *"../$dependencyName"* ]]; then
      echo "1"
      return 0
    fi
  done < "$filepath"

  echo "0"
  return 1
}

deps=("utils" "models" "server-utils" "server-globals" "server-tools" "server-models" "server-permissions")
doneDeps=()

function checkDependencyUsedByFolder {
  local dependencyName=$1
  local changedFile=$2  # The specific file that changed

  # Skip if already processed
  if echo "${doneDeps[@]}" | grep -w -q "$dependencyName"; then
    return
  fi

  # Extract the module name from the changed file path
  local moduleType=$(echo "$changedFile" | grep -o '/src/[^/]*/' | cut -d'/' -f3)
  echo "📦 Checking dependency: $dependencyName, changed in module: $moduleType"

  # Check each service
  for folder in "${SERVICE_CONTAINER_FOLDERS_ARRAY[@]}"; do
    # Only check if the service actually uses this dependency
    shouldInstall=$(hasDependency "$dependencyName" "$ROOT_DIR/$folder/package.json")
    if [ "$shouldInstall" = "1" ]; then
      # Add additional check for the specific module that changed
      if grep -q "import.*from.*$moduleType" "$ROOT_DIR/$folder/src" 2>/dev/null; then
        echo "✅ Adding $folder (uses $moduleType from $dependencyName)"
        CHANGED_SERVICE_FOLDERS+=("$folder")
      else
        echo "⏭️ Skipping $folder (doesn't use $moduleType from $dependencyName)"
      fi
    fi
  done

  doneDeps+=("$dependencyName")
}

for topDep in "${deps[@]}"; do
  # Get the actual changed files for this dependency
  dep_changes=$(git diff --name-only "$BEFORE_COMMIT" "$AFTER_COMMIT" -- "$ROOT_DIR/workspace/resources/$topDep")

  # Skip if no changes or only lock file changes
  if [[ -z "$dep_changes" ]] || [[ "$dep_changes" =~ ^.*pnpm-lock\.yaml$ ]]; then
    continue
  fi

  echo "📦 Changes detected in $topDep:"
  echo "$dep_changes"

  # Pass the changed files to the check function
  checkDependencyUsedByFolder "$topDep" "$dep_changes"

  # Check dependencies of dependencies more carefully
  for dep in "${deps[@]}"; do
    if [[ "$topDep" == "$dep" ]]; then
      continue
    fi

    # Only check if the dependency actually uses the changed files
    shouldInstall=$(hasDependency "$topDep" "$ROOT_DIR/workspace/resources/$dep/package.json")
    if [ "$shouldInstall" = "1" ]; then
      # Verify that the dependency uses the changed parts
      checkDependencyUsedByFolder "$dep" "$dep_changes"
    fi
  done
done

# Remove duplicates
CHANGED_SERVICE_FOLDERS=($(echo "${CHANGED_SERVICE_FOLDERS[@]}" | tr ' ' '\n' | sort -u | tr '\n' ' '))

# ======================== Check if a service has changed ========================

# Get the list of changed folders, including resources paths
CHANGED_FOLDERS=$(git diff --name-only "$BEFORE_COMMIT" "$AFTER_COMMIT" \
    | grep -E "workspace/" \
    | awk -F/ '{
        if ($3 == "resources") {
            # For resources paths, include the full path
            print $1"/"$2"/"$3"/"$4
        } else {
            # For other paths, keep the original behavior
            print $1"/"$2"/"$3
        }
    }' \
    | uniq)

# Filter out folders where only `pnpm-lock.yaml` has changed
# In the main loop where we process changed folders:
for folder in $CHANGED_FOLDERS; do
    if [[ "$folder" == *"/resources/"* ]]; then
        # Add resource folders to their own array
        CHANGED_RESOURCE_FOLDERS+=("$folder")
        echo "📦 Found resource change in: $folder"
    elif echo "${SERVICE_CONTAINER_FOLDERS_ARRAY[@]}" | grep -w -q "$folder"; then
        # For service folders, keep the original behavior
        if git diff --name-only "$BEFORE_COMMIT" "$AFTER_COMMIT" -- "$ROOT_DIR/$folder" | grep -qv "pnpm-lock.yaml"; then
            CHANGED_SERVICE_FOLDERS+=("$folder")
        fi
    else
        echo "$folder not in services"
    fi
done

# Remove duplicates
CHANGED_SERVICE_FOLDERS=($(echo "${CHANGED_SERVICE_FOLDERS[@]}" | tr ' ' '\n' | sort -u | tr '\n' ' '))
CHANGED_RESOURCE_FOLDERS=($(echo "${CHANGED_RESOURCE_FOLDERS[@]}" | tr ' ' '\n' | sort -u | tr '\n' ' '))

# Combine the arrays and create the comma-separated string
CHANGED_FOLDERS_CSV=$(IFS=, ; echo "${CHANGED_RESOURCE_FOLDERS[*]} ${CHANGED_SERVICE_FOLDERS[*]}" | tr -s ' ' ',' | sed 's/^,//;s/,$//')

# Exporting the filtered folders
echo "CHANGED_FOLDERS=$CHANGED_FOLDERS_CSV"  # Changed from CHANGED_SERVICE_FOLDERS_CSV
