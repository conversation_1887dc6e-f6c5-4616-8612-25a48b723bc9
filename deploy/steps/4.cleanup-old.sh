#!/bin/bash -x
set -e

: '
<PERSON><PERSON><PERSON> to clean up old Google Cloud Run services that are no longer defined in the current configuration. This script uses
`service-info.js` to retrieve information about existing services and compare them with the ones currently running in Cloud Run.

Usage:
  ./4.cleanup-old.sh

Environment:
  - Requires `service-info.js` located in "deploy/util/" to fetch service details.
  - Uses `gcloud` to list and delete services in Google Cloud Run.

Steps:
  1. **List Cloud Run Services**: Uses `gcloud` to get a list of all services currently deployed in the specified region.
  2. **Check Against Current Configuration**: Uses `service-info.js` to check if the service exists in the configuration.
  3. **Cleanup**: Deletes any services not present in the current configuration.

Dependencies:
  - Node.js: To execute `service-info.js`.
  - Google Cloud SDK (`gcloud`): To list and delete services in Cloud Run.

Notes:
  - Ensure `gcloud` is authenticated with the correct project and region before running this script.
  - Use with caution as it will delete services not found in the current configuration.

Examples:
  # Clean up old services in the configured Google Cloud region
  ./4.cleanup-old.sh
'

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")

# Call service-info.js to get global configuration values
GOOGLE_REGION=$(node "$SCRIPT_DIR/../util/service-info.js" getConfigGlobal google_region)

# List all Cloud Run services in the specified region
SERVICE_EXISTS=$(gcloud run services list --platform managed --region "$GOOGLE_REGION" --format="value(metadata.name)")

if [ -z "$SERVICE_EXISTS" ]; then
  echo "⚠️ No services to clean up."
  exit 0
fi

for service in $SERVICE_EXISTS; do
  # Check if the service exists in the current configuration using service-info.js
  exists=$(node "$SCRIPT_DIR/../util/service-info.js" getServiceInfoByName "$service")

  if [ -z "$exists" ]; then
    echo "🔵 Cleaning up $service..."
    # Delete the service using gcloud
    printf 'yes' | gcloud run services delete "$service" --platform managed --region "$GOOGLE_REGION"
    echo "🟢 $service cleaned up successfully!"
  fi
done
