apiVersion: v1
kind: ConfigMap
metadata:
  name: mtls-config
  labels:
    app.kubernetes.io/component: mtls
    app.kubernetes.io/part-of: divinci
data:
  MTLS_ENABLED: "true"
---
apiVersion: v1
kind: Secret
metadata:
  name: mtls-ca-cert
type: Opaque
data:
  # This is a placeholder. The actual certificate should be provided during deployment.
  # Use: kubectl create secret generic mtls-ca-cert --from-file=ca.crt=/path/to/ca.crt
  ca.crt: ""
---
apiVersion: v1
kind: Secret
metadata:
  name: mtls-public-api-certs
type: Opaque
data:
  # These are placeholders. The actual certificates should be provided during deployment.
  # Use: kubectl create secret generic mtls-public-api-certs --from-file=client.crt=/path/to/client.crt --from-file=client.key=/path/to/client.key
  client.crt: ""
  client.key: ""
---
apiVersion: v1
kind: Secret
metadata:
  name: mtls-ffmpeg-certs
type: Opaque
data:
  # These are placeholders. The actual certificates should be provided during deployment.
  # Use: kubectl create secret generic mtls-ffmpeg-certs --from-file=server.crt=/path/to/server.crt --from-file=server.key=/path/to/server.key
  server.crt: ""
  server.key: ""
---
apiVersion: v1
kind: Secret
metadata:
  name: mtls-open-parse-certs
type: Opaque
data:
  # These are placeholders. The actual certificates should be provided during deployment.
  # Use: kubectl create secret generic mtls-open-parse-certs --from-file=server.crt=/path/to/server.crt --from-file=server.key=/path/to/server.key
  server.crt: ""
  server.key: ""
---
apiVersion: v1
kind: Secret
metadata:
  name: mtls-pyannote-certs
type: Opaque
data:
  # These are placeholders. The actual certificates should be provided during deployment.
  # Use: kubectl create secret generic mtls-pyannote-certs --from-file=server.crt=/path/to/server.crt --from-file=server.key=/path/to/server.key
  server.crt: ""
  server.key: ""
