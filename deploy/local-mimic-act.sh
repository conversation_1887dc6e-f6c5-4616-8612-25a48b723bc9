#!/bin/bash -x

# shellcheck disable=SC2016
: '
📓 Script to mimic the GitHub Actions workflow locally using Bash commands. This script automates the process of:
  1. Setting up the build and deploy environment.
  2. Identifying changed services.
  3. Configuring Google Cloud authentication.
  4. Building and deploying Docker images for changed services.

▶️ Usage:
  `./local-mimic-act.sh [BEFORE_COMMIT] [AFTER_COMMIT]`

  # `-b` skips the deploy step
  `GITHUB_BASE_REF=develop ./deploy/local-mimic-act.sh -b`

🧾 Arguments:
  BEFORE_COMMIT   The commit hash to compare changes from (default: HEAD^ if not provided).
  AFTER_COMMIT    The commit hash to compare changes to (default: HEAD if not provided).

🌎 Environment:
  - The script determines the environment (develop, stage, prod) based on the `GITHUB_BASE_REF` variable.
  - It uses environment-specific `.env` files located in the "private-keys/<environment>/" directory for deployment.
  - Requires `gcloud` to be installed and authenticated.

🔗 Dependencies:
  - `git`: To handle source control and identify changed files.
  - `docker`: To build and push Docker images.
  - `gcloud`: To authenticate and deploy to Google Cloud services.

🔢 Steps:
  1. **Environment Setup**: Determines the environment (develop, stage, prod) based on `GITHUB_BASE_REF` and sets related environment variables.
  2. **Get Changed Services**: Uses a script to identify which services have changed between the specified commits.
  3. **Google Cloud Authentication**: Ensures that `gcloud` is authenticated and configures Docker to use the `gcloud` credentials.
  4. **Build Docker Images**: Builds Docker images for the changed services using a specified environment.
  5. **Deploy Services**: Deploys the changed Docker images to Google Cloud Run.

🗒️ Notes:
  - Before running this script, ensure that `gcloud` is authenticated (`gcloud auth login`).
  - This script assumes it is being run from within a Git repository with all necessary submodules initialized.
  - The script checks for required tools (`git`, `docker`) and exits if any are missing.

ℹ️ Examples:
  # Run with default commit range (HEAD^ to HEAD)
  ./local-mimic-act.sh

  # Run with a specific commit range
  ./local-mimic-act.sh abc123def456 HEAD
'

set -e
set -o pipefail

echo "🏁 Starting local mimic act deploy script: "

BUILD_ONLY=false
while getopts "b" opt; do
  case $opt in
    b) BUILD_ONLY=true ;;
    *) echo "Invalid option: -$OPTARG" >&2 ;;
  esac
done
shift $((OPTIND -1))

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
ROOT_DIR=$(dirname "$SCRIPT_DIR")

# ======================== Step 1: Ensure required tools are installed ========================
command -v git >/dev/null 2>&1 || { echo >&2 "git is not installed. Aborting."; exit 1; }
command -v docker >/dev/null 2>&1 || { echo >&2 "docker is not installed. Aborting."; exit 1; }
# command -v jq >/dev/null 2>&1 || { echo >&2 "jq is not installed. Aborting."; exit 1; }

# Ensure submodules are updated
git submodule update --init --recursive

# ======================== Step 2: Set up build/deploy environment ========================
echo "Current Github $GITHUB_BASE_REF"
if [[ -z "$GITHUB_BASE_REF" ]]; then
  echo "No GITHUB_BASE_REF set, defaulting to \"develop\""
  GITHUB_BASE_REF="develop"
fi

if [[ "$GITHUB_BASE_REF" == "develop" ]]; then
  WEB_CLIENT_HOST="chat.dev.divinci.app"
  ENV_URL=https://$WEB_CLIENT_HOST
  # ENV_URL=https://chat.dev.divinci.app
  ENVIRONMENT=develop
  NODE_ENV=development
elif [[ "$GITHUB_BASE_REF" == "stage" ]]; then
  WEB_CLIENT_HOST="chat.stage.divinci.app"
  ENV_URL=https://$WEB_CLIENT_HOST
  # ENV_URL=https://chat.stage.divinci.app
  ENVIRONMENT=stage
  NODE_ENV=production
elif [[ "$GITHUB_BASE_REF" == "main" ]]; then
  WEB_CLIENT_HOST="chat.divinci.app"
  ENV_URL=https://$WEB_CLIENT_HOST
  # ENV_URL=https://chat.divinci.app
  ENVIRONMENT=prod
  NODE_ENV=production
else
  echo "Unknown branch, skipping URL set"
  exit 1
fi
echo "✅ Success: github base_ref is $GITHUB_BASE_REF"

# ======================== Step 3: Get changed services ========================
echo "Get changed services"
BEFORE_COMMIT=${1:-"HEAD^"}
AFTER_COMMIT=${2:-"HEAD"}

CHANGED_FOLDER_OUTPUT=$(bash "$SCRIPT_DIR/steps/1.changed-git-folders.sh" "$BEFORE_COMMIT" "$AFTER_COMMIT" | grep 'CHANGED_FOLDERS=')

eval "$CHANGED_FOLDER_OUTPUT"
if [[ -z "$CHANGED_FOLDERS" ]]; then
  echo "No folders changed. Exiting."
  exit 1
fi

echo "CHANGED_FOLDERS=$CHANGED_FOLDERS"

# ======================== Step 4: Configure gcloud auth ========================
echo "Configure gcloud auth"
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)"; then
  echo "You need to authenticate to Google Cloud before running this script."
  exit 1
fi
gcloud auth configure-docker us-docker.pkg.dev

# ======================== Step 5: Build Changed Images ========================
echo "Build changed images"
bash "$SCRIPT_DIR/steps/2.docker-build.sh" "$CHANGED_FOLDERS" "$ENVIRONMENT" "$WEB_CLIENT_HOST" "$DD_API_KEY" "$DD_API_ID"

# ======================== Step 6: Deploy Changed Images ========================
if [ "$BUILD_ONLY" = true ]; then
  echo "Build only flag set, skipping deployment"
  exit "✅ Success, build-skipped. "
fi

echo "Deploying changed images"
bash "$SCRIPT_DIR/steps/3.deploy-new.sh" "$CHANGED_FOLDERS" "$ENVIRONMENT"

# ======================== Done ========================
echo "✅ Success"
