{"globals": {"docker_org": "openai-api-4375643", "google_region": "us-central1", "name_prefix": "divinci"}, "resources": {"develop": {"default": {"cpu": "2000m", "memory": "2Gi"}, "api-pyannote": {"cpu": "4000m", "memory": "8Gi"}}, "stage": {"default": {"cpu": "2000m", "memory": "2Gi"}, "server-api": {"cpu": "2000m", "memory": "2Gi"}, "api-pyannote": {"cpu": "4000m", "memory": "8Gi"}, "audio-diarization": {"cpu": "2000m", "memory": "8Gi"}}, "prod": {"default": {"cpu": "2000m", "memory": "2Gi"}, "web-client": {"cpu": "2000m", "memory": "2Gi"}, "server-api": {"cpu": "2000m", "memory": "2Gi"}, "server-api-live": {"cpu": "2000m", "memory": "2Gi"}, "api-pyannote": {"cpu": "4000m", "memory": "8Gi"}, "audio-diarization": {"cpu": "4000m", "memory": "8Gi"}}}, "services": {"web-client": {"public": true, "env": "client", "folder": "workspace/clients/web", "docker_file": "deploy/docker/ci/client-public.ci.Dockerfile", "port": 8080, "description": "The web client frontend of the Divinci web app (develop). ", "priority": 2, "scale": {"min": 0, "max": 100}}, "server-api": {"public": true, "env": "server", "folder": "workspace/servers/public-api", "docker_file": "deploy/docker/ci/api-bundle.ci.Dockerfile", "port": 8081, "description": "Base API Server (develop).", "priority": 1, "scale": {"min": 0, "max": 100}}, "server-api-live": {"public": true, "env": "server", "folder": "workspace/servers/public-api-live", "docker_file": "deploy/docker/ci/api-bundle.ci.Dockerfile", "port": 8082, "description": "Live API Server which handled websocket connections (develop).", "priority": 1, "scale": {"min": 0, "max": 100}, "requiresJWTSecrets": true}, "api-webhook": {"public": true, "env": "server", "folder": "workspace/servers/public-api-webhook", "docker_file": "deploy/docker/ci/api-bundle.ci.Dockerfile", "port": 8083, "description": "Webhook API Server which handles external webhook requests (develop).", "priority": 1, "scale": {"min": 0, "max": 100}}, "api-open-parse": {"public": true, "env": "server", "folder": "workspace/workers/open-parse", "docker_file": "deploy/docker/ci/open-parse.ci.Dockerfile", "port": 8084, "description": "API Server which handles open-parse requests.", "priority": 1, "scale": {"min": 0, "max": 100}}, "api-pyannote": {"public": true, "env": "worker", "folder": "workspace/workers/audio-speaker-diarization@pyannote", "docker_file": "deploy/docker/ci/pyannote.ci.Dockerfile", "port": 8085, "description": "Speaker diarization service using pyannote.audio.", "priority": 1, "scale": {"min": 0, "max": 100}}, "api-ffmpeg": {"public": true, "env": "worker", "folder": "workspace/workers/audio-splitter@ffmpeg", "docker_file": "deploy/docker/ci/ffmpeg.ci.Dockerfile", "port": 8086, "description": "Audio processing service using FFmpeg.", "priority": 1, "scale": {"min": 0, "max": 100}}}}