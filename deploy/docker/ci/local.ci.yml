# 📓 Please run the `local-mimic-act.sh` script first:
# `GITHUB_BASE_REF=develop ./deploy/local-mimic-act.sh`

networks:
  ci-network:
    driver: bridge

services:
  ci-web-client:
    image: us-docker.pkg.dev/openai-api-4375643/gcr.io/divinci-develop-web-client:develop
    networks:
      - ci-network
    ports:
      - 8080:8080

  # ci-embed-client:
  #   image: us-docker.pkg.dev/openai-api-4375643/gcr.io/divinci-develop-embed-client:develop
  #   networks:
  #     - ci-network
  #   ports:
  #     - 8081:8081

  ci-api:
    image: us-docker.pkg.dev/openai-api-4375643/gcr.io/divinci-develop-server-api:develop
    networks:
      - ci-network
    ports:
      - 9080:8080
    depends_on:
      - ci-mongodb
      - ci-redis

  ci-api-live:
    image: us-docker.pkg.dev/openai-api-4375643/gcr.io/divinci-develop-server-api-live:develop
    networks:
      - ci-network
    ports:
      - 9081:8080
    depends_on:
      - ci-mongodb
      - ci-redis

  ci-api-webhook:
    image: us-docker.pkg.dev/openai-api-4375643/gcr.io/divinci-develop-api-webhook:develop
    networks:
      - ci-network
    ports:
      - 9082:8080
    depends_on:
      - ci-mongodb
      - ci-redis

  ci-api-open-parse:
    image: us-docker.pkg.dev/openai-api-4375643/gcr.io/divinci-develop-api-open-parse:develop
    networks:
      - ci-network
    ports:
      - 9083:8080

  ci-mongodb:
    image: mongo
    command: mongod --quiet --logpath /dev/null
    restart: always
    env_file:
      - ../../private-keys/develop/mongodb.env
    networks:
      - ci-network
    ports:
      - "27017:27017"

  ci-redis:
    image: redis
    restart: always
    command: redis-server --save 20 1 --loglevel warning
    env_file:
      - ../../private-keys/develop/redis.env
    networks:
      - ci-network
