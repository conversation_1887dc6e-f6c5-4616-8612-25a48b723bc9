# client-public.ci.Dockerfile
# Stage 1 - the build process
# FROM divinci-resources AS builder
ARG DOCKER_ORG="openai-api-4375643"
ARG TAG=develop  # Add TAG argument with default
ARG PLATFORM="linux/amd64"

FROM --platform=${PLATFORM} us-docker.pkg.dev/${DOCKER_ORG}/gcr.io/divinci-resources:${TAG} AS builder

ARG DESCRIPTION
ARG APP_FOLDER
ARG ENVIRONMENT
ARG WEB_CLIENT_HOST
ARG PORT="8080"

# Metadata
LABEL org.opencontainers.image.description=${DESCRIPTION}

# Switch to root to handle permissions
USER root

# Create app directory and set permissions
RUN mkdir -p /home/<USER>/clients/app/node_modules && \
  mkdir -p /home/<USER>/resources/models/dist && \
  chown -R node:node /home/<USER>

WORKDIR /home/<USER>/

# Install app dependencies
COPY --chown=node:node ./${APP_FOLDER} ./clients/app
COPY --chown=node:node ./private-keys/${ENVIRONMENT} ./clients/app/env/

WORKDIR /home/<USER>/clients/app

# Install pnpm globally
USER root
RUN npm install -g pnpm@10.11.0

# Switch to node user before running pnpm commands
USER node

# "--ignore-scripts" so it doesn't build
# Configure pnpm to allow build scripts
RUN pnpm config set unsafe-perm true
RUN pnpm install --ignore-scripts
RUN pnpm run prepare:ci

# Set memory limit for build
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Run build
RUN pnpm run build

# Stage 2 - the production environment
FROM --platform=${PLATFORM} nginx:1.27.3-bookworm

ARG DESCRIPTION
ARG ENVIRONMENT
ARG APP_FOLDER
ARG WEB_CLIENT_HOST
ARG PORT="8080"

# Metadata
LABEL org.opencontainers.image.description=${DESCRIPTION}

# Install envsubst and SSL tools
RUN apt-get update && apt-get install -y \
  gettext \
  openssl \
  ca-certificates \
  && rm -rf /var/lib/apt/lists/*

# Create directories for SSL certificates (not mTLS)
RUN mkdir -p /etc/ssl/certs /etc/ssl/private

# Copy built files from the builder stage
COPY --from=builder /home/<USER>/clients/app/public /usr/share/nginx/html
COPY ./${APP_FOLDER}/nginx.conf /etc/nginx/conf.d/default.conf.template

# Create directory for entrypoint scripts
RUN mkdir -p /client-docker-entrypoint

# Copy and set up entrypoint scripts
COPY ./deploy/docker/ci/client-docker-entrypoint-updated.sh /client-docker-entrypoint.sh
COPY ./deploy/scripts/extract-gcp-certs.sh /client-docker-entrypoint/extract-gcp-certs.sh
RUN chmod +x /client-docker-entrypoint.sh /client-docker-entrypoint/extract-gcp-certs.sh

# Substitute environment variables in nginx.conf
ENV WEB_CLIENT_HOST=${WEB_CLIENT_HOST}
ENV HTTP_PORT=${PORT}
ENV HTTPS_PORT=8443
EXPOSE ${PORT} 8443

# Use the entrypoint script
ENTRYPOINT ["/client-docker-entrypoint.sh"]
