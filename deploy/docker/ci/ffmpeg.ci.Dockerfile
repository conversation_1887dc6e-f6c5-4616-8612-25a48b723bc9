# ffmpeg.ci.Dockerfile
ARG DOCKER_ORG="openai-api-4375643"
ARG TAG=develop
ARG PLATFORM="linux/amd64"

# Build stage
FROM --platform=${PLATFORM} node:22-bookworm-slim AS builder

ARG DESCRIPTION
ARG APP_FOLDER
ARG ENVIRONMENT
ARG PORT="8086"

# Metadata
LABEL org.opencontainers.image.description=${DESCRIPTION}

# Install required system dependencies
RUN apt-get update && \
    apt-get install -y ffmpeg && \
    rm -rf /var/lib/apt/lists/*

# Switch to root to handle permissions
USER root

# Create app directory and set permissions
RUN mkdir -p /home/<USER>/app && \
    chown -R node:node /home/<USER>

WORKDIR /home/<USER>/

# Bundle app source
COPY --chown=node:node ./${APP_FOLDER} ./app/

# Copy environment files
COPY --chown=node:node ./private-keys/${ENVIRONMENT} ./app/env/

WORKDIR /home/<USER>/app

# Install pnpm globally
USER root
RUN npm install -g pnpm@10.11.0

# Switch to node user before running pnpm commands
USER node

# Install dependencies
# Configure pnpm to allow build scripts
RUN pnpm config set unsafe-perm true
RUN pnpm install

# Runtime stage
FROM --platform=${PLATFORM} node:22-bookworm-slim

ARG DESCRIPTION
ARG ENVIRONMENT
ARG PORT="8086"

# Metadata
LABEL org.opencontainers.image.description=${DESCRIPTION}

# Install ffmpeg
RUN apt-get update && \
    apt-get install -y ffmpeg && \
    rm -rf /var/lib/apt/lists/*

# Set up directories and permissions
USER root
RUN mkdir -p /home/<USER>/app && \
    mkdir -p /home/<USER>/app/env && \
    mkdir -p /file-tmp && \
    mkdir -p /etc/ssl/certs && \
    mkdir -p /etc/ssl/private && \
    chmod 755 /etc/ssl/certs && \
    chmod 750 /etc/ssl/private && \
    chown -R node:node /home/<USER>/file-tmp && \
    chmod -R 755 /file-tmp

WORKDIR /home/<USER>/

# Copy and set up entrypoint script
COPY ./deploy/docker/ci/service-mtls-entrypoint.sh /service-mtls-entrypoint.sh
RUN chmod +x /service-mtls-entrypoint.sh

# Copy application files from builder
COPY --from=builder --chown=node:node /home/<USER>/app ./app/

WORKDIR /home/<USER>/app

# Switch to node user for runtime
USER node

# Environment variables
ENV NODE_ENV=production
ENV PORT=${PORT}

# Expose the port
EXPOSE ${PORT}

# Use the entrypoint script instead of direct command
ENTRYPOINT ["/service-mtls-entrypoint.sh", "ffmpeg"]
