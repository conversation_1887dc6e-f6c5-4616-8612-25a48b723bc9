#!/bin/bash
# Entrypoint script for public-api with mTLS support for service connections

set -e

# Configuration
CERT_DIR="/etc/ssl"
CA_CERT="$CERT_DIR/certs/ca.crt"
CLIENT_CERT="$CERT_DIR/certs/client.crt"
CLIENT_KEY="$CERT_DIR/private/client.key"

# Function to check certificate validity
check_certificate() {
  local cert_file=$1
  local cert_name=$2
  
  if [ ! -f "$cert_file" ]; then
    echo "❌ $cert_name not found at $cert_file"
    return 1
  fi
  
  echo "✅ Found $cert_name at $cert_file"
  echo "📄 Certificate content (first few lines):"
  head -3 "$cert_file"
  
  # Check certificate format
  if ! grep -q "BEGIN CERTIFICATE" "$cert_file"; then
    echo "⚠️ WARNING: $cert_name does not appear to be in PEM format!"
    echo "Checking certificate format..."
    
    # Display detailed information about the certificate
    echo "Certificate size: $(wc -c < "$cert_file") bytes"
    echo "First 50 characters: $(head -c 50 "$cert_file" | hexdump -C)"
    
    return 1
  fi
  
  return 0
}

# Function to check private key validity
check_private_key() {
  local key_file=$1
  local key_name=$2
  
  if [ ! -f "$key_file" ]; then
    echo "❌ $key_name not found at $key_file"
    return 1
  fi
  
  echo "✅ Found $key_name at $key_file"
  echo "📄 Key content (first few lines):"
  head -3 "$key_file"
  
  # Check key format
  if ! grep -q "BEGIN PRIVATE KEY\|BEGIN RSA PRIVATE KEY" "$key_file"; then
    echo "⚠️ WARNING: $key_name does not appear to be in PEM format!"
    echo "Checking key format..."
    
    # Display detailed information about the key
    echo "Key size: $(wc -c < "$key_file") bytes"
    echo "First 50 characters: $(head -c 50 "$key_file" | hexdump -C)"
    
    return 1
  fi
  
  return 0
}

# Check for required certificates for service-to-service mTLS
echo "🔍 Checking for mTLS certificates for service connections..."
MTLS_ENABLED="1"

# Check CA certificate
if ! check_certificate "$CA_CERT" "CA certificate"; then
  MTLS_ENABLED="0"
fi

# Check client certificate
if ! check_certificate "$CLIENT_CERT" "Client certificate"; then
  MTLS_ENABLED="0"
fi

# Check client key
if ! check_private_key "$CLIENT_KEY" "Client key"; then
  MTLS_ENABLED="0"
fi

# Set environment variables for mTLS
if [ "$MTLS_ENABLED" = "1" ]; then
  echo "✅ mTLS is enabled for service connections"
  
  # Add mTLS configuration to environment
  echo "# mTLS configuration for service connections" >> /home/<USER>/servers/app/env/.env
  echo "MTLS_ENABLED=true" >> /home/<USER>/servers/app/env/.env
  echo "MTLS_CA_CERT=$CA_CERT" >> /home/<USER>/servers/app/env/.env
  echo "MTLS_CLIENT_CERT=$CLIENT_CERT" >> /home/<USER>/servers/app/env/.env
  echo "MTLS_CLIENT_KEY=$CLIENT_KEY" >> /home/<USER>/servers/app/env/.env
else
  echo "⚠️ mTLS is disabled for service connections due to missing or invalid certificates"
  
  # Add disabled mTLS configuration to environment
  echo "# mTLS configuration for service connections" >> /home/<USER>/servers/app/env/.env
  echo "MTLS_ENABLED=false" >> /home/<USER>/servers/app/env/.env
fi

# Start the API server
echo "🚀 Starting public-api server..."
exec npm run bundle:start
