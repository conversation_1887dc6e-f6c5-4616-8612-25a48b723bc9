ARG DOCKER_ORG="openai-api-4375643"
ARG TAG=develop

# Build stage
FROM python:3.11.11-bookworm AS builder

ARG APP_FOLDER

# Install system dependencies for PyMuPDF
RUN apt-get update && \
    apt-get install -y \
    swig \
    tesseract-ocr \
    && rm -rf /var/lib/apt/lists/*

# Switch to root for permissions
USER root

# Create app directory and set permissions
RUN mkdir -p /home/<USER>/app && \
  mkdir -p /openparse-tmp && \
  addgroup --system python && \
  adduser --system --group python && \
  chown -R python:python /home/<USER>/openparse-tmp

WORKDIR /home/<USER>/

# Bundle app source
COPY ./${APP_FOLDER} ./app/

WORKDIR /home/<USER>/app

# Install build dependencies and compile requirements
RUN pip install --upgrade pip && \
    pip install pymupdf==1.23.3 && \
    pip install python-dotenv && \
    pip install gunicorn && \
    pip install --no-cache-dir -r requirements.txt

# Runtime stage
FROM python:3.11.11-bookworm

ARG DESCRIPTION
ARG ENVIRONMENT
ARG PORT="8084"

# Metadata
LABEL org.opencontainers.image.description=${DESCRIPTION}

# Set up directories and permissions
USER root
RUN mkdir -p /home/<USER>/app && \
    mkdir -p /home/<USER>/app/env && \
    mkdir -p /openparse-tmp && \
    mkdir -p /etc/ssl/certs && \
    mkdir -p /etc/ssl/private && \
    chmod 755 /etc/ssl/certs && \
    chmod 750 /etc/ssl/private && \
    addgroup --system python && \
    adduser --system --group python && \
    chown -R python:python /home/<USER>/openparse-tmp

# Install runtime dependencies as root
RUN pip install --upgrade pip && \
    pip install gunicorn && \
    pip install python-dotenv && \
    pip install flask && \
    pip install flask-cors

WORKDIR /home/<USER>/

# Copy application files from builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /home/<USER>/app ./app/

# Copy environment files
COPY --chown=python:python ./private-keys/${ENVIRONMENT} ./app/env/

# Copy and set up entrypoint script
COPY ./deploy/docker/ci/service-mtls-entrypoint.sh /service-mtls-entrypoint.sh
RUN chmod +x /service-mtls-entrypoint.sh

WORKDIR /home/<USER>/app

# Switch to python user for runtime
USER python

# Cloud Run specific environment variables
ENV PYTHONUNBUFFERED=1
ENV ENV_FOLDER="/home/<USER>/app/env"
ENV PYTHONPATH=/home/<USER>/app
ENV PORT=${PORT}
ENV THREADS=50

# Expose the port
EXPOSE ${PORT}

# Use the entrypoint script instead of direct command
ENTRYPOINT ["/service-mtls-entrypoint.sh", "open-parse"]
