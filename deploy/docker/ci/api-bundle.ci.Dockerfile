# api-bundle.ci.Dockerfile
# https://hub.docker.com/_/node/
# FROM divinci-resources AS bundle
ARG DOCKER_ORG="openai-api-4375643"
ARG TAG=develop
ARG PLATFORM="linux/amd64"

FROM --platform=${PLATFORM} us-docker.pkg.dev/${DOCKER_ORG}/gcr.io/divinci-resources:${TAG} AS bundle

ARG APP_FOLDER

# Switch to root for permissions
USER root

# Create app directory and set permissions
RUN mkdir -p /home/<USER>/servers/app/node_modules && \
  mkdir -p /home/<USER>/resources/models/dist && \
  chown -R node:node /home/<USER>

WORKDIR /home/<USER>/

# Bundle app source
COPY --chown=node:node ./${APP_FOLDER} ./servers/app

WORKDIR /home/<USER>/servers/app

# Install pnpm globally
USER root
RUN npm install -g pnpm@10.11.0

# Switch to node user before pnpm commands
USER node

# Configure pnpm to allow build scripts
RUN pnpm config set unsafe-perm true
RUN pnpm install --ignore-scripts
RUN pnpm run bundle:build

FROM node:22-bookworm-slim

ARG DESCRIPTION
ARG ENVIRONMENT
ARG PORT="8080"

# Metadata
LABEL org.opencontainers.image.description=${DESCRIPTION}

# Set up directories and permissions
USER root
RUN mkdir -p /home/<USER>/servers/app/dist && \
  mkdir -p /home/<USER>/servers/app/env && \
  mkdir -p /etc/ssl/certs && \
  mkdir -p /etc/ssl/private && \
  chmod 755 /etc/ssl/certs && \
  chmod 750 /etc/ssl/private && \
  chown -R node:node /home/<USER>

WORKDIR /home/<USER>/

COPY --chown=node:node ./private-keys/${ENVIRONMENT} ./servers/app/env/
COPY --chown=node:node --from=bundle /home/<USER>/servers/app/package.json ./servers/app/package.json
COPY --chown=node:node --from=bundle /home/<USER>/servers/app/dist ./servers/app/dist

WORKDIR /home/<USER>/servers/app

# Copy and set up entrypoint script
COPY ./deploy/docker/ci/api-mtls-entrypoint.sh /api-mtls-entrypoint.sh
RUN chmod +x /api-mtls-entrypoint.sh

# Switch to node user for runtime
USER node

ENV ENV_FOLDER="/home/<USER>/servers/app/env"
ENV HTTP_PORT=${PORT}
ENV MTLS_CERT_DIR="/etc/ssl"
EXPOSE ${PORT}
ENTRYPOINT ["/api-mtls-entrypoint.sh"]
