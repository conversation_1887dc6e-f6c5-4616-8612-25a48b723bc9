#!/bin/sh
set -e

# Run the certificate extraction and fix script if it exists
if [ -f "/deploy/scripts/extract-gcp-certs.sh" ]; then
  echo "🔧 Running certificate extraction and fix script..."
  /deploy/scripts/extract-gcp-certs.sh
else
  # Copy the script from the current directory if available
  if [ -f "/client-docker-entrypoint/extract-gcp-certs.sh" ]; then
    echo "🔧 Running certificate extraction and fix script from entrypoint directory..."
    /client-docker-entrypoint/extract-gcp-certs.sh
  fi
fi

# Check for required SSL certificates
echo "🔍 Checking for SSL certificates..."
SSL_ENABLED="1"

# Check if server certificate and key exist
if [ ! -f "/etc/ssl/certs/server.crt" ]; then
  echo "❌ Server certificate not found at /etc/ssl/certs/server.crt"
  SSL_ENABLED="0"
else
  echo "✅ Found server certificate at /etc/ssl/certs/server.crt"
  echo "📄 Certificate content (first few lines):"
  head -3 /etc/ssl/certs/server.crt

  # Check certificate format
  if ! grep -q "BEGIN CERTIFICATE" /etc/ssl/certs/server.crt; then
    echo "⚠️ WARNING: Server certificate does not appear to be in PEM format!"
    echo "Checking certificate format..."

    # Display detailed information about the certificate
    echo "Certificate size: $(wc -c < /etc/ssl/certs/server.crt) bytes"
    echo "First 50 characters: $(head -c 50 /etc/ssl/certs/server.crt | hexdump -C)"

    SSL_ENABLED="0"
  fi

  if [ ! -f "/etc/ssl/private/server.key" ]; then
    echo "❌ Server key not found at /etc/ssl/private/server.key"
    SSL_ENABLED="0"
  else
    echo "✅ Found server key at /etc/ssl/private/server.key"
    echo "📄 Key content (first few lines):"
    head -3 /etc/ssl/private/server.key

    # Check key format
    if ! grep -q "BEGIN PRIVATE KEY\|BEGIN RSA PRIVATE KEY" /etc/ssl/private/server.key; then
      echo "⚠️ WARNING: Server key does not appear to be in PEM format!"
      echo "Checking key format..."

      # Display detailed information about the key
      echo "Key size: $(wc -c < /etc/ssl/private/server.key) bytes"
      echo "First 50 characters: $(head -c 50 /etc/ssl/private/server.key | hexdump -C)"

      # Attempt to fix key format issues by checking if it's a secret value containing "latest"
      if grep -q "latest" /etc/ssl/private/server.key; then
        echo "⚠️ Server key appears to be a GCP secret reference. Attempting to extract actual key..."
        # Try to find the actual key in the mounted volume
        if [ -d "/etc/ssl/private/latest" ]; then
          echo "Found 'latest' subdirectory, checking for key file inside..."
          cp /etc/ssl/private/latest /etc/ssl/private/fixed_server.key
          if grep -q "BEGIN PRIVATE KEY\|BEGIN RSA PRIVATE KEY" /etc/ssl/private/fixed_server.key; then
            echo "✅ Successfully extracted key from 'latest' directory"
            echo "Original key: $(wc -c < /etc/ssl/private/server.key) bytes"
            echo "New key: $(wc -c < /etc/ssl/private/fixed_server.key) bytes"
            mv /etc/ssl/private/fixed_server.key /etc/ssl/private/server.key
          else
            echo "❌ Could not find valid key in 'latest' subdirectory"
            SSL_ENABLED="0"
          fi
        else
          echo "❌ No 'latest' subdirectory found"
          SSL_ENABLED="0"
        fi
      else
        SSL_ENABLED="0"
      fi
    fi
  fi
fi

# Define common location blocks for reuse
LOCATION_BLOCKS='
    # Main location block
    location / {
        try_files $uri $uri/ /index.html;

        proxy_ssl_server_name on;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }

    # JavaScript files
    location ~ \.js$ {
        gzip_static on;
        gzip_proxied any;
        gzip_types text/javascript;
    }

    # Static assets
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|webp|eot|otf|ttf|woff|woff2)$ {
        expires 1y;
        access_log off;
        add_header Cache-Control "public, must-revalidate, proxy-revalidate";
    }

    # HTML, CSS, JS, SVG files with gzip
    location ~ \.(html|js|css|svg)$ {
        gzip_static on;
    }

    # Error pages
    error_page 404 /404.html;
    location = /404.html {
        internal;
    }
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        internal;
    }
'

# Generate the proper nginx configuration file based on certificate availability
if [ "$SSL_ENABLED" = "1" ]; then
  echo "✅ SSL is enabled."

  echo "ℹ️ SSL is enabled for client-side (browser) connections"
  # Create nginx configuration with SSL
  cat > /etc/nginx/conf.d/default.conf << EOF
server {
    listen 8080;
    listen 8443 ssl;
    server_name ${WEB_CLIENT_HOST};

    # Root directory and index file
    root /usr/share/nginx/html;
    index index.html;

    # SSL Certificate Configuration
    ssl_certificate /etc/ssl/certs/server.crt;
    ssl_certificate_key /etc/ssl/private/server.key;

    # TLS Protocols and Ciphers
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384';
    ssl_prefer_server_ciphers on;

    # Headers and security settings
    add_header X-Content-Type-Options "nosniff";
    add_header X-Frame-Options "DENY";
    add_header X-XSS-Protection "1; mode=block";
$LOCATION_BLOCKS
}
EOF

else
  echo "ℹ️ SSL is disabled, using HTTP only"
  # Create nginx configuration without SSL
  cat > /etc/nginx/conf.d/default.conf << EOF
server {
    listen 8080;
    server_name ${WEB_CLIENT_HOST};

    # Root directory and index file
    root /usr/share/nginx/html;
    index index.html;

    # Headers and security settings
    add_header X-Content-Type-Options "nosniff";
    add_header X-Frame-Options "DENY";
    add_header X-XSS-Protection "1; mode=block";
$LOCATION_BLOCKS
}
EOF
fi

# Test nginx configuration
echo "🔍 Testing nginx configuration..."
if nginx -t; then
  echo "✅ Nginx configuration test passed"
else
  echo "❌ Nginx configuration test failed"

  # Fallback to a minimal HTTP-only configuration if SSL config fails
  echo "⚠️ Falling back to minimal HTTP-only configuration"
  cat > /etc/nginx/conf.d/default.conf << EOF
server {
    listen 8080;
    server_name ${WEB_CLIENT_HOST};
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files \$uri \$uri/ /index.html;
    }

    location ~ \.js$ {
        gzip_static on;
    }

    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|webp|eot|otf|ttf|woff|woff2)$ {
        expires 1y;
    }
}
EOF
  echo "✅ Created minimal fallback configuration"

  # Test the fallback configuration
  if ! nginx -t; then
    echo "❌ Even fallback configuration failed! Starting nginx with default config..."
    rm -f /etc/nginx/conf.d/default.conf
  fi
fi

# Start services
echo "🚀 Starting nginx..."
exec nginx -g "daemon off;"
