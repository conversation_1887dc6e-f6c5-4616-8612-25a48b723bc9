#!/bin/bash

set -e

# CHANGED_FOLDERS: workspace/resources/server-globals,workspace/clients/web,workspace/servers/public-api,workspace/servers/public-api-live,workspace/servers/public-api-webhook


echo "⚒️ Building and pushing Docker image..."


# Export Docker cache
# Export Docker cache
docker buildx create --use
export DOCKER_BUILDKIT=1
export DOCKER_CLI_EXPERIMENTAL=enabled
export BUILDX_CACHE_PATH=./tmp/.buildx-cache

chmod +x ./deploy/steps/2.docker-build.sh
# ./deploy/steps/2.docker-build.sh "workspace/servers/public-api-live" "develop" "chat.dev.divinci.app"
./deploy/steps/2.docker-build.sh "workspace/clients/web" "develop" "chat.dev.divinci.app"
