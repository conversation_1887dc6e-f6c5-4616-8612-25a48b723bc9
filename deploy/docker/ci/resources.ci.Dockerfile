# resources.ci.Dockerfile
# Build stage
ARG PLATFORM="linux/amd64"

FROM --platform=${PLATFORM} node:22-bookworm-slim AS builder

# Switch to root for initial setup
USER root

# Create all necessary directories and set permissions
RUN mkdir -p /home/<USER>/resources && \
  mkdir -p /home/<USER>/resources/models/dist && \
  mkdir -p /home/<USER>/tarballs && \
  mkdir -p /home/<USER>/.npm && \
  touch /home/<USER>/.npmrc && \
  chown -R node:node /home/<USER>

WORKDIR /home/<USER>/

# Bundle app source with correct permissions
COPY --chown=node:node ./workspace/tarballs/ ./tarballs
COPY --chown=node:node ./workspace/resources/ ./resources
COPY --chown=node:node ./workspace/install-with-pnpm.sh ./

# Install pnpm
RUN npm install -g pnpm@10.11.0

# Switch to node user for pnpm operations
USER node

# First install tools package explicitly
WORKDIR /home/<USER>/resources/tools
# Configure pnpm to allow build scripts
RUN pnpm config set unsafe-perm true
RUN pnpm install
RUN pnpm run build

# Then install the rest of the resources
WORKDIR /home/<USER>/
RUN /bin/bash ./install-with-pnpm.sh --type resources

# Final stage
FROM node:22-bookworm-slim

# Switch to root for setup
USER root

# Create directories and set permissions in final stage
RUN mkdir -p /home/<USER>/resources && \
  mkdir -p /home/<USER>/resources/models/dist && \
  mkdir -p /home/<USER>/tarballs && \
  chown -R node:node /home/<USER>

# Copy files with correct ownership
COPY --chown=node:node --from=builder /home/<USER>/home/<USER>

WORKDIR /home/<USER>/

# Switch to node user for runtime
USER node
