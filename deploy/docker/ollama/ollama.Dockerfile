FROM ollama/ollama:latest

# Set environment variable for the host
ENV OLLAMA_HOST=0.0.0.0
# Show additional debug information (e.g. ENV OLLAMA_DEBUG=1)
ENV OLLAMA_DEBUG=0
# The duration that models stay loaded in memory (default "5m")
ENV OLLAMA_KEEP_ALIVE=0
#📓 The maximum number of requests Ollama will queue when busy before rejecting additional requests. The default is 512.
ENV OLLAMA_MAX_QUEUE=5000000
#📓 The maximum number of parallel requests each model will process at the same time. The default will auto-select either 4 or 1 based on available memory.
# ENV OLLAMA_NUM_PARALLEL=10000
# A comma separated list of allowed origins.
#ENV OLLAMA_ORIGINS
# Always schedule model across all GPUs;
ENV OLLAMA_SCHED_SPREAD=1
#📓 NoHistory disables readline history.
ENV OLLAMA_NOHISTORY=1
#ENV OLLAMA_MAX_LOADED_MODELS  # Maximum number of loaded models per GPU
#ENV OLLAMA_NOPRUNE            # Do not prune model blobs on startup
#📓 Enabled flash attention. See FlashAttension.md
ENV OLLAMA_FLASH_ATTENTION=1
#ENV OLLAMA_KV_CACHE_TYPE      # Quantization type for the K/V cache (default: f16)
#ENV OLLAMA_LLM_LIBRARY        # Set LLM library to bypass autodetection
#ENV OLLAMA_GPU_OVERHEAD       # Reserve a portion of VRAM per GPU (bytes)
#ENV OLLAMA_LOAD_TIMEOUT       # How long to allow model loads to stall before giving up (default "5m")

# Copy the custom script into the container
COPY run-ollama.sh /usr/local/bin/run-ollama.sh

# Set the working directory (optional)
WORKDIR /usr/local/bin

# Make the script executable
RUN chmod +x run-ollama.sh

# Expose the port
EXPOSE 11434

# Set the entrypoint to the script
ENTRYPOINT ["./run-ollama.sh"]
