#!/usr/bin/env bash

# Start the Ollama server in the background
echo "🌞🦙 Starting Ollama server..."
ollama serve &

# Wait for the server to start
echo "⏳ Waiting for the server to initialize..."
sleep 5

# Function to check if model exists
model_exists() {
    ollama list | grep -q "$1"
}

# Pull nomic-embed-text if not present
if ! model_exists "nomic-embed-text"; then
    echo "⤵️ Pulling nomic-embed-text..."
    ollama pull nomic-embed-text || {
        echo "❌ Failed to pull nomic-embed-text model."
        exit 1
    }
else
    echo "✅ nomic-embed-text model already installed"
fi

# Pull llama3-groq-tool-use if not present
if ! model_exists "llama3-groq-tool-use"; then
    echo "⤵️ Pulling llama3-groq-tool-use model..."
    ollama pull llama3-groq-tool-use || {
        echo "❌ Failed to pull llama3-groq-tool-use model."
        exit 1
    }
else
    echo "✅ llama3-groq-tool-use model already installed"
fi

# Stop the temporary server
echo "🛑 Stopping temporary server..."
pkill -f "ollama serve"

# Restart the server in the foreground
echo "🔄 Restarting Ollama server..."
exec ollama serve
