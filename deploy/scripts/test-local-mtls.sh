#!/bin/bash
# Script to test local mTLS setup

set -e

# Get the environment stage (default to local if not provided)
STAGE=${1:-local}
echo "Testing certificates for stage: $STAGE"

# Configuration
CA_CERT="./private-keys/$STAGE/certs/mtls/ca/ca.crt"
CLIENT_CERT="./private-keys/$STAGE/certs/mtls/services/public-api/client.crt"
CLIENT_KEY="./private-keys/$STAGE/certs/mtls/services/public-api/client.key"

# Function to test mTLS connection to a service
test_mtls_connection() {
  local service_name=$1
  local service_port=$2

  echo "🔍 Testing mTLS connection to $service_name on port $service_port..."

  # Use curl with mTLS certificates
  response=$(curl --cacert "$CA_CERT" \
          --cert "$CLIENT_CERT" \
          --key "$CLIENT_KEY" \
          -s -v \
          "https://localhost:$service_port/health" 2>&1)

  # Check if the connection was successful
  if echo "$response" | grep -q "SSL connection using"; then
    echo "✅ SSL handshake with $service_name was successful"
  else
    echo "❌ SSL handshake with $service_name failed"
    echo "$response"
  fi

  # Check if the server certificate was verified
  if echo "$response" | grep -q "SSL certificate verify ok"; then
    echo "✅ Server certificate for $service_name was verified"
  else
    echo "❌ Server certificate verification for $service_name failed"
  fi

  # Check if the client certificate was sent
  if echo "$response" | grep -q "SSL certificate verify ok"; then
    echo "✅ Client certificate was accepted by $service_name"
  else
    echo "❌ Client certificate was rejected by $service_name"
  fi

  # Check HTTP status code
  status_code=$(echo "$response" | grep -o "< HTTP/[0-9.]* [0-9]*" | awk '{print $3}')
  if [ ! -z "$status_code" ]; then
    echo "📊 HTTP status code: $status_code"
    if [ "$status_code" = "200" ]; then
      echo "✅ Successfully connected to $service_name with mTLS"
      return 0
    else
      echo "⚠️ Connected to $service_name but received status code $status_code"
      return 1
    fi
  else
    echo "❌ Failed to connect to $service_name with mTLS"
    return 1
  fi
}

# Check if certificates exist
if [ ! -f "$CA_CERT" ] || [ ! -f "$CLIENT_CERT" ] || [ ! -f "$CLIENT_KEY" ]; then
  echo "❌ mTLS certificates not found. Please run generate-mtls-certs.sh first."
  exit 1
fi

# Test connections to each service
echo "🧪 Testing mTLS connections to services..."

# Test FFmpeg service
test_mtls_connection "FFmpeg" "19001"

# Test Open-Parse service
test_mtls_connection "Open-Parse" "19002"

# Test Pyannote service
test_mtls_connection "Pyannote" "19000"

echo "✅ mTLS connection tests completed!"
