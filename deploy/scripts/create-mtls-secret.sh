#!/bin/bash
# Script to create a GCP Secret for mTLS certificates using Cloudflare Origin Certificates

# Usage: ./create-mtls-secret.sh <environment>
# Example: ./create-mtls-secret.sh staging
#
# This script assumes that certificates are in the private-keys/<environment>/certs/mtls/ directory

set -e

ENVIRONMENT=$1
CERT_DIR="./private-keys/${ENVIRONMENT}/certs/mtls"

if [ -z "$ENVIRONMENT" ]; then
  echo "Usage: $0 <environment>"
  echo "Example: $0 staging"
  exit 1
fi

# Check if the certificate directory exists
if [ ! -d "$CERT_DIR" ]; then
  echo "Error: Certificate directory $CERT_DIR does not exist"
  exit 1
fi

# Check if the required certificate files exist
if [ ! -f "$CERT_DIR/origin.crt" ]; then
  echo "Error: Origin certificate file $CERT_DIR/origin.crt does not exist"
  exit 1
fi

if [ ! -f "$CERT_DIR/origin.key" ]; then
  echo "Error: Origin key file $CERT_DIR/origin.key does not exist"
  exit 1
fi

if [ ! -f "$CERT_DIR/client.crt" ]; then
  echo "Error: Client certificate file $CERT_DIR/client.crt does not exist"
  exit 1
fi

SECRET_NAME="mtls-certs-$ENVIRONMENT"

echo "Creating GCP Secret $SECRET_NAME using Cloudflare Origin certificates..."

# Create the secret for origin.crt (server certificate)
gcloud secrets create "$SECRET_NAME-server-crt" --data-file="$CERT_DIR/origin.crt" --replication-policy="automatic" || \
  gcloud secrets versions add "$SECRET_NAME-server-crt" --data-file="$CERT_DIR/origin.crt"

# Create the secret for origin.key (server key)
gcloud secrets create "$SECRET_NAME-server-key" --data-file="$CERT_DIR/origin.key" --replication-policy="automatic" || \
  gcloud secrets versions add "$SECRET_NAME-server-key" --data-file="$CERT_DIR/origin.key"

# Create the secret for client.crt
gcloud secrets create "$SECRET_NAME-client-crt" --data-file="$CERT_DIR/client.crt" --replication-policy="automatic" || \
  gcloud secrets versions add "$SECRET_NAME-client-crt" --data-file="$CERT_DIR/client.crt"

echo "Secrets created successfully!"
echo "Now you need to create a Kubernetes Secret in GCP Cloud Run:"
echo ""
echo "1. Go to GCP Console > Cloud Run > Configuration > Secrets"
echo "2. Create a new secret named '$SECRET_NAME'"
echo "3. Add the following keys:"
echo "   - server.crt: Reference the secret '$SECRET_NAME-server-crt' (using origin.crt)"
echo "   - server.key: Reference the secret '$SECRET_NAME-server-key' (using origin.key)"
echo "   - client.crt: Reference the secret '$SECRET_NAME-client-crt'"
echo ""
echo "Then update your service to use this secret as shown in the service template."
echo ""
echo "NOTE: This script now uses the Cloudflare Origin Certificate (origin.crt/key) instead"
echo "of the server.crt/key. This is required for Cloudflare Full (Strict) SSL/TLS mode."

exit 0
