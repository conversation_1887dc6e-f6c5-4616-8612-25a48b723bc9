#!/bin/bash
# <PERSON>ript to create client certificate secrets for GCP

set -e

# Check if we have PEM certificate files
if [ -f "private-keys/staging/certs/mtls/client.crt.pem" ] && [ -f "private-keys/staging/certs/mtls/client.key.pem" ]; then
  echo "Found PEM certificate and key files."
  CLIENT_CRT="private-keys/staging/certs/mtls/client.crt.pem"
  CLIENT_KEY="private-keys/staging/certs/mtls/client.key.pem"
else
  echo "PEM certificate or key file not found."
  echo "Looking for client.crt and client.key in private-keys/staging/certs/mtls/"

  if [ -f "private-keys/staging/certs/mtls/client.crt" ] && [ -f "private-keys/staging/certs/mtls/client.key" ]; then
    echo "Found regular certificate and key files."
    CLIENT_CRT="private-keys/staging/certs/mtls/client.crt"
    CLIENT_KEY="private-keys/staging/certs/mtls/client.key"
  else
    echo "No client certificate files found."
    echo "Cannot proceed without certificate files."
    exit 1
  fi
fi

# Verify certificate format
echo "Verifying certificate format..."
if ! grep -q "BEGIN CERTIFICATE" "$CLIENT_CRT"; then
  echo "⚠️ WARNING: Certificate does not appear to be in PEM format!"
  echo "Attempting to fix..."

  # Create a temporary file with proper PEM format
  TEMP_CLIENT_CRT=$(mktemp)
  echo "-----BEGIN CERTIFICATE-----" > "$TEMP_CLIENT_CRT"
  cat "$CLIENT_CRT" >> "$TEMP_CLIENT_CRT"
  echo "-----END CERTIFICATE-----" >> "$TEMP_CLIENT_CRT"

  # Check if the fixed file looks valid
  if grep -q "BEGIN CERTIFICATE" "$TEMP_CLIENT_CRT" && grep -q "END CERTIFICATE" "$TEMP_CLIENT_CRT"; then
    echo "✅ Successfully fixed certificate format"
    CLIENT_CRT="$TEMP_CLIENT_CRT"
  else
    echo "❌ Could not fix certificate format"
  fi
fi

# Verify key format
echo "Verifying key format..."
if ! grep -q "BEGIN PRIVATE KEY\|BEGIN RSA PRIVATE KEY" "$CLIENT_KEY"; then
  echo "⚠️ WARNING: Private key does not appear to be in PEM format!"
  echo "Attempting to fix..."

  # Create a temporary file with proper PEM format
  TEMP_CLIENT_KEY=$(mktemp)
  echo "-----BEGIN PRIVATE KEY-----" > "$TEMP_CLIENT_KEY"
  cat "$CLIENT_KEY" >> "$TEMP_CLIENT_KEY"
  echo "-----END PRIVATE KEY-----" >> "$TEMP_CLIENT_KEY"

  # Check if the fixed file looks valid
  if grep -q "BEGIN PRIVATE KEY" "$TEMP_CLIENT_KEY" && grep -q "END PRIVATE KEY" "$TEMP_CLIENT_KEY"; then
    echo "✅ Successfully fixed key format"
    CLIENT_KEY="$TEMP_CLIENT_KEY"
  else
    echo "❌ Could not fix key format"
  fi
fi

# Create or update the client.crt secret
echo "Creating/updating client.crt secret..."
if gcloud secrets describe client-crt &>/dev/null; then
  echo "Secret 'client-crt' already exists. Adding a new version..."
  gcloud secrets versions add client-crt --data-file="$CLIENT_CRT"
else
  echo "Creating new secret 'client-crt'..."
  gcloud secrets create client-crt --data-file="$CLIENT_CRT" --replication-policy="automatic"
fi
echo "client.crt secret updated successfully."

# Create or update the client.key secret
echo "Creating/updating client.key secret..."
if gcloud secrets describe client-key &>/dev/null; then
  echo "Secret 'client-key' already exists. Adding a new version..."
  gcloud secrets versions add client-key --data-file="$CLIENT_KEY"
else
  echo "Creating new secret 'client-key'..."
  gcloud secrets create client-key --data-file="$CLIENT_KEY" --replication-policy="automatic"
fi
echo "client.key secret updated successfully."

echo "Verifying secrets..."
echo "client.crt content (first few lines):"
gcloud secrets versions access latest --secret=client-crt | head -5

echo "client.key content (first few lines):"
gcloud secrets versions access latest --secret=client-key | head -5

echo "Done. Now you need to mount these secrets in your GCP Cloud Run service:"
echo "1. Go to GCP Console > Cloud Run > Your Client Service > Configuration > Secrets"
echo "2. Add the first secret volume:"
echo "   - Name: mtls-client-crt"
echo "   - Mount path: /etc/ssl/client"
echo "   - Secret: client-crt"
echo "   - File name: client.crt"
echo "3. Add the second secret volume:"
echo "   - Name: mtls-client-key"
echo "   - Mount path: /etc/ssl/private"
echo "   - Secret: client-key"
echo "   - File name: client.key"
echo "4. Make sure these environment variables are set:"
echo "   - API_IS_SECURE=1"
echo "   - API_MTLS_ENABLED=1"