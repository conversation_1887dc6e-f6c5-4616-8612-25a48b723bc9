#!/bin/bash
# <PERSON>ript to extract and fix certificate formats from GCP secrets

set -e

echo "🔍 Checking for mounted secrets..."

# Function to create a proper PEM formatted file
fix_pem_format() {
  local input_file=$1
  local output_file=$2
  local type=$3

  echo "Attempting to fix $type file format..."
  if [ ! -f "$input_file" ]; then
    echo "❌ Input file not found: $input_file"
    return 1
  fi

  if [[ "$type" == "certificate" ]]; then
    echo "-----BEGIN CERTIFICATE-----" > "$output_file"
    cat "$input_file" >> "$output_file"
    echo "-----END CERTIFICATE-----" >> "$output_file"

    if grep -q "BEGIN CERTIFICATE" "$output_file" && grep -q "END CERTIFICATE" "$output_file"; then
      echo "✅ Successfully fixed certificate format"
      return 0
    else
      echo "❌ Could not fix certificate format"
      return 1
    fi
  elif [[ "$type" == "key" ]]; then
    echo "-----BEGIN PRIVATE KEY-----" > "$output_file"
    cat "$input_file" >> "$output_file"
    echo "-----END PRIVATE KEY-----" >> "$output_file"

    if grep -q "BEGIN PRIVATE KEY" "$output_file" && grep -q "END PRIVATE KEY" "$output_file"; then
      echo "✅ Successfully fixed key format"
      return 0
    else
      echo "❌ Could not fix key format"
      return 1
    fi
  else
    echo "❌ Unknown type: $type"
    return 1
  fi
}

# Check if certificates are mounted in the expected locations
mkdir -p /etc/ssl/certs /etc/ssl/private

# Server certificate
if [ -f "/etc/ssl/certs/server.crt" ]; then
  echo "✅ Found server certificate at /etc/ssl/certs/server.crt"

  # Check if certificate is in proper format
  if ! grep -q "BEGIN CERTIFICATE" /etc/ssl/certs/server.crt; then
    echo "⚠️ Server certificate does not appear to be in PEM format!"

    # Check if it's a directory mount from GCP Secret
    if [ -d "/etc/ssl/certs/latest" ]; then
      echo "Found 'latest' subdirectory, attempting to extract certificate..."
      cp /etc/ssl/certs/latest /etc/ssl/certs/server.crt.raw
      fix_pem_format "/etc/ssl/certs/server.crt.raw" "/etc/ssl/certs/server.crt.fixed" "certificate"
      if [ $? -eq 0 ]; then
        mv /etc/ssl/certs/server.crt.fixed /etc/ssl/certs/server.crt
        echo "✅ Fixed server certificate format"
      else
        echo "❌ Could not fix server certificate format"
      fi
    else
      # Try to fix the format directly
      fix_pem_format "/etc/ssl/certs/server.crt" "/etc/ssl/certs/server.crt.fixed" "certificate"
      if [ $? -eq 0 ]; then
        mv /etc/ssl/certs/server.crt.fixed /etc/ssl/certs/server.crt
        echo "✅ Fixed server certificate format"
      else
        echo "❌ Could not fix server certificate format"
      fi
    fi
  fi
else
  echo "❌ Server certificate not found at /etc/ssl/certs/server.crt"
fi

# Server key
if [ -f "/etc/ssl/private/server.key" ]; then
  echo "✅ Found server key at /etc/ssl/private/server.key"

  # Check if key is in proper format
  if ! grep -q "BEGIN PRIVATE KEY\|BEGIN RSA PRIVATE KEY" /etc/ssl/private/server.key; then
    echo "⚠️ Server key does not appear to be in PEM format!"

    # Check if it's a directory mount from GCP Secret
    if [ -d "/etc/ssl/private/latest" ]; then
      echo "Found 'latest' subdirectory, attempting to extract key..."
      cp /etc/ssl/private/latest /etc/ssl/private/server.key.raw
      fix_pem_format "/etc/ssl/private/server.key.raw" "/etc/ssl/private/server.key.fixed" "key"
      if [ $? -eq 0 ]; then
        mv /etc/ssl/private/server.key.fixed /etc/ssl/private/server.key
        echo "✅ Fixed server key format"
      else
        echo "❌ Could not fix server key format"
      fi
    else
      # Try to fix the format directly
      fix_pem_format "/etc/ssl/private/server.key" "/etc/ssl/private/server.key.fixed" "key"
      if [ $? -eq 0 ]; then
        mv /etc/ssl/private/server.key.fixed /etc/ssl/private/server.key
        echo "✅ Fixed server key format"
      else
        echo "❌ Could not fix server key format"
      fi
    fi
  fi
else
  echo "❌ Server key not found at /etc/ssl/private/server.key"
fi

# Client certificates have been deprecated in server-only TLS mode
# All the client-related certificate code has been removed from this script
echo "ℹ️ Client certificates are no longer used in server-only TLS mode"

echo "Certificate verification and fix completed."