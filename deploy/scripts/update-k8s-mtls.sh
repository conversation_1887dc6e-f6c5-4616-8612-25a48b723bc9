#!/bin/bash
# <PERSON>ript to update Kubernetes deployment files with mTLS configuration

set -e

# Get the environment stage (default to staging if not provided)
STAGE=${1:-staging}
echo "Updating Kubernetes deployment files for stage: $STAGE"

# Function to update a deployment file with mTLS configuration
update_deployment() {
  local service_name=$1
  local deployment_file=$2
  local cert_secret=$3

  echo "🔧 Updating $service_name deployment file: $deployment_file"

  # Create a backup of the original file
  cp "$deployment_file" "${deployment_file}.bak"

  # Add environment variables for mTLS
  sed -i '' -e '/env:/a\
        - name: MTLS_ENABLED\
          valueFrom:\
            configMapKeyRef:\
              name: mtls-config\
              key: MTLS_ENABLED\
        - name: MTLS_CA_CERT\
          value: /etc/ssl/certs/ca.crt\
        - name: MTLS_SERVER_CERT\
          value: /etc/ssl/certs/server.crt\
        - name: MTLS_SERVER_KEY\
          value: /etc/ssl/private/server.key' "$deployment_file"

  # Add volume mounts for mTLS certificates
  sed -i '' -e '/volumeMounts:/a\
        - name: mtls-ca-cert\
          mountPath: /etc/ssl/certs/ca.crt\
          subPath: ca.crt\
          readOnly: true\
        - name: mtls-certs\
          mountPath: /etc/ssl/certs/server.crt\
          subPath: server.crt\
          readOnly: true\
        - name: mtls-certs\
          mountPath: /etc/ssl/private/server.key\
          subPath: server.key\
          readOnly: true' "$deployment_file"

  # Add volumes for mTLS certificates
  sed -i '' -e '/volumes:/a\
      - name: mtls-ca-cert\
        secret:\
          secretName: mtls-ca-cert\
      - name: mtls-certs\
        secret:\
          secretName: '"$cert_secret"'' "$deployment_file"

  echo "✅ Updated $service_name deployment file"
}

# Function to update the public-api deployment file with client certificate configuration
update_public_api_deployment() {
  local deployment_file=$1

  echo "🔧 Updating public-api deployment file: $deployment_file"

  # Create a backup of the original file
  cp "$deployment_file" "${deployment_file}.bak"

  # Add environment variables for mTLS
  sed -i '' -e '/env:/a\
        - name: MTLS_ENABLED\
          valueFrom:\
            configMapKeyRef:\
              name: mtls-config\
              key: MTLS_ENABLED\
        - name: MTLS_CA_CERT\
          value: /etc/ssl/certs/ca.crt\
        - name: MTLS_CLIENT_CERT\
          value: /etc/ssl/certs/client.crt\
        - name: MTLS_CLIENT_KEY\
          value: /etc/ssl/private/client.key' "$deployment_file"

  # Add volume mounts for mTLS certificates
  sed -i '' -e '/volumeMounts:/a\
        - name: mtls-ca-cert\
          mountPath: /etc/ssl/certs/ca.crt\
          subPath: ca.crt\
          readOnly: true\
        - name: mtls-client-certs\
          mountPath: /etc/ssl/certs/client.crt\
          subPath: client.crt\
          readOnly: true\
        - name: mtls-client-certs\
          mountPath: /etc/ssl/private/client.key\
          subPath: client.key\
          readOnly: true' "$deployment_file"

  # Add volumes for mTLS certificates
  sed -i '' -e '/volumes:/a\
      - name: mtls-ca-cert\
        secret:\
          secretName: mtls-ca-cert\
      - name: mtls-client-certs\
        secret:\
          secretName: mtls-public-api-certs' "$deployment_file"

  echo "✅ Updated public-api deployment file"
}

# Main script

echo "🔍 Updating Kubernetes deployment files with mTLS configuration..."

# Update public-api deployment
update_public_api_deployment "deploy/kubernetes/public-api-deployment.yaml"

# Update FFmpeg deployment
update_deployment "ffmpeg" "deploy/kubernetes/ffmpeg-deployment.yaml" "mtls-ffmpeg-certs"

# Update open-parse deployment
update_deployment "open-parse" "deploy/kubernetes/open-parse-deployment.yaml" "mtls-open-parse-certs"

# Update pyannote deployment
update_deployment "pyannote" "deploy/kubernetes/pyannote-deployment.yaml" "mtls-pyannote-certs"

echo "✅ All Kubernetes deployment files updated with mTLS configuration"
