#!/bin/bash
# Script to download Cloudflare Origin CA root certificates

set -e

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

# Environment setup
ENVIRONMENT=${1:-"staging"}
echo "🔍 Downloading Cloudflare Origin CA root certificates for $ENVIRONMENT environment..."

# Create directory for certificates
CERT_DIR="$REPO_ROOT/private-keys/${ENVIRONMENT}/certs/mtls"
mkdir -p "$CERT_DIR"

# Download Cloudflare Origin CA — RSA Root Certificate
echo "📥 Downloading Cloudflare Origin CA — RSA Root Certificate..."
curl -s https://developers.cloudflare.com/ssl/static/origin_ca_rsa_root.pem -o "$CERT_DIR/origin_ca_rsa_root.pem"

# Download Cloudflare Origin CA — ECC Root Certificate
echo "📥 Downloading Cloudflare Origin CA — ECC Root Certificate..."
curl -s https://developers.cloudflare.com/ssl/static/origin_ca_ecc_root.pem -o "$CERT_DIR/origin_ca_ecc_root.pem"

# Also save copies in the current directory for the add-ca-to-certificates.sh script
curl -s https://developers.cloudflare.com/ssl/static/origin_ca_rsa_root.pem -o origin_ca_rsa_root.pem
curl -s https://developers.cloudflare.com/ssl/static/origin_ca_ecc_root.pem -o origin_ca_ecc_root.pem

# Verify the downloads
if [ -f "$CERT_DIR/origin_ca_rsa_root.pem" ] && [ -f "$CERT_DIR/origin_ca_ecc_root.pem" ]; then
  echo "✅ Cloudflare Origin CA root certificates downloaded successfully to $CERT_DIR."
else
  echo "❌ Failed to download Cloudflare Origin CA root certificates."
  exit 1
fi

echo "📋 Next steps:"
echo "1. Run the add-ca-to-certificates.sh script to add these root certificates to your certificate chain"
echo "2. Update your GCP secrets with the modified certificates"
echo "3. Restart your service to apply the changes"

echo "✅ Download complete."
