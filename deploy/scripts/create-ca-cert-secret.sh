#!/bin/bash
# <PERSON><PERSON>t to create a GCP secret for the CA certificate

set -e

# Get the directory of the current script
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
ROOT_DIR=$(dirname "$(dirname "$SCRIPT_DIR")")

# Define input parameters
ENVIRONMENT=$1
if [ -z "$ENVIRONMENT" ]; then
  echo "❌ Environment not specified. Usage: $0 <environment>"
  exit 1
fi

# Check if the environment is valid
if [ "$ENVIRONMENT" != "develop" ] && [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
  echo "❌ Invalid environment: $ENVIRONMENT. Valid values are: develop, staging, production"
  exit 1
fi

# Define the CA certificate file
CA_CERT_FILE="$ROOT_DIR/private-keys/$ENVIRONMENT/certs/mtls/ca.crt"

# Check if the CA certificate file exists
if [ ! -f "$CA_CERT_FILE" ]; then
  echo "❌ CA certificate file not found: $CA_CERT_FILE"
  exit 1
fi

echo "🔍 Creating GCP secret for CA certificate in $ENVIRONMENT environment..."

# Create the GCP secret
gcloud secrets create ca-crt \
  --replication-policy="automatic" \
  --data-file="$CA_CERT_FILE" \
  --labels="environment=$ENVIRONMENT" \
  || gcloud secrets versions add ca-crt \
  --data-file="$CA_CERT_FILE"

echo "✅ GCP secret for CA certificate created successfully!"

# Update the secret version
gcloud secrets versions add ca-crt --data-file="$CA_CERT_FILE"

echo "✅ GCP secret for CA certificate updated successfully!"

# List the secrets to verify
echo "🔍 Listing GCP secrets..."
gcloud secrets list --filter="name:ca-crt"

echo "🔍 Listing versions of the CA certificate secret..."
gcloud secrets versions list ca-crt

echo "✅ Done!"
