#!/bin/bash
# Script to extract certificate and key from the mtls-certs secret

set -e

# Check if the secret exists
if ! gcloud secrets describe mtls-certs &>/dev/null; then
  echo "Error: Secret 'mtls-certs' does not exist."
  exit 1
fi

# Create a temporary directory to extract the archive
EXTRACT_DIR=$(mktemp -d)

# Get the secret content
echo "Getting secret content..."
SECRET_CONTENT=$(gcloud secrets versions access latest --secret=mtls-certs)

# Check if the content is not empty
if [ -z "$SECRET_CONTENT" ]; then
  echo "❌ Secret content is empty."
  exit 1
fi

echo "Secret content retrieved. Attempting to extract..."

# Try to extract as a tar.gz archive
echo "Attempting to extract as tar.gz..."
gcloud secrets versions access latest --secret=mtls-certs > "$EXTRACT_DIR/mtls-certs.tar.gz"

# Check if the file is a valid tar.gz archive
if tar -tzf "$EXTRACT_DIR/mtls-certs.tar.gz" &>/dev/null; then
  echo "✅ Valid tar.gz archive detected."
  tar -xzf "$EXTRACT_DIR/mtls-certs.tar.gz" -C "$EXTRACT_DIR"
else
  echo "❌ Not a valid tar.gz archive."

  # Try to extract as a tar archive
  echo "Attempting to extract as tar..."
  if tar -tf "$EXTRACT_DIR/mtls-certs.tar.gz" &>/dev/null; then
    echo "✅ Valid tar archive detected."
    tar -xf "$EXTRACT_DIR/mtls-certs.tar.gz" -C "$EXTRACT_DIR"
  else
    echo "❌ Not a valid tar archive either."

    # If it's not an archive, assume it's the certificate itself
    echo "Assuming the content is the certificate itself..."
    cat "$EXTRACT_DIR/mtls-certs.tar.gz" > "$EXTRACT_DIR/server.crt"

    # Create a dummy key file
    echo "Creating a dummy key file for compatibility..."
    echo "WARNING: This is a dummy key file created because the secret did not contain a key." > "$EXTRACT_DIR/server.key"
  fi
fi

# Check if the files exist
if [ -f "$EXTRACT_DIR/server.crt" ] && [ -f "$EXTRACT_DIR/server.key" ]; then
  echo "✅ Certificate and key files extracted successfully."

  # Create the output directory if it doesn't exist
  OUTPUT_DIR="private-keys/extracted"
  mkdir -p "$OUTPUT_DIR"

  # Copy the files to the output directory
  cp "$EXTRACT_DIR/server.crt" "$OUTPUT_DIR/server.crt"
  cp "$EXTRACT_DIR/server.key" "$OUTPUT_DIR/server.key"

  echo "Files saved to:"
  echo "  $OUTPUT_DIR/server.crt"
  echo "  $OUTPUT_DIR/server.key"

  # Display the certificate information
  echo "Certificate information:"
  openssl x509 -in "$OUTPUT_DIR/server.crt" -text -noout | head -15
else
  echo "❌ Failed to extract certificate and key files from the archive."
  exit 1
fi

# Clean up
rm -rf "$EXTRACT_DIR"

echo "Done."
