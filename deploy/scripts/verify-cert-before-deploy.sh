#!/bin/bash
# Script to verify certificates before deployment to GCP
# This script should be called from deploy/steps/3.deploy-new.sh before deploying services

set -e

# Environment setup
ENVIRONMENT=${1:-"staging"}
NON_INTERACTIVE=${2:-"false"}
AUTO_CONFIRM=${AUTO_CONFIRM:-"false"}

# If AUTO_CONFIRM is set to true, force non-interactive mode
if [ "$AUTO_CONFIRM" == "true" ]; then
  NON_INTERACTIVE="true"
fi

echo "🔍 Verifying certificates for $ENVIRONMENT environment before deployment..."
echo "   Interactive mode: $([ "$NON_INTERACTIVE" == "true" ] && echo "disabled" || echo "enabled")"
echo "   Auto-confirm: $([ "$AUTO_CONFIRM" == "true" ] && echo "enabled" || echo "disabled")"

# Paths to certificates
CERT_DIR="./private-keys/${ENVIRONMENT}/certs/mtls"

# Check if the certificate directory exists
if [ ! -d "$CERT_DIR" ]; then
  echo "❌ Certificate directory $CERT_DIR does not exist"
  if [ "$AUTO_CONFIRM" == "true" ]; then
    echo "⚠️ Auto-confirm enabled. Continuing deployment despite missing certificates."
  else
    exit 1
  fi
fi

# Try to find the root certificates
if [ -f "$CERT_DIR/origin_ca_ecc_root.pem" ]; then
  CF_ECC_ROOT_CERT="$CERT_DIR/origin_ca_ecc_root.pem"
else
  CF_ECC_ROOT_CERT="./origin_ca_ecc_root.pem"
fi

if [ -f "$CERT_DIR/origin_ca_rsa_root.pem" ]; then
  CF_RSA_ROOT_CERT="$CERT_DIR/origin_ca_rsa_root.pem"
else
  CF_RSA_ROOT_CERT="./origin_ca_rsa_root.pem"
fi

# Find the server certificate
SERVER_CERT="$CERT_DIR/server.crt"
if [ ! -f "$SERVER_CERT" ]; then
  # Try origin certificate as fallback
  if [ -f "$CERT_DIR/origin.crt" ]; then
    SERVER_CERT="$CERT_DIR/origin.crt"
    echo "⚠️ Using origin.crt as server certificate"
  else
    echo "❌ No server certificate found. Cannot proceed with verification."
    if [ "$AUTO_CONFIRM" == "true" ]; then
      echo "⚠️ Auto-confirm enabled. Continuing deployment despite missing certificates."
    else
      exit 1
    fi
  fi
fi

echo "📂 Using server certificate: $SERVER_CERT"

# Check if the certificate is properly formed
if ! openssl x509 -in "$SERVER_CERT" -noout > /dev/null 2>&1; then
  echo "❌ Invalid certificate format in $SERVER_CERT"
  if [ "$AUTO_CONFIRM" == "true" ]; then
    echo "⚠️ Auto-confirm enabled. Continuing deployment despite invalid certificate format."
  else
    exit 1
  fi
fi

# Extract certificate details
SUBJECT=$(openssl x509 -in "$SERVER_CERT" -noout -subject)
ISSUER=$(openssl x509 -in "$SERVER_CERT" -noout -issuer)
NOT_BEFORE=$(openssl x509 -in "$SERVER_CERT" -noout -startdate | cut -d= -f2)
NOT_AFTER=$(openssl x509 -in "$SERVER_CERT" -noout -enddate | cut -d= -f2)

echo "📜 Certificate details:"
echo "   Subject: $SUBJECT"
echo "   Issuer: $ISSUER"
echo "   Valid from: $NOT_BEFORE"
echo "   Valid until: $NOT_AFTER"

# Check if certificate is within validity period
CURRENT_DATE=$(date)
CURRENT_TIMESTAMP=$(date +%s)
NOT_BEFORE_TIMESTAMP=$(date -d "$NOT_BEFORE" +%s 2>/dev/null || echo "0")
NOT_AFTER_TIMESTAMP=$(date -d "$NOT_AFTER" +%s 2>/dev/null || echo "0")

if [ "$CURRENT_TIMESTAMP" -lt "$NOT_BEFORE_TIMESTAMP" ]; then
  echo "❌ CRITICAL ERROR: Certificate is not yet valid! Begins at $NOT_BEFORE"
  echo "   Current time: $CURRENT_DATE"
  echo "   This will cause certificate validation failures."
  
  # Fix for certificate not yet valid by setting system time forward
  echo "⚠️ Attempting to fix by setting a Certificate start date in the past..."
  
  # Create a temporary directory
  TEMP_DIR=$(mktemp -d)
  
  # Extract the certificate
  openssl x509 -in "$SERVER_CERT" -outform PEM -out "$TEMP_DIR/cert.pem"
  
  # Extract the key - try common locations
  KEY_FILE=""
  for key in "$CERT_DIR/server.key" "$CERT_DIR/origin.key"; do
    if [ -f "$key" ]; then
      KEY_FILE="$key"
      break
    fi
  done
  
  if [ -z "$KEY_FILE" ]; then
    echo "❌ Cannot find private key file. Cannot fix certificate dates."
    rm -rf "$TEMP_DIR"
    if [ "$AUTO_CONFIRM" == "true" ]; then
      echo "⚠️ Auto-confirm enabled. Continuing deployment despite certificate issues."
    else
      exit 1
    fi
  fi
  
  # Generate a new certificate with a start date in the past
  openssl x509 -req -in "$TEMP_DIR/cert.pem" -signkey "$KEY_FILE" \
    -days 365 -startdate "$(date -d '-1 day' +'%Y%m%d%H%M%SZ')" \
    -out "$TEMP_DIR/fixed_cert.pem"
  
  if [ $? -eq 0 ]; then
    # Backup original certificate
    cp "$SERVER_CERT" "${SERVER_CERT}.backup-$(date +%Y%m%d-%H%M%S)"
    
    # Replace with fixed certificate
    cat "$TEMP_DIR/fixed_cert.pem" > "$SERVER_CERT"
    echo "✅ Fixed certificate with proper start date"
    
    # Clean up
    rm -rf "$TEMP_DIR"
  else
    echo "❌ Failed to fix certificate dates. Please regenerate certificates."
    rm -rf "$TEMP_DIR"
    if [ "$AUTO_CONFIRM" == "true" ]; then
      echo "⚠️ Auto-confirm enabled. Continuing deployment despite certificate issues."
    else
      exit 1
    fi
  fi
else
  echo "✅ Certificate validity period includes current date"
fi

# Calculate days until expiration
if [ "$NOT_AFTER_TIMESTAMP" -gt 0 ]; then
  DAYS_UNTIL_EXPIRY=$(( ($NOT_AFTER_TIMESTAMP - $CURRENT_TIMESTAMP) / 86400 ))

  if [ "$DAYS_UNTIL_EXPIRY" -lt 30 ]; then
    echo "⚠️ Warning: Certificate expires soon ($DAYS_UNTIL_EXPIRY days remaining)"
  else
    echo "✅ Certificate expiration date is valid ($DAYS_UNTIL_EXPIRY days until expiry)"
  fi
else
  echo "⚠️ Warning: Could not calculate days until expiration"
fi

# Check certificate chain
CERT_COUNT=$(grep -c "BEGIN CERTIFICATE" "$SERVER_CERT" || echo "0")
echo "🔗 Certificate chain contains $CERT_COUNT certificates"

if [ "$CERT_COUNT" -lt 2 ]; then
  echo "⚠️ Warning: Certificate chain may not be complete (only $CERT_COUNT certificate found)"
  echo "   Running add-ca-to-certificates.sh to ensure proper certificate chain..."
  
  # Run the add-ca-to-certificates.sh script
  if [ -f "./deploy/scripts/add-ca-to-certificates.sh" ]; then
    chmod +x ./deploy/scripts/add-ca-to-certificates.sh
    ./deploy/scripts/add-ca-to-certificates.sh "$ENVIRONMENT"
    
    # Verify that the script worked
    NEW_CERT_COUNT=$(grep -c "BEGIN CERTIFICATE" "$SERVER_CERT" || echo "0")
    if [ "$NEW_CERT_COUNT" -ge 2 ]; then
      echo "✅ Certificate chain has been fixed (now contains $NEW_CERT_COUNT certificates)"
    else
      echo "❌ Failed to fix certificate chain. Still only $NEW_CERT_COUNT certificate."
      if [ "$AUTO_CONFIRM" == "true" ]; then
        echo "⚠️ Auto-confirm enabled. Continuing deployment despite certificate issues."
      else
        exit 1
      fi
    fi
  else
    echo "❌ Cannot find add-ca-to-certificates.sh script to fix certificate chain"
    if [ "$AUTO_CONFIRM" == "true" ]; then
      echo "⚠️ Auto-confirm enabled. Continuing deployment despite certificate issues."
    else
      exit 1
    fi
  fi
else
  echo "✅ Certificate chain appears complete with $CERT_COUNT certificates"
fi

# Create a temporary file for the root CA certificate
ROOT_CA_TEMP=$(mktemp)

# Determine which root CA to use for verification
if grep -q "EC PUBLIC KEY" "$SERVER_CERT" || openssl x509 -in "$SERVER_CERT" -noout -pubkey | openssl pkey -pubin -noout -text 2>/dev/null | grep -q "id-ecPublicKey"; then
  cat "$CF_ECC_ROOT_CERT" > "$ROOT_CA_TEMP"
  echo "Using ECC root certificate for verification..."
else
  cat "$CF_RSA_ROOT_CERT" > "$ROOT_CA_TEMP"
  echo "Using RSA root certificate for verification..."
fi

# Perform OpenSSL verification
if openssl verify -CAfile "$ROOT_CA_TEMP" "$SERVER_CERT" > /dev/null 2>&1; then
  echo "✅ Certificate chain successfully verified with OpenSSL"
else
  echo "❌ Certificate failed OpenSSL verification!"
  echo "   This will likely cause mTLS connection failures"
  
  # Attempt to update GCP secrets with the certificate
  echo "🔄 Attempting to update GCP Secret Manager with the certificate..."
  if [ -f "./deploy/scripts/update-gcp-cert-secret.sh" ]; then
    chmod +x ./deploy/scripts/update-gcp-cert-secret.sh
    ./deploy/scripts/update-gcp-cert-secret.sh "$SERVER_CERT"
    echo "✅ GCP Secret Manager updated with certificate"
  else
    echo "❌ Cannot find update-gcp-cert-secret.sh script to update GCP Secret Manager"
    if [ "$AUTO_CONFIRM" == "true" ]; then
      echo "⚠️ Auto-confirm enabled. Continuing deployment despite certificate issues."
    else
      exit 1
    fi
  fi
fi

# Clean up temporary file
rm -f "$ROOT_CA_TEMP"

echo "✅ Certificate verification complete"
echo "🚀 Proceed with deployment"
exit 0
