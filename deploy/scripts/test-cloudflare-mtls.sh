#!/bin/bash
# Script to test mTLS connections through Cloudflare

set -e

# Load Cloudflare credentials
if [ -f "private-keys/staging/cloudflare.env" ]; then
  source private-keys/staging/cloudflare.env
else
  echo "❌ Cloudflare credentials not found at private-keys/staging/cloudflare.env"
  exit 1
fi

# Check if required environment variables are set
if [ -z "$CF_API_TOKEN" ] || [ -z "$CF_ZONE_ID" ]; then
  echo "❌ Cloudflare API token or Zone ID not set"
  echo "Please ensure CF_API_TOKEN and CF_ZONE_ID are set in private-keys/staging/cloudflare.env"
  exit 1
fi

# Get the environment stage (default to staging if not provided)
STAGE=${1:-staging}
echo "Testing certificates for stage: $STAGE"

# Configuration
CA_CERT="./private-keys/$STAGE/certs/mtls/ca/ca.crt"
CLIENT_CERT="./private-keys/$STAGE/certs/mtls/services/public-api/client.crt"
CLIENT_KEY="./private-keys/$STAGE/certs/mtls/services/public-api/client.key"

# Function to check if a DNS record exists
check_dns_record() {
  local service_name=$1

  echo "🔍 Checking DNS record for $service_name.divinci.internal..."

  # Get DNS records for the service
  response=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/dns_records?name=$service_name.divinci.internal" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json")

  # Check if the request was successful
  success=$(echo "$response" | grep -o '"success":true' || echo "")
  if [ -z "$success" ]; then
    echo "❌ Failed to get DNS records for $service_name"
    echo "$response"
    return 1
  fi

  # Check if the DNS record exists
  count=$(echo "$response" | grep -o '"count":[0-9]*' | cut -d':' -f2)
  if [ "$count" -eq "0" ]; then
    echo "❌ DNS record for $service_name.divinci.internal not found"
    return 1
  else
    echo "✅ DNS record for $service_name.divinci.internal found"

    # Get the IP address
    ip=$(echo "$response" | grep -o '"content":"[^"]*"' | cut -d'"' -f4)
    echo "ℹ️ IP address: $ip"

    return 0
  fi
}

# Function to test mTLS connection to a service
test_mtls_connection() {
  local service_name=$1
  local service_port=$2

  echo "🔍 Testing mTLS connection to $service_name.divinci.internal:$service_port..."

  # Use curl with mTLS certificates
  response=$(curl --cacert "$CA_CERT" \
          --cert "$CLIENT_CERT" \
          --key "$CLIENT_KEY" \
          -s -v \
          "https://$service_name.divinci.internal:$service_port/health" 2>&1)

  # Check if the connection was successful
  if echo "$response" | grep -q "SSL handshake"; then
    echo "✅ SSL handshake with $service_name was successful"
  else
    echo "❌ SSL handshake with $service_name failed"
    echo "$response" | grep -A 10 -B 10 "error"
    return 1
  fi

  # Check if the server certificate was verified
  if echo "$response" | grep -q "server certificate verification OK"; then
    echo "✅ Server certificate for $service_name was verified"
  else
    echo "❌ Server certificate verification for $service_name failed"
    return 1
  fi

  # Check if the client certificate was sent
  if echo "$response" | grep -q "SSL certificate verify ok"; then
    echo "✅ Client certificate was accepted by $service_name"
  else
    echo "❌ Client certificate was rejected by $service_name"
    return 1
  fi

  # Check HTTP status code
  status_code=$(echo "$response" | grep -o "< HTTP/[0-9.]* [0-9]*" | awk '{print $3}')
  if [ ! -z "$status_code" ]; then
    echo "📊 HTTP status code: $status_code"
    if [ "$status_code" = "200" ]; then
      echo "✅ Successfully connected to $service_name with mTLS"
      return 0
    else
      echo "⚠️ Connected to $service_name but received status code $status_code"
      return 1
    fi
  else
    echo "❌ Failed to connect to $service_name with mTLS"
    return 1
  fi
}

# Check if certificates exist
if [ ! -f "$CA_CERT" ] || [ ! -f "$CLIENT_CERT" ] || [ ! -f "$CLIENT_KEY" ]; then
  echo "❌ mTLS certificates not found. Please run generate-mtls-certs.sh first."
  exit 1
fi

# Check DNS records
echo "🔍 Checking DNS records in Cloudflare..."
check_dns_record "ffmpeg"
check_dns_record "open-parse"
check_dns_record "pyannote"

# Test mTLS connections
echo "🧪 Testing mTLS connections through Cloudflare..."
test_mtls_connection "ffmpeg" "443"
test_mtls_connection "open-parse" "443"
test_mtls_connection "pyannote" "443"

echo "✅ mTLS connection tests completed!"
