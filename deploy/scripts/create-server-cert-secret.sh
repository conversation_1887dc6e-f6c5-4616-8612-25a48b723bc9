#!/bin/bash
# <PERSON>ript to create the server-cert and server-key secrets in GCP

set -e

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

# Get the environment from the command line argument or use a default
ENVIRONMENT=${1:-"staging"}
echo "🔍 Creating/updating secrets for $ENVIRONMENT environment..."

# Define paths based on the environment
CERT_DIR="$REPO_ROOT/private-keys/${ENVIRONMENT}/certs/mtls"
CERT_FILE="${CERT_DIR}/server.crt"
KEY_FILE="${CERT_DIR}/server.key"

# Check if the certificate directory exists
if [ ! -d "$CERT_DIR" ]; then
  echo "❌ Certificate directory not found: $CERT_DIR"
  echo "Available environments:"
  ls -la ./private-keys/
  exit 1
fi

# Check if the certificate files exist
if [ ! -f "$CERT_FILE" ]; then
  echo "❌ Certificate file not found: $CERT_FILE"
  echo "Available files in $CERT_DIR:"
  ls -la "$CERT_DIR"
  exit 1
fi

if [ ! -f "$KEY_FILE" ]; then
  echo "❌ Key file not found: $KEY_FILE"
  echo "Available files in $CERT_DIR:"
  ls -la "$CERT_DIR"
  exit 1
fi

# Create or update the server-crt secret
echo "🔐 Creating/updating server-crt secret..."
if gcloud secrets describe server-crt &>/dev/null; then
  echo "✅ Secret 'server-crt' already exists. Adding a new version..."
  gcloud secrets versions add server-crt --data-file="$CERT_FILE"
else
  echo "✅ Secret 'server-crt' does not exist. Creating it..."
  gcloud secrets create server-crt --replication-policy="automatic" --data-file="$CERT_FILE"
fi

# Create or update the server-key secret
echo "🔐 Creating/updating server-key secret..."
if gcloud secrets describe server-key &>/dev/null; then
  echo "✅ Secret 'server-key' already exists. Adding a new version..."
  gcloud secrets versions add server-key --data-file="$KEY_FILE"
else
  echo "✅ Secret 'server-key' does not exist. Creating it..."
  gcloud secrets create server-key --replication-policy="automatic" --data-file="$KEY_FILE"
fi

# Verify the secrets
echo "🔍 Verifying secrets..."

# Verify server-crt secret
if gcloud secrets versions access latest --secret=server-crt &>/dev/null; then
  echo "✅ Secret 'server-crt' with version 'latest' exists and is accessible."
  echo "Certificate content (first few lines):"
  gcloud secrets versions access latest --secret=server-crt | head -5
else
  echo "❌ Error: Could not access the latest version of the secret 'server-crt'."
  exit 1
fi

# Verify server-key secret
if gcloud secrets versions access latest --secret=server-key &>/dev/null; then
  echo "✅ Secret 'server-key' with version 'latest' exists and is accessible."
  echo "Key content (first few lines):"
  gcloud secrets versions access latest --secret=server-key | head -5
else
  echo "❌ Error: Could not access the latest version of the secret 'server-key'."
  exit 1
fi

echo "🎉 Done! Secrets updated successfully for $ENVIRONMENT environment."
