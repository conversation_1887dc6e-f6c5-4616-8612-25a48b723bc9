#!/bin/bash
# <PERSON>ript to fix Cloudflare Error 526 (Invalid SSL Certificate)
# This script will:
# 1. Download Cloudflare Origin CA root certificates
# 2. Install the Cloudflare Origin Certificate
# 3. Add the Cloudflare Root CA to the certificate chain
# 4. Update GCP secrets

set -e

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Environment setup
ENVIRONMENT=${1:-"staging"}
echo "🔍 Starting Cloudflare Error 526 fix for $ENVIRONMENT environment..."

# Step 1: Download Cloudflare Origin CA root certificates
echo "📥 Step 1: Downloading Cloudflare Origin CA root certificates..."
"$SCRIPT_DIR/download-cloudflare-ca-certs.sh" $ENVIRONMENT
echo "✅ Step 1 completed successfully."

# Step 2: Install the Cloudflare Origin Certificate
echo "🔐 Step 2: Installing Cloudflare Origin Certificate..."
"$SCRIPT_DIR/install-cloudflare-origin-cert.sh" $ENVIRONMENT
echo "✅ Step 2 completed successfully."

# Step 3: Add the Cloudflare Root CA to the certificate chain
echo "🔗 Step 3: Adding Cloudflare Root CA to certificate chain..."
"$SCRIPT_DIR/add-ca-to-certificates.sh" $ENVIRONMENT
echo "✅ Step 3 completed successfully."

# Step 4: Update GCP secrets
echo "🔄 Step 4: Updating GCP secrets..."
"$SCRIPT_DIR/create-server-cert-secret.sh"
echo "✅ Step 4 completed successfully."

echo "🎉 All steps completed successfully!"
echo "📋 Next steps:"
echo "1. Make sure your GCP Cloud Run service has the following volume mounts:"
echo "   - Secret 'server-crt' mounted at '/etc/ssl/certs'"
echo "   - Secret 'server-key' mounted at '/etc/ssl/private'"
echo "2. Make sure your service has the following environment variables:"
echo "   - ENABLE_MTLS=1"
echo "   - MTLS_CERT_DIR=/etc/ssl"
echo "3. Restart your service to apply the changes"
echo "4. Run the following command to verify the fix:"
echo "   \"$SCRIPT_DIR/tests/cf-cert-test.sh\""
