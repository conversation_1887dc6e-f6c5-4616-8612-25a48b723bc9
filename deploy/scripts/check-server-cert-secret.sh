#!/bin/bash
# <PERSON><PERSON>t to check the status of the server-cert secret in GCP

set -e

echo "Checking if secret 'server-cert' exists..."
if gcloud secrets describe server-cert &>/dev/null; then
  echo "✅ Secret 'server-cert' exists."
  echo "Note: The application expects the certificate to be mounted at '/etc/ssl/certs/server.crt'"

  echo "Listing versions of the secret..."
  gcloud secrets versions list server-cert

  echo "Checking if version 'latest' exists and is accessible..."
  if gcloud secrets versions access latest --secret=server-cert &>/dev/null; then
    echo "✅ Version 'latest' exists and is accessible."

    echo "Certificate content (first few lines):"
    gcloud secrets versions access latest --secret=server-cert | head -5

    echo "Checking if the certificate is in PEM format..."
    if gcloud secrets versions access latest --secret=server-cert | grep -q "BEGIN CERTIFICATE"; then
      echo "✅ Certificate is in PEM format."
    else
      echo "❌ Certificate is NOT in PEM format. It should start with '-----BEGIN CERTIFICATE-----'."
    fi
  else
    echo "❌ Version 'latest' does not exist or is not accessible."
  fi
else
  echo "❌ Secret 'server-cert' does not exist."
fi

echo "Done."
