#!/bin/bash
# Script to generate CA and service certificates for mTLS

set -e

# Get the environment stage (default to local if not provided)
STAGE=${1:-local}
echo "Generating certificates for stage: $STAGE"

# Configuration
CERT_DIR="./private-keys/$STAGE/certs/mtls"
CA_DIR="$CERT_DIR/ca"
SERVICE_DIR="$CERT_DIR/services"
CA_DAYS=3650  # 10 years
SERVICE_DAYS=730  # 2 years

# Create directories
mkdir -p "$CA_DIR"
mkdir -p "$SERVICE_DIR"
mkdir -p "$SERVICE_DIR/public-api"
mkdir -p "$SERVICE_DIR/ffmpeg"
mkdir -p "$SERVICE_DIR/open-parse"
mkdir -p "$SERVICE_DIR/pyannote"

# Function to generate CA certificate
generate_ca() {
  echo "Generating CA certificate..."

  # Generate CA private key
  openssl genrsa -out "$CA_DIR/ca.key" 4096

  # Generate CA certificate
  openssl req -x509 -new -nodes -key "$CA_DIR/ca.key" -sha256 -days $CA_DAYS -out "$CA_DIR/ca.crt" \
    -subj "/C=US/ST=California/L=San Francisco/O=Divinci AI/OU=Security/CN=Divinci Internal CA"

  echo "CA certificate generated at $CA_DIR/ca.crt"
  echo "CA private key generated at $CA_DIR/ca.key"
}

# Function to generate service certificate
generate_service_cert() {
  SERVICE_NAME=$1
  SERVICE_DNS=$2

  echo "Generating certificate for $SERVICE_NAME..."

  SERVICE_PATH="$SERVICE_DIR/$SERVICE_NAME"

  # Generate service private key
  openssl genrsa -out "$SERVICE_PATH/server.key" 2048

  # Create config file for SAN
  cat > "$SERVICE_PATH/openssl.cnf" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = req_ext

[dn]
C = US
ST = California
L = San Francisco
O = Divinci AI
OU = $SERVICE_NAME
CN = $SERVICE_NAME.divinci.internal

[req_ext]
subjectAltName = @alt_names

[alt_names]
DNS.1 = $SERVICE_NAME
DNS.2 = $SERVICE_NAME.divinci.internal
DNS.3 = localhost
EOF

  # Add additional DNS names if provided
  if [ ! -z "$SERVICE_DNS" ]; then
    IFS=',' read -ra DNS_ARRAY <<< "$SERVICE_DNS"
    COUNT=4
    for DNS in "${DNS_ARRAY[@]}"; do
      echo "DNS.$COUNT = $DNS" >> "$SERVICE_PATH/openssl.cnf"
      COUNT=$((COUNT+1))
    done
  fi

  # Generate CSR
  openssl req -new -key "$SERVICE_PATH/server.key" -out "$SERVICE_PATH/server.csr" -config "$SERVICE_PATH/openssl.cnf"

  # Create extension file for SAN
  cat > "$SERVICE_PATH/v3.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $SERVICE_NAME
DNS.2 = $SERVICE_NAME.divinci.internal
DNS.3 = localhost
EOF

  # Add additional DNS names if provided
  if [ ! -z "$SERVICE_DNS" ]; then
    IFS=',' read -ra DNS_ARRAY <<< "$SERVICE_DNS"
    COUNT=4
    for DNS in "${DNS_ARRAY[@]}"; do
      echo "DNS.$COUNT = $DNS" >> "$SERVICE_PATH/v3.ext"
      COUNT=$((COUNT+1))
    done
  fi

  # Sign the certificate
  openssl x509 -req -in "$SERVICE_PATH/server.csr" -CA "$CA_DIR/ca.crt" -CAkey "$CA_DIR/ca.key" \
    -CAcreateserial -out "$SERVICE_PATH/server.crt" -days $SERVICE_DAYS \
    -extfile "$SERVICE_PATH/v3.ext"

  # Create client certificate (for public-api to authenticate to services)
  if [ "$SERVICE_NAME" = "public-api" ]; then
    echo "Generating client certificate for $SERVICE_NAME..."

    # Generate client private key
    openssl genrsa -out "$SERVICE_PATH/client.key" 2048

    # Create config file for client cert
    cat > "$SERVICE_PATH/client.cnf" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = req_ext

[dn]
C = US
ST = California
L = San Francisco
O = Divinci AI
OU = $SERVICE_NAME-client
CN = $SERVICE_NAME-client.divinci.internal

[req_ext]
subjectAltName = @alt_names

[alt_names]
DNS.1 = $SERVICE_NAME-client
DNS.2 = $SERVICE_NAME-client.divinci.internal
EOF

    # Generate client CSR
    openssl req -new -key "$SERVICE_PATH/client.key" -out "$SERVICE_PATH/client.csr" -config "$SERVICE_PATH/client.cnf"

    # Create extension file for client cert
    cat > "$SERVICE_PATH/client-v3.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $SERVICE_NAME-client
DNS.2 = $SERVICE_NAME-client.divinci.internal
EOF

    # Sign the client certificate
    openssl x509 -req -in "$SERVICE_PATH/client.csr" -CA "$CA_DIR/ca.crt" -CAkey "$CA_DIR/ca.key" \
      -CAcreateserial -out "$SERVICE_PATH/client.crt" -days $SERVICE_DAYS \
      -extfile "$SERVICE_PATH/client-v3.ext"
  fi

  echo "Certificate for $SERVICE_NAME generated at $SERVICE_PATH/server.crt"
  echo "Private key for $SERVICE_NAME generated at $SERVICE_PATH/server.key"

  # Clean up temporary files
  rm -f "$SERVICE_PATH/server.csr" "$SERVICE_PATH/openssl.cnf" "$SERVICE_PATH/v3.ext"
  if [ "$SERVICE_NAME" = "public-api" ]; then
    rm -f "$SERVICE_PATH/client.csr" "$SERVICE_PATH/client.cnf" "$SERVICE_PATH/client-v3.ext"
  fi
}

# Generate CA certificate
generate_ca

# Generate service certificates
generate_service_cert "public-api" "local-api,api.divinci.internal"
generate_service_cert "ffmpeg" "audio-splitter-ffmpeg,ffmpeg.divinci.internal"
generate_service_cert "open-parse" "local-open-parse,open-parse.divinci.internal"
generate_service_cert "pyannote" "audio-speak-dia-pyannote,pyannote.divinci.internal"

# Copy CA certificate to each service directory for convenience
cp "$CA_DIR/ca.crt" "$SERVICE_DIR/public-api/"
cp "$CA_DIR/ca.crt" "$SERVICE_DIR/ffmpeg/"
cp "$CA_DIR/ca.crt" "$SERVICE_DIR/open-parse/"
cp "$CA_DIR/ca.crt" "$SERVICE_DIR/pyannote/"

# Set proper permissions
chmod 644 "$CA_DIR/ca.crt"
chmod 600 "$CA_DIR/ca.key"
find "$SERVICE_DIR" -name "*.crt" -exec chmod 644 {} \;
find "$SERVICE_DIR" -name "*.key" -exec chmod 600 {} \;

echo "Certificate generation complete!"
echo "CA and service certificates are in $CERT_DIR"
echo ""
echo "Next steps:"
echo "1. Distribute the certificates to the appropriate services"
echo "2. Update service configurations to use the certificates"
echo "3. Test mTLS connections between services"
