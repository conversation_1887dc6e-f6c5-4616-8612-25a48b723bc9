#!/bin/bash
# <PERSON>ript to copy Cloudflare Origin certificate files to standard locations
# Usage: ./copy-cert-files.sh <environment>
# Example: ./copy-cert-files.sh staging

set -e

ENVIRONMENT=$1
CERT_DIR="./private-keys/${ENVIRONMENT}/certs/mtls"

if [ -z "$ENVIRONMENT" ]; then
  echo "Usage: $0 <environment>"
  echo "Example: $0 staging"
  exit 1
fi

# Check if the certificate directory exists
if [ ! -d "$CERT_DIR" ]; then
  echo "Error: Certificate directory $CERT_DIR does not exist"
  exit 1
fi

# Check if the origin certificate files exist
if [ ! -f "$CERT_DIR/origin.crt" ]; then
  echo "Error: Origin certificate file $CERT_DIR/origin.crt does not exist"
  exit 1
fi

if [ ! -f "$CERT_DIR/origin.key" ]; then
  echo "Error: Origin key file $CERT_DIR/origin.key does not exist"
  exit 1
fi

echo "Copying Cloudflare Origin certificates to standard locations for $ENVIRONMENT environment..."

# Make backup of existing files if they exist
if [ -f "$CERT_DIR/server.crt" ]; then
  cp "$CERT_DIR/server.crt" "$CERT_DIR/server.crt.backup"
  echo "Backed up server.crt to server.crt.backup"
fi

if [ -f "$CERT_DIR/server.key" ]; then
  cp "$CERT_DIR/server.key" "$CERT_DIR/server.key.backup"
  echo "Backed up server.key to server.key.backup"
fi

if [ -f "$CERT_DIR/server.crt.pem" ]; then
  cp "$CERT_DIR/server.crt.pem" "$CERT_DIR/server.crt.pem.backup"
  echo "Backed up server.crt.pem to server.crt.pem.backup"
fi

if [ -f "$CERT_DIR/server.key.pem" ]; then
  cp "$CERT_DIR/server.key.pem" "$CERT_DIR/server.key.pem.backup"
  echo "Backed up server.key.pem to server.key.pem.backup"
fi

# Copy origin certificates to standard locations
cp "$CERT_DIR/origin.crt" "$CERT_DIR/server.crt"
cp "$CERT_DIR/origin.key" "$CERT_DIR/server.key"
cp "$CERT_DIR/origin.crt" "$CERT_DIR/server.crt.pem"
cp "$CERT_DIR/origin.key" "$CERT_DIR/server.key.pem"

echo "Certificate files copied successfully!"
echo "The following files have been updated with Cloudflare Origin certificates:"
echo "- $CERT_DIR/server.crt"
echo "- $CERT_DIR/server.key"
echo "- $CERT_DIR/server.crt.pem"
echo "- $CERT_DIR/server.key.pem"
echo ""
echo "Original files have been backed up with .backup extension if they existed."
echo ""
echo "IMPORTANT NEXT STEPS:"
echo "1. Use the create-mtls-secret.sh script to update the GCP secrets with these certificates"
echo "2. Restart any services using these certificates for changes to take effect"
echo "3. Verify on Cloudflare dashboard that 'Full (strict)' mode is enabled"

exit 0

# This script replaces the original deprecated script and now handles copying 
# Cloudflare Origin certificates to standard locations expected by the system
