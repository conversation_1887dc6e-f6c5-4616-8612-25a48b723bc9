#!/bin/bash
# Script to verify Cloudflare configuration for mTLS

set -e

# Load Cloudflare credentials
if [ -f "private-keys/staging/cloudflare.env" ]; then
  source private-keys/staging/cloudflare.env
else
  echo "❌ Cloudflare credentials not found at private-keys/staging/cloudflare.env"
  exit 1
fi

# Check if required environment variables are set
if [ -z "$CF_API_TOKEN" ] || [ -z "$CF_ZONE_ID" ]; then
  echo "❌ Cloudflare API token or Zone ID not set"
  echo "Please ensure CF_API_TOKEN and CF_ZONE_ID are set in private-keys/staging/cloudflare.env"
  exit 1
fi

# Function to check DNS records for a service
check_dns_record() {
  local service_name=$1
  local expected_ip=$2
  
  echo "🔍 Checking DNS record for $service_name..."
  
  # Get DNS records for the service
  response=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/dns_records?name=$service_name.divinci.internal" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json")
  
  # Check if the request was successful
  success=$(echo "$response" | grep -o '"success":true' || echo "")
  if [ -z "$success" ]; then
    echo "❌ Failed to get DNS records for $service_name"
    echo "$response"
    return 1
  fi
  
  # Check if the DNS record exists
  count=$(echo "$response" | grep -o '"count":[0-9]*' | cut -d':' -f2)
  if [ "$count" -eq "0" ]; then
    echo "❌ DNS record for $service_name.divinci.internal not found"
    echo "Creating DNS record..."
    
    # Create DNS record
    create_response=$(curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/dns_records" \
      -H "Authorization: Bearer $CF_API_TOKEN" \
      -H "Content-Type: application/json" \
      --data "{\"type\":\"A\",\"name\":\"$service_name.divinci.internal\",\"content\":\"$expected_ip\",\"ttl\":120,\"proxied\":false}")
    
    # Check if the creation was successful
    create_success=$(echo "$create_response" | grep -o '"success":true' || echo "")
    if [ -z "$create_success" ]; then
      echo "❌ Failed to create DNS record for $service_name"
      echo "$create_response"
      return 1
    fi
    
    echo "✅ DNS record for $service_name.divinci.internal created"
  else
    echo "✅ DNS record for $service_name.divinci.internal found"
    
    # Get the current IP address
    current_ip=$(echo "$response" | grep -o '"content":"[^"]*"' | cut -d'"' -f4)
    
    # Check if the IP address matches the expected IP
    if [ "$current_ip" != "$expected_ip" ]; then
      echo "⚠️ Current IP ($current_ip) does not match expected IP ($expected_ip)"
      echo "Updating DNS record..."
      
      # Get the record ID
      record_id=$(echo "$response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
      
      # Update DNS record
      update_response=$(curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CF_ZONE_ID/dns_records/$record_id" \
        -H "Authorization: Bearer $CF_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "{\"type\":\"A\",\"name\":\"$service_name.divinci.internal\",\"content\":\"$expected_ip\",\"ttl\":120,\"proxied\":false}")
      
      # Check if the update was successful
      update_success=$(echo "$update_response" | grep -o '"success":true' || echo "")
      if [ -z "$update_success" ]; then
        echo "❌ Failed to update DNS record for $service_name"
        echo "$update_response"
        return 1
      fi
      
      echo "✅ DNS record for $service_name.divinci.internal updated"
    else
      echo "✅ DNS record for $service_name.divinci.internal has the correct IP"
    fi
  fi
  
  return 0
}

# Function to test mTLS connection through Cloudflare
test_mtls_connection() {
  local service_name=$1
  local service_port=$2
  
  echo "🔍 Testing mTLS connection to $service_name.divinci.internal:$service_port..."
  
  # Use curl with mTLS certificates
  if curl --cacert "private-keys/mtls/ca/ca.crt" \
          --cert "private-keys/mtls/services/public-api/client.crt" \
          --key "private-keys/mtls/services/public-api/client.key" \
          -s -o /dev/null -w "%{http_code}" \
          "https://$service_name.divinci.internal:$service_port/health" | grep -q "200\|401\|403"; then
    echo "✅ Successfully connected to $service_name.divinci.internal with mTLS"
    return 0
  else
    echo "❌ Failed to connect to $service_name.divinci.internal with mTLS"
    return 1
  fi
}

# Main script

echo "🔍 Verifying Cloudflare configuration for mTLS..."

# Check DNS records for each service
check_dns_record "ffmpeg" "********"
check_dns_record "open-parse" "********"
check_dns_record "pyannote" "********"
check_dns_record "public-api" "********"

echo "✅ DNS records verified"

# Test mTLS connections through Cloudflare
test_mtls_connection "ffmpeg" "8001"
test_mtls_connection "open-parse" "8002"
test_mtls_connection "pyannote" "8003"

echo "✅ mTLS connection tests completed!"
