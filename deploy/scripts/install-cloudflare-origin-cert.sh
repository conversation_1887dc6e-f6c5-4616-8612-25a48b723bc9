#!/bin/bash
# Script to install Cloudflare Origin Certificate for GCP Cloud Run

set -e

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

# Environment setup
ENVIRONMENT=${1:-"staging"}
echo "🔍 Installing Cloudflare Origin Certificate for $ENVIRONMENT environment..."

# Paths
CERT_DIR="$REPO_ROOT/private-keys/${ENVIRONMENT}/certs/mtls"
mkdir -p "$CERT_DIR"

# Check if certificate files already exist in the target directory
if [ -f "$CERT_DIR/origin.crt" ] && [ -f "$CERT_DIR/origin.key" ]; then
  echo "✅ Found existing Cloudflare Origin Certificate files in $CERT_DIR."
  ORIGIN_CERT="$CERT_DIR/origin.crt"
  ORIGIN_KEY="$CERT_DIR/origin.key"
else
  # Check if certificate files exist in the private-keys directory
  if [ -f "$CERT_DIR/origin-cert.pem" ] && [ -f "$CERT_DIR/origin-key.pem" ]; then
    echo "✅ Found Cloudflare Origin Certificate files in $CERT_DIR."
    ORIGIN_CERT="$CERT_DIR/origin-cert.pem"
    ORIGIN_KEY="$CERT_DIR/origin-key.pem"
  # Check if certificate files are in the current directory
  elif [ -f "origin-cert.pem" ] && [ -f "origin-key.pem" ]; then
    echo "✅ Found Cloudflare Origin Certificate files in current directory."
    ORIGIN_CERT="origin-cert.pem"
    ORIGIN_KEY="origin-key.pem"
  # Check if server.crt and server.key exist
  elif [ -f "$CERT_DIR/server.crt" ] && [ -f "$CERT_DIR/server.key" ]; then
    echo "✅ Found server certificate files in $CERT_DIR. Using these as origin certificates."
    ORIGIN_CERT="$CERT_DIR/server.crt"
    ORIGIN_KEY="$CERT_DIR/server.key"

    # Copy them to origin.crt and origin.key
    echo "📋 Copying server certificate files to origin certificate files..."
    cp "$ORIGIN_CERT" "$CERT_DIR/origin.crt"
    cp "$ORIGIN_KEY" "$CERT_DIR/origin.key"
  else
    echo "❌ Cloudflare Origin Certificate files not found."
    echo "Please ensure the certificate files exist in one of these locations:"
    echo "  - $CERT_DIR/origin.crt and $CERT_DIR/origin.key"
    echo "  - $CERT_DIR/origin-cert.pem and $CERT_DIR/origin-key.pem"
    echo "  - $CERT_DIR/server.crt and $CERT_DIR/server.key"
    echo "  - ./origin-cert.pem and ./origin-key.pem (current directory)"
    exit 1
  fi

  # Copy the certificate files to the certificate directory if they're not already there
  if [ "$ORIGIN_CERT" != "$CERT_DIR/origin.crt" ] && [ "$ORIGIN_CERT" != "$CERT_DIR/server.crt" ]; then
    echo "📋 Copying Cloudflare Origin Certificate files to $CERT_DIR..."
    cp "$ORIGIN_CERT" "$CERT_DIR/origin.crt"
    cp "$ORIGIN_KEY" "$CERT_DIR/origin.key"
  fi
fi

# Also save as server.crt and server.key for compatibility
cp "$CERT_DIR/origin.crt" "$CERT_DIR/server.crt"
cp "$CERT_DIR/origin.crt" "$CERT_DIR/server.crt.pem"
cp "$CERT_DIR/origin.key" "$CERT_DIR/server.key"
cp "$CERT_DIR/origin.key" "$CERT_DIR/server.key.pem"

echo "✅ Cloudflare Origin Certificate files copied successfully."

# Create GCP secrets
echo "🔐 Creating GCP secrets for Cloudflare Origin Certificate..."

# Create server-crt secret
if gcloud secrets describe server-crt &>/dev/null; then
  echo "Secret 'server-crt' already exists. Adding a new version..."
  gcloud secrets versions add server-crt --data-file="$CERT_DIR/server.crt"
else
  echo "Creating secret 'server-crt'..."
  gcloud secrets create server-crt --replication-policy="automatic" --data-file="$CERT_DIR/server.crt"
fi

# Create server-key secret
if gcloud secrets describe server-key &>/dev/null; then
  echo "Secret 'server-key' already exists. Adding a new version..."
  gcloud secrets versions add server-key --data-file="$CERT_DIR/server.key"
else
  echo "Creating secret 'server-key'..."
  gcloud secrets create server-key --replication-policy="automatic" --data-file="$CERT_DIR/server.key"
fi

echo "✅ GCP secrets created/updated successfully."

echo "📋 Next steps:"
echo "1. Make sure your GCP Cloud Run service has the following volume mounts:"
echo "   - Secret 'server-crt' mounted at '/etc/ssl/certs'"
echo "   - Secret 'server-key' mounted at '/etc/ssl/private'"
echo "2. Make sure your service has the following environment variables:"
echo "   - ENABLE_MTLS=1"
echo "   - MTLS_CERT_DIR=/etc/ssl"
echo "3. Restart your service to apply the changes"
echo "4. Check Cloudflare for resolution of Error 526"

echo "✅ Installation complete."
