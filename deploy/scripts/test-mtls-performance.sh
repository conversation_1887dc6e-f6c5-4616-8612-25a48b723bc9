#!/bin/bash
# Script to test mTLS performance

set -e

# Get the environment stage (default to local if not provided)
STAGE=${1:-local}
echo "Testing performance for stage: $STAGE"

# Configuration
CA_CERT="./private-keys/$STAGE/certs/mtls/ca/ca.crt"
CLIENT_CERT="./private-keys/$STAGE/certs/mtls/services/public-api/client.crt"
CLIENT_KEY="./private-keys/$STAGE/certs/mtls/services/public-api/client.key"
TEST_ITERATIONS=100
TEST_CONCURRENCY=10

# Function to test service performance with mTLS
test_mtls_performance() {
  local service_name=$1
  local service_port=$2

  echo "🧪 Testing mTLS performance for $service_name on port $service_port..."

  # Test with mTLS
  echo "ℹ️ Testing with mTLS..."
  mtls_start_time=$(date +%s.%N)

  for i in $(seq 1 $TEST_ITERATIONS); do
    # Use curl with mTLS certificates
    curl --cacert "$CA_CERT" \
         --cert "$CLIENT_CERT" \
         --key "$CLIENT_KEY" \
         -s -o /dev/null \
         "https://localhost:$service_port/health" &

    # Limit concurrency
    if [ $(( i % $TEST_CONCURRENCY )) -eq 0 ]; then
      wait
    fi
  done

  # Wait for any remaining requests to complete
  wait

  mtls_end_time=$(date +%s.%N)
  mtls_duration=$(echo "$mtls_end_time - $mtls_start_time" | bc)
  mtls_avg_time=$(echo "$mtls_duration / $TEST_ITERATIONS" | bc -l)

  echo "📊 mTLS Performance Results for $service_name:"
  echo "   Total Time: $mtls_duration seconds"
  echo "   Average Request Time: $mtls_avg_time seconds"
  echo "   Requests per Second: $(echo "$TEST_ITERATIONS / $mtls_duration" | bc -l)"

  # Test without mTLS (HTTP)
  echo "ℹ️ Testing without mTLS (HTTP)..."
  http_start_time=$(date +%s.%N)

  for i in $(seq 1 $TEST_ITERATIONS); do
    # Use curl without mTLS certificates
    curl -s -o /dev/null \
         "http://localhost:$service_port/health" &

    # Limit concurrency
    if [ $(( i % $TEST_CONCURRENCY )) -eq 0 ]; then
      wait
    fi
  done

  # Wait for any remaining requests to complete
  wait

  http_end_time=$(date +%s.%N)
  http_duration=$(echo "$http_end_time - $http_start_time" | bc)
  http_avg_time=$(echo "$http_duration / $TEST_ITERATIONS" | bc -l)

  echo "📊 HTTP Performance Results for $service_name:"
  echo "   Total Time: $http_duration seconds"
  echo "   Average Request Time: $http_avg_time seconds"
  echo "   Requests per Second: $(echo "$TEST_ITERATIONS / $http_duration" | bc -l)"

  # Calculate overhead
  overhead=$(echo "($mtls_duration - $http_duration) / $http_duration * 100" | bc -l)
  echo "📊 mTLS Overhead for $service_name: $overhead%"

  return 0
}

# Check if certificates exist
if [ ! -f "$CA_CERT" ] || [ ! -f "$CLIENT_CERT" ] || [ ! -f "$CLIENT_KEY" ]; then
  echo "❌ mTLS certificates not found. Please run generate-mtls-certs.sh first."
  exit 1
fi

# Test performance for each service
echo "🧪 Testing mTLS performance for services..."

# Test FFmpeg service
test_mtls_performance "FFmpeg" "19001"

# Test Open-Parse service
test_mtls_performance "Open-Parse" "19002"

# Test Pyannote service
test_mtls_performance "Pyannote" "19000"

echo "✅ mTLS performance tests completed!"
