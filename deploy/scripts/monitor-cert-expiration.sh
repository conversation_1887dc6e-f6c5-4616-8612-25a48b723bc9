#!/bin/bash
# Script to monitor certificate expiration

set -e

# Get the environment stage (default to local if not provided)
STAGE=${1:-local}
echo "Monitoring certificates for stage: $STAGE"

# Configuration
CERT_DIR="./private-keys/$STAGE/certs/mtls"
CA_DIR="$CERT_DIR/ca"
SERVICE_DIR="$CERT_DIR/services"
WARNING_DAYS=30  # Warn if certificate expires within this many days

# Function to check certificate expiration
check_cert_expiration() {
  local cert_file=$1
  local cert_name=$2

  echo "🔍 Checking expiration for $cert_name at $cert_file..."

  # Get certificate expiration date
  expiration_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)

  # Convert to seconds since epoch
  expiration_seconds=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$expiration_date" +%s)
  current_seconds=$(date +%s)

  # Calculate days until expiration
  days_until_expiration=$(( (expiration_seconds - current_seconds) / 86400 ))

  echo "ℹ️ $cert_name expires on $expiration_date ($days_until_expiration days from now)"

  # Check if certificate is expiring soon
  if [ $days_until_expiration -le $WARNING_DAYS ]; then
    echo "⚠️ WARNING: $cert_name expires in $days_until_expiration days!"
    return 1
  fi

  return 0
}

# Function to send alert
send_alert() {
  local cert_name=$1
  local days_until_expiration=$2

  echo "📧 Sending alert for $cert_name expiring in $days_until_expiration days..."

  # Send alert (this is a placeholder - replace with your actual alerting mechanism)
  echo "ALERT: $cert_name expires in $days_until_expiration days!" >> cert-expiration-alerts.log

  # Example: Send email alert
  # echo "ALERT: $cert_name expires in $days_until_expiration days!" | mail -s "Certificate Expiration Alert" <EMAIL>

  echo "✅ Alert sent"
}

# Main script

echo "🔍 Monitoring certificate expiration..."

# Check CA certificate
ca_cert="$CA_DIR/ca.crt"
if [ -f "$ca_cert" ]; then
  check_cert_expiration "$ca_cert" "CA certificate"
  if [ $? -ne 0 ]; then
    expiration_date=$(openssl x509 -in "$ca_cert" -noout -enddate | cut -d= -f2)
    expiration_seconds=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$expiration_date" +%s)
    current_seconds=$(date +%s)
    days_until_expiration=$(( (expiration_seconds - current_seconds) / 86400 ))
    send_alert "CA certificate" "$days_until_expiration"
  fi
else
  echo "❌ CA certificate not found at $ca_cert"
fi

# Check service certificates
for service in public-api ffmpeg open-parse pyannote; do
  server_cert="$SERVICE_DIR/$service/server.crt"
  if [ -f "$server_cert" ]; then
    check_cert_expiration "$server_cert" "$service server certificate"
    if [ $? -ne 0 ]; then
      expiration_date=$(openssl x509 -in "$server_cert" -noout -enddate | cut -d= -f2)
      expiration_seconds=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$expiration_date" +%s)
      current_seconds=$(date +%s)
      days_until_expiration=$(( (expiration_seconds - current_seconds) / 86400 ))
      send_alert "$service server certificate" "$days_until_expiration"
    fi
  else
    echo "❌ $service server certificate not found at $server_cert"
  fi
done

# Check client certificate for public-api
client_cert="$SERVICE_DIR/public-api/client.crt"
if [ -f "$client_cert" ]; then
  check_cert_expiration "$client_cert" "public-api client certificate"
  if [ $? -ne 0 ]; then
    expiration_date=$(openssl x509 -in "$client_cert" -noout -enddate | cut -d= -f2)
    expiration_seconds=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$expiration_date" +%s)
    current_seconds=$(date +%s)
    days_until_expiration=$(( (expiration_seconds - current_seconds) / 86400 ))
    send_alert "public-api client certificate" "$days_until_expiration"
  fi
else
  echo "❌ public-api client certificate not found at $client_cert"
fi

echo "✅ Certificate expiration monitoring completed!"
