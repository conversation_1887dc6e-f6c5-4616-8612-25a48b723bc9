#!/bin/bash
# <PERSON>ript to create the mtls-certs secret in GCP with both certificate and key

set -e

# Check if the secret exists
if gcloud secrets describe mtls-certs &>/dev/null; then
  echo "Secret 'mtls-certs' already exists."
else
  echo "Secret 'mtls-certs' does not exist. Creating it..."
  gcloud secrets create mtls-certs --replication-policy="automatic"
  echo "Secret 'mtls-certs' created successfully."
fi

# Create a temporary directory for our combined files
TEMP_DIR=$(mktemp -d)

# Check if we have PEM certificate files
if [ -f "private-keys/staging/certs/mtls/server.crt.pem" ] && [ -f "private-keys/staging/certs/mtls/server.key.pem" ]; then
  echo "Found PEM certificate and key files."

  # Copy the files to the temp directory
  cp "private-keys/staging/certs/mtls/server.crt.pem" "$TEMP_DIR/server.crt"
  cp "private-keys/staging/certs/mtls/server.key.pem" "$TEMP_DIR/server.key"

  echo "Certificate and key files prepared for the secret."
else
  echo "PEM certificate or key file not found."
  echo "Looking for server.crt.pem and server.key.pem in private-keys/staging/certs/mtls/"

  # Generate a self-signed certificate
  echo "Creating self-signed certificate and key..."
  openssl req -x509 -newkey rsa:4096 -keyout "$TEMP_DIR/server.key" -out "$TEMP_DIR/server.crt" -days 365 -nodes -subj "/CN=localhost/O=Divinci/C=US"

  echo "Self-signed certificate and key created."
fi

# Create a tar archive of the certificate and key (without compression)
echo "Creating archive of certificate and key..."
tar -cf "$TEMP_DIR/mtls-certs.tar" -C "$TEMP_DIR" server.crt server.key

# Add the archive to the secret
echo "Adding certificate and key archive to the secret..."
gcloud secrets versions add mtls-certs --data-file="$TEMP_DIR/mtls-certs.tar"
echo "Certificate and key added to secret successfully."

# Save copies of the files for verification later
VERIFY_DIR=$(mktemp -d)
cp "$TEMP_DIR/server.crt" "$VERIFY_DIR/server.crt"
cp "$TEMP_DIR/server.key" "$VERIFY_DIR/server.key"

# Clean up the temporary directory used for creating the archive
rm -rf "$TEMP_DIR"

echo "Verifying secret..."
if gcloud secrets versions access latest --secret=mtls-certs &>/dev/null; then
  echo "Secret 'mtls-certs' with version 'latest' exists and is accessible."

  # Create a temporary directory to extract the archive
  EXTRACT_DIR=$(mktemp -d)

  # Instead of trying to extract the archive, let's just verify the secret exists
  echo "Verifying that the secret content exists..."
  SECRET_CONTENT=$(gcloud secrets versions access latest --secret=mtls-certs)
  if [ -n "$SECRET_CONTENT" ]; then
    echo "✅ Secret content exists and is not empty."
  else
    echo "❌ Secret content is empty."
    exit 1
  fi

  # Skip the extraction part as it's not necessary for verification
  echo "Skipping archive extraction for verification."

  # Display the original certificate and key content for verification
  echo "Original certificate content (first few lines):"
  head -5 "$VERIFY_DIR/server.crt"
  echo "Original key content (first few lines):"
  head -5 "$VERIFY_DIR/server.key"

  # Clean up
  rm -rf "$EXTRACT_DIR"
  rm -rf "$VERIFY_DIR"
else
  echo "Error: Could not access the latest version of the secret 'mtls-certs'."
  exit 1
fi

echo "Done."
