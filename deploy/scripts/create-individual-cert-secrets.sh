#!/bin/bash
# <PERSON>ript to create individual secrets for each certificate file

set -e

# Check if we have PEM certificate files
if [ -f "private-keys/staging/certs/mtls/server.crt.pem" ] && [ -f "private-keys/staging/certs/mtls/server.key.pem" ]; then
  echo "Found PEM certificate and key files."
  SERVER_CRT="private-keys/staging/certs/mtls/server.crt.pem"
  SERVER_KEY="private-keys/staging/certs/mtls/server.key.pem"
else
  echo "PEM certificate or key file not found."
  echo "Looking for server.crt and server.key in private-keys/staging/certs/mtls/"
  
  if [ -f "private-keys/staging/certs/mtls/server.crt" ] && [ -f "private-keys/staging/certs/mtls/server.key" ]; then
    echo "Found regular certificate and key files."
    SERVER_CRT="private-keys/staging/certs/mtls/server.crt"
    SERVER_KEY="private-keys/staging/certs/mtls/server.key"
  else
    echo "No certificate files found. Creating self-signed certificates..."
    
    # Create a temporary directory
    TEMP_DIR=$(mktemp -d)
    
    # Generate a self-signed certificate
    echo "Creating self-signed certificate and key..."
    openssl req -x509 -newkey rsa:4096 -keyout "$TEMP_DIR/server.key" -out "$TEMP_DIR/server.crt" -days 365 -nodes -subj "/CN=localhost/O=Divinci/C=US"
    
    SERVER_CRT="$TEMP_DIR/server.crt"
    SERVER_KEY="$TEMP_DIR/server.key"
    
    echo "Self-signed certificate and key created."
  fi
fi

# Create or update the server.crt secret
echo "Creating/updating server.crt secret..."
if gcloud secrets describe server-crt &>/dev/null; then
  echo "Secret 'server-crt' already exists. Adding a new version..."
  gcloud secrets versions add server-crt --data-file="$SERVER_CRT"
else
  echo "Creating new secret 'server-crt'..."
  gcloud secrets create server-crt --data-file="$SERVER_CRT" --replication-policy="automatic"
fi
echo "server.crt secret updated successfully."

# Create or update the server.key secret
echo "Creating/updating server.key secret..."
if gcloud secrets describe server-key &>/dev/null; then
  echo "Secret 'server-key' already exists. Adding a new version..."
  gcloud secrets versions add server-key --data-file="$SERVER_KEY"
else
  echo "Creating new secret 'server-key'..."
  gcloud secrets create server-key --data-file="$SERVER_KEY" --replication-policy="automatic"
fi
echo "server.key secret updated successfully."

# Clean up temporary directory if it was created
if [ -n "$TEMP_DIR" ]; then
  rm -rf "$TEMP_DIR"
fi

echo "Verifying secrets..."
echo "server.crt content (first few lines):"
gcloud secrets versions access latest --secret=server-crt | head -5

echo "server.key content (first few lines):"
gcloud secrets versions access latest --secret=server-key | head -5

echo "Done."
