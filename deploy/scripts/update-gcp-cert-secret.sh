#!/bin/bash
# Script to update the GCP Secret with a properly formatted PEM certificate

# Usage: ./update-gcp-cert-secret.sh <certificate_file>
# Example: ./update-gcp-cert-secret.sh private-keys/staging/certs/mtls/server.crt.pem

set -e

CERT_FILE=$1

if [ -z "$CERT_FILE" ]; then
  echo "Usage: $0 <certificate_file>"
  echo "Example: $0 private-keys/staging/certs/mtls/server.crt.pem"
  exit 1
fi

# Check if the certificate file exists
if [ ! -f "$CERT_FILE" ]; then
  echo "Error: Certificate file $CERT_FILE does not exist"
  exit 1
fi

# Check if the certificate file is a valid PEM file
if ! grep -q "BEGIN CERTIFICATE" "$CERT_FILE"; then
  echo "Error: Certificate file $CERT_FILE is not a valid PEM file"
  echo "It should start with '-----BEGIN CERTIFICATE-----'"
  exit 1
fi

# Update the GCP Secret
echo "Updating GCP Secret 'server-cert' with certificate from $CERT_FILE..."
gcloud secrets versions add server-cert --data-file="$CERT_FILE"

echo "Secret updated successfully!"
echo ""
echo "To verify the certificate format, you can run:"
echo "gcloud secrets versions access latest --secret=server-cert | openssl x509 -text -noout"
echo ""
echo "If you need to create a new secret instead of updating an existing one, run:"
echo "gcloud secrets create server-cert --data-file=\"$CERT_FILE\" --replication-policy=\"automatic\""

exit 0
