#!/bin/bash
# Script to rotate mTLS certificates

set -e

# Get the environment stage (default to local if not provided)
STAGE=${1:-local}
echo "Rotating certificates for stage: $STAGE"

# Configuration
CERT_DIR="./private-keys/$STAGE/certs/mtls"
CA_DIR="$CERT_DIR/ca"
SERVICE_DIR="$CERT_DIR/services"
BACKUP_DIR="./private-keys/$STAGE/certs/mtls-backup/$(date +%Y%m%d%H%M%S)"
CA_DAYS=3650  # 10 years
SERVICE_DAYS=730  # 2 years

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Function to backup existing certificates
backup_certs() {
  echo "📦 Backing up existing certificates to $BACKUP_DIR..."

  # Copy CA certificates
  if [ -d "$CA_DIR" ]; then
    mkdir -p "$BACKUP_DIR/ca"
    cp -r "$CA_DIR"/* "$BACKUP_DIR/ca/"
  fi

  # Copy service certificates
  if [ -d "$SERVICE_DIR" ]; then
    mkdir -p "$BACKUP_DIR/services"
    cp -r "$SERVICE_DIR"/* "$BACKUP_DIR/services/"
  fi

  echo "✅ Certificates backed up"
}

# Function to generate new certificates
generate_new_certs() {
  echo "🔑 Generating new certificates..."

  # Run the certificate generation script
  ./deploy/scripts/generate-mtls-certs.sh

  echo "✅ New certificates generated"
}

# Function to update Kubernetes secrets
update_k8s_secrets() {
  echo "🔄 Updating Kubernetes secrets..."

  # Update CA certificate secret
  kubectl create secret generic mtls-ca-cert \
    --from-file=ca.crt="$CA_DIR/ca.crt" \
    --dry-run=client -o yaml | kubectl apply -f -

  # Update public-api certificate secret
  kubectl create secret generic mtls-public-api-certs \
    --from-file=client.crt="$SERVICE_DIR/public-api/client.crt" \
    --from-file=client.key="$SERVICE_DIR/public-api/client.key" \
    --dry-run=client -o yaml | kubectl apply -f -

  # Update ffmpeg certificate secret
  kubectl create secret generic mtls-ffmpeg-certs \
    --from-file=server.crt="$SERVICE_DIR/ffmpeg/server.crt" \
    --from-file=server.key="$SERVICE_DIR/ffmpeg/server.key" \
    --dry-run=client -o yaml | kubectl apply -f -

  # Update open-parse certificate secret
  kubectl create secret generic mtls-open-parse-certs \
    --from-file=server.crt="$SERVICE_DIR/open-parse/server.crt" \
    --from-file=server.key="$SERVICE_DIR/open-parse/server.key" \
    --dry-run=client -o yaml | kubectl apply -f -

  # Update pyannote certificate secret
  kubectl create secret generic mtls-pyannote-certs \
    --from-file=server.crt="$SERVICE_DIR/pyannote/server.crt" \
    --from-file=server.key="$SERVICE_DIR/pyannote/server.key" \
    --dry-run=client -o yaml | kubectl apply -f -

  echo "✅ Kubernetes secrets updated"
}

# Function to restart services
restart_services() {
  echo "🔄 Restarting services..."

  # Restart services in Kubernetes
  kubectl rollout restart deployment public-api
  kubectl rollout restart deployment ffmpeg
  kubectl rollout restart deployment open-parse
  kubectl rollout restart deployment pyannote

  echo "✅ Services restarted"
}

# Main script

echo "🔄 Starting mTLS certificate rotation..."

# Backup existing certificates
backup_certs

# Generate new certificates
generate_new_certs

# Ask if we should update Kubernetes secrets
read -p "Do you want to update Kubernetes secrets? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
  update_k8s_secrets

  # Ask if we should restart services
  read -p "Do you want to restart services? (y/n) " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    restart_services
  fi
fi

echo "✅ mTLS certificate rotation completed!"
