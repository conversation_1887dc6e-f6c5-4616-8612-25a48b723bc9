#!/bin/bash
# Script to test mTLS connections between services

set -e

# Configuration
CA_CERT="./private-keys/mtls/ca/ca.crt"
CLIENT_CERT="./private-keys/mtls/services/public-api/client.crt"
CLIENT_KEY="./private-keys/mtls/services/public-api/client.key"

# Function to test mTLS connection to a service
test_mtls_connection() {
  local service_name=$1
  local service_url=$2
  
  echo "🔍 Testing mTLS connection to $service_name at $service_url..."
  
  # Use curl with mTLS certificates
  if curl --cacert "$CA_CERT" \
          --cert "$CLIENT_CERT" \
          --key "$CLIENT_KEY" \
          -s -o /dev/null -w "%{http_code}" \
          "$service_url" | grep -q "200\|401\|403"; then
    echo "✅ Successfully connected to $service_name with mTLS"
    return 0
  else
    echo "❌ Failed to connect to $service_name with mTLS"
    return 1
  fi
}

# Check if certificates exist
if [ ! -f "$CA_CERT" ] || [ ! -f "$CLIENT_CERT" ] || [ ! -f "$CLIENT_KEY" ]; then
  echo "❌ mTLS certificates not found. Please run generate-mtls-certs.sh first."
  exit 1
fi

# Test connections to each service
echo "🧪 Testing mTLS connections to services..."

# Test FFmpeg service
test_mtls_connection "FFmpeg" "https://ffmpeg:8001/health"

# Test Open-Parse service
test_mtls_connection "Open-Parse" "https://open-parse:8002/health"

# Test Pyannote service
test_mtls_connection "Pyannote" "https://pyannote:8003/health"

echo "✅ mTLS connection tests completed!"
