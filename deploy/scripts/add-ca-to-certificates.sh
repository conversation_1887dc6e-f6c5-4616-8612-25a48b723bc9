#!/bin/bash
# Script to add Cloudflare Origin CA root certificate to the certificate chain
# This helps fix SSL handshake failures (Error 525) in Cloudflare Full Strict mode

set -e

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

# Environment setup
ENVIRONMENT=${1:-"staging"}
echo "🔍 Preparing certificates for $ENVIRONMENT environment..."

# Paths to certificates
CERT_DIR="$REPO_ROOT/private-keys/${ENVIRONMENT}/certs/mtls"

# Try to find the root certificates in the certificate directory first
if [ -f "$CERT_DIR/origin_ca_ecc_root.pem" ]; then
  CF_ECC_ROOT_CERT="$CERT_DIR/origin_ca_ecc_root.pem"
else
  CF_ECC_ROOT_CERT="$SCRIPT_DIR/origin_ca_ecc_root.pem"
fi

if [ -f "$CERT_DIR/origin_ca_rsa_root.pem" ]; then
  CF_RSA_ROOT_CERT="$CERT_DIR/origin_ca_rsa_root.pem"
else
  CF_RSA_ROOT_CERT="$SCRIPT_DIR/origin_ca_rsa_root.pem"
fi

# Check if the certificate directory exists
if [ ! -d "$CERT_DIR" ]; then
  echo "❌ Certificate directory $CERT_DIR does not exist"
  exit 1
fi

# Function to add root CA certificate to chain
add_root_ca_to_chain() {
  local cert_file=$1
  local tmp_file="${cert_file}.tmp"
  local chain_file="${cert_file}.chain"
  local log_file="${cert_file}.log"

  echo "Adding Cloudflare Origin CA root certificate to chain in $cert_file..."
  echo "$(date) - Starting certificate chain creation for $cert_file" > "$log_file"

  # Check if the file exists
  if [ ! -f "$cert_file" ]; then
    echo "❌ Certificate file not found: $cert_file"
    echo "$(date) - ERROR: Certificate file not found: $cert_file" >> "$log_file"
    return 1
  fi

  # Make a backup of the original certificate
  cp "$cert_file" "${cert_file}.backup"
  echo "✅ Created backup of original certificate at ${cert_file}.backup"
  echo "$(date) - Created backup at ${cert_file}.backup" >> "$log_file"

  # Extract certificate details for logging
  local subject
  subject=$(openssl x509 -in "$cert_file" -noout -subject 2>/dev/null || echo "Unable to extract subject")
  local issuer
  issuer=$(openssl x509 -in "$cert_file" -noout -issuer 2>/dev/null || echo "Unable to extract issuer")
  local dates
  dates=$(openssl x509 -in "$cert_file" -noout -dates 2>/dev/null || echo "Unable to extract dates")

  echo "$(date) - Certificate details:" >> "$log_file"
  echo "  Subject: $subject" >> "$log_file"
  echo "  Issuer: $issuer" >> "$log_file"
  echo "  Validity: $dates" >> "$log_file"

  # Determine if using ECC or RSA based on certificate content and key algorithm
  local key_type
  key_type=$(openssl x509 -in "$cert_file" -noout -pubkey 2>/dev/null |
             openssl pkey -pubin -noout -text 2>/dev/null |
             grep -q "id-ecPublicKey" && echo "EC" || echo "RSA")

  echo "$(date) - Detected key type: $key_type" >> "$log_file"

  local root_cert
  if [ "$key_type" = "EC" ]; then
    if [ -f "$CF_ECC_ROOT_CERT" ]; then
      echo "Using ECC root certificate..."
      echo "$(date) - Using ECC root certificate: $CF_ECC_ROOT_CERT" >> "$log_file"
      root_cert="$CF_ECC_ROOT_CERT"
    else
      echo "❌ ECC root certificate not found at $CF_ECC_ROOT_CERT"
      echo "$(date) - ERROR: ECC root certificate not found at $CF_ECC_ROOT_CERT" >> "$log_file"
      return 1
    fi
  else
    if [ -f "$CF_RSA_ROOT_CERT" ]; then
      echo "Using RSA root certificate..."
      echo "$(date) - Using RSA root certificate: $CF_RSA_ROOT_CERT" >> "$log_file"
      root_cert="$CF_RSA_ROOT_CERT"
    else
      echo "❌ RSA root certificate not found at $CF_RSA_ROOT_CERT"
      echo "$(date) - ERROR: RSA root certificate not found at $CF_RSA_ROOT_CERT" >> "$log_file"
      return 1
    fi
  fi

  # Create certificate chain in proper order
  echo "$(date) - Creating certificate chain in proper order" >> "$log_file"
  # Extract each certificate into separate files
  awk 'BEGIN {c=0} /BEGIN CERT/{c++} { print > ("cert-" c ".pem")}' < "$cert_file"

  # Start with the leaf certificate (cert-1.pem should be the leaf)
  if [ -f "cert-1.pem" ]; then
    cat "cert-1.pem" > "$chain_file"
    echo "$(date) - Added leaf certificate to chain" >> "$log_file"
  else
    # If we don't have multiple certs in the file, just use the original
    cat "$cert_file" > "$chain_file"
    echo "$(date) - Using original certificate as leaf" >> "$log_file"
  fi

  # Add intermediate certificates if they exist (skip the leaf)
  for cert in cert-*.pem; do
    if [ "$cert" != "cert-1.pem" ] && [ -f "$cert" ]; then
      cat "$cert" >> "$chain_file"
      echo "$(date) - Added intermediate certificate: $cert" >> "$log_file"
    fi
  done

  # Add the root CA certificate
  cat "$root_cert" >> "$chain_file"
  echo "$(date) - Added root CA certificate" >> "$log_file"

  # Verify the chain
  echo "Verifying certificate chain..."
  echo "$(date) - Verifying certificate chain" >> "$log_file"

  # Extract the root CA certificate for verification
  CERT_COUNT=$(grep -c "BEGIN CERTIFICATE" "$chain_file")
  echo "$(date) - Chain contains $CERT_COUNT certificates" >> "$log_file"

  # Copy the verified chain to the destination
  if [ "$CERT_COUNT" -ge 2 ]; then
    echo "✅ Certificate chain verified with $CERT_COUNT certificates"
    echo "$(date) - Chain verification successful" >> "$log_file"
    cat "$chain_file" > "$tmp_file"
    mv "$tmp_file" "$cert_file"
  else
    echo "⚠️ Warning: Certificate chain may not be complete, only contains $CERT_COUNT certificate"
    echo "$(date) - WARNING: Chain may be incomplete with only $CERT_COUNT certificate" >> "$log_file"

    # Fallback to simple concatenation if verification fails
    echo "Using direct concatenation as fallback..."
    echo "$(date) - Using fallback method: direct concatenation" >> "$log_file"
    cat "$cert_file" "$root_cert" > "$tmp_file"
    mv "$tmp_file" "$cert_file"
  fi

  # Clean up temporary files
  rm -f cert-*.pem "$chain_file" 2>/dev/null

  echo "✅ Successfully added root CA to certificate chain"
  echo "$(date) - Completed certificate chain creation for $cert_file" >> "$log_file"
  echo "Log file created at $log_file"

  return 0
}

# Process Origin Certificate if it exists (preferred)
if [ -f "$CERT_DIR/origin.crt" ]; then
  echo "✅ Found Cloudflare Origin certificate"
  add_root_ca_to_chain "$CERT_DIR/origin.crt"

  # Copy to standard locations
  echo "Copying updated certificate to standard locations..."
  cp "$CERT_DIR/origin.crt" "$CERT_DIR/server.crt"
  cp "$CERT_DIR/origin.crt" "$CERT_DIR/server.crt.pem"
  echo "✅ Updated server.crt and server.crt.pem with the new certificate chain"
else
  echo "⚠️ Cloudflare Origin certificate not found, checking for server certificate..."

  # Process Server Certificate if Origin Certificate doesn't exist
  if [ -f "$CERT_DIR/server.crt" ]; then
    echo "✅ Found server certificate"
    add_root_ca_to_chain "$CERT_DIR/server.crt"

    # Update PEM version if it exists
    if [ -f "$CERT_DIR/server.crt.pem" ]; then
      cp "$CERT_DIR/server.crt" "$CERT_DIR/server.crt.pem"
      echo "✅ Updated server.crt.pem with the new certificate chain"
    fi
  else
    echo "❌ Neither origin.crt nor server.crt found. Cannot proceed."
    exit 1
  fi
fi

echo "🔍 Verifying certificate chain..."
if [ -f "$CERT_DIR/server.crt" ]; then
  CERT_COUNT=$(grep -c "BEGIN CERTIFICATE" "$CERT_DIR/server.crt")
  echo "Certificate chain contains $CERT_COUNT certificates"

  # Create a temporary file for the root CA certificate
  ROOT_CA_TEMP=$(mktemp)

  # Determine which root CA to use for verification
  if grep -q "EC PUBLIC KEY" "$CERT_DIR/server.crt"; then
    cat "$CF_ECC_ROOT_CERT" > "$ROOT_CA_TEMP"
    echo "Using ECC root certificate for verification..."
  else
    cat "$CF_RSA_ROOT_CERT" > "$ROOT_CA_TEMP"
    echo "Using RSA root certificate for verification..."
  fi

  # Perform OpenSSL verification
  if openssl verify -CAfile "$ROOT_CA_TEMP" "$CERT_DIR/server.crt" > /dev/null 2>&1; then
    echo "✅ Certificate chain successfully verified with OpenSSL"

    # Check certificate expiration
    EXPIRATION_DATE=$(openssl x509 -in "$CERT_DIR/server.crt" -noout -enddate | cut -d= -f2)
    CURRENT_DATE=$(date)

    # Use platform-independent way to get timestamps
    if [[ "$OSTYPE" == "darwin"* ]]; then
      # macOS
      EXPIRATION_TIMESTAMP=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$EXPIRATION_DATE" +%s 2>/dev/null || echo 0)
      CURRENT_TIMESTAMP=$(date +%s)
    else
      # Linux
      EXPIRATION_TIMESTAMP=$(date -d "$EXPIRATION_DATE" +%s 2>/dev/null || echo 0)
      CURRENT_TIMESTAMP=$(date +%s)
    fi

    # Calculate days until expiration
    if [ "$EXPIRATION_TIMESTAMP" -gt 0 ]; then
      DAYS_UNTIL_EXPIRY=$(( ($EXPIRATION_TIMESTAMP - $CURRENT_TIMESTAMP) / 86400 ))

      if [ "$DAYS_UNTIL_EXPIRY" -lt 30 ]; then
        echo "⚠️ Warning: Certificate expires in $DAYS_UNTIL_EXPIRY days"
      else
        echo "✅ Certificate expiration date is valid ($DAYS_UNTIL_EXPIRY days until expiry)"
      fi
    else
      echo "⚠️ Warning: Could not parse expiration date: $EXPIRATION_DATE"
      # Set a default value to continue
      DAYS_UNTIL_EXPIRY=365
    fi

    # Check that validity period includes current date
    NOT_BEFORE=$(openssl x509 -in "$CERT_DIR/server.crt" -noout -startdate | cut -d= -f2)

    # Use platform-independent way to get timestamps
    if [[ "$OSTYPE" == "darwin"* ]]; then
      # macOS
      NOT_BEFORE_TIMESTAMP=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$NOT_BEFORE" +%s 2>/dev/null || echo 0)
    else
      # Linux
      NOT_BEFORE_TIMESTAMP=$(date -d "$NOT_BEFORE" +%s 2>/dev/null || echo 0)
    fi

    if [ "$NOT_BEFORE_TIMESTAMP" -gt 0 ] && [ "$NOT_BEFORE_TIMESTAMP" -gt "$CURRENT_TIMESTAMP" ]; then
      echo "⚠️ CRITICAL ERROR: Certificate is not yet valid! Begins at $NOT_BEFORE"
      echo "    Current time:  $CURRENT_DATE"
      echo "    This will cause certificate validation failures."
    elif [ "$NOT_BEFORE_TIMESTAMP" -eq 0 ]; then
      echo "⚠️ Warning: Could not parse certificate start date: $NOT_BEFORE"
      echo "    Continuing anyway..."
    else
      echo "✅ Certificate validity period starts correctly"
    fi
  else
    echo "❌ Certificate failed OpenSSL verification!"

    # Print details for troubleshooting
    echo "Certificate details:"
    openssl x509 -in "$CERT_DIR/server.crt" -noout -text | grep "Subject:" -A1
    openssl x509 -in "$CERT_DIR/server.crt" -noout -text | grep "Issuer:" -A1
    openssl x509 -in "$CERT_DIR/server.crt" -noout -text | grep "Validity" -A2

    echo "❓ Attempting to diagnose the issue..."

    # Check if the format is correct
    if ! openssl x509 -in "$CERT_DIR/server.crt" -noout -text > /dev/null 2>&1; then
      echo "  ❌ Certificate format is invalid, not a proper X.509 certificate"
    fi

    # Check if chain is properly formed
    if grep -q "END CERTIFICATE" "$CERT_DIR/server.crt" && grep -q "BEGIN CERTIFICATE" "$CERT_DIR/server.crt"; then
      echo "  ✅ Certificate contains proper BEGIN/END markers"
    else
      echo "  ❌ Certificate lacks proper BEGIN/END markers"
    fi

    echo "  ⚠️ Certificate chain verification failed - you may need to regenerate certificates"
  fi

  # Clean up temporary file
  rm -f "$ROOT_CA_TEMP"

  if [ "$CERT_COUNT" -ge 2 ]; then
    echo "✅ Certificate chain contains multiple certificates ($CERT_COUNT found)"
  else
    echo "⚠️ Warning: Certificate chain may not be complete, only contains 1 certificate"
  fi
else
  echo "❌ Cannot verify certificate chain - server.crt not found"
fi

echo "📋 To apply these changes to your CI/CD:"
echo "1. Update your GCP secrets with the modified certificates"
echo "2. Run: ./deploy/scripts/create-mtls-secret.sh $ENVIRONMENT"
echo "3. Restart your services to pick up the new certificates"
echo "4. Monitor Cloudflare for resolution of Error 525"
echo
echo "✅ Certificate preparation complete"