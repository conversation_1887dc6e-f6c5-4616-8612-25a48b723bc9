# mTLS Connection Troubleshooting Guide: GCP to Cloudflare

## Key Issues Identified

After analyzing the test results, I've identified the following issues that are preventing successful mTLS connections between GCP Cloud Run and Cloudflare:

1. **Certificate Mismatch**: The certificate stored in GCP Secret Manager doesn't match the one being presented by the origin server.
2. **Certificate Expiration**: The certificate presented by the origin server is expired.
3. **mTLS Handshake Failures**: Although basic TLS connections succeed, mTLS connections consistently fail.
4. **Missing Cloudflare Origin CA Verification**: Certificates don't verify against Cloudflare's Root CA.

## Recommendations

### 1. Fix Certificate Expiration and Mismatch

The certificate on your origin server appears to be expired. The test reports:
```
Verify return code: 10 (certificate has expired)
```

Additionally, there's a mismatch between the certificate in GCP Secret Manager and the one being presented:
```
GCP Certificate Fingerprint: 79:0D:29:21:D4:D2:87:8F:4C:58:8E:B5:11:0C:D9:96:F2:2E:01:9E:79:71:64:88:88:32:18:9F:53:91:30:E7
Origin Certificate Fingerprint: 2A:D3:E7:DF:8E:D6:5D:3D:C9:9E:F8:F1:9C:9A:61:2C:E6:75:D0:B0:4F:B5:DF:B4:C8:D0:97:52:40:99:0E:D0
```

**Action Items**:
1. Generate a new origin certificate using Cloudflare's Origin CA:
   ```bash
   # Using Cloudflare dashboard or CLI
   ./generate-cloudflare-origin-cert.sh stage.divinci.app
   ```

2. Update the GCP Secret Manager with the new certificate:
   ```bash
   ./deploy/scripts/update-gcp-cert-secret.sh staging server.crt
   ./deploy/scripts/update-gcp-cert-secret.sh staging server.key
   ```

3. Restart your Cloud Run services to pick up the new certificate.

### 2. Validate Certificate Chain

While the certificate chain appears complete (contains multiple certificates), it's failing verification against Cloudflare's Root CA:

```
Certificate chain contains 2 certificates
✅ Certificate chain is complete (contains multiple certificates)
❌ Certificate does not verify against Cloudflare Root CAs
```

**Action Items**:
1. Ensure the certificate chain includes the Cloudflare Origin CA Root certificate:
   ```bash
   # Download the Cloudflare Origin CA root certificates if needed
   ./deploy/scripts/download-cloudflare-ca-certs.sh
   
   # Combine the certificates properly
   cat server.crt origin_ca_ecc_root.pem > server-chain.crt
   ```

2. Update GCP Secret Manager with the complete chain:
   ```bash
   ./deploy/scripts/create-server-cert-secret.sh staging server-chain.crt server.key
   ```

### 3. Configure Proper mTLS Settings

The mTLS connections are failing despite successful basic TLS connections, suggesting a configuration issue:

```
✅ Basic TLS connection successful
❌ mTLS connection failed
❌ mTLS handshake failed
```

**Action Items**:
1. Ensure both client and server certificates are properly configured:
   ```bash
   # Generate new client certificates if needed
   ./deploy/scripts/generate-mtls-keys.sh
   
   # Create proper secrets for both client and server
   ./deploy/scripts/create-mtls-certs-secret.sh staging
   ```

2. Configure your Cloud Run service to require mTLS:
   - Update your service YAML to include mTLS configuration:
     ```yaml
     apiVersion: serving.knative.dev/v1
     kind: Service
     metadata:
       name: your-service
     spec:
       template:
         spec:
           containers:
           - image: gcr.io/your-project/your-image
             env:
             - name: SSL_CERT_FILE
               value: /etc/secrets/server.crt
             - name: SSL_KEY_FILE
               value: /etc/secrets/server.key
             volumeMounts:
             - name: cert-secret
               mountPath: /etc/secrets
               readOnly: true
           volumes:
           - name: cert-secret
             secret:
               secretName: server-cert
     ```

3. Configure Cloudflare to use Full (Strict) SSL mode:
   ```bash
   # Check current SSL mode (if you can get the Zone ID)
   ./change-cloudflare-ssl-mode.sh full_strict divinci.app
   ```

### 4. Test with Known Working Client Certificates

Test the mTLS connection using a well-formed client certificate:

```bash
# Test manually with openssl
openssl s_client -connect chat.stage.divinci.app:443 \
  -cert /path/to/client.crt \
  -key /path/to/client.key
```

### 5. Check Cloudflare Access Settings

If you're using Cloudflare Access, ensure the right policies are set for mTLS:

1. In Cloudflare Dashboard, check Access policies
2. Ensure the service is configured to accept mTLS connections
3. Check for any conflicting rules that might interfere with mTLS authentication

### 6. Verify Server Configuration

Ensure your Node.js server is correctly configured to use mTLS:

```javascript
const https = require('https');
const fs = require('fs');

const options = {
  key: fs.readFileSync('/etc/secrets/server.key'),
  cert: fs.readFileSync('/etc/secrets/server.crt'),
  ca: fs.readFileSync('/etc/secrets/cloudflare_ca.pem'), // Cloudflare's CA
  requestCert: true,  // Request client certificate
  rejectUnauthorized: true // Reject connections without valid client cert
};

https.createServer(options, app).listen(443);
```

## Implementation Plan

1. **Generate new certificates**:
   - Create new Cloudflare Origin certificates
   - Update both local files and GCP Secret Manager

2. **Update certificate chain**:
   - Include Cloudflare Root CA in the chain
   - Verify the chain locally before deploying

3. **Configure mTLS properly**:
   - Update service definitions
   - Set Cloudflare SSL mode to Full (Strict)

4. **Test connections**:
   - Test direct connection to origin server
   - Test through Cloudflare with mTLS

5. **Restart services**:
   - Deploy updated certificate secrets
   - Restart affected Cloud Run services

## Notes on Certificate Issues

The current certificate shows "Not Before: Apr 23 17:35:00 2025 GMT" which appears to be a future date (the server thinks the certificate isn't valid yet). This could be a system time issue or a certificate generation error.

Also, the certificate shows "Subject: O=CloudFlare, Inc., OU=CloudFlare Origin CA, CN=CloudFlare Origin Certificate" but should have your domain as the CN, not a generic Cloudflare value.

The most likely cause of mTLS failure is a combination of the certificate expiration/validity issue and potential server-side configuration that isn't properly set up to validate client certificates.