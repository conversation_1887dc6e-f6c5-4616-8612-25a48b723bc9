# Manual Cloudflare Fixes for CORS Issues

This guide provides detailed step-by-step instructions for manually implementing the fixes for CORS issues in the Cloudflare dashboard.

## Current Status

Based on our tests, we're experiencing CORS issues with the Divinci API:

1. **OPTIONS Preflight Requests**: Return 503 Service Unavailable
2. **No CORS Headers**: No CORS headers are present in the responses

This confirms that the Cloudflare mTLS rule is blocking OPTIONS requests, and no Transform Rules are in place to add CORS headers.

## Step 1: Update the mTLS Rule in Cloudflare

1. Log in to the Cloudflare dashboard for divinci.app
2. Go to **Security** > **WAF** > **Custom Rules**
3. Look for a rule with a name related to mTLS or client certificate verification
4. The rule expression should contain `cf.tls_client_auth.cert_verified` and domain names like `api.stage.divinci.app`
5. Click on **Edit** for that rule
6. In the rule expression, add `(http.request.method ne "OPTIONS") and` at the beginning of the expression

**Original Expression (example):**
```
(not cf.tls_client_auth.cert_verified and http.host eq "api.stage.divinci.app") or 
(not cf.tls_client_auth.cert_verified and http.host eq "api.dev.divinci.app") or 
(not cf.tls_client_auth.cert_verified and http.host eq "api.divinci.app") or 
...
```

**Modified Expression:**
```
(http.request.method ne "OPTIONS") and (
  (not cf.tls_client_auth.cert_verified and http.host eq "api.stage.divinci.app") or 
  (not cf.tls_client_auth.cert_verified and http.host eq "api.dev.divinci.app") or 
  (not cf.tls_client_auth.cert_verified and http.host eq "api.divinci.app") or 
  ...
)
```

7. Click **Save** or **Deploy** to apply the changes

## Step 2: Create Transform Rules for CORS Headers

### Create Transform Rule for OPTIONS Requests

1. Go to **Rules** > **Transform Rules**
2. Click **Create Transform Rule**
3. In the **Rule configuration** section:
   - **Name**: `Add CORS Headers for OPTIONS Requests`
   - **Status**: Enabled
   - **Expression**: `(http.request.method eq "OPTIONS")`
   - **Description**: `Add CORS headers for OPTIONS preflight requests`

4. In the **Then...** section:
   - Select **Modify response header**
   - Click **Add header modification**
   - For the first header:
     - **Name**: `Access-Control-Allow-Origin`
     - **Operation**: `Set dynamic`
     - **Value**: `http.request.headers["Origin"][0]`
   - Click **Add header modification** again
   - For the second header:
     - **Name**: `Access-Control-Allow-Methods`
     - **Operation**: `Set`
     - **Value**: `GET, POST, PUT, DELETE, OPTIONS, PATCH`
   - Click **Add header modification** again
   - For the third header:
     - **Name**: `Access-Control-Allow-Headers`
     - **Operation**: `Set`
     - **Value**: `Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization`
   - Click **Add header modification** again
   - For the fourth header:
     - **Name**: `Access-Control-Allow-Credentials`
     - **Operation**: `Set`
     - **Value**: `true`
   - Click **Add header modification** again
   - For the fifth header:
     - **Name**: `Access-Control-Max-Age`
     - **Operation**: `Set`
     - **Value**: `86400`

5. Click **Save** or **Deploy** to apply the changes

### Create Transform Rule for Regular Requests

1. Go to **Rules** > **Transform Rules**
2. Click **Create Transform Rule**
3. In the **Rule configuration** section:
   - **Name**: `Add CORS Headers for Regular Requests`
   - **Status**: Enabled
   - **Expression**: `true`
   - **Description**: `Add CORS headers for all responses`

4. In the **Then...** section:
   - Select **Modify response header**
   - Click **Add header modification**
   - For the first header:
     - **Name**: `Access-Control-Allow-Origin`
     - **Operation**: `Set dynamic`
     - **Value**: `http.request.headers["Origin"][0]`
   - Click **Add header modification** again
   - For the second header:
     - **Name**: `Access-Control-Allow-Credentials`
     - **Operation**: `Set`
     - **Value**: `true`

5. Click **Save** or **Deploy** to apply the changes

## Step 3: Test the Fixes

After implementing the changes, test the CORS functionality:

1. Run the test script:
   ```bash
   ./test-cors-fixes.sh --verbose
   ```

2. Check the browser:
   - Open Chrome or Firefox
   - Open the Developer Tools (F12)
   - Go to the Network tab
   - Navigate to https://chat.stage.divinci.app
   - Look for requests to api.stage.divinci.app
   - Verify that OPTIONS requests return 204 No Content with the appropriate CORS headers
   - Verify that regular requests return 200 OK with the appropriate CORS headers
   - Check the console for any CORS-related errors

## Troubleshooting

If you still encounter CORS issues after implementing the fixes:

### 1. Check Rule Order

Ensure that the Transform Rules are being applied after the WAF rules:
1. Go to **Rules** > **Transform Rules**
2. Check the order of the rules
3. Make sure the OPTIONS rule is above the regular rule

### 2. Check for Conflicting Rules

Look for other rules that might be interfering with the CORS headers:
1. Go to **Security** > **WAF** > **Custom Rules**
2. Look for rules that might be blocking OPTIONS requests
3. Check if there are any rules with higher priority that might be overriding your changes

### 3. Check Cloudflare Logs

Check the Cloudflare logs for any errors or issues:
1. Go to **Analytics & Logs** > **Logs**
2. Filter for requests to api.stage.divinci.app
3. Look for OPTIONS requests and check their status codes
4. Check for any error messages or issues

### 4. Try Temporary Bypass

As a temporary test, you can try disabling the mTLS rule entirely:
1. Go to **Security** > **WAF** > **Custom Rules**
2. Find the mTLS rule
3. Toggle it to "Disabled"
4. Test if CORS works without the rule
5. Remember to re-enable the rule after testing!

## Next Steps

After successfully implementing and testing the CORS fixes:

1. Document the changes made
2. Update the team on the changes
3. Consider implementing automated tests to detect CORS issues in the future
4. Consider integrating the CORS tests into your CI/CD pipeline
