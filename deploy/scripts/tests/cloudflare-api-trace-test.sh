#!/bin/bash
# Cloudflare API Trace Test
# This test uses Cloudflare's Request Tracer API to trace a request through Cloudflare's infrastructure.

set -e

# Configuration
DOMAIN=${1:-"chat.stage.divinci.app"}
CF_API_TOKEN=${2:-"xQI66zSWAOjLnWlyoZne8_CjVgStYXYbH26f3p2c"}
CF_EMAIL=${3:-""}
CF_API_KEY=${4:-""}
CF_ACCOUNT_ID=${5:-""}
OUTPUT_DIR="./test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== Cloudflare API Trace Test ==="
echo "Domain: $DOMAIN"
echo

# Step 1: Get Cloudflare Account ID if not provided
if [ -z "$CF_ACCOUNT_ID" ]; then
  echo "Step 1: Getting Cloudflare Account ID..."
  CF_ACCOUNT_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/accounts" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json" | jq -r '.result[0].id')

  if [ -z "$CF_ACCOUNT_ID" ] || [ "$CF_ACCOUNT_ID" == "null" ]; then
    echo "❌ Failed to get Cloudflare Account ID"
    echo "   Please provide your Cloudflare Account ID as the fifth parameter"
    exit 1
  fi

  echo "✅ Found Cloudflare Account ID: $CF_ACCOUNT_ID"
else
  echo "Step 1: Using provided Cloudflare Account ID: $CF_ACCOUNT_ID"
fi

# Step 2: Get Zone ID for the domain
echo "Step 2: Getting Zone ID for $DOMAIN..."
ZONE_NAME=$(echo "$DOMAIN" | awk -F. '{print $(NF-1)"."$NF}')
echo "Zone Name: $ZONE_NAME"

ZONE_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$ZONE_NAME" \
  -H "Authorization: Bearer $CF_API_TOKEN" \
  -H "Content-Type: application/json")

# Save the response for debugging
echo "$ZONE_RESPONSE" > "$OUTPUT_DIR/zone_response.json"

# Check if the API call was successful
SUCCESS=$(echo "$ZONE_RESPONSE" | jq -r '.success')
if [ "$SUCCESS" != "true" ]; then
  echo "❌ API call failed: $(echo "$ZONE_RESPONSE" | jq -r '.errors[0].message')"
  echo "⚠️ Continuing with tests but some functionality may be limited"
  ZONE_ID=""
else
  # Extract the zone ID
  ZONE_ID=$(echo "$ZONE_RESPONSE" | jq -r '.result[0].id')

  if [ -z "$ZONE_ID" ] || [ "$ZONE_ID" == "null" ]; then
    echo "❌ Failed to get Zone ID for $ZONE_NAME"
    echo "⚠️ Continuing with tests but some functionality may be limited"
    ZONE_ID=""
  else
    echo "✅ Found Zone ID: $ZONE_ID"
  fi
fi

# Step 3: Check SSL mode
echo "Step 3: Checking SSL mode..."
if [ -z "$ZONE_ID" ]; then
  echo "⚠️ Cannot check SSL mode without Zone ID"
  echo "   Skipping SSL mode check"
else
  SSL_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/settings/ssl" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json")

  # Save the response for debugging
  echo "$SSL_RESPONSE" > "$OUTPUT_DIR/ssl_mode_response.json"

  # Check if the API call was successful
  SUCCESS=$(echo "$SSL_RESPONSE" | jq -r '.success')
  if [ "$SUCCESS" != "true" ]; then
    echo "❌ API call failed: $(echo "$SSL_RESPONSE" | jq -r '.errors[0].message')"
  else
    # Extract the SSL mode
    SSL_MODE=$(echo "$SSL_RESPONSE" | jq -r '.result.value')

    if [ -z "$SSL_MODE" ] || [ "$SSL_MODE" == "null" ]; then
      echo "❌ Failed to get SSL mode"
    else
      echo "✅ SSL mode: $SSL_MODE"

      if [ "$SSL_MODE" == "strict" ]; then
        echo "   SSL mode is set to Full (Strict), which requires a valid certificate on your origin server"
      elif [ "$SSL_MODE" == "full" ]; then
        echo "   SSL mode is set to Full, which accepts self-signed certificates on your origin server"
      elif [ "$SSL_MODE" == "flexible" ]; then
        echo "   SSL mode is set to Flexible, which does not require SSL on your origin server"
      fi
    fi
  fi
fi

# Step 4: Send trace request using Request Tracer API
echo "Step 4: Sending trace request using Request Tracer API..."

# Check if we have the email and API key for the Request Tracer API
if [ -z "$CF_EMAIL" ] || [ -z "$CF_API_KEY" ]; then
  echo "⚠️ Cloudflare Email or API Key not provided, using alternative trace method"

  # Use the alternative method with the API token
  TRACE_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/dns_records" \
    -H "Authorization: Bearer $CF_API_TOKEN" \
    -H "Content-Type: application/json")

  echo "$TRACE_RESPONSE" > "$OUTPUT_DIR/cf_trace_alternative.json"
  echo "✅ Alternative trace completed, results saved to $OUTPUT_DIR/cf_trace_alternative.json"
else
  # Use the Request Tracer API
  TRACE_RESPONSE=$(curl -s -X POST "https://api.cloudflare.com/client/v4/accounts/$CF_ACCOUNT_ID/request-tracer/trace" \
    -H "X-Auth-Email: $CF_EMAIL" \
    -H "X-Auth-Key: $CF_API_KEY" \
    -H "Content-Type: application/json" \
    --data '{
      "method": "GET",
      "url": "https://'"$DOMAIN"'/",
      "headers": {
        "User-Agent": "Cloudflare-Trace-Test",
        "Accept": "text/html"
      },
      "protocol": "HTTP/1.1"
    }')

  echo "$TRACE_RESPONSE" > "$OUTPUT_DIR/cf_trace_response.json"

  # Check if the trace was successful
  SUCCESS=$(echo "$TRACE_RESPONSE" | jq -r '.success')
  if [ "$SUCCESS" == "true" ]; then
    echo "✅ Trace completed successfully"

    # Extract status code
    STATUS_CODE=$(echo "$TRACE_RESPONSE" | jq -r '.result.status_code')
    echo "   Status code: $STATUS_CODE"

    # Extract trace steps
    TRACE_STEPS=$(echo "$TRACE_RESPONSE" | jq -r '.result.trace | length')
    echo "   Trace contains $TRACE_STEPS steps"

    # Look for SSL-related errors in the trace
    SSL_ERRORS=$(echo "$TRACE_RESPONSE" | jq -r '.result.trace[] | select(.description | contains("SSL") or contains("TLS") or contains("certificate"))')
    if [ -n "$SSL_ERRORS" ]; then
      echo "❌ SSL-related errors found in the trace:"
      echo "$SSL_ERRORS" | jq -r '.description'
    else
      echo "✅ No SSL-related errors found in the trace"
    fi
  else
    echo "❌ Trace failed"
    echo "   Error: $(echo "$TRACE_RESPONSE" | jq -r '.errors[0].message')"
  fi
fi

# Step 5: Test direct connection to Cloudflare edge
echo "Step 5: Testing direct connection to Cloudflare edge..."
EDGE_RESPONSE=$(curl -s -I -H "Host: $DOMAIN" "https://*******/cdn-cgi/trace")
echo "$EDGE_RESPONSE" > "$OUTPUT_DIR/cf_edge_response.txt"
echo "✅ Edge connection test completed, results saved to $OUTPUT_DIR/cf_edge_response.txt"

# Step 6: Test with Cloudflare Access bypass headers
echo "Step 6: Testing with Cloudflare Access bypass headers..."
CF_ACCESS_CLIENT_ID=${6:-"f13e5d39e1997a5ddb674362e73199c5.access"}
CF_ACCESS_CLIENT_SECRET=${7:-""}

if [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
  ACCESS_RESPONSE=$(curl -s -I -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" "https://$DOMAIN/")
  echo "$ACCESS_RESPONSE" > "$OUTPUT_DIR/cf_access_response.txt"

  # Check for Error 526
  if echo "$ACCESS_RESPONSE" | grep -q "526"; then
    echo "❌ Error 526 detected with Cloudflare Access bypass headers"
    echo "   This confirms that the issue is with the SSL certificate on your origin server"
  else
    echo "✅ No Error 526 detected with Cloudflare Access bypass headers"
    echo "   Status: $(echo "$ACCESS_RESPONSE" | grep "HTTP/" | awk '{print $2}')"
  fi
else
  echo "⚠️ Cloudflare Access Client Secret not provided, skipping Access bypass test"
fi

echo
echo "=== Cloudflare API Trace Test Complete ==="
echo "Results saved to $OUTPUT_DIR/"
