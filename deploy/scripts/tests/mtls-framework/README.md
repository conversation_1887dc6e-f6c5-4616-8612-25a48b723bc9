# mTLS Testing Framework

This directory contains a modular testing framework for mTLS (Mutual TLS) in the Divinci API server.

## Overview

The mTLS Testing Framework provides a set of tools for testing and managing mTLS certificates and connections. It includes:

1. Common utilities for certificate validation, connection testing, and more
2. A comprehensive test runner for verifying mTLS functionality
3. A certificate management utility for generating, rotating, and verifying certificates

## Files

- `common.sh`: Common functions and utilities used by other scripts
- `run-tests.sh`: Main test runner for executing mTLS tests
- `certificate-manager.sh`: Utility for managing mTLS certificates

## Usage

### Running Tests

To run all mTLS tests:

```bash
./run-tests.sh
```

To run specific tests:

```bash
./run-tests.sh --test basic
./run-tests.sh --test certificates
./run-tests.sh --test mtls
./run-tests.sh --test cloudflare
```

To test a specific domain or environment:

```bash
./run-tests.sh --domain api.stage.divinci.app --environment staging
```

### Managing Certificates

To check certificate status:

```bash
./certificate-manager.sh --action status --environment staging
```

To generate new certificates:

```bash
./certificate-manager.sh --action generate --environment staging
```

To rotate certificates:

```bash
./certificate-manager.sh --action rotate --environment staging
```

To verify certificates:

```bash
./certificate-manager.sh --action verify --environment staging
```

## Features

### Test Runner

The test runner (`run-tests.sh`) provides the following tests:

- **Basic Connectivity Test**: Tests basic connectivity to the API server
- **Certificate Validation Test**: Validates certificates and keys
- **mTLS Connection Test**: Tests mTLS connections using OpenSSL and curl
- **Cloudflare SSL Mode Test**: Checks Cloudflare SSL mode configuration

### Certificate Manager

The certificate manager (`certificate-manager.sh`) provides the following features:

- **Status**: Shows the status of certificates, including expiration dates
- **Generate**: Generates new client certificates and keys
- **Rotate**: Rotates client certificates and keys
- **Verify**: Verifies that certificates and keys are valid and match

### Common Utilities

The common utilities (`common.sh`) provide the following functions:

- Certificate validation and verification
- Certificate expiration checking
- Certificate information retrieval
- Basic connectivity testing
- mTLS connection testing
- Cloudflare SSL mode checking and setting
- Client certificate generation

## Requirements

- Bash 4.0 or later
- OpenSSL
- curl
- Access to the Divinci API server
- Cloudflare API credentials (for Cloudflare SSL mode tests)

## Environment Variables

The following environment variables are used by the framework:

- `CF_ACCESS_CLIENT_ID`: Cloudflare Access client ID
- `CF_ACCESS_CLIENT_SECRET`: Cloudflare Access client secret
- `CF_EMAIL`: Cloudflare API email
- `CF_API_KEY`: Cloudflare API key
- `CF_ACCOUNT_ID`: Cloudflare account ID

These variables can be set in the environment or in the file `/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env`.

## Test Results

Test results are saved to the `test-results` directory with timestamps for future reference.
