#!/bin/bash
# Main test runner for mTLS testing framework

# Source common functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# Default values
DOMAIN="api.stage.divinci.app"
ENVIRONMENT="staging"
ENDPOINT="/health"
TEST_TYPE="all"
PORT="443"
TLS_VERSION=""
CIPHER=""
ORIGIN_IP=""

# Parse command line arguments
function parse_args() {
  while [[ $# -gt 0 ]]; do
    case $1 in
      --domain|-d)
        DOMAIN="$2"
        shift 2
        ;;
      --environment|-e)
        ENVIRONMENT="$2"
        shift 2
        ;;
      --endpoint|-p)
        ENDPOINT="$2"
        shift 2
        ;;
      --test|-t)
        TEST_TYPE="$2"
        shift 2
        ;;
      --port)
        PORT="$2"
        shift 2
        ;;
      --tls-version)
        TLS_VERSION="$2"
        shift 2
        ;;
      --cipher)
        CIPHER="$2"
        shift 2
        ;;
      --origin-ip)
        ORIGIN_IP="$2"
        shift 2
        ;;
      --help|-h)
        show_help
        exit 0
        ;;
      *)
        error "Unknown option: $1"
        show_help
        exit 1
        ;;
    esac
  done
}

# Show help
function show_help() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --domain, -d      Domain to test (default: api.stage.divinci.app)"
  echo "  --environment, -e Environment to use (default: staging)"
  echo "  --endpoint, -p    Endpoint to test (default: /health)"
  echo "  --port            Port to connect to (default: 443)"
  echo "  --origin-ip       Origin IP address (optional, uses domain if not specified)"
  echo "  --test, -t        Test type to run (default: all)"
  echo "                    Available test types: all, basic, mtls, cloudflare, certificates,"
  echo "                    tls-versions, cipher-suites, certificate-chain"
  echo "  --tls-version     TLS version to test (1.0, 1.1, 1.2, 1.3)"
  echo "                    Only used with test type 'tls-versions'"
  echo "  --cipher          Cipher suite to test (e.g., 'ECDHE-RSA-AES128-GCM-SHA256')"
  echo "                    Only used with test type 'cipher-suites'"
  echo "  --help, -h        Show this help message"
}

# Run basic connectivity test
function run_basic_connectivity_test() {
  section "Basic Connectivity Test"
  test_basic_connectivity "$DOMAIN" "$ENDPOINT"
  return $?
}

# Run certificate validation test
function run_certificate_validation_test() {
  section "Certificate Validation Test"

  # Get certificate paths
  cert_paths=$(get_cert_paths "$ENVIRONMENT")
  if [ -n "$cert_paths" ]; then
    eval "$cert_paths"
  else
    error "Failed to get certificate paths"
    return 1
  fi

  if [ -z "$SERVER_CERT" ] || [ -z "$SERVER_KEY" ]; then
    error "Server certificate or key not found"
    return 1
  fi

  # Validate server certificate and key match
  info "Validating server certificate and key match..."
  validate_cert_key_match "$SERVER_CERT" "$SERVER_KEY"
  local server_match_result=$?

  # Check server certificate expiration
  info "Checking server certificate expiration..."
  check_cert_expiration "$SERVER_CERT" 30
  local server_expiration_result=$?

  # Get server certificate information
  info "Server certificate information:"
  get_cert_info "$SERVER_CERT"

  # Validate client certificate and key match (if available)
  if [ -n "$CLIENT_CERT" ] && [ -n "$CLIENT_KEY" ]; then
    info "Validating client certificate and key match..."
    validate_cert_key_match "$CLIENT_CERT" "$CLIENT_KEY"
    local client_match_result=$?

    # Check client certificate expiration
    info "Checking client certificate expiration..."
    check_cert_expiration "$CLIENT_CERT" 30
    local client_expiration_result=$?

    # Get client certificate information
    info "Client certificate information:"
    get_cert_info "$CLIENT_CERT"

    if [ $client_match_result -eq 0 ] && [ $client_expiration_result -eq 0 ]; then
      success "Client certificate validation passed"
    else
      error "Client certificate validation failed"
    fi
  else
    warning "Client certificate or key not found, skipping client certificate validation"
  fi

  if [ $server_match_result -eq 0 ] && [ $server_expiration_result -eq 0 ]; then
    success "Server certificate validation passed"
    return 0
  else
    error "Server certificate validation failed"
    return 1
  fi
}

# Run mTLS connection test
function run_mtls_connection_test() {
  section "mTLS Connection Test"

  # Get certificate paths
  cert_paths=$(get_cert_paths "$ENVIRONMENT")
  if [ -n "$cert_paths" ]; then
    eval "$cert_paths"
  else
    error "Failed to get certificate paths"
    return 1
  fi

  if [ -z "$CLIENT_CERT" ] || [ -z "$CLIENT_KEY" ]; then
    error "Client certificate or key not found"
    return 1
  fi

  # Test mTLS connection
  test_mtls_connection "$DOMAIN" "$CLIENT_CERT" "$CLIENT_KEY"
  local mtls_result=$?

  # Test HTTP request with mTLS
  test_http_request_with_mtls "$DOMAIN" "$ENDPOINT" "$CLIENT_CERT" "$CLIENT_KEY"
  local http_result=$?

  if [ $mtls_result -eq 0 ] && [ $http_result -eq 0 ]; then
    success "mTLS connection test passed"
    return 0
  else
    error "mTLS connection test failed"
    return 1
  fi
}

# Run Cloudflare SSL mode test
function run_cloudflare_ssl_test() {
  section "Cloudflare SSL Mode Test"

  # Check Cloudflare SSL mode
  check_cloudflare_ssl_mode "$DOMAIN"
  return $?
}

# Run TLS version test
function run_tls_version_test() {
  section "TLS Version Test"

  if [ -n "$TLS_VERSION" ]; then
    # Test specific TLS version
    test_tls_version "$DOMAIN" "$TLS_VERSION" "$PORT"
    return $?
  else
    # Test all TLS versions
    info "Testing all TLS versions..."

    # Test TLS 1.2
    test_tls_version "$DOMAIN" "1.2" "$PORT"
    local tls12_result=$?

    # Test TLS 1.3
    test_tls_version "$DOMAIN" "1.3" "$PORT"
    local tls13_result=$?

    # Try older versions (might not be supported)
    info "Testing older TLS versions (might not be supported)..."

    # Test TLS 1.1
    test_tls_version "$DOMAIN" "1.1" "$PORT"
    local tls11_result=$?

    # Test TLS 1.0
    test_tls_version "$DOMAIN" "1.0" "$PORT"
    local tls10_result=$?

    # Summary
    if [ $tls12_result -eq 0 ] || [ $tls13_result -eq 0 ]; then
      success "TLS version test passed (at least one modern version supported)"
      return 0
    else
      error "TLS version test failed (no modern versions supported)"
      return 1
    fi
  fi
}

# Run cipher suite test
function run_cipher_suite_test() {
  section "Cipher Suite Test"

  if [ -n "$CIPHER" ]; then
    # Test specific cipher suite
    test_cipher_suite "$DOMAIN" "$CIPHER" "$PORT"
    return $?
  else
    # Test common cipher suites
    info "Testing common cipher suites..."

    # Modern cipher suites
    local modern_ciphers=(
      "ECDHE-RSA-AES128-GCM-SHA256"
      "ECDHE-RSA-AES256-GCM-SHA384"
      "ECDHE-ECDSA-AES128-GCM-SHA256"
      "ECDHE-ECDSA-AES256-GCM-SHA384"
    )

    local passed=0

    for cipher in "${modern_ciphers[@]}"; do
      test_cipher_suite "$DOMAIN" "$cipher" "$PORT"
      if [ $? -eq 0 ]; then
        passed=$((passed + 1))
      fi
    done

    if [ $passed -gt 0 ]; then
      success "Cipher suite test passed ($passed modern ciphers supported)"
      return 0
    else
      error "Cipher suite test failed (no modern ciphers supported)"
      return 1
    fi
  fi
}

# Run certificate chain test
function run_certificate_chain_test() {
  section "Certificate Chain Test"

  # Create results directory
  local results_dir=$(create_results_dir "certificate-chain")

  # Extract certificate chain
  extract_certificate_chain "$DOMAIN" "$PORT" "$results_dir"
  local extract_result=$?

  # Verify certificate against Cloudflare Root CAs
  verify_certificate_against_cloudflare_roots "$results_dir/server_cert.pem" "$results_dir"
  local verify_result=$?

  if [ $extract_result -eq 0 ] && [ $verify_result -eq 0 ]; then
    success "Certificate chain test passed"
    return 0
  else
    error "Certificate chain test failed"
    return 1
  fi
}

# Run all tests
function run_all_tests() {
  # Create results file
  local results_file=$(create_results_dir "mtls-comprehensive")

  # Redirect output to both console and file
  exec > >(tee -a "$results_file") 2>&1

  section "mTLS Comprehensive Test"
  info "Domain: $DOMAIN"
  info "Environment: $ENVIRONMENT"
  info "Endpoint: $ENDPOINT"

  # Load environment variables
  load_env_vars

  # Check prerequisites
  check_openssl
  check_curl

  # Run tests
  run_basic_connectivity_test
  local basic_result=$?

  run_certificate_validation_test
  local cert_result=$?

  run_mtls_connection_test
  local mtls_result=$?

  run_cloudflare_ssl_test
  local cf_result=$?

  run_tls_version_test
  local tls_result=$?

  run_cipher_suite_test
  local cipher_result=$?

  run_certificate_chain_test
  local chain_result=$?

  # Summary
  section "Test Summary"

  if [ $basic_result -eq 0 ]; then
    success "Basic connectivity test: PASSED"
  else
    error "Basic connectivity test: FAILED"
  fi

  if [ $cert_result -eq 0 ]; then
    success "Certificate validation test: PASSED"
  else
    error "Certificate validation test: FAILED"
  fi

  if [ $mtls_result -eq 0 ]; then
    success "mTLS connection test: PASSED"
  else
    error "mTLS connection test: FAILED"
  fi

  if [ $cf_result -eq 0 ]; then
    success "Cloudflare SSL mode test: PASSED"
  else
    error "Cloudflare SSL mode test: FAILED"
  fi

  info "Test results saved to: $results_file"

  if [ $tls_result -eq 0 ]; then
    success "TLS version test: PASSED"
  else
    error "TLS version test: FAILED"
  fi

  if [ $cipher_result -eq 0 ]; then
    success "Cipher suite test: PASSED"
  else
    error "Cipher suite test: FAILED"
  fi

  if [ $chain_result -eq 0 ]; then
    success "Certificate chain test: PASSED"
  else
    error "Certificate chain test: FAILED"
  fi

  info "Test results saved to: $results_file"

  if [ $basic_result -eq 0 ] && [ $cert_result -eq 0 ] && [ $mtls_result -eq 0 ] && [ $cf_result -eq 0 ] &&
     [ $tls_result -eq 0 ] && [ $cipher_result -eq 0 ] && [ $chain_result -eq 0 ]; then
    success "All tests passed"
    return 0
  else
    error "Some tests failed"
    return 1
  fi
}

# Main function
function main() {
  parse_args "$@"

  # Use domain as origin IP if not specified
  if [ -z "$ORIGIN_IP" ]; then
    ORIGIN_IP="$DOMAIN"
  fi

  case $TEST_TYPE in
    all)
      run_all_tests
      ;;
    basic)
      run_basic_connectivity_test
      ;;
    certificates)
      run_certificate_validation_test
      ;;
    mtls)
      run_mtls_connection_test
      ;;
    cloudflare)
      run_cloudflare_ssl_test
      ;;
    tls-versions)
      run_tls_version_test
      ;;
    cipher-suites)
      run_cipher_suite_test
      ;;
    certificate-chain)
      run_certificate_chain_test
      ;;
    *)
      error "Unknown test type: $TEST_TYPE"
      show_help
      exit 1
      ;;
  esac
}

# Run main function
main "$@"
