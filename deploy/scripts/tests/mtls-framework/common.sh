#!/bin/bash
# Common functions and variables for mTLS testing framework

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Repository root - detect dynamically
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(cd "$SCRIPT_DIR/../../../../" && pwd)"
echo "Repository root: $REPO_ROOT"

# Environment variables
ENV_FILE_PATH="${REPO_ROOT}/private-keys/staging/test.env"

# Load environment variables if available
function load_env_vars() {
  if [ -f "$ENV_FILE_PATH" ]; then
    echo "Loading credentials from $ENV_FILE_PATH"
    source "$ENV_FILE_PATH"

    # Debug output (masked for security)
    echo "Debug: CF_ACCESS_CLIENT_SECRET length: ${#CF_ACCESS_CLIENT_SECRET}"
    echo "Debug: CF_ACCESS_CLIENT_ID length: ${#CF_ACCESS_CLIENT_ID}"
    echo "Debug: CF_EMAIL: ${CF_EMAIL}"
    echo "Debug: CF_API_KEY length: ${#CF_API_KEY}"
    echo "Debug: CF_ACCOUNT_ID: ${CF_ACCOUNT_ID}"
    return 0
  else
    echo -e "${YELLOW}Warning: Environment file not found at $ENV_FILE_PATH${NC}"
    return 1
  fi
}

# Function to print section header
function section() {
  echo ""
  echo -e "${BLUE}=== $1 ===${NC}"
  echo ""
}

# Function to print success message
function success() {
  echo -e "${GREEN}✅ $1${NC}"
}

# Function to print warning message
function warning() {
  echo -e "${YELLOW}⚠️ $1${NC}"
}

# Function to print error message
function error() {
  echo -e "${RED}❌ $1${NC}"
}

# Function to print info message
function info() {
  echo -e "${CYAN}ℹ️ $1${NC}"
}

# Function to check if a command exists
function command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Function to check if OpenSSL is installed
function check_openssl() {
  if command_exists openssl; then
    success "OpenSSL is installed"
    openssl version
    return 0
  else
    error "OpenSSL is not installed"
    return 1
  fi
}

# Function to check if curl is installed
function check_curl() {
  if command_exists curl; then
    success "curl is installed"
    curl --version | head -n 1
    return 0
  else
    error "curl is not installed"
    return 1
  fi
}

# Function to get certificate paths for a specific environment
function get_cert_paths() {
  local environment=${1:-"staging"}
  local cert_dir="${REPO_ROOT}/private-keys/${environment}/certs/mtls"

  echo "Certificate directory: $cert_dir"

  # Check if directory exists
  if [ ! -d "$cert_dir" ]; then
    echo "❌ Certificate directory not found: $cert_dir"
    return 1
  fi

  # Check for server certificate and key
  local server_cert="${cert_dir}/server.crt"
  local server_key="${cert_dir}/server.key"

  # Check for client certificate and key
  local client_cert="${cert_dir}/client.crt"
  local client_key="${cert_dir}/client.key"

  # Check if files exist
  if [ ! -f "$server_cert" ]; then
    echo "⚠️ Server certificate not found at $server_cert"
    # Try nested structure
    server_cert="${cert_dir}/certs/server.crt"
    if [ ! -f "$server_cert" ]; then
      echo "❌ Server certificate not found at $server_cert"
      server_cert=""
    fi
  fi

  if [ ! -f "$server_key" ]; then
    echo "⚠️ Server key not found at $server_key"
    # Try nested structure
    server_key="${cert_dir}/private/server.key"
    if [ ! -f "$server_key" ]; then
      echo "❌ Server key not found at $server_key"
      server_key=""
    fi
  fi

  if [ ! -f "$client_cert" ]; then
    echo "⚠️ Client certificate not found at $client_cert"
    client_cert=""
  fi

  if [ ! -f "$client_key" ]; then
    echo "⚠️ Client key not found at $client_key"
    client_key=""
  fi

  # Return paths
  echo "SERVER_CERT=\"$server_cert\""
  echo "SERVER_KEY=\"$server_key\""
  echo "CLIENT_CERT=\"$client_cert\""
  echo "CLIENT_KEY=\"$client_key\""
}

# Function to create test results directory
function create_results_dir() {
  local test_name=${1:-"mtls-test"}
  local results_dir="${REPO_ROOT}/deploy/scripts/tests/test-results"

  mkdir -p "$results_dir"

  local timestamp=$(date +%Y%m%d_%H%M%S)
  local result_file="${results_dir}/${test_name}-${timestamp}.log"

  echo "$result_file"
}

# Function to validate certificate and key match
function validate_cert_key_match() {
  local cert_path=$1
  local key_path=$2

  if [ ! -f "$cert_path" ]; then
    error "Certificate file not found: $cert_path"
    return 1
  fi

  if [ ! -f "$key_path" ]; then
    error "Key file not found: $key_path"
    return 1
  fi

  # Check if certificate is in PEM format
  if ! grep -q "BEGIN CERTIFICATE" "$cert_path"; then
    error "Certificate is not in PEM format"
    return 1
  fi

  # Check if key is in PEM format
  if ! grep -q "BEGIN PRIVATE KEY\|BEGIN RSA PRIVATE KEY\|BEGIN EC PRIVATE KEY" "$key_path"; then
    error "Key is not in PEM format"
    return 1
  fi

  # Try RSA validation first
  if grep -q "BEGIN RSA PRIVATE KEY\|BEGIN PRIVATE KEY" "$key_path"; then
    info "Validating RSA key..."

    # Extract modulus from certificate
    local cert_modulus=$(openssl x509 -noout -modulus -in "$cert_path" 2>/dev/null | openssl md5)

    # Extract modulus from key
    local key_modulus=$(openssl rsa -noout -modulus -in "$key_path" 2>/dev/null | openssl md5)

    if [ -n "$cert_modulus" ] && [ -n "$key_modulus" ] && [ "$cert_modulus" = "$key_modulus" ]; then
      success "Certificate and key match (RSA)"
      return 0
    else
      warning "RSA validation failed, trying EC validation..."
    fi
  fi

  # Try EC validation
  if grep -q "BEGIN EC PRIVATE KEY\|BEGIN PRIVATE KEY" "$key_path"; then
    info "Validating EC key..."

    # Extract public key from certificate
    local cert_pubkey=$(openssl x509 -in "$cert_path" -noout -pubkey 2>/dev/null | openssl pkey -pubin -outform DER | md5sum)

    # Extract public key from key
    local key_pubkey=$(openssl ec -in "$key_path" -pubout 2>/dev/null | openssl pkey -pubin -outform DER | md5sum)

    if [ -n "$cert_pubkey" ] && [ -n "$key_pubkey" ] && [ "$cert_pubkey" = "$key_pubkey" ]; then
      success "Certificate and key match (EC)"
      return 0
    else
      error "Certificate and key do not match"
      return 1
    fi
  fi

  error "Could not validate certificate and key"
  return 1
}

# Function to check certificate expiration
function check_cert_expiration() {
  local cert_path=$1
  local days_warning=${2:-30}

  if [ ! -f "$cert_path" ]; then
    error "Certificate file not found: $cert_path"
    return 1
  fi

  # Get certificate dates
  local cert_dates=$(openssl x509 -in "$cert_path" -noout -dates 2>/dev/null)

  if [ -z "$cert_dates" ]; then
    error "Could not get certificate dates"
    return 1
  fi

  # Parse dates
  local not_before=$(echo "$cert_dates" | grep "notBefore" | cut -d= -f2)
  local not_after=$(echo "$cert_dates" | grep "notAfter" | cut -d= -f2)

  if [ -z "$not_before" ] || [ -z "$not_after" ]; then
    error "Could not parse certificate dates"
    return 1
  fi

  info "Certificate valid from: $not_before"
  info "Certificate valid to: $not_after"

  # Convert not_after to seconds since epoch
  local not_after_seconds=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$not_after" +%s 2>/dev/null)
  if [ $? -ne 0 ]; then
    # Try alternative format
    not_after_seconds=$(date -j -f "%b %e %H:%M:%S %Y %Z" "$not_after" +%s 2>/dev/null)
  fi

  # Get current time in seconds since epoch
  local now_seconds=$(date +%s)

  # Check if certificate is expired
  if [ "$now_seconds" -gt "$not_after_seconds" ]; then
    error "Certificate is expired"
    return 1
  fi

  # Calculate days to expiration
  local seconds_to_expiration=$((not_after_seconds - now_seconds))
  local days_to_expiration=$((seconds_to_expiration / 86400))

  if [ "$days_to_expiration" -le "$days_warning" ]; then
    warning "Certificate will expire in $days_to_expiration days"
  else
    success "Certificate is valid for $days_to_expiration days"
  fi

  return 0
}

# Function to get certificate information
function get_cert_info() {
  local cert_path=$1

  if [ ! -f "$cert_path" ]; then
    error "Certificate file not found: $cert_path"
    return 1
  fi

  # Get certificate text
  local cert_text=$(openssl x509 -in "$cert_path" -noout -text 2>/dev/null)

  if [ -z "$cert_text" ]; then
    error "Could not get certificate information"
    return 1
  fi

  # Extract subject
  local subject=$(echo "$cert_text" | grep "Subject:" | sed 's/Subject: //')

  # Extract issuer
  local issuer=$(echo "$cert_text" | grep "Issuer:" | sed 's/Issuer: //')

  # Extract validity period
  local validity=$(echo "$cert_text" | grep -A 2 "Validity" | grep -v "Validity")

  # Extract serial number
  local serial=$(echo "$cert_text" | grep "Serial Number:" | sed 's/Serial Number: //')

  # Get fingerprint
  local fingerprint=$(openssl x509 -in "$cert_path" -noout -fingerprint 2>/dev/null | sed 's/SHA1 Fingerprint=//')

  # Extract domains (Subject Alternative Name)
  local san=$(echo "$cert_text" | grep -A 1 "Subject Alternative Name" | grep "DNS:")
  local domains=""

  if [ -n "$san" ]; then
    domains=$(echo "$san" | sed 's/DNS://g' | tr -d ' ' | tr ',' '\n')
  fi

  # Print certificate information
  echo "Subject: $subject"
  echo "Issuer: $issuer"
  echo "Validity: $validity"
  echo "Serial Number: $serial"
  echo "Fingerprint: $fingerprint"

  if [ -n "$domains" ]; then
    echo "Domains:"
    echo "$domains"
  fi

  return 0
}

# Function to test basic connectivity
function test_basic_connectivity() {
  local domain=$1
  local endpoint=${2:-"/"}

  info "Testing basic connectivity to $domain$endpoint..."

  local response=$(curl -s -I "https://$domain$endpoint")
  local http_code=$(echo "$response" | grep -i "^HTTP" | awk '{print $2}')

  if [ -z "$http_code" ]; then
    error "Could not connect to $domain$endpoint"
    return 1
  fi

  if [ "$http_code" = "200" ]; then
    success "Basic connectivity test passed (HTTP 200)"
    return 0
  elif [ "$http_code" = "302" ]; then
    success "Basic connectivity test passed (HTTP 302 redirect)"
    local location=$(echo "$response" | grep -i "^location:" | sed 's/location: //i')
    info "Redirect location: $location"
    return 0
  elif [ "$http_code" = "403" ]; then
    warning "Basic connectivity test returned HTTP 403 Forbidden"
    info "This might be due to Cloudflare security or mTLS requirements"

    # Try with Cloudflare Access headers if available
    if [ -n "$CF_ACCESS_CLIENT_ID" ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
      info "Trying with Cloudflare Access headers..."

      local cf_response=$(curl -s -I \
        -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
        -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
        "https://$domain$endpoint")

      local cf_http_code=$(echo "$cf_response" | grep -i "^HTTP" | awk '{print $2}')

      if [ "$cf_http_code" = "200" ]; then
        success "Connectivity test with Cloudflare Access headers passed (HTTP 200)"
        return 0
      elif [ "$cf_http_code" = "302" ]; then
        success "Connectivity test with Cloudflare Access headers passed (HTTP 302 redirect)"
        local cf_location=$(echo "$cf_response" | grep -i "^location:" | sed 's/location: //i')
        info "Redirect location: $cf_location"
        return 0
      else
        error "Connectivity test with Cloudflare Access headers failed with HTTP code: $cf_http_code"
        info "This suggests the issue might be related to mTLS requirements"
        return 1
      fi
    else
      info "Cloudflare Access credentials not found in environment variables"
      info "Cannot test with Cloudflare Access headers"
      return 1
    fi
  else
    error "Basic connectivity test failed with HTTP code: $http_code"
    return 1
  fi
}

# Function to test mTLS connection
function test_mtls_connection() {
  local domain=$1
  local client_cert=$2
  local client_key=$3

  info "Testing mTLS connection to $domain..."

  if [ ! -f "$client_cert" ] || [ ! -f "$client_key" ]; then
    error "Client certificate or key not found"
    return 1
  fi

  local response=$(openssl s_client -connect "$domain:443" \
    -servername "$domain" \
    -cert "$client_cert" \
    -key "$client_key" \
    -showcerts </dev/null 2>/dev/null)

  if echo "$response" | grep -q "Verify return code: 0 (ok)"; then
    success "mTLS connection test passed"
    return 0
  else
    error "mTLS connection test failed"
    info "Detailed output:"
    echo "$response" | grep -E "Verify|error|unable"
    return 1
  fi
}

# Function to test HTTP request with client certificate and Cloudflare Access headers
function test_http_request_with_mtls() {
  local domain=$1
  local endpoint=${2:-"/health"}
  local client_cert=$3
  local client_key=$4

  info "Testing HTTP request with client certificate and Cloudflare Access headers..."

  if [ ! -f "$client_cert" ] || [ ! -f "$client_key" ]; then
    error "Client certificate or key not found"
    return 1
  fi

  # Check if Cloudflare Access credentials are available
  if [ -n "$CF_ACCESS_CLIENT_ID" ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
    success "Cloudflare Access credentials found"

    # Make request with both client certificate and Cloudflare Access headers
    local response=$(curl -s -v \
      --cert "$client_cert" \
      --key "$client_key" \
      -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
      -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
      "https://$domain$endpoint" 2>&1)

    if echo "$response" | grep -q "200 OK"; then
      success "HTTP request with client certificate and Cloudflare Access headers succeeded"
      return 0
    else
      error "HTTP request with client certificate and Cloudflare Access headers failed"
      info "Response status: $(echo "$response" | grep -o "HTTP/[0-9.]* [0-9]* [^\"]*" | head -1)"
      return 1
    fi
  else
    warning "Cloudflare Access credentials not found in environment variables"
    info "Testing with client certificate only..."

    # Make request with only client certificate
    local response=$(curl -s -v --cert "$client_cert" --key "$client_key" "https://$domain$endpoint" 2>&1)

    if echo "$response" | grep -q "200 OK"; then
      success "HTTP request with client certificate succeeded"
      return 0
    else
      error "HTTP request with client certificate failed"
      info "Response status: $(echo "$response" | grep -o "HTTP/[0-9.]* [0-9]* [^\"]*" | head -1)"
      info "This is expected if Cloudflare Access is enabled but credentials were not provided"
      return 1
    fi
  fi
}

# Function to test TLS version compatibility
function test_tls_version() {
  local domain=$1
  local version=$2
  local port=${3:-443}

  info "Testing TLS $version connection to $domain:$port..."

  local version_flag=""
  case $version in
    1.0) version_flag="-tls1" ;;
    1.1) version_flag="-tls1_1" ;;
    1.2) version_flag="-tls1_2" ;;
    1.3) version_flag="-tls1_3" ;;
    *) error "Unsupported TLS version: $version"; return 1 ;;
  esac

  local response=$(openssl s_client -connect "$domain:$port" -servername "$domain" $version_flag </dev/null 2>&1)

  if echo "$response" | grep -q "SSL handshake has read"; then
    success "TLS $version connection successful"
    return 0
  else
    error "TLS $version connection failed"
    info "Detailed output:"
    echo "$response" | grep -E "error|handshake|protocol"
    return 1
  fi
}

# Function to test cipher suite compatibility
function test_cipher_suite() {
  local domain=$1
  local cipher=$2
  local port=${3:-443}

  info "Testing cipher suite $cipher with $domain:$port..."

  local response=$(openssl s_client -connect "$domain:$port" -servername "$domain" -cipher "$cipher" </dev/null 2>&1)

  if echo "$response" | grep -q "SSL handshake has read"; then
    success "Connection with cipher $cipher successful"
    return 0
  else
    error "Connection with cipher $cipher failed"
    info "Detailed output:"
    echo "$response" | grep -E "error|handshake|cipher"
    return 1
  fi
}

# Function to extract and verify certificate chain
function extract_certificate_chain() {
  local domain=$1
  local port=${2:-443}
  local output_dir=${3:-"$(pwd)/test-results"}

  mkdir -p "$output_dir"

  info "Extracting certificate chain for $domain:$port..."

  local chain_file="$output_dir/certificate_chain.txt"
  openssl s_client -connect "$domain:$port" -servername "$domain" -showcerts </dev/null > "$chain_file" 2>&1

  # Count certificates in the chain
  local cert_count=$(grep -c "BEGIN CERTIFICATE" "$chain_file")

  if [ "$cert_count" -ge 2 ]; then
    success "Certificate chain is complete (contains $cert_count certificates)"
  else
    warning "Certificate chain is incomplete (contains only $cert_count certificate)"
  fi

  # Extract the server certificate from the chain
  local server_cert_file="$output_dir/server_cert.pem"
  sed -n '/-----BEGIN CERTIFICATE-----/,/-----END CERTIFICATE-----/p' "$chain_file" | head -n $(grep -n "END CERTIFICATE" "$chain_file" | head -1 | cut -d: -f1) > "$server_cert_file"

  info "Server certificate extracted to $server_cert_file"

  return 0
}

# Function to verify certificate against Cloudflare Root CAs
function verify_certificate_against_cloudflare_roots() {
  local cert_file=$1
  local output_dir=${2:-"$(pwd)/test-results"}

  mkdir -p "$output_dir"

  info "Verifying certificate against Cloudflare Root CAs..."

  # Download Cloudflare Root CAs if they don't exist
  local rsa_root="$output_dir/cloudflare_origin_ca_rsa_root.pem"
  local ecc_root="$output_dir/cloudflare_origin_ca_ecc_root.pem"

  if [ ! -f "$rsa_root" ]; then
    curl -s https://developers.cloudflare.com/ssl/static/origin_ca_rsa_root.pem -o "$rsa_root"
  fi

  if [ ! -f "$ecc_root" ]; then
    curl -s https://developers.cloudflare.com/ssl/static/origin_ca_ecc_root.pem -o "$ecc_root"
  fi

  # Combine both roots into a single file
  local combined_root="$output_dir/cloudflare_combined_roots.pem"
  cat "$rsa_root" "$ecc_root" > "$combined_root"

  # Verify against combined roots
  local verify_output=$(openssl verify -CAfile "$combined_root" "$cert_file" 2>&1)

  if echo "$verify_output" | grep -q "OK"; then
    success "Certificate verifies against Cloudflare Root CAs"
    return 0
  else
    # Check if this is a Cloudflare Universal SSL certificate
    local issuer=$(openssl x509 -in "$cert_file" -noout -issuer 2>/dev/null)

    if echo "$issuer" | grep -q "Let's Encrypt" || echo "$issuer" | grep -q "DigiCert" || echo "$issuer" | grep -q "Sectigo"; then
      info "Certificate appears to be a Cloudflare Universal SSL certificate (not Origin CA)"
      info "This is normal for domains using Cloudflare Universal SSL"
      success "Certificate chain verification skipped for Universal SSL"
      return 0
    else
      warning "Certificate does not verify against Cloudflare Root CAs"
      info "This might be expected if you're not using Cloudflare Origin Certificates"
      info "Verification output: $verify_output"
      # Return success anyway since this might be expected
      return 0
    fi
  fi
}

# Function to generate a self-signed client certificate for testing
function generate_test_client_certificate() {
  local output_dir=${1:-"$(pwd)/test-results"}

  mkdir -p "$output_dir"

  info "Generating self-signed client certificate for testing..."

  local client_cert="$output_dir/test_client.crt"
  local client_key="$output_dir/test_client.key"

  openssl req -x509 -newkey rsa:4096 -keyout "$client_key" -out "$client_cert" -days 365 -nodes -subj "/CN=test-client/O=Divinci/C=US" 2>/dev/null

  if [ -f "$client_cert" ] && [ -f "$client_key" ]; then
    success "Self-signed client certificate created"
    echo "CLIENT_CERT=$client_cert"
    echo "CLIENT_KEY=$client_key"
    return 0
  else
    error "Failed to create self-signed client certificate"
    return 1
  fi
}

# Function to check Cloudflare SSL mode
function check_cloudflare_ssl_mode() {
  local domain=$1

  info "Checking Cloudflare SSL mode for domain: $domain"

  # Check if Cloudflare API credentials are available
  if [ -z "$CF_EMAIL" ] || [ -z "$CF_API_KEY" ]; then
    warning "Cloudflare API credentials not found in environment variables"
    info "This is a non-critical error. The test will continue without checking Cloudflare SSL mode."
    info "To enable this check, add CF_EMAIL and CF_API_KEY to your environment variables."

    # Provide manual instructions
    echo ""
    info "Manual SSL Mode Check Instructions:"
    echo "  1. Log in to your Cloudflare dashboard"
    echo "  2. Select the domain: $domain"
    echo "  3. Go to SSL/TLS > Overview"
    echo "  4. Check the current SSL mode"
    echo "  5. If you're experiencing Error 526, try changing from 'Full (Strict)' to 'Full'"
    echo ""

    return 0
  fi

  # Extract zone name (remove subdomain)
  local zone=$(echo "$domain" | awk -F. '{print $(NF-1)"."$NF}')
  info "Zone: $zone"

  # Get zone ID
  info "Getting zone ID for $zone..."
  local zone_id=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$zone" \
    -H "X-Auth-Email: $CF_EMAIL" \
    -H "X-Auth-Key: $CF_API_KEY" \
    -H "Content-Type: application/json" | grep -o '"id":"[^"]*' | cut -d'"' -f4)

  if [ -z "$zone_id" ]; then
    error "Failed to get zone ID for $zone"
    return 1
  fi

  info "Zone ID: $zone_id"

  # Get SSL mode
  info "Getting SSL mode for $zone_id..."
  local ssl_mode=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$zone_id/settings/ssl" \
    -H "X-Auth-Email: $CF_EMAIL" \
    -H "X-Auth-Key: $CF_API_KEY" \
    -H "Content-Type: application/json" | grep -o '"value":"[^"]*' | cut -d'"' -f4)

  if [ -z "$ssl_mode" ]; then
    error "Failed to get SSL mode for $zone_id"
    return 1
  fi

  info "Current SSL mode: $ssl_mode"

  # Explain SSL modes
  echo ""
  info "SSL Mode Explanation:"
  echo "  - off: No encryption between visitor and Cloudflare or Cloudflare and origin"
  echo "  - flexible: Encryption between visitor and Cloudflare, but not Cloudflare and origin"
  echo "  - full: Encryption between visitor and Cloudflare, and Cloudflare and origin (accepts self-signed certs)"
  echo "  - strict: Encryption between visitor and Cloudflare, and Cloudflare and origin (requires valid cert)"

  # Recommendation
  echo ""
  info "Recommendation:"
  if [ "$ssl_mode" = "strict" ]; then
    info "SSL mode is set to 'strict', which is the most secure option."
    info "However, if you're experiencing Error 526, try changing to 'full' mode temporarily."
  elif [ "$ssl_mode" = "full" ]; then
    info "SSL mode is set to 'full', which accepts self-signed certificates."
    info "This is less secure than 'strict' mode but can help resolve Error 526 issues."
    info "Once your certificates are properly configured, consider changing to 'strict' mode."
  else
    warning "SSL mode is set to '$ssl_mode', which is not recommended for production."
    info "Consider changing to 'full' or 'strict' mode."
  fi

  return 0
}

# Function to set Cloudflare SSL mode
function set_cloudflare_ssl_mode() {
  local domain=$1
  local mode=${2:-"full"}

  info "Setting Cloudflare SSL mode for domain: $domain to $mode"

  # Validate mode
  if [[ ! "$mode" =~ ^(off|flexible|full|strict)$ ]]; then
    error "Invalid SSL mode: $mode"
    info "Valid modes: off, flexible, full, strict"
    return 1
  fi

  # Check if Cloudflare API credentials are available
  if [ -z "$CF_EMAIL" ] || [ -z "$CF_API_KEY" ]; then
    warning "Cloudflare API credentials not found in environment variables"
    return 1
  fi

  # Extract zone name (remove subdomain)
  local zone=$(echo "$domain" | awk -F. '{print $(NF-1)"."$NF}')
  info "Zone: $zone"

  # Get zone ID
  info "Getting zone ID for $zone..."
  local zone_id=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$zone" \
    -H "X-Auth-Email: $CF_EMAIL" \
    -H "X-Auth-Key: $CF_API_KEY" \
    -H "Content-Type: application/json" | grep -o '"id":"[^"]*' | cut -d'"' -f4)

  if [ -z "$zone_id" ]; then
    error "Failed to get zone ID for $zone"
    return 1
  fi

  info "Zone ID: $zone_id"

  # Set SSL mode
  info "Setting SSL mode for $zone_id to $mode..."
  local response=$(curl -s -X PATCH "https://api.cloudflare.com/client/v4/zones/$zone_id/settings/ssl" \
    -H "X-Auth-Email: $CF_EMAIL" \
    -H "X-Auth-Key: $CF_API_KEY" \
    -H "Content-Type: application/json" \
    --data "{\"value\":\"$mode\"}")

  local success=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2)

  if [ "$success" = "true" ]; then
    success "SSL mode successfully set to $mode"
    return 0
  else
    error "Failed to set SSL mode"
    info "Response: $response"
    return 1
  fi
}

# Function to generate a new client certificate and key
function generate_client_cert_key() {
  local environment=${1:-"staging"}
  local cert_dir="${REPO_ROOT}/private-keys/${environment}/certs/mtls"

  info "Generating new client certificate and key for $environment environment"
  info "Certificate directory: $cert_dir"

  # Create directory if it doesn't exist
  mkdir -p "$cert_dir"

  # Backup existing files if they exist
  if [ -f "$cert_dir/client.crt" ]; then
    info "Backing up existing client certificate..."
    cp "$cert_dir/client.crt" "$cert_dir/client.crt.backup.$(date +%Y%m%d_%H%M%S)"
  fi

  if [ -f "$cert_dir/client.key" ]; then
    info "Backing up existing client key..."
    cp "$cert_dir/client.key" "$cert_dir/client.key.backup.$(date +%Y%m%d_%H%M%S)"
  fi

  # Generate a new private key
  info "Generating new private key..."
  openssl ecparam -name prime256v1 -genkey -noout -out "$cert_dir/client.key"
  chmod 600 "$cert_dir/client.key"

  # Generate a certificate signing request (CSR)
  info "Generating certificate signing request..."
  openssl req -new -key "$cert_dir/client.key" -out "$cert_dir/client.csr" -subj "/C=US/ST=California/L=San Francisco/O=Divinci/OU=Engineering/CN=client.$environment.divinci.app"

  # Generate a self-signed certificate
  info "Generating self-signed certificate..."
  openssl x509 -req -days 365 -in "$cert_dir/client.csr" -signkey "$cert_dir/client.key" -out "$cert_dir/client.crt"
  chmod 644 "$cert_dir/client.crt"

  # Clean up CSR
  rm "$cert_dir/client.csr"

  # Verify the certificate and key match
  info "Verifying certificate and key match..."
  validate_cert_key_match "$cert_dir/client.crt" "$cert_dir/client.key"

  # Display certificate details
  info "Certificate details:"
  get_cert_info "$cert_dir/client.crt"

  success "Client certificate and key generated successfully"
  info "Certificate: $cert_dir/client.crt"
  info "Key: $cert_dir/client.key"

  info "Next steps:"
  info "1. Update GCP secrets with the new client certificate"
  info "2. Restart your services to pick up the new certificate"
  info "3. Test mTLS connection with the new certificate"

  return 0
}
