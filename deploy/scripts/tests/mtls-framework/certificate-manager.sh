#!/bin/bash
# Certificate management utility for mTLS testing framework

# Source common functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# Default values
ENVIRONMENT="staging"
ACTION="status"

# Parse command line arguments
function parse_args() {
  while [[ $# -gt 0 ]]; do
    case $1 in
      --environment|-e)
        ENVIRONMENT="$2"
        shift 2
        ;;
      --action|-a)
        ACTION="$2"
        shift 2
        ;;
      --help|-h)
        show_help
        exit 0
        ;;
      *)
        error "Unknown option: $1"
        show_help
        exit 1
        ;;
    esac
  done
}

# Show help
function show_help() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --environment, -e Environment to use (default: staging)"
  echo "  --action, -a      Action to perform (default: status)"
  echo "                    Available actions: status, generate, rotate, verify"
  echo "  --help, -h        Show this help message"
}

# Show certificate status
function show_certificate_status() {
  section "Certificate Status for $ENVIRONMENT Environment"
  
  # Get certificate paths
  eval $(get_cert_paths "$ENVIRONMENT")
  
  # Check server certificate
  if [ -n "$SERVER_CERT" ]; then
    success "Server certificate found: $SERVER_CERT"
    check_cert_expiration "$SERVER_CERT" 30
    get_cert_info "$SERVER_CERT"
  else
    error "Server certificate not found"
  fi
  
  # Check server key
  if [ -n "$SERVER_KEY" ]; then
    success "Server key found: $SERVER_KEY"
  else
    error "Server key not found"
  fi
  
  # Check client certificate
  if [ -n "$CLIENT_CERT" ]; then
    success "Client certificate found: $CLIENT_CERT"
    check_cert_expiration "$CLIENT_CERT" 30
    get_cert_info "$CLIENT_CERT"
  else
    error "Client certificate not found"
  fi
  
  # Check client key
  if [ -n "$CLIENT_KEY" ]; then
    success "Client key found: $CLIENT_KEY"
  else
    error "Client key not found"
  fi
  
  # Validate certificate and key match
  if [ -n "$SERVER_CERT" ] && [ -n "$SERVER_KEY" ]; then
    info "Validating server certificate and key match..."
    validate_cert_key_match "$SERVER_CERT" "$SERVER_KEY"
  fi
  
  if [ -n "$CLIENT_CERT" ] && [ -n "$CLIENT_KEY" ]; then
    info "Validating client certificate and key match..."
    validate_cert_key_match "$CLIENT_CERT" "$CLIENT_KEY"
  fi
}

# Generate new certificates
function generate_certificates() {
  section "Generating New Certificates for $ENVIRONMENT Environment"
  
  # Generate client certificate and key
  generate_client_cert_key "$ENVIRONMENT"
}

# Rotate certificates
function rotate_certificates() {
  section "Rotating Certificates for $ENVIRONMENT Environment"
  
  # Get certificate paths
  eval $(get_cert_paths "$ENVIRONMENT")
  
  # Check if certificates exist
  if [ -z "$CLIENT_CERT" ] || [ -z "$CLIENT_KEY" ]; then
    error "Client certificate or key not found"
    info "Generating new certificates instead of rotating..."
    generate_certificates
    return $?
  fi
  
  # Check certificate expiration
  info "Checking current certificate expiration..."
  check_cert_expiration "$CLIENT_CERT" 30
  
  # Backup existing certificates
  local timestamp=$(date +%Y%m%d_%H%M%S)
  local cert_dir=$(dirname "$CLIENT_CERT")
  
  info "Backing up existing certificates..."
  cp "$CLIENT_CERT" "$cert_dir/client.crt.backup.$timestamp"
  cp "$CLIENT_KEY" "$cert_dir/client.key.backup.$timestamp"
  
  # Generate new certificates
  info "Generating new certificates..."
  generate_client_cert_key "$ENVIRONMENT"
  
  success "Certificate rotation completed"
  info "Old certificates backed up with timestamp: $timestamp"
  info "Next steps:"
  info "1. Update GCP secrets with the new client certificate"
  info "2. Restart your services to pick up the new certificate"
  info "3. Test mTLS connection with the new certificate"
}

# Verify certificates
function verify_certificates() {
  section "Verifying Certificates for $ENVIRONMENT Environment"
  
  # Get certificate paths
  eval $(get_cert_paths "$ENVIRONMENT")
  
  # Check if certificates exist
  if [ -z "$SERVER_CERT" ] || [ -z "$SERVER_KEY" ]; then
    error "Server certificate or key not found"
    return 1
  fi
  
  if [ -z "$CLIENT_CERT" ] || [ -z "$CLIENT_KEY" ]; then
    error "Client certificate or key not found"
    return 1
  fi
  
  # Validate server certificate and key match
  info "Validating server certificate and key match..."
  validate_cert_key_match "$SERVER_CERT" "$SERVER_KEY"
  local server_match_result=$?
  
  # Validate client certificate and key match
  info "Validating client certificate and key match..."
  validate_cert_key_match "$CLIENT_CERT" "$CLIENT_KEY"
  local client_match_result=$?
  
  # Check server certificate expiration
  info "Checking server certificate expiration..."
  check_cert_expiration "$SERVER_CERT" 30
  local server_expiration_result=$?
  
  # Check client certificate expiration
  info "Checking client certificate expiration..."
  check_cert_expiration "$CLIENT_CERT" 30
  local client_expiration_result=$?
  
  # Summary
  section "Verification Summary"
  
  if [ $server_match_result -eq 0 ]; then
    success "Server certificate and key match"
  else
    error "Server certificate and key do not match"
  fi
  
  if [ $client_match_result -eq 0 ]; then
    success "Client certificate and key match"
  else
    error "Client certificate and key do not match"
  fi
  
  if [ $server_expiration_result -eq 0 ]; then
    success "Server certificate is not expired"
  else
    error "Server certificate is expired or will expire soon"
  fi
  
  if [ $client_expiration_result -eq 0 ]; then
    success "Client certificate is not expired"
  else
    error "Client certificate is expired or will expire soon"
  fi
  
  if [ $server_match_result -eq 0 ] && [ $client_match_result -eq 0 ] && 
     [ $server_expiration_result -eq 0 ] && [ $client_expiration_result -eq 0 ]; then
    success "All certificates are valid"
    return 0
  else
    error "Some certificates are invalid"
    return 1
  fi
}

# Main function
function main() {
  parse_args "$@"
  
  case $ACTION in
    status)
      show_certificate_status
      ;;
    generate)
      generate_certificates
      ;;
    rotate)
      rotate_certificates
      ;;
    verify)
      verify_certificates
      ;;
    *)
      error "Unknown action: $ACTION"
      show_help
      exit 1
      ;;
  esac
}

# Run main function
main "$@"
