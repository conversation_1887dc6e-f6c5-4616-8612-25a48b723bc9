#!/bin/bash
# Cloudflare Access Test Script
# This script tests API endpoints with Cloudflare Access headers

set -e

# Load environment variables
if [ -f "/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env" ]; then
  echo "Loading credentials from /Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env"
  source "/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env"
  
  # Debug output (masked for security)
  echo "Debug: CF_ACCESS_CLIENT_SECRET length: ${#CF_ACCESS_CLIENT_SECRET}"
  echo "Debug: CF_ACCESS_CLIENT_ID length: ${#CF_ACCESS_CLIENT_ID}"
fi

# Get domain from command line
DOMAIN=${1:-"api.stage.divinci.app"}
ENDPOINT=${2:-"/health"}
REPO_ROOT="/Users/<USER>/Documents/Divinci/server3/server"
CERT_DIR="$REPO_ROOT/private-keys/staging/certs/mtls"
OUTPUT_DIR="$REPO_ROOT/deploy/scripts/tests/test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== Cloudflare Access Test ==="
echo "Domain: $DOMAIN"
echo "Endpoint: $ENDPOINT"
echo "Certificate directory: $CERT_DIR"
echo ""

# Function to print section header
section() {
  echo ""
  echo "=== $1 ==="
  echo ""
}

# Test 1: Basic request without headers
section "Test 1: Basic Request (No Headers)"
echo "Testing basic request without Cloudflare Access headers..."
RESPONSE=$(curl -s -I "https://$DOMAIN$ENDPOINT")
HTTP_CODE=$(echo "$RESPONSE" | grep -i "^HTTP" | awk '{print $2}')

if [ "$HTTP_CODE" = "200" ]; then
  echo "✅ Basic request succeeded (HTTP 200)"
  echo "   This suggests the endpoint is not protected by Cloudflare Access"
elif [ "$HTTP_CODE" = "302" ]; then
  echo "✅ Basic request redirected (HTTP 302)"
  LOCATION=$(echo "$RESPONSE" | grep -i "^location:" | sed 's/location: //i')
  echo "   Redirect location: $LOCATION"
  
  if [[ "$LOCATION" == *"cloudflareaccess.com"* ]]; then
    echo "   This confirms the endpoint is protected by Cloudflare Access"
  fi
elif [ "$HTTP_CODE" = "403" ]; then
  echo "⚠️ Basic request returned HTTP 403 Forbidden"
  echo "   This suggests the endpoint is protected by Cloudflare Access or other security measures"
else
  echo "❌ Basic request failed with HTTP code: $HTTP_CODE"
fi

# Test 2: Request with Cloudflare Access headers
section "Test 2: Request with Cloudflare Access Headers"
if [ -n "$CF_ACCESS_CLIENT_ID" ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
  echo "Testing request with Cloudflare Access headers..."
  CF_RESPONSE=$(curl -s -I \
    -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
    -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
    "https://$DOMAIN$ENDPOINT")
  CF_HTTP_CODE=$(echo "$CF_RESPONSE" | grep -i "^HTTP" | awk '{print $2}')
  
  if [ "$CF_HTTP_CODE" = "200" ]; then
    echo "✅ Request with Cloudflare Access headers succeeded (HTTP 200)"
    echo "   This confirms the endpoint is protected by Cloudflare Access"
  elif [ "$CF_HTTP_CODE" = "302" ]; then
    echo "✅ Request with Cloudflare Access headers redirected (HTTP 302)"
    CF_LOCATION=$(echo "$CF_RESPONSE" | grep -i "^location:" | sed 's/location: //i')
    echo "   Redirect location: $CF_LOCATION"
  else
    echo "❌ Request with Cloudflare Access headers failed with HTTP code: $CF_HTTP_CODE"
    echo "   This suggests additional authentication (like mTLS) is required"
  fi
else
  echo "⚠️ Cloudflare Access credentials not found in environment variables"
  echo "   Cannot test with Cloudflare Access headers"
fi

# Test 3: Request with Cloudflare Access headers and mTLS
section "Test 3: Request with Cloudflare Access Headers and mTLS"
if [ -n "$CF_ACCESS_CLIENT_ID" ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ] && [ -f "$CERT_DIR/client.crt" ] && [ -f "$CERT_DIR/client.key" ]; then
  echo "Testing request with Cloudflare Access headers and mTLS..."
  MTLS_RESPONSE=$(curl -s -I \
    -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
    -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
    --cert "$CERT_DIR/client.crt" \
    --key "$CERT_DIR/client.key" \
    "https://$DOMAIN$ENDPOINT")
  MTLS_HTTP_CODE=$(echo "$MTLS_RESPONSE" | grep -i "^HTTP" | awk '{print $2}')
  
  if [ "$MTLS_HTTP_CODE" = "200" ]; then
    echo "✅ Request with Cloudflare Access headers and mTLS succeeded (HTTP 200)"
    echo "   This confirms the endpoint requires both Cloudflare Access and mTLS"
  elif [ "$MTLS_HTTP_CODE" = "302" ]; then
    echo "✅ Request with Cloudflare Access headers and mTLS redirected (HTTP 302)"
    MTLS_LOCATION=$(echo "$MTLS_RESPONSE" | grep -i "^location:" | sed 's/location: //i')
    echo "   Redirect location: $MTLS_LOCATION"
  else
    echo "❌ Request with Cloudflare Access headers and mTLS failed with HTTP code: $MTLS_HTTP_CODE"
    echo "   This suggests there might be an issue with the client certificate or mTLS configuration"
  fi
else
  if [ ! -f "$CERT_DIR/client.crt" ] || [ ! -f "$CERT_DIR/client.key" ]; then
    echo "⚠️ Client certificate or key not found"
    echo "   Cannot test with mTLS"
  else
    echo "⚠️ Cloudflare Access credentials not found in environment variables"
    echo "   Cannot test with Cloudflare Access headers"
  fi
fi

# Test 4: Full request with body
section "Test 4: Full Request with Body"
if [ -n "$CF_ACCESS_CLIENT_ID" ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ] && [ -f "$CERT_DIR/client.crt" ] && [ -f "$CERT_DIR/client.key" ]; then
  echo "Testing full request with body..."
  FULL_RESPONSE=$(curl -s \
    -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
    -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
    --cert "$CERT_DIR/client.crt" \
    --key "$CERT_DIR/client.key" \
    "https://$DOMAIN$ENDPOINT")
  
  # Save response to file
  echo "$FULL_RESPONSE" > "$OUTPUT_DIR/cloudflare_access_test_response.txt"
  
  if [ -n "$FULL_RESPONSE" ]; then
    echo "✅ Full request succeeded"
    echo "   Response saved to $OUTPUT_DIR/cloudflare_access_test_response.txt"
    echo "   First 100 characters of response:"
    echo "   ${FULL_RESPONSE:0:100}"
  else
    echo "❌ Full request failed (empty response)"
  fi
else
  echo "⚠️ Skipping full request test due to missing credentials or certificates"
fi

# Summary
section "Summary"
echo "1. Basic request: $([ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "302" ] && echo "✅ Passed" || echo "❌ Failed") ($HTTP_CODE)"
if [ -n "$CF_HTTP_CODE" ]; then
  echo "2. Cloudflare Access: $([ "$CF_HTTP_CODE" = "200" ] || [ "$CF_HTTP_CODE" = "302" ] && echo "✅ Passed" || echo "❌ Failed") ($CF_HTTP_CODE)"
else
  echo "2. Cloudflare Access: ⚠️ Not tested"
fi
if [ -n "$MTLS_HTTP_CODE" ]; then
  echo "3. Cloudflare Access + mTLS: $([ "$MTLS_HTTP_CODE" = "200" ] || [ "$MTLS_HTTP_CODE" = "302" ] && echo "✅ Passed" || echo "❌ Failed") ($MTLS_HTTP_CODE)"
else
  echo "3. Cloudflare Access + mTLS: ⚠️ Not tested"
fi

echo ""
echo "=== Test Complete ==="
echo "Results saved to $OUTPUT_DIR/cloudflare_access_test_response.txt"
