# CORS Fixes Implementation Guide

This guide provides step-by-step instructions for implementing and testing the CORS fixes for the Divinci API.

## Step 1: Implement the Fixes in Cloudflare

Follow the instructions in the `manual-cloudflare-fixes.md` file to implement the necessary changes in the Cloudflare dashboard:

1. Update the mTLS rule to exclude OPTIONS requests
2. Create Transform Rules to add CORS headers to responses

## Step 2: Test the Fixes

After implementing the changes, use the `test-cors-fixes.sh` script to verify that the CORS issues have been resolved:

```bash
# Run the test script
./test-cors-fixes.sh --verbose
```

The script will:
1. Test OPTIONS preflight requests to api.stage.divinci.app
2. Test regular GET requests to api.stage.divinci.app
3. Check for the presence of CORS headers in the responses

## Step 3: Verify in the Browser

To verify that the CORS issues have been resolved in the browser:

1. Open Chrome or Firefox
2. Open the Developer Tools (F12)
3. Go to the Network tab
4. Navigate to https://chat.stage.divinci.app
5. Look for requests to api.stage.divinci.app
6. Verify that OPTIONS requests return 204 No Content with the appropriate CORS headers
7. Verify that regular requests return 200 OK with the appropriate CORS headers
8. Check the console for any CORS-related errors

## Troubleshooting

If you still encounter CORS issues after implementing the fixes:

### 1. Check the mTLS Rule

Verify that the mTLS rule has been properly updated to exclude OPTIONS requests:

```
(http.request.method ne "OPTIONS") and (
  (not cf.tls_client_auth.cert_verified and http.host eq "api.stage.divinci.app") or 
  ...
)
```

### 2. Check the Transform Rules

Verify that the Transform Rules have been properly created and are adding the necessary CORS headers:

- **OPTIONS Transform Rule**: Should add all CORS headers for OPTIONS requests
- **Regular Transform Rule**: Should add basic CORS headers for all responses

### 3. Check Rule Order

Ensure that the Transform Rules are being applied after the WAF rules. The order of execution in Cloudflare is:

1. WAF Rules
2. Transform Rules

### 4. Test with curl

Use curl to test the API directly:

```bash
# Test OPTIONS request
curl -v -X OPTIONS "https://api.stage.divinci.app/health" \
  -H "Origin: https://chat.stage.divinci.app" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type, Authorization"

# Test GET request
curl -v -X GET "https://api.stage.divinci.app/health" \
  -H "Origin: https://chat.stage.divinci.app"
```

### 5. Check Cloudflare Logs

Check the Cloudflare logs for any errors or issues:

1. Go to the Cloudflare dashboard
2. Go to Analytics > Logs
3. Look for requests to api.stage.divinci.app
4. Check for any errors or issues with the requests

## Next Steps

After successfully implementing and testing the CORS fixes:

1. Document the changes made
2. Update the team on the changes
3. Consider implementing automated tests to detect CORS issues in the future
4. Consider integrating the CORS tests into your CI/CD pipeline
