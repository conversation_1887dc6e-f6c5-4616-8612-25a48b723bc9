#!/bin/bash
# Script to test direct connection to Cloudflare-protected site by bypassing Access authentication

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

DOMAIN="chat.stage.divinci.app"

# Cloudflare Access credentials from staging test.env
CF_ID="f13e5d39e1997a5ddb674362e73199c5.access"
CF_SECRET="c2190b8f11da9b3e8244e18e0a29225f23e70784561ef244867bbc4a2f925752"

echo -e "${BLUE}=== Cloudflare Direct Access Test ===${NC}"
echo -e "${BLUE}Testing domain: ${DOMAIN}${NC}"
echo -e "${BLUE}Using Cloudflare Access credentials from staging environment${NC}"
echo

# First, let's check for common Cloudflare cookies and headers in previous API tests
echo -e "${BLUE}=== Checking for existing Cloudflare headers in API tests ===${NC}"
echo -e "Searching for CF-Authorization headers in project files..."

# Search for CF headers in API tests
CF_HEADERS=$(grep -r "CF-Authorization" /Users/<USER>/Documents/Divinci/server3/server --include="*.ts" --include="*.js" 2>/dev/null | head -5)

if [ -n "$CF_HEADERS" ]; then
  echo -e "${GREEN}Found Cloudflare authorization headers in the codebase:${NC}"
  echo "$CF_HEADERS"
  echo
else
  echo -e "${YELLOW}No Cloudflare authorization headers found in the codebase.${NC}"
  echo
fi

# Test with common Cloudflare bypass headers
echo -e "${BLUE}=== Testing with common Cloudflare Access bypass headers ===${NC}"
echo -e "Attempting to bypass Cloudflare Access to see the real status code..."

# Using the predefined access credentials
echo -e "\n${YELLOW}Using CF-Access-Client-Id and CF-Access-Client-Secret headers${NC}"
echo -e "CF_ID: $CF_ID"
echo -e "CF_SECRET: [HIDDEN]"

# Test with custom headers - verbose output redirected to file to avoid clutter
echo -e "Testing with CF-Access-Client-Id and CF-Access-Client-Secret headers..."
curl -s -v -H "CF-Access-Client-Id: $CF_ID" -H "CF-Access-Client-Secret: $CF_SECRET" https://$DOMAIN/ > /dev/null 2> curl_output_1.log
RESPONSE_CODE_1=$(grep -o "< HTTP/[0-9.]* [0-9]*" curl_output_1.log | tail -1 | awk '{print $3}')

if [ -n "$RESPONSE_CODE_1" ]; then
  echo -e "Response Code: ${RESPONSE_CODE_1}"
  
  if [[ "$RESPONSE_CODE_1" == "525" ]]; then
    echo -e "${RED}✓ Successfully bypassed Access and received Error 525${NC}"
    echo -e "${YELLOW}This confirms the SSL handshake failure between Cloudflare and your origin${NC}"
  elif [[ "$RESPONSE_CODE_1" == "200" ]]; then
    echo -e "${GREEN}✓ Successfully accessed the site with HTTP 200${NC}"
  elif [[ "$RESPONSE_CODE_1" == "302" || "$RESPONSE_CODE_1" == "301" ]]; then
    echo -e "${YELLOW}⚠ Still being redirected (HTTP ${RESPONSE_CODE_1})${NC}"
    REDIRECT=$(grep -i "< location:" curl_output_1.log | tail -1 | sed 's/< location: //')
    echo -e "Redirect location: ${REDIRECT}"
  else
    echo -e "${YELLOW}⚠ Received HTTP ${RESPONSE_CODE_1}${NC}"
  fi
else
  echo -e "${RED}✗ Could not determine response code${NC}"
fi

# Create a JavaScript test that uses a headless browser to check the real error
echo -e "\n${BLUE}=== Creating a Node.js E2E test script ===${NC}"
echo -e "This will create a script that uses Puppeteer to test the site with Cloudflare headers..."

# Create a Node.js script for E2E testing with Puppeteer
cat > cf-e2e-test.js << 'EOF'
const puppeteer = require('puppeteer');

// You'll need to install puppeteer first:
// npm install puppeteer

// Replace these with your actual values if you have them
const CF_ACCESS_CLIENT_ID = process.env.CF_ACCESS_CLIENT_ID || "YOUR_CLIENT_ID";
const CF_ACCESS_CLIENT_SECRET = process.env.CF_ACCESS_CLIENT_SECRET || "YOUR_CLIENT_SECRET";
const DOMAIN = "chat.stage.divinci.app";

async function testWithCloudflareHeaders() {
  console.log(`Testing domain: ${DOMAIN} with Cloudflare Access headers`);
  
  // Launch browser
  const browser = await puppeteer.launch({
    headless: "new",
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Set extra headers to bypass Cloudflare Access
    await page.setExtraHTTPHeaders({
      'CF-Access-Client-Id': CF_ACCESS_CLIENT_ID,
      'CF-Access-Client-Secret': CF_ACCESS_CLIENT_SECRET
    });
    
    // Enable request interception to modify headers
    await page.setRequestInterception(true);
    
    // Add headers to all requests
    page.on('request', request => {
      const headers = request.headers();
      headers['CF-Access-Client-Id'] = CF_ACCESS_CLIENT_ID;
      headers['CF-Access-Client-Secret'] = CF_ACCESS_CLIENT_SECRET;
      request.continue({ headers });
    });
    
    // Capture response status codes
    page.on('response', response => {
      console.log(`URL: ${response.url()}, Status: ${response.status()}`);
    });
    
    // Navigate to the domain
    console.log(`Navigating to https://${DOMAIN}/`);
    await page.goto(`https://${DOMAIN}/`, { waitUntil: 'networkidle2' });
    
    // Check for error messages in the page content
    const content = await page.content();
    if (content.includes('Error 525')) {
      console.log('Error 525 detected in page content: SSL handshake failed');
    } else if (content.includes('Error 526')) {
      console.log('Error 526 detected in page content: Invalid SSL certificate');
    }
    
    // Take a screenshot of what we're seeing
    await page.screenshot({ path: 'cloudflare-access-test.png' });
    console.log('Screenshot saved to cloudflare-access-test.png');
    
    // Get the page title
    const title = await page.title();
    console.log(`Page title: ${title}`);
    
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    await browser.close();
  }
}

// Check for JWT cookie value in our API tests
async function checkWithExistingJWTCookie() {
  console.log('\nChecking with JWT cookie from a session...');
  
  // Replace this with an actual JWT from your browser session if available
  const JWT_COOKIE = process.env.CF_JWT_COOKIE || "";
  
  if (!JWT_COOKIE) {
    console.log('No JWT cookie provided. Skipping this test.');
    return;
  }
  
  // Launch browser
  const browser = await puppeteer.launch({
    headless: "new",
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Set the JWT cookie before navigating
    await page.setCookie({
      name: 'CF_Authorization',
      value: JWT_COOKIE,
      domain: DOMAIN,
      path: '/',
    });
    
    // Navigate to the domain
    console.log(`Navigating to https://${DOMAIN}/ with JWT cookie`);
    await page.goto(`https://${DOMAIN}/`, { waitUntil: 'networkidle2' });
    
    // Take a screenshot
    await page.screenshot({ path: 'cloudflare-jwt-test.png' });
    console.log('Screenshot saved to cloudflare-jwt-test.png');
    
  } catch (error) {
    console.error('Error during JWT test:', error);
  } finally {
    await browser.close();
  }
}

// Run both tests
async function runTests() {
  await testWithCloudflareHeaders();
  await checkWithExistingJWTCookie();
  console.log('All tests completed');
}

runTests();
EOF

echo -e "${GREEN}Created E2E test script: cf-e2e-test.js${NC}"
echo -e "To run this script:"
echo -e "1. Make sure you have Node.js installed"
echo -e "2. Install Puppeteer: npm install puppeteer"
echo -e "3. Update the script with your actual Cloudflare Access credentials"
echo -e "4. Run: CF_ACCESS_CLIENT_ID=your_id CF_ACCESS_CLIENT_SECRET=your_secret node cf-e2e-test.js"
echo

# Create a simple curl test script to attempt different header combinations
cat > cf-curl-test.sh << 'EOF'
#!/bin/bash

DOMAIN="chat.stage.divinci.app"

# Set your Cloudflare Access credentials here or pass as environment variables
CF_ACCESS_CLIENT_ID=${CF_ACCESS_CLIENT_ID:-"your_client_id"}
CF_ACCESS_CLIENT_SECRET=${CF_ACCESS_CLIENT_SECRET:-"your_client_secret"}

echo "Testing $DOMAIN with Cloudflare Access bypass headers"

# Test 1: With CF-Access-Client-Id and CF-Access-Client-Secret
echo -e "\nTest 1: With CF-Access-Client headers"
curl -s -I -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" https://$DOMAIN/

# Test 2: With User-Agent that might bypass some protections
echo -e "\nTest 2: With custom User-Agent"
curl -s -I -H "User-Agent: Divinci-API-Test" -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" https://$DOMAIN/

# Test 3: With all headers from our API tests if you know them
echo -e "\nTest 3: With additional headers"
curl -s -I -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
           -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
           -H "X-Test-Bypass: true" \
           -H "X-Origin-URL: https://$DOMAIN/" \
           -H "x-auth0-access-token: test-token" \
           https://$DOMAIN/

echo -e "\nAll tests completed"
EOF

chmod +x cf-curl-test.sh
echo -e "${GREEN}Created curl test script: cf-curl-test.sh${NC}"
echo -e "To run this script:"
echo -e "CF_ACCESS_CLIENT_ID=your_id CF_ACCESS_CLIENT_SECRET=your_secret ./cf-curl-test.sh"
echo

# Create a README file with instructions
cat > cf-access-testing-readme.md << 'EOF'
# Cloudflare Access Testing

This directory contains scripts to test a Cloudflare Access-protected site and diagnose Error 525 issues.

## Scripts

1. **cf-curl-test.sh**: Simple curl tests with different header combinations to bypass Cloudflare Access
2. **cf-e2e-test.js**: Node.js script using Puppeteer for browser-based testing

## How to Use

### Finding Cloudflare Access Credentials

You'll need proper credentials to bypass Cloudflare Access:

1. Check your API test code for CF-Access-Client-Id and CF-Access-Client-Secret headers
2. Check for service tokens in your Cloudflare dashboard:
   - Go to Cloudflare Dashboard > Access > Service Auth > Service Tokens
   - Create a new token if needed or use an existing one

### Running the Tests

For the curl test:
```
CF_ACCESS_CLIENT_ID=your_id CF_ACCESS_CLIENT_SECRET=your_secret ./cf-curl-test.sh
```

For the Node.js test:
1. Install dependencies:
   ```
   npm install puppeteer
   ```
2. Run the test:
   ```
   CF_ACCESS_CLIENT_ID=your_id CF_ACCESS_CLIENT_SECRET=your_secret node cf-e2e-test.js
   ```

## Understanding Error 525

Error 525 means "SSL handshake failed" between Cloudflare and your origin server. This can happen because:

1. Cipher suite mismatch: Your server doesn't support the ciphers Cloudflare is trying to use
2. Protocol version mismatch: Your server doesn't support the TLS version Cloudflare is using
3. Certificate issues: While not a 526 (invalid cert), there could still be cert configuration issues
4. Server configuration: Your web server's SSL/TLS configuration is incompatible with Cloudflare

## Fixing Error 525

1. Ensure your origin server is using the Cloudflare Origin Certificate
2. Configure web server to support at least TLS 1.2 or higher
3. Add common cipher suites that Cloudflare supports
4. Check if the server is timing out during the handshake process
5. Test direct connection to origin server (if possible) to identify any SSL configuration issues
EOF

echo -e "${GREEN}Created README file: cf-access-testing-readme.md${NC}"
echo

echo -e "${BLUE}=== Final Recommendations for Error 525 ===${NC}"
echo -e "Error 525 (SSL handshake failure) is different from Error 526 (invalid certificate)."
echo -e "If you've confirmed that you're getting Error 525, check these specific issues:"
echo
echo -e "1. TLS Protocol Version:"
echo -e "   - Ensure your server supports TLS 1.2 or TLS 1.3"
echo -e "   - Check nginx config for: ssl_protocols TLSv1.2 TLSv1.3;"
echo
echo -e "2. Cipher Suite Compatibility:"
echo -e "   - Ensure your server supports cipher suites compatible with Cloudflare"
echo -e "   - Check nginx config for: ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:...';"
echo
echo -e "3. Certificate Chain:"
echo -e "   - Verify the complete certificate chain is present on your server"
echo -e "   - Some servers need both the certificate and CA certificate in the same file"
echo
echo -e "4. Server Configuration:"
echo -e "   - Check for SSL session timeout settings"
echo -e "   - Check for any SSL renegotiation settings that might cause issues"
echo
echo -e "5. Cloudflare Configuration:"
echo -e "   - Temporarily change to 'Full' mode (not Strict) to test if it resolves the issue"
echo -e "   - If it works in 'Full' mode, there's still a certificate configuration issue to fix"
echo

echo -e "${GREEN}Testing tools created successfully!${NC}"