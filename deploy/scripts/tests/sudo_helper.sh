#!/bin/bash
#
# sudo_helper.sh - Helper script for running commands with sudo and handling output files
#
# This script helps run commands with sudo while ensuring proper permissions for output files.
#

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to ensure directory exists and has proper permissions
ensure_directory() {
  local dir="$1"
  
  # Create directory if it doesn't exist
  if [ ! -d "$dir" ]; then
    mkdir -p "$dir"
  fi
  
  # Ensure directory has proper permissions
  chmod 755 "$dir"
}

# Function to run a command with sudo and handle output redirection
run_with_sudo() {
  local cmd="$1"
  local output_file="$2"
  local output_dir="$(dirname "$output_file")"
  
  # Ensure output directory exists and has proper permissions
  ensure_directory "$output_dir"
  
  # Create an empty output file with proper permissions if it doesn't exist
  if [ ! -f "$output_file" ]; then
    touch "$output_file"
  fi
  
  # Ensure output file has proper permissions
  chmod 666 "$output_file"
  
  # Run the command with sudo and redirect output to the file
  eval "sudo $cmd > '$output_file' 2>&1"
  
  # Return the exit code of the command
  return $?
}

# Function to run a command with sudo and tee output to a file
run_with_sudo_tee() {
  local cmd="$1"
  local output_file="$2"
  local output_dir="$(dirname "$output_file")"
  
  # Ensure output directory exists and has proper permissions
  ensure_directory "$output_dir"
  
  # Create an empty output file with proper permissions if it doesn't exist
  if [ ! -f "$output_file" ]; then
    touch "$output_file"
  fi
  
  # Ensure output file has proper permissions
  chmod 666 "$output_file"
  
  # Run the command with sudo and tee output to the file
  eval "sudo $cmd | tee '$output_file'"
  
  # Return the exit code of the command
  return ${PIPESTATUS[0]}
}

# Main function
main() {
  local mode="$1"
  local cmd="$2"
  local output_file="$3"
  
  case "$mode" in
    "run")
      run_with_sudo "$cmd" "$output_file"
      ;;
    "tee")
      run_with_sudo_tee "$cmd" "$output_file"
      ;;
    *)
      echo "Usage: $0 [run|tee] <command> <output_file>"
      exit 1
      ;;
  esac
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
