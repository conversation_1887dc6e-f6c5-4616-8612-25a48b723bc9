#!/bin/bash
# <PERSON>ript to implement a Cloudflare Worker for CORS handling
# This approach avoids issues with the Transform Rules API

# Source master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
CLOUDFLARE_ZONE_ID="9b26e2c415f36b0f656204133c8ab87c"
CLOUDFLARE_API_TOKEN="****************************************"
CLOUDFLARE_ACCOUNT_ID="14a6fa23390363382f378b5bd4a0f849"
OUTPUT_DIR="$SCRIPT_DIR/test-results/cors-worker"
mkdir -p "$OUTPUT_DIR"

echo "=== Implementing Cloudflare Worker for CORS Handling ==="
echo "Zone ID: $CLOUDFLARE_ZONE_ID"
echo "Account ID: $CLOUDFLARE_ACCOUNT_ID"
echo

# Step 1: Fix the existing firewall rule for OPTIONS requests
fix_options_firewall_rule() {
  echo "Step 1: Fixing Cloudflare firewall rule for OPTIONS requests..."
  
  # Get current firewall rules
  echo "📋 Retrieving current firewall rules..."
  curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/firewall/rules" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -H "Content-Type: application/json" > "$OUTPUT_DIR/firewall_rules.json"
  
  # Find the rule ID that matches OPTIONS
  OPTIONS_RULE_ID=$(jq -r '.result[] | select(.description == "Allow All OPTIONs") | .id' "$OUTPUT_DIR/firewall_rules.json")
  
  if [ -n "$OPTIONS_RULE_ID" ]; then
    echo "✅ Found OPTIONS rule with ID: $OPTIONS_RULE_ID"
    
    # Get the filter ID for this rule
    FILTER_ID=$(jq -r ".result[] | select(.id == \"$OPTIONS_RULE_ID\") | .filter.id" "$OUTPUT_DIR/firewall_rules.json")
    echo "✅ Found filter ID: $FILTER_ID"
    
    # Get current action of the rule
    CURRENT_ACTION=$(jq -r ".result[] | select(.id == \"$OPTIONS_RULE_ID\") | .action" "$OUTPUT_DIR/firewall_rules.json")
    echo "Current action: $CURRENT_ACTION"
    
    if [ "$CURRENT_ACTION" != "skip" ]; then
      echo "⚠️ Rule action is not 'skip'. Updating rule..."
      # Update the rule action to skip
      curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/firewall/rules/$OPTIONS_RULE_ID" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "{
          \"id\": \"$OPTIONS_RULE_ID\",
          \"action\": \"skip\",
          \"priority\": 1,
          \"paused\": false,
          \"description\": \"Allow All OPTIONs\",
          \"filter\": {
            \"id\": \"$FILTER_ID\"
          }
        }" > "$OUTPUT_DIR/update_rule_response.json"
      
      if jq -e '.success == true' "$OUTPUT_DIR/update_rule_response.json" > /dev/null; then
        echo "✅ Rule updated successfully to 'skip' action"
      else
        echo "❌ Failed to update rule"
        echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/update_rule_response.json")"
      fi
    else
      echo "✅ Rule already has 'skip' action"
    fi
  else
    # Rule doesn't exist, create it
    echo "⚠️ OPTIONS rule not found. Creating new rule..."
    
    # Create filter first
    curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/filters" \
      -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
      -H "Content-Type: application/json" \
      --data "[
        {
          \"expression\": \"(http.request.method eq \\\"OPTIONS\\\")\",
          \"paused\": false
        }
      ]" > "$OUTPUT_DIR/create_filter_response.json"
    
    if jq -e '.[0].success == true' "$OUTPUT_DIR/create_filter_response.json" > /dev/null; then
      echo "✅ Filter created successfully"
      FILTER_ID=$(jq -r '.[0].result.id' "$OUTPUT_DIR/create_filter_response.json")
      
      # Create rule using the filter
      curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/firewall/rules" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "[
          {
            \"filter\": {
              \"id\": \"$FILTER_ID\"
            },
            \"action\": \"skip\",
            \"priority\": 1,
            \"paused\": false,
            \"description\": \"Allow All OPTIONs\"
          }
        ]" > "$OUTPUT_DIR/create_rule_response.json"
      
      if jq -e '.[0].success == true' "$OUTPUT_DIR/create_rule_response.json" > /dev/null; then
        echo "✅ Rule created successfully with 'skip' action"
      else
        echo "❌ Failed to create rule"
        echo "Error: $(jq -r '.[0].errors[0].message' "$OUTPUT_DIR/create_rule_response.json")"
      fi
    else
      echo "❌ Failed to create filter"
      echo "Error: $(jq -r '.[0].errors[0].message' "$OUTPUT_DIR/create_filter_response.json")"
    fi
  fi
  
  echo "Step 1 complete"
  echo
}

# Step 2: Create a CORS worker script
create_cors_worker_script() {
  echo "Step 2: Creating Cloudflare Worker for CORS handling..."
  
  # Create worker script file
  cat > "$OUTPUT_DIR/cors-worker.js" << 'EOF'
// CORS Handler Worker for Divinci API
// This worker adds CORS headers to API responses

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',  // Will be replaced with the actual origin
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '86400',
};

function handleOptions(request) {
  // Get the origin from the request
  const origin = request.headers.get('Origin');
  
  // Update the corsHeaders with the actual origin if it exists
  if (origin) {
    corsHeaders['Access-Control-Allow-Origin'] = origin;
  }
  
  // Create a new response with CORS headers
  return new Response(null, {
    status: 204,
    headers: corsHeaders,
  });
}

function addCorsHeaders(response, request) {
  // Get the origin from the request
  const origin = request.headers.get('Origin');
  
  // Clone the response
  const newResponse = new Response(response.body, response);
  
  // Add CORS headers
  newResponse.headers.set('Access-Control-Allow-Origin', origin || '*');
  newResponse.headers.set('Access-Control-Allow-Credentials', 'true');
  
  return newResponse;
}

async function handleRequest(request) {
  // Handle CORS preflight requests
  if (request.method === 'OPTIONS') {
    return handleOptions(request);
  }
  
  try {
    // Forward the request to the origin
    let response = await fetch(request);
    
    // Add CORS headers to the response
    response = addCorsHeaders(response, request);
    
    return response;
  } catch (error) {
    // Handle errors
    return new Response(`Error: ${error.message}`, {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
        ...corsHeaders,
      },
    });
  }
}

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});
EOF
  
  echo "✅ CORS worker script created at $OUTPUT_DIR/cors-worker.js"
  echo "Step 2 complete"
  echo
}

# Step 3: Deploy the CORS worker
deploy_cors_worker() {
  echo "Step 3: Checking for existing CORS worker..."
  
  # Check if worker already exists
  curl -s -X GET "https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/cors-handler" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" > "$OUTPUT_DIR/check_worker_response.json"
  
  WORKER_EXISTS=$(jq -r '.success' "$OUTPUT_DIR/check_worker_response.json")
  
  echo "Step 3a: Uploading worker script..."
  # Upload worker script
  curl -s -X PUT "https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/cors-handler" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -F "metadata={\"body_part\":\"script\",\"bindings\":[{\"type\":\"plain_text\",\"name\":\"DEBUG\",\"text\":\"false\"}]};type=application/json" \
    -F "script=@$OUTPUT_DIR/cors-worker.js" > "$OUTPUT_DIR/upload_worker_response.json"
  
  if jq -e '.success == true' "$OUTPUT_DIR/upload_worker_response.json" > /dev/null; then
    echo "✅ Worker script uploaded successfully"
  else
    echo "❌ Failed to upload worker script"
    echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/upload_worker_response.json")"
    
    # Alternative approach for older Cloudflare API
    echo "Trying alternative upload method..."
    curl -s -X PUT "https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/cors-handler" \
      -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
      -H "Content-Type: application/javascript" \
      --data-binary "@$OUTPUT_DIR/cors-worker.js" > "$OUTPUT_DIR/upload_worker_alt_response.json"
    
    if jq -e '.success == true' "$OUTPUT_DIR/upload_worker_alt_response.json" > /dev/null; then
      echo "✅ Worker script uploaded successfully with alternative method"
    else
      echo "❌ Failed to upload worker script with alternative method"
      echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/upload_worker_alt_response.json")"
      return 1
    fi
  fi
  
  echo "Step 3b: Setting up worker route..."
  # Create a route for the worker
  curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/workers/routes" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -H "Content-Type: application/json" \
    --data "{
      \"pattern\": \"*api.stage.divinci.app/*\",
      \"script\": \"cors-handler\"
    }" > "$OUTPUT_DIR/create_route_response.json"
  
  if jq -e '.success == true' "$OUTPUT_DIR/create_route_response.json" > /dev/null; then
    echo "✅ Worker route created successfully"
  else
    # Check if the route already exists
    ERROR_MESSAGE=$(jq -r '.errors[0].message' "$OUTPUT_DIR/create_route_response.json")
    if [[ "$ERROR_MESSAGE" == *"already exists"* ]]; then
      echo "⚠️ Worker route already exists"
      
      # Get the existing routes
      curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/workers/routes" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" > "$OUTPUT_DIR/get_routes_response.json"
      
      # Find the route ID for our pattern
      ROUTE_ID=$(jq -r '.result[] | select(.pattern == "*api.stage.divinci.app/*") | .id' "$OUTPUT_DIR/get_routes_response.json")
      
      if [ -n "$ROUTE_ID" ]; then
        echo "Found existing route with ID: $ROUTE_ID"
        
        # Update the existing route
        curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/workers/routes/$ROUTE_ID" \
          -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
          -H "Content-Type: application/json" \
          --data "{
            \"pattern\": \"*api.stage.divinci.app/*\",
            \"script\": \"cors-handler\"
          }" > "$OUTPUT_DIR/update_route_response.json"
        
        if jq -e '.success == true' "$OUTPUT_DIR/update_route_response.json" > /dev/null; then
          echo "✅ Worker route updated successfully"
        else
          echo "❌ Failed to update worker route"
          echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/update_route_response.json")"
        fi
      else
        echo "❌ Could not find existing route for pattern '*api.stage.divinci.app/*'"
      fi
    else
      echo "❌ Failed to create worker route"
      echo "Error: $ERROR_MESSAGE"
    fi
  fi
  
  echo "Step 3 complete"
  echo
}

# Step 4: Verify worker deployment
verify_worker_deployment() {
  echo "Step 4: Verifying worker deployment..."
  
  # Check if worker is deployed
  curl -s -X GET "https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/cors-handler" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" > "$OUTPUT_DIR/verify_worker_response.json"
  
  if jq -e '.success == true' "$OUTPUT_DIR/verify_worker_response.json" > /dev/null; then
    echo "✅ CORS worker is deployed"
    WORKER_MODIFIED=$(jq -r '.result.modified_on' "$OUTPUT_DIR/verify_worker_response.json")
    echo "Last modified: $WORKER_MODIFIED"
  else
    echo "❌ CORS worker is not deployed"
    echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/verify_worker_response.json")"
  fi
  
  # Check worker routes
  curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/workers/routes" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" > "$OUTPUT_DIR/verify_routes_response.json"
  
  if jq -e '.success == true' "$OUTPUT_DIR/verify_routes_response.json" > /dev/null; then
    echo "✅ Worker routes retrieved successfully"
    ROUTES=$(jq -r '.result[] | select(.script == "cors-handler") | .pattern' "$OUTPUT_DIR/verify_routes_response.json")
    
    if [ -n "$ROUTES" ]; then
      echo "Worker is active on the following routes:"
      echo "$ROUTES" | sed 's/^/  - /'
    else
      echo "❌ No routes found for CORS worker"
    fi
  else
    echo "❌ Failed to retrieve worker routes"
    echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/verify_routes_response.json")"
  fi
  
  echo "Step 4 complete"
  echo
}

# Step 5: Test the CORS worker
test_cors_worker() {
  echo "Step 5: Testing CORS worker..."
  
  # Test OPTIONS request
  echo "Testing OPTIONS request..."
  curl -v -X OPTIONS "https://api.stage.divinci.app/health" \
    -H "Origin: https://chat.stage.divinci.app" \
    -H "Access-Control-Request-Method: GET" \
    -H "Access-Control-Request-Headers: Content-Type, Authorization" \
    -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
    -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
    > "$OUTPUT_DIR/test_options_output.txt" 2>&1
  
  # Check the response
  OPTIONS_STATUS=$(grep -i "< HTTP/2" "$OUTPUT_DIR/test_options_output.txt" | awk '{print $3}')
  echo "OPTIONS status code: $OPTIONS_STATUS"
  
  CORS_HEADERS=$(grep -i "access-control-" "$OUTPUT_DIR/test_options_output.txt")
  
  if [ -n "$CORS_HEADERS" ]; then
    echo "✅ CORS headers found in OPTIONS response:"
    echo "$CORS_HEADERS" | sed 's/^/  /'
  else
    echo "❌ No CORS headers found in OPTIONS response"
  fi
  
  # Test GET request
  echo "Testing GET request..."
  curl -v -X GET "https://api.stage.divinci.app/health" \
    -H "Origin: https://chat.stage.divinci.app" \
    -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
    -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
    > "$OUTPUT_DIR/test_get_output.txt" 2>&1
  
  # Check the response
  GET_STATUS=$(grep -i "< HTTP/2" "$OUTPUT_DIR/test_get_output.txt" | awk '{print $3}')
  echo "GET status code: $GET_STATUS"
  
  CORS_HEADERS=$(grep -i "access-control-" "$OUTPUT_DIR/test_get_output.txt")
  
  if [ -n "$CORS_HEADERS" ]; then
    echo "✅ CORS headers found in GET response:"
    echo "$CORS_HEADERS" | sed 's/^/  /'
  else
    echo "❌ No CORS headers found in GET response"
  fi
  
  echo "Step 5 complete"
  echo
}

# Copy the CORS worker to the Cloudflare worker directory
copy_cors_worker() {
  echo "Step 6: Copying CORS worker to project files..."
  
  # Create directory if it doesn't exist
  mkdir -p "$REPO_ROOT/workers/cors-handler"
  
  # Copy the worker script
  cp "$OUTPUT_DIR/cors-worker.js" "$REPO_ROOT/cors-worker-updated.js"
  
  echo "✅ CORS worker copied to $REPO_ROOT/cors-worker-updated.js"
  
  # Create a README file explaining the worker
  cat > "$REPO_ROOT/workers/cors-handler/README.md" << 'EOF'
# CORS Handler Worker

This Cloudflare Worker adds CORS headers to API responses to allow cross-origin requests from authorized domains.

## Features

- Handles OPTIONS preflight requests with appropriate CORS headers
- Adds CORS headers to all API responses
- Preserves origin-specific CORS headers
- Works alongside mTLS authentication

## Deployment

The worker is deployed to the Cloudflare account and configured to handle requests to api.stage.divinci.app.

To update the worker:

1. Edit the `cors-worker.js` file
2. Run the deployment script:
   ```bash
   ./deploy/scripts/tests/fix-cloudflare-cors-worker.sh
   ```

## Implementation Details

The worker performs the following tasks:

1. For OPTIONS requests:
   - Responds with a 204 No Content status
   - Adds all necessary CORS headers
   - Uses the Origin from the request as the Access-Control-Allow-Origin

2. For all other requests:
   - Forwards the request to the origin
   - Adds basic CORS headers to the response
   - Preserves all other headers and status codes
EOF
  
  echo "✅ README file created at $REPO_ROOT/workers/cors-handler/README.md"
  echo "Step 6 complete"
  echo
}

# Main execution
echo "Starting implementation of Cloudflare Worker for CORS handling..."
fix_options_firewall_rule
create_cors_worker_script
deploy_cors_worker
verify_worker_deployment
test_cors_worker
copy_cors_worker

echo "=== CORS Worker Implementation Complete ==="
echo "The CORS worker has been implemented and deployed to Cloudflare."
echo "Results and logs saved to $OUTPUT_DIR/"
echo
echo "Next steps:"
echo "1. Test the API with CORS requests from your frontend application"
echo "2. Monitor the logs for any issues"
echo "3. If needed, update the worker script at $REPO_ROOT/cors-worker-updated.js and redeploy"