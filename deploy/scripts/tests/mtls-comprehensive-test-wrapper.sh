#!/bin/bash
# Wrapper script for mtls-comprehensive-test.sh
# This script redirects to the mtls-framework for better organization

echo "⚠️ This script is deprecated and will be removed in a future release."
echo "Please use the mtls-framework instead:"
echo "  ./mtls-framework/run-tests.sh [options]"
echo ""
echo "Redirecting to mtls-framework..."
echo ""

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Parse arguments
DOMAIN=${1:-"api.stage.divinci.app"}
ENVIRONMENT=${2:-"staging"}

# Run the test using the framework
"$SCRIPT_DIR/mtls-framework/run-tests.sh" \
  --domain "$DOMAIN" \
  --environment "$ENVIRONMENT" \
  --test "all"
