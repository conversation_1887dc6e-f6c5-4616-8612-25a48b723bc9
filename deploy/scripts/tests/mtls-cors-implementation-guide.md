# mTLS and CORS Implementation Guide

This guide provides a comprehensive approach to implementing and fixing mTLS and CORS issues in the Divinci API.

## Problem Analysis

Based on our testing, we've identified several issues:

1. **mTLS Configuration Issues**:
   - `ENABLE_MTLS` environment variable inconsistently set to "1" instead of "true"
   - Certificate mounts correctly configured but not always properly referenced
   - Cloudflare firewall rules blocking OPTIONS requests that should be exempt from mTLS

2. **CORS Issues**:
   - OPTIONS preflight requests returning 503 Service Unavailable
   - Missing CORS headers in responses
   - Failed attempts to use Cloudflare Transform Rules to add CORS headers

3. **Server Issues**:
   - 500 Server Error responses from some endpoints
   - Inconsistent handling of OPTIONS requests

## Implementation Strategy

Our implementation strategy uses a multi-layered approach:

1. **Fix Server Configuration**:
   - Update `ENABLE_MTLS` to use "true" consistently
   - Ensure proper certificate mounts

2. **Fix Cloudflare Rules**:
   - Update firewall rules to exempt OPTIONS requests from mTLS checks
   - Ensure correct SSL mode (strict)

3. **Implement CORS Worker**:
   - Deploy a Cloudflare Worker to handle CORS headers
   - This approach is more reliable than Transform Rules

## Implementation Steps

### Step 1: Fix Server Configuration

Run the `fix-mtls-enable-value.sh` script to update the `ENABLE_MTLS` environment variable:

```bash
./fix-mtls-enable-value.sh
```

This script:
- Checks all Cloud Run services for `ENABLE_MTLS="1"`
- Updates the value to `ENABLE_MTLS="true"`
- Applies the changes to the services

### Step 2: Implement Cloudflare Worker for CORS

Run the `fix-cloudflare-cors-worker.sh` script to deploy a CORS Worker:

```bash
./fix-cloudflare-cors-worker.sh
```

This script:
1. Updates the Cloudflare firewall rule for OPTIONS requests
2. Creates a CORS Worker script
3. Deploys the worker to Cloudflare
4. Sets up a route for the worker
5. Tests the worker functionality

#### Worker Architecture

The CORS Worker functions as follows:

1. **For OPTIONS Requests**:
   - Intercepts the request before it reaches the origin server
   - Returns a 204 No Content response with appropriate CORS headers
   - Uses the Origin from the request to set Access-Control-Allow-Origin

2. **For Regular Requests**:
   - Forwards the request to the origin server
   - Adds CORS headers to the response
   - Preserves all other headers and response data

This approach has several advantages:
- No need to modify the origin server
- Works alongside mTLS authentication
- Can handle any domain or endpoint pattern
- More reliable than Transform Rules

### Step 3: Test the Implementation

Run the `test-cors-worker.sh` script to verify the implementation:

```bash
./test-cors-worker.sh
```

This script:
1. Tests Cloudflare configuration
2. Tests certificate configuration
3. Tests CORS preflight requests
4. Tests regular HTTP requests with CORS
5. Generates a summary report

## Troubleshooting

### Common Issues and Solutions

1. **Worker Deployment Failures**:
   - Check Cloudflare API token permissions
   - Verify account and zone IDs
   - Check for existing routes with the same pattern

2. **CORS Headers Missing**:
   - Verify the worker route matches the API domain
   - Check if the worker is assigned to the correct zone
   - Ensure the worker script has the correct CORS headers

3. **OPTIONS Requests Still Failing**:
   - Verify the firewall rule is correctly configured to skip OPTIONS requests
   - Check if there are other rules blocking OPTIONS requests
   - Verify the mTLS rule excludes OPTIONS requests

4. **Server Errors**:
   - Check server logs for errors
   - Verify the server is correctly handling OPTIONS requests
   - Check if the server is correctly configured for mTLS

### Verifying the Fix

To verify that the CORS and mTLS issues are fixed:

1. **Browser Test**:
   - Open the browser developer console
   - Make a request from `chat.stage.divinci.app` to `api.stage.divinci.app`
   - Verify no CORS errors are shown

2. **curl Test**:
   ```bash
   # Test OPTIONS request
   curl -v -X OPTIONS "https://api.stage.divinci.app/health" \
     -H "Origin: https://chat.stage.divinci.app" \
     -H "Access-Control-Request-Method: GET" \
     -H "CF-Access-Client-Id: YOUR_CLIENT_ID" \
     -H "CF-Access-Client-Secret: YOUR_CLIENT_SECRET"
   
   # Test GET request
   curl -v -X GET "https://api.stage.divinci.app/health" \
     -H "Origin: https://chat.stage.divinci.app" \
     -H "CF-Access-Client-Id: YOUR_CLIENT_ID" \
     -H "CF-Access-Client-Secret: YOUR_CLIENT_SECRET"
   ```

3. **Automated Test**:
   ```bash
   ./test-cors-worker.sh
   ```

## Maintenance and Updates

### Updating the CORS Worker

If you need to update the CORS Worker:

1. Edit the `cors-worker.js` file
2. Run the deployment script again:
   ```bash
   ./fix-cloudflare-cors-worker.sh
   ```

### Monitoring

Monitor the following:

1. **Cloudflare Logs**:
   - Check for errors related to the worker
   - Monitor for requests being blocked by firewall rules

2. **Server Logs**:
   - Check for errors related to OPTIONS requests
   - Monitor for mTLS authentication failures

3. **Frontend Application**:
   - Monitor for CORS errors in the console
   - Check browser network tab for CORS preflight requests

## Security Considerations

### mTLS Security

- Ensure client certificates are properly secured
- Rotate certificates regularly
- Monitor for unauthorized access attempts

### CORS Security

- Avoid using `Access-Control-Allow-Origin: *`
- Only allow trusted origins
- Limit allowed HTTP methods and headers

## Conclusion

This implementation provides a robust solution for mTLS and CORS issues by:

1. Ensuring consistent mTLS configuration across all services
2. Implementing a Cloudflare Worker to handle CORS headers
3. Configuring Cloudflare firewall rules to allow OPTIONS requests without mTLS

By following this guide, you should have a working implementation of mTLS and CORS that allows secure cross-origin requests to your API.