# Cloudflare Access Testing

This directory contains scripts to test a Cloudflare Access-protected site and diagnose Error 525 issues.

## Scripts

1. **cf-curl-test.sh**: Simple curl tests with different header combinations to bypass Cloudflare Access
2. **cf-e2e-test.js**: Node.js script using P<PERSON>peteer for browser-based testing

## How to Use

### Finding Cloudflare Access Credentials

You'll need proper credentials to bypass Cloudflare Access:

1. Check your API test code for CF-Access-Client-Id and CF-Access-Client-Secret headers
2. Check for service tokens in your Cloudflare dashboard:
   - Go to Cloudflare Dashboard > Access > Service Auth > Service Tokens
   - Create a new token if needed or use an existing one

### Running the Tests

For the curl test:
```
CF_ACCESS_CLIENT_ID=your_id CF_ACCESS_CLIENT_SECRET=your_secret ./cf-curl-test.sh
```

For the Node.js test:
1. Install dependencies:
   ```
   npm install puppeteer
   ```
2. Run the test:
   ```
   CF_ACCESS_CLIENT_ID=your_id CF_ACCESS_CLIENT_SECRET=your_secret node cf-e2e-test.js
   ```

## Understanding Error 525

Error 525 means "SSL handshake failed" between Cloudflare and your origin server. This can happen because:

1. Cipher suite mismatch: Your server doesn't support the ciphers Cloudflare is trying to use
2. Protocol version mismatch: Your server doesn't support the TLS version Cloudflare is using
3. Certificate issues: While not a 526 (invalid cert), there could still be cert configuration issues
4. Server configuration: Your web server's SSL/TLS configuration is incompatible with Cloudflare

## Fixing Error 525

1. Ensure your origin server is using the Cloudflare Origin Certificate
2. Configure web server to support at least TLS 1.2 or higher
3. Add common cipher suites that Cloudflare supports
4. Check if the server is timing out during the handshake process
5. Test direct connection to origin server (if possible) to identify any SSL configuration issues
