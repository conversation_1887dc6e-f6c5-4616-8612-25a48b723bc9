#!/bin/bash
# Script to check service logs for OPTIONS request errors

# Source master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
PROJECT_ID="openai-api-4375643"
REGION="us-central1"
SERVICE_NAME=${1:-"divinci-staging-server-api"}
OUTPUT_DIR="$SCRIPT_DIR/test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== Checking Service Logs for OPTIONS Request Errors ==="
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo "Service Name: $SERVICE_NAME"
echo

# Create log filter for OPTIONS errors
LOG_FILTER="resource.type=cloud_run_revision AND resource.labels.service_name=$SERVICE_NAME AND severity>=WARNING AND (textPayload:OPTIONS OR textPayload:CORS OR jsonPayload.message:OPTIONS OR jsonPayload.message:CORS)"

echo "📋 Retrieving logs for OPTIONS and CORS errors..."
gcloud logging read "$LOG_FILTER" --project=$PROJECT_ID --format="json" --limit=50 > "$OUTPUT_DIR/options_errors.json"

# Check if logs were found
if [ -s "$OUTPUT_DIR/options_errors.json" ]; then
  echo "✅ Logs retrieved and saved to $OUTPUT_DIR/options_errors.json"
  
  # Extract and display relevant error messages
  echo "📋 Extracting relevant error messages..."
  jq -r '.[] | "\n----------\nTimestamp: \(.timestamp)\nSeverity: \(.severity)\nMessage: \(if .textPayload then .textPayload else .jsonPayload.message end)"' "$OUTPUT_DIR/options_errors.json" > "$OUTPUT_DIR/options_errors_summary.txt"
  
  echo "📋 Summary of OPTIONS/CORS errors:"
  cat "$OUTPUT_DIR/options_errors_summary.txt"
else
  echo "ℹ️ No OPTIONS or CORS errors found in the logs"
fi

# Now get successful OPTIONS requests to see if there are any
LOG_FILTER="resource.type=cloud_run_revision AND resource.labels.service_name=$SERVICE_NAME AND textPayload:OPTIONS AND severity<=INFO"

echo "📋 Retrieving logs for successful OPTIONS requests..."
gcloud logging read "$LOG_FILTER" --project=$PROJECT_ID --format="json" --limit=20 > "$OUTPUT_DIR/options_success.json"

# Check if logs were found
if [ -s "$OUTPUT_DIR/options_success.json" ]; then
  echo "✅ Successful OPTIONS requests found and saved to $OUTPUT_DIR/options_success.json"
  
  # Extract and display relevant success messages
  echo "📋 Extracting relevant success messages..."
  jq -r '.[] | "\n----------\nTimestamp: \(.timestamp)\nSeverity: \(.severity)\nMessage: \(if .textPayload then .textPayload else .jsonPayload.message end)"' "$OUTPUT_DIR/options_success.json" > "$OUTPUT_DIR/options_success_summary.txt"
  
  echo "📋 Summary of successful OPTIONS requests:"
  cat "$OUTPUT_DIR/options_success_summary.txt"
else
  echo "⚠️ No successful OPTIONS requests found in the logs"
fi

echo "=== Log Analysis Complete ==="