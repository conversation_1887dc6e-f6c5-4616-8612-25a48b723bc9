#!/bin/bash
# fix-certificate-mounts.sh
# Script to fix certificate mount issues in GCP Cloud Run services

set -e

# Configuration
SERVICE_NAME=${1:-""}
PROJECT_ID=${2:-$(gcloud config get-value project)}
REGION=${3:-"us-central1"}
OUTPUT_DIR="./test-results"
mkdir -p "$OUTPUT_DIR"

# Define expected mount paths and secret names
CERT_MOUNT_PATH="/etc/ssl/certs"
KEY_MOUNT_PATH="/etc/ssl/private"
CERT_SECRET_NAME="server-crt"
KEY_SECRET_NAME="server-key"

# Print usage information if no service name is provided
if [ -z "$SERVICE_NAME" ]; then
  echo "Usage: $0 <service-name> [project-id] [region]"
  echo
  echo "Example: $0 divinci-api-webhook"
  echo
  echo "Running in analysis mode (no service specified)..."
  echo "Use check-certificate-mounts.sh to view all services with mount issues."
  
  if [ -f "./deploy/scripts/tests/check-certificate-mounts.sh" ]; then
    chmod +x ./deploy/scripts/tests/check-certificate-mounts.sh
    ./deploy/scripts/tests/check-certificate-mounts.sh "$PROJECT_ID" "$REGION"
    exit 0
  else
    echo "❌ check-certificate-mounts.sh script not found."
    echo "Please run this script from the project root directory."
    exit 1
  fi
fi

echo "=== Fixing Certificate Mounts for GCP Cloud Run Service ==="
echo "Service Name: $SERVICE_NAME"
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo

# Check if the service exists
echo "🔍 Checking if service exists..."
if ! gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(metadata.name)" &>/dev/null; then
  echo "❌ Service '$SERVICE_NAME' not found in region $REGION"
  echo "Available services in region $REGION:"
  gcloud run services list --region="$REGION" --format="table(metadata.name,status.url,status.conditions.status)" | grep -v "READY"
  exit 1
fi

echo "✅ Service '$SERVICE_NAME' found"

# Get the current service YAML
echo "📥 Retrieving current service configuration..."
TEMP_YAML=$(mktemp)
gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format=yaml > "$TEMP_YAML"

# Check for existing volume mounts
CERT_MOUNT=$(grep -A 2 "mountPath: $CERT_MOUNT_PATH" "$TEMP_YAML" || echo "")
KEY_MOUNT=$(grep -A 2 "mountPath: $KEY_MOUNT_PATH" "$TEMP_YAML" || echo "")

# Check for existing volumes
CERT_VOLUME=$(grep -A 5 "name: mtls-certs" "$TEMP_YAML" || echo "")
KEY_VOLUME=$(grep -A 5 "name: mtls-key" "$TEMP_YAML" || echo "")

# Check for existing ENABLE_MTLS environment variable
ENABLE_MTLS=$(grep -A 2 "name: ENABLE_MTLS" "$TEMP_YAML" || echo "")

# Create a modified YAML file
echo "✏️ Creating modified service configuration..."
MODIFIED_YAML=$(mktemp)
cp "$TEMP_YAML" "$MODIFIED_YAML"

# Check if we need to add or modify volumeMounts
if [ -z "$CERT_MOUNT" ] || [ -z "$KEY_MOUNT" ]; then
  echo "🔧 Adding or updating volume mounts..."
  
  # First, check if volumeMounts section exists
  if ! grep -q "volumeMounts:" "$MODIFIED_YAML"; then
    # Add volumeMounts section after resources section
    sed -i '/resources:/,/limits:/{/limits:/a\
          volumeMounts:\
            - name: mtls-certs\
              mountPath: /etc/ssl/certs\
            - name: mtls-key\
              mountPath: /etc/ssl/private
    }' "$MODIFIED_YAML"
  else
    # volumeMounts section exists, add/update the specific mounts
    if [ -z "$CERT_MOUNT" ]; then
      sed -i '/volumeMounts:/a\
            - name: mtls-certs\
              mountPath: /etc/ssl/certs
      ' "$MODIFIED_YAML"
    fi
    
    if [ -z "$KEY_MOUNT" ]; then
      sed -i '/volumeMounts:/a\
            - name: mtls-key\
              mountPath: /etc/ssl/private
      ' "$MODIFIED_YAML"
    fi
  fi
else
  echo "✅ Volume mounts already exist"
fi

# Check if we need to add or modify volumes
if [ -z "$CERT_VOLUME" ] || [ -z "$KEY_VOLUME" ]; then
  echo "🔧 Adding or updating volumes..."
  
  # First, check if volumes section exists at spec.template.spec level
  if ! grep -q "      volumes:" "$MODIFIED_YAML"; then
    # Add volumes section after containers section ends
    sed -i '/containers:/,/.*:/{ /.*:/{x;/containers:/!{x;s/$/\n      volumes:\n        - name: mtls-certs\n          secret:\n            secretName: server-crt\n            items:\n              - key: latest\n                path: server.crt\n        - name: mtls-key\n          secret:\n            secretName: server-key\n            items:\n              - key: latest\n                path: server.key/;}}}' "$MODIFIED_YAML"
  else
    # volumes section exists, add/update the specific volumes
    if [ -z "$CERT_VOLUME" ]; then
      sed -i '/      volumes:/a\
        - name: mtls-certs\
          secret:\
            secretName: server-crt\
            items:\
              - key: latest\
                path: server.crt
      ' "$MODIFIED_YAML"
    fi
    
    if [ -z "$KEY_VOLUME" ]; then
      sed -i '/      volumes:/a\
        - name: mtls-key\
          secret:\
            secretName: server-key\
            items:\
              - key: latest\
                path: server.key
      ' "$MODIFIED_YAML"
    fi
  fi
else
  echo "✅ Volumes already exist"
fi

# Check if we need to add or modify ENABLE_MTLS environment variable
if [ -z "$ENABLE_MTLS" ]; then
  echo "🔧 Adding ENABLE_MTLS environment variable..."
  
  # Find the env section and add the variable
  if grep -q "          env:" "$MODIFIED_YAML"; then
    # env section exists, add the variable
    sed -i '/          env:/a\
            - name: ENABLE_MTLS\
              value: "true"
    ' "$MODIFIED_YAML"
  else
    # No env section exists, add it after ports section
    sed -i '/          ports:/,/containerPort:/{/containerPort:/a\
          env:\
            - name: ENABLE_MTLS\
              value: "true"
    }' "$MODIFIED_YAML"
  fi
else
  echo "✅ ENABLE_MTLS environment variable already exists"
  
  # Check if it's set to "true"
  if ! grep -A 1 "name: ENABLE_MTLS" "$MODIFIED_YAML" | grep -q 'value: "true"'; then
    echo "🔧 Updating ENABLE_MTLS to true..."
    sed -i '/name: ENABLE_MTLS/,/value:/{s/value:.*/value: "true"/}' "$MODIFIED_YAML"
  fi
fi

# Check if any changes were made
if diff -q "$TEMP_YAML" "$MODIFIED_YAML" >/dev/null; then
  echo "✅ No changes needed. Service already has proper certificate mounts."
  rm "$TEMP_YAML" "$MODIFIED_YAML"
  exit 0
fi

# Save the modified YAML for reference and deployment
FINAL_YAML="$OUTPUT_DIR/${SERVICE_NAME}_fixed.yaml"
cp "$MODIFIED_YAML" "$FINAL_YAML"

echo "📄 Modified YAML saved to $FINAL_YAML"

# Verify the secrets exist in Secret Manager before attempting deployment
echo "🔍 Verifying that required secrets exist in Secret Manager..."
CERT_SECRET_EXISTS=false
KEY_SECRET_EXISTS=false

if gcloud secrets describe $CERT_SECRET_NAME &>/dev/null; then
  echo "✅ Secret '$CERT_SECRET_NAME' exists in Secret Manager"
  CERT_SECRET_EXISTS=true
else
  echo "❌ Secret '$CERT_SECRET_NAME' does not exist in Secret Manager"
fi

if gcloud secrets describe $KEY_SECRET_NAME &>/dev/null; then
  echo "✅ Secret '$KEY_SECRET_NAME' exists in Secret Manager"
  KEY_SECRET_EXISTS=true
else
  echo "❌ Secret '$KEY_SECRET_NAME' does not exist in Secret Manager"
fi

# Prompt for deployment if in interactive mode
echo
echo "Review the changes to be made:"
diff -u "$TEMP_YAML" "$MODIFIED_YAML" || true

if [ "$CERT_SECRET_EXISTS" = true ] && [ "$KEY_SECRET_EXISTS" = true ]; then
  echo
  read -p "Do you want to apply these changes to the service? (y/n): " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 Deploying changes to service '$SERVICE_NAME'..."
    if gcloud run services replace "$FINAL_YAML" --region="$REGION"; then
      echo "✅ Service successfully updated with proper certificate mounts"
    else
      echo "❌ Failed to update service. Please check the error message above."
      exit 1
    fi
  else
    echo "❌ Deployment cancelled by user"
  fi
else
  echo "❌ Cannot deploy changes because one or more required secrets do not exist."
  echo "   Please create the missing secrets before deploying the changes."
  echo "   You can run the following scripts to create the required secrets:"
  echo "   - ./deploy/scripts/create-server-cert-secret.sh"
  echo "   - ./deploy/scripts/create-mtls-secret.sh"
  exit 1
fi

# Clean up temporary files
rm "$TEMP_YAML" "$MODIFIED_YAML"

echo
echo "=== Certificate Mount Fix Complete ==="
echo "The fix has been applied to service '$SERVICE_NAME'"
echo

exit 0