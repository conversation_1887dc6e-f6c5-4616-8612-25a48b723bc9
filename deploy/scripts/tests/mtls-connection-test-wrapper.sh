#!/bin/bash
# Wrapper script for mtls-connection-test.sh
# This script redirects to the mtls-framework for better organization

echo "⚠️ This script is deprecated and will be removed in a future release."
echo "Please use the mtls-framework instead:"
echo "  ./mtls-framework/run-tests.sh --test mtls [options]"
echo ""
echo "Redirecting to mtls-framework..."
echo ""

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Parse arguments
DOMAIN=${1:-"api.stage.divinci.app"}
ORIGIN_IP=${2:-"$DOMAIN"}
PORT=${3:-"443"}
ENVIRONMENT=${4:-"staging"}

# Run the test using the framework
"$SCRIPT_DIR/mtls-framework/run-tests.sh" \
  --domain "$DOMAIN" \
  --environment "$ENVIRONMENT" \
  --port "$PORT" \
  --origin-ip "$ORIGIN_IP" \
  --test "mtls"
