#!/bin/bash
# CORS mTLS Test with Cloudflare Access Headers
# This script tests CORS preflight requests with mTLS and Cloudflare Access headers

# Source master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
DOMAIN="api.stage.divinci.app"
ORIGIN="https://chat.stage.divinci.app"
ENDPOINT="/ai-chat/trending"
REPO_ROOT="$(get_repo_root)"
CLIENT_CERT="$REPO_ROOT/private-keys/staging/certs/mtls/client.crt"
CLIENT_KEY="$REPO_ROOT/private-keys/staging/certs/mtls/client.key"
OUTPUT_DIR="$SCRIPT_DIR/test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== CORS mTLS Test with Cloudflare Access Headers ==="
echo "API Domain: $DOMAIN"
echo "Origin: $ORIGIN"
echo "Endpoint: $ENDPOINT"
echo "Client Certificate: $CLIENT_CERT"
echo "Client Key: $CLIENT_KEY"
echo

# Check if client certificate and key exist
if [ ! -f "$CLIENT_CERT" ] || [ ! -f "$CLIENT_KEY" ]; then
  echo "❌ Client certificate or key not found"
  exit 1
fi

# Test 1: OPTIONS request (CORS preflight) with mTLS and Cloudflare Access headers
echo "Test 1: OPTIONS request (CORS preflight) with mTLS and Cloudflare Access headers"
echo "Command:"
echo "curl -v -X OPTIONS https://$DOMAIN$ENDPOINT \\"
echo "  --cert $CLIENT_CERT \\"
echo "  --key $CLIENT_KEY \\"
echo "  -H \"Origin: $ORIGIN\" \\"
echo "  -H \"Access-Control-Request-Method: GET\" \\"
echo "  -H \"CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID\" \\"
echo "  -H \"CF-Access-Client-Secret: [HIDDEN]\" \\"

# Execute the command
curl -v -X OPTIONS "https://$DOMAIN$ENDPOINT" \
  --cert "$CLIENT_CERT" \
  --key "$CLIENT_KEY" \
  -H "Origin: $ORIGIN" \
  -H "Access-Control-Request-Method: GET" \
  -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
  -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
  > "$OUTPUT_DIR/cors_preflight_output.txt" 2>&1

# Display the output
echo
echo "Response:"
cat "$OUTPUT_DIR/cors_preflight_output.txt"
echo

# Test 2: GET request with mTLS and Cloudflare Access headers
echo "Test 2: GET request with mTLS and Cloudflare Access headers"
echo "Command:"
echo "curl -v https://$DOMAIN$ENDPOINT \\"
echo "  --cert $CLIENT_CERT \\"
echo "  --key $CLIENT_KEY \\"
echo "  -H \"Origin: $ORIGIN\" \\"
echo "  -H \"CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID\" \\"
echo "  -H \"CF-Access-Client-Secret: [HIDDEN]\" \\"

# Execute the command
curl -v "https://$DOMAIN$ENDPOINT" \
  --cert "$CLIENT_CERT" \
  --key "$CLIENT_KEY" \
  -H "Origin: $ORIGIN" \
  -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
  -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
  > "$OUTPUT_DIR/cors_get_output.txt" 2>&1

# Display the output
echo
echo "Response:"
cat "$OUTPUT_DIR/cors_get_output.txt"
echo

# Test 3: GET request with only Cloudflare Access headers (no mTLS)
echo "Test 3: GET request with only Cloudflare Access headers (no mTLS)"
echo "Command:"
echo "curl -v https://$DOMAIN$ENDPOINT \\"
echo "  -H \"Origin: $ORIGIN\" \\"
echo "  -H \"CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID\" \\"
echo "  -H \"CF-Access-Client-Secret: [HIDDEN]\" \\"

# Execute the command
curl -v "https://$DOMAIN$ENDPOINT" \
  -H "Origin: $ORIGIN" \
  -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
  -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
  > "$OUTPUT_DIR/cors_no_mtls_output.txt" 2>&1

# Display the output
echo
echo "Response:"
cat "$OUTPUT_DIR/cors_no_mtls_output.txt"
echo

echo "=== CORS mTLS Test Complete ==="
echo "Results saved to $OUTPUT_DIR/"
