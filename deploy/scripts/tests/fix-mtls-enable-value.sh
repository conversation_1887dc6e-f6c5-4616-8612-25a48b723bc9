#!/bin/bash
# Script to fix ENABLE_MTLS environment variable values across all services
# This updates the value from "1" to "true" for consistency

# Source master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
PROJECT_ID="openai-api-4375643"
REGION="us-central1"

echo "=== Fixing ENABLE_MTLS Environment Variable ==="
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo

# Function to update ENABLE_MTLS for a specific service
update_service_mtls() {
  local service_name=$1
  
  echo "🔍 Checking service: $service_name"
  
  # Check if ENABLE_MTLS exists and is set to "1"
  local has_mtls=$(gcloud run services describe $service_name --region=$REGION --format="json" | jq '.spec.template.spec.containers[0].env[] | select(.name=="ENABLE_MTLS" and .value=="1")' 2>/dev/null)
  
  if [ -n "$has_mtls" ]; then
    echo "  ⚠️ ENABLE_MTLS found with value \"1\", updating to \"true\""
    
    # Create a temporary YAML file with the current configuration
    gcloud run services describe $service_name --region=$REGION --format="yaml" > /tmp/service-config.yaml
    
    # Update the ENABLE_MTLS value in the YAML
    sed -i '' 's/name: ENABLE_MTLS\n  value: "1"/name: ENABLE_MTLS\n  value: "true"/' /tmp/service-config.yaml
    
    # Apply the updated configuration
    echo "  🔄 Applying updated configuration to $service_name..."
    gcloud run services replace /tmp/service-config.yaml --region=$REGION
    
    echo "  ✅ Configuration updated successfully"
  else
    echo "  ℹ️ No changes needed (ENABLE_MTLS not found or already set to \"true\")"
  fi
  
  echo "---"
}

# Get all services
echo "📋 Retrieving list of Cloud Run services..."
services=$(gcloud run services list --region=$REGION --format="value(name)")

# Process each service
for service in $services; do
  if [[ $service == divinci-staging-* ]]; then
    update_service_mtls $service
  fi
done

echo "=== ENABLE_MTLS Fix Complete ==="
echo "Please run the comprehensive tests again to verify the changes"