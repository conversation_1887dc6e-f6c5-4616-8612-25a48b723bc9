# mTLS Implementation Improvement Plan

This document outlines a comprehensive plan to improve the mTLS implementation in our codebase, focusing on testing, certificate management, and standardization.

## 1. Add Unit Tests

### 1.1 Server-Side Certificate Loading Tests

**Location**: `workspace/servers/public-api/tests/unit/mtls/certificate-loading.test.ts`

**Tests to Implement**:
- Test loading server certificate from different paths
- Test loading client CA certificate from different paths
- Test handling of missing certificates
- Test handling of malformed certificates
- Test environment variable overrides for certificate paths

**Implementation Steps**:
1. Create a mock file system for testing
2. Extract certificate loading logic into a testable function
3. Write tests for each scenario
4. Ensure tests run in CI pipeline

### 1.2 HTTPS Server Creation Tests

**Location**: `workspace/servers/public-api/tests/unit/mtls/https-server.test.ts`

**Tests to Implement**:
- Test HTTPS server creation with valid certificates
- Test HTTPS server creation with missing certificates
- Test HTTPS server creation with invalid certificates
- Test HTTPS server fallback to HTTP when mTLS setup fails
- Test environment variable controls for mTLS enablement

**Implementation Steps**:
1. Extract HTTPS server creation logic into a testable function
2. Create mock certificates for testing
3. Write tests for each scenario
4. Ensure tests run in CI pipeline

### 1.3 Client Certificate Verification Tests

**Location**: `workspace/servers/public-api/tests/unit/mtls/client-verification.test.ts`

**Tests to Implement**:
- Test verification of valid client certificates
- Test rejection of invalid client certificates
- Test handling of missing client certificates
- Test handling of expired client certificates
- Test handling of client certificates with incorrect CA

**Implementation Steps**:
1. Extract client certificate verification logic into a testable function
2. Create mock client certificates for testing
3. Write tests for each scenario
4. Ensure tests run in CI pipeline

### 1.4 Client-Side mTLS Tests

**Location**: `workspace/clients/web/tests/unit/mtls/client-mtls.test.ts`

**Tests to Implement**:
- Test client certificate loading from different paths
- Test HTTPS agent creation with valid certificates
- Test handling of missing client certificates
- Test handling of invalid client certificates
- Test environment variable controls for mTLS enablement

**Implementation Steps**:
1. Extract client-side mTLS logic into testable functions
2. Create mock certificates for testing
3. Write tests for each scenario
4. Ensure tests run in CI pipeline

## 2. Add Integration Tests

### 2.1 Local mTLS Server Setup

**Location**: `workspace/servers/public-api/tests/integration/mtls/local-mtls-server.ts`

**Implementation Steps**:
1. Create a simple HTTPS server with mTLS enabled
2. Generate test certificates for the server and clients
3. Configure the server to require client certificates
4. Implement endpoints for testing different scenarios

### 2.2 End-to-End mTLS Tests

**Location**: `workspace/servers/public-api/tests/integration/mtls/e2e-mtls.test.ts`

**Tests to Implement**:
- Test successful connection with valid client certificate
- Test rejected connection with invalid client certificate
- Test rejected connection with no client certificate
- Test rejected connection with expired client certificate
- Test connection with Cloudflare Access headers

**Implementation Steps**:
1. Set up the local mTLS server for testing
2. Create test clients with different certificate configurations
3. Write tests for each scenario
4. Ensure tests run in CI pipeline

### 2.3 Cross-Service mTLS Tests

**Location**: `workspace/servers/public-api/tests/integration/mtls/cross-service-mtls.test.ts`

**Tests to Implement**:
- Test mTLS communication between services
- Test handling of certificate rotation
- Test handling of certificate expiration
- Test handling of certificate revocation

**Implementation Steps**:
1. Set up multiple local services with mTLS
2. Configure services to communicate with each other using mTLS
3. Write tests for each scenario
4. Ensure tests run in CI pipeline

## 3. Improve Certificate Management

### 3.1 Certificate Validation Utility

**Location**: `workspace/resources/server-utils/src/certificates/validation.ts`

**Features to Implement**:
- Function to validate certificate and key match
- Function to check certificate expiration
- Function to verify certificate chain
- Function to validate certificate against CA

**Implementation Steps**:
1. Create utility functions for certificate validation
2. Write unit tests for each function
3. Integrate with existing certificate loading code
4. Add documentation for usage

### 3.2 Certificate Rotation System

**Location**: `workspace/resources/server-utils/src/certificates/rotation.ts`

**Features to Implement**:
- Function to generate new certificates
- Function to update certificates in GCP Secret Manager
- Function to notify services of certificate changes
- Scheduled job for certificate rotation

**Implementation Steps**:
1. Create utility functions for certificate rotation
2. Write unit tests for each function
3. Create a scheduled job for certificate rotation
4. Add documentation for usage

### 3.3 Certificate Expiration Monitoring

**Location**: `workspace/resources/server-utils/src/certificates/monitoring.ts`

**Features to Implement**:
- Function to check certificate expiration dates
- Function to send alerts for expiring certificates
- Dashboard for certificate status
- Integration with monitoring system

**Implementation Steps**:
1. Create utility functions for certificate monitoring
2. Write unit tests for each function
3. Integrate with existing monitoring system
4. Create a dashboard for certificate status

## 4. Standardize Certificate Paths

### 4.1 Certificate Path Configuration

**Location**: `workspace/resources/server-globals/src/certificates/paths.ts`

**Features to Implement**:
- Constants for standard certificate paths
- Function to resolve certificate paths based on environment
- Function to validate certificate paths
- Documentation for certificate path standards

**Implementation Steps**:
1. Define standard paths for certificates
2. Create utility functions for path resolution
3. Write unit tests for each function
4. Update existing code to use standardized paths

### 4.2 Certificate Path Migration

**Location**: `deploy/scripts/migrate-certificate-paths.sh`

**Features to Implement**:
- Script to migrate certificates to standard paths
- Validation of certificate locations
- Backup of existing certificates
- Update of GCP Secret Manager paths

**Implementation Steps**:
1. Create migration script
2. Test migration in development environment
3. Document migration process
4. Schedule migration for production

## 5. Add Automated Verification

### 5.1 Pre-Deployment Certificate Verification

**Location**: `deploy/scripts/verify-certificates.sh`

**Features to Implement**:
- Script to verify certificates before deployment
- Validation of certificate and key matching
- Validation of certificate chain
- Validation of certificate expiration

**Implementation Steps**:
1. Create verification script
2. Integrate with CI/CD pipeline
3. Add failure conditions for invalid certificates
4. Document verification process

### 5.2 Runtime Certificate Verification

**Location**: `workspace/servers/public-api/src/setup/certificate-verification.ts`

**Features to Implement**:
- Function to verify certificates at runtime
- Logging of certificate issues
- Alerting for certificate problems
- Graceful handling of certificate issues

**Implementation Steps**:
1. Create runtime verification functions
2. Integrate with server startup process
3. Add logging and alerting for issues
4. Document verification process

## Implementation Timeline

### Phase 1: Unit Tests (Weeks 1-2)
- Implement server-side certificate loading tests
- Implement HTTPS server creation tests
- Implement client certificate verification tests
- Implement client-side mTLS tests

### Phase 2: Certificate Management (Weeks 3-4)
- Implement certificate validation utility
- Implement certificate path standardization
- Create pre-deployment verification script
- Update existing code to use new utilities

### Phase 3: Integration Tests (Weeks 5-6)
- Set up local mTLS server for testing
- Implement end-to-end mTLS tests
- Implement cross-service mTLS tests
- Integrate tests with CI pipeline

### Phase 4: Advanced Features (Weeks 7-8)
- Implement certificate rotation system
- Implement certificate expiration monitoring
- Create certificate path migration script
- Implement runtime certificate verification

## Success Criteria

1. All unit tests pass with >90% coverage
2. Integration tests verify end-to-end mTLS functionality
3. Certificate paths are standardized across the codebase
4. Certificate validation is automated in the CI/CD pipeline
5. Certificate rotation and monitoring systems are in place
6. Documentation is updated with new certificate management practices
