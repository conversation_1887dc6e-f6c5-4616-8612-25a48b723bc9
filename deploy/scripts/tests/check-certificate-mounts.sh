#!/bin/bash
# check-certificate-mounts.sh
# Script to verify proper certificate secret mounts in GCP Cloud Run services
# This script checks that all Cloud Run services have the correct volume mounts for mTLS certificates

set -e

# Configuration
PROJECT_ID=${1:-$(gcloud config get-value project)}
REGION=${2:-"us-central1"}
OUTPUT_DIR="./test-results"
mkdir -p "$OUTPUT_DIR"

# Define expected mount paths and secret names
CERT_MOUNT_PATH="/etc/ssl/certs"
KEY_MOUNT_PATH="/etc/ssl/private"
CERT_SECRET_NAME="server-crt"
KEY_SECRET_NAME="server-key"

echo "=== GCP Cloud Run Certificate Mount Verification ==="
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo

# Get the list of all services
echo "📋 Retrieving list of Cloud Run services..."
SERVICES=$(gcloud run services list --region=$REGION --format="value(metadata.name)")

if [ -z "$SERVICES" ]; then
  echo "❌ No Cloud Run services found in region $REGION"
  exit 1
fi

# Create summary file
SUMMARY_FILE="$OUTPUT_DIR/certificate_mount_verification_$(date +%Y%m%d%H%M%S).md"
echo "# Certificate Mount Verification Report" > "$SUMMARY_FILE"
echo "Generated on: $(date)" >> "$SUMMARY_FILE"
echo "Project: $PROJECT_ID" >> "$SUMMARY_FILE"
echo "Region: $REGION" >> "$SUMMARY_FILE"
echo >> "$SUMMARY_FILE"
echo "| Service | Certificate Mount | Key Mount | Certificate Secret | Key Secret | ENABLE_MTLS Env | Status |" >> "$SUMMARY_FILE"
echo "|---------|-------------------|-----------|-------------------|------------|-----------------|--------|" >> "$SUMMARY_FILE"

# Initialize counters
TOTAL_SERVICES=0
PROPERLY_CONFIGURED=0
MISSING_CERT_MOUNT=0
MISSING_KEY_MOUNT=0
MISSING_CERT_SECRET=0
MISSING_KEY_SECRET=0
MISSING_ENABLE_MTLS=0

# Check each service
for SERVICE_NAME in $SERVICES; do
  echo "🔍 Checking service: $SERVICE_NAME"
  ((TOTAL_SERVICES++))
  
  # Get service details
  SERVICE_INFO=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format=json)
  echo "$SERVICE_INFO" > "$OUTPUT_DIR/${SERVICE_NAME}_service_info.json"
  
  # Check for certificate mount
  CERT_MOUNT=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.containers[0].volumeMounts[] | select(.mountPath=="'"$CERT_MOUNT_PATH"'") | .name' 2>/dev/null || echo "")
  if [ -n "$CERT_MOUNT" ]; then
    echo "✅ Certificate mount found at $CERT_MOUNT_PATH (name: $CERT_MOUNT)"
    CERT_MOUNT_STATUS="✅"
  else
    echo "❌ Certificate mount not found at $CERT_MOUNT_PATH"
    CERT_MOUNT_STATUS="❌"
    ((MISSING_CERT_MOUNT++))
  fi
  
  # Check for key mount
  KEY_MOUNT=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.containers[0].volumeMounts[] | select(.mountPath=="'"$KEY_MOUNT_PATH"'") | .name' 2>/dev/null || echo "")
  if [ -n "$KEY_MOUNT" ]; then
    echo "✅ Key mount found at $KEY_MOUNT_PATH (name: $KEY_MOUNT)"
    KEY_MOUNT_STATUS="✅"
  else
    echo "❌ Key mount not found at $KEY_MOUNT_PATH"
    KEY_MOUNT_STATUS="❌"
    ((MISSING_KEY_MOUNT++))
  fi
  
  # Check for certificate secret in volumes
  CERT_SECRET=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.volumes[] | select(.name=="mtls-certs") | .secret.secretName' 2>/dev/null || echo "")
  if [ -n "$CERT_SECRET" ]; then
    echo "✅ Certificate secret found: $CERT_SECRET"
    # Verify the secret exists in Secret Manager
    if gcloud secrets describe $CERT_SECRET &>/dev/null; then
      echo "  ✅ Secret '$CERT_SECRET' exists in Secret Manager"
      CERT_SECRET_STATUS="✅"
    else
      echo "  ❌ Secret '$CERT_SECRET' does not exist in Secret Manager"
      CERT_SECRET_STATUS="❌ (missing in GCP)"
      ((MISSING_CERT_SECRET++))
    fi
  else
    echo "❌ Certificate secret not found"
    CERT_SECRET_STATUS="❌"
    ((MISSING_CERT_SECRET++))
  fi
  
  # Check for key secret in volumes
  KEY_SECRET=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.volumes[] | select(.name=="mtls-key") | .secret.secretName' 2>/dev/null || echo "")
  if [ -n "$KEY_SECRET" ]; then
    echo "✅ Key secret found: $KEY_SECRET"
    # Verify the secret exists in Secret Manager
    if gcloud secrets describe $KEY_SECRET &>/dev/null; then
      echo "  ✅ Secret '$KEY_SECRET' exists in Secret Manager"
      KEY_SECRET_STATUS="✅"
    else
      echo "  ❌ Secret '$KEY_SECRET' does not exist in Secret Manager"
      KEY_SECRET_STATUS="❌ (missing in GCP)"
      ((MISSING_KEY_SECRET++))
    fi
  else
    echo "❌ Key secret not found"
    KEY_SECRET_STATUS="❌"
    ((MISSING_KEY_SECRET++))
  fi
  
  # Check for ENABLE_MTLS environment variable
  ENABLE_MTLS=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.containers[0].env[] | select(.name=="ENABLE_MTLS") | .value' 2>/dev/null || echo "")
  if [ -n "$ENABLE_MTLS" ]; then
    if [ "$ENABLE_MTLS" == "true" ]; then
      echo "✅ ENABLE_MTLS environment variable is set to true"
      ENABLE_MTLS_STATUS="✅"
    else
      echo "⚠️ ENABLE_MTLS environment variable is set to '$ENABLE_MTLS' (expected 'true')"
      ENABLE_MTLS_STATUS="⚠️ (value: $ENABLE_MTLS)"
      ((MISSING_ENABLE_MTLS++))
    fi
  else
    echo "❌ ENABLE_MTLS environment variable not found"
    ENABLE_MTLS_STATUS="❌"
    ((MISSING_ENABLE_MTLS++))
  fi
  
  # Overall status
  if [ "$CERT_MOUNT_STATUS" == "✅" ] && [ "$KEY_MOUNT_STATUS" == "✅" ] && 
     [ "$CERT_SECRET_STATUS" == "✅" ] && [ "$KEY_SECRET_STATUS" == "✅" ] && 
     [ "$ENABLE_MTLS_STATUS" == "✅" ]; then
    OVERALL_STATUS="✅ Properly configured"
    ((PROPERLY_CONFIGURED++))
  else
    OVERALL_STATUS="❌ Configuration issues"
  fi
  
  # Add to summary
  echo "| $SERVICE_NAME | $CERT_MOUNT_STATUS | $KEY_MOUNT_STATUS | $CERT_SECRET_STATUS | $KEY_SECRET_STATUS | $ENABLE_MTLS_STATUS | $OVERALL_STATUS |" >> "$SUMMARY_FILE"
  
  echo "---"
done

# Add statistics to summary
echo >> "$SUMMARY_FILE"
echo "## Summary Statistics" >> "$SUMMARY_FILE"
echo >> "$SUMMARY_FILE"
echo "- Total services: $TOTAL_SERVICES" >> "$SUMMARY_FILE"
echo "- Properly configured: $PROPERLY_CONFIGURED" >> "$SUMMARY_FILE"
echo "- Services missing certificate mount: $MISSING_CERT_MOUNT" >> "$SUMMARY_FILE"
echo "- Services missing key mount: $MISSING_KEY_MOUNT" >> "$SUMMARY_FILE"
echo "- Services missing certificate secret: $MISSING_CERT_SECRET" >> "$SUMMARY_FILE"
echo "- Services missing key secret: $MISSING_KEY_SECRET" >> "$SUMMARY_FILE"
echo "- Services missing ENABLE_MTLS environment variable: $MISSING_ENABLE_MTLS" >> "$SUMMARY_FILE"

echo
echo "=== Certificate Mount Verification Complete ==="
echo "Summary report saved to: $SUMMARY_FILE"

# Fix permissions
chmod +x "$SUMMARY_FILE"

# Print summary statistics
echo
echo "📊 Summary Statistics:"
echo "   Total services: $TOTAL_SERVICES"
echo "   Properly configured: $PROPERLY_CONFIGURED"
echo "   Services missing certificate mount: $MISSING_CERT_MOUNT"
echo "   Services missing key mount: $MISSING_KEY_MOUNT"
echo "   Services missing certificate secret: $MISSING_CERT_SECRET"
echo "   Services missing key secret: $MISSING_KEY_SECRET"
echo "   Services missing ENABLE_MTLS environment variable: $MISSING_ENABLE_MTLS"
echo

# Provide recommendations
if [ $PROPERLY_CONFIGURED -lt $TOTAL_SERVICES ]; then
  echo "⚠️ Some services have certificate mount configuration issues."
  echo "   Please review the detailed report for specific recommendations."
  
  # Provide specific recommendations based on the issues found
  if [ $MISSING_CERT_MOUNT -gt 0 ] || [ $MISSING_KEY_MOUNT -gt 0 ]; then
    echo "   - Volume mounts are missing in some services. Ensure the service template includes:"
    echo "     volumeMounts:"
    echo "       - name: mtls-certs"
    echo "         mountPath: /etc/ssl/certs"
    echo "       - name: mtls-key"
    echo "         mountPath: /etc/ssl/private"
  fi
  
  if [ $MISSING_CERT_SECRET -gt 0 ] || [ $MISSING_KEY_SECRET -gt 0 ]; then
    echo "   - Secret volumes are missing in some services. Ensure the service template includes:"
    echo "     volumes:"
    echo "       - name: mtls-certs"
    echo "         secret:"
    echo "           secretName: server-crt"
    echo "           items:"
    echo "             - key: latest"
    echo "               path: server.crt"
    echo "       - name: mtls-key"
    echo "         secret:"
    echo "           secretName: server-key"
    echo "           items:"
    echo "             - key: latest"
    echo "               path: server.key"
  fi
  
  if [ $MISSING_ENABLE_MTLS -gt 0 ]; then
    echo "   - ENABLE_MTLS environment variable is missing or not set to 'true' in some services."
    echo "     Add this environment variable to enable mTLS verification in the application."
  fi
else
  echo "✅ All services are properly configured with certificate mounts."
fi

exit 0