#!/bin/bash

# Test for redirect loops
# This script tests if there are redirect loops in the API

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
URL="https://api.stage.divinci.app/ai-chat/trending"
MAX_REDIRECTS=5
VERBOSE=false

# Function to display usage information
usage() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --url URL               URL to test (default: https://api.stage.divinci.app/ai-chat/trending)"
  echo "  --max-redirects N       Maximum number of redirects to follow (default: 5)"
  echo "  --verbose               Enable verbose output"
  echo "  --help                  Display this help message"
  echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --url)
      URL="$2"
      shift
      shift
      ;;
    --max-redirects)
      MAX_REDIRECTS="$2"
      shift
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    --help)
      usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      usage
      exit 1
      ;;
  esac
done

# Function to test for redirect loops
test_redirect() {
  echo -e "${BLUE}Testing for redirect loops at $URL...${NC}"
  
  # Build curl command
  CURL_CMD="curl -s -i -L --max-redirs $MAX_REDIRECTS \"$URL\""
  
  if [ "$VERBOSE" = true ]; then
    CURL_CMD="curl -v -L --max-redirs $MAX_REDIRECTS \"$URL\""
    echo "Running: $CURL_CMD"
  fi
  
  # Run the command
  RESPONSE=$(eval $CURL_CMD)
  
  # Save the response to a file for easier processing
  echo "$RESPONSE" > redirect_response.txt
  
  # Check for redirect loop
  if grep -q "Maximum ($MAX_REDIRECTS) redirects followed" redirect_response.txt; then
    echo -e "${RED}Redirect loop detected!${NC}"
    
    # Extract redirect URLs
    REDIRECTS=$(grep -i "Location:" redirect_response.txt)
    
    echo -e "${YELLOW}Redirect chain:${NC}"
    echo "$REDIRECTS"
  else
    echo -e "${GREEN}No redirect loop detected.${NC}"
    
    # Extract status code
    STATUS_CODE=$(grep -i "HTTP/" redirect_response.txt | tail -1 | awk '{print $2}')
    
    echo -e "${BLUE}Final status code: $STATUS_CODE${NC}"
  fi
  
  # If verbose, show full response
  if [ "$VERBOSE" = true ]; then
    echo -e "${BLUE}Full response:${NC}"
    cat redirect_response.txt
  fi
  
  # Clean up
  rm redirect_response.txt
}

# Function to test OPTIONS request
test_options_redirect() {
  echo -e "\n${BLUE}Testing OPTIONS request for redirect loops at $URL...${NC}"
  
  # Build curl command
  CURL_CMD="curl -s -i -X OPTIONS -L --max-redirs $MAX_REDIRECTS \"$URL\" -H \"Origin: https://chat.stage.divinci.app\" -H \"Access-Control-Request-Method: GET\" -H \"Access-Control-Request-Headers: Content-Type, Authorization\""
  
  if [ "$VERBOSE" = true ]; then
    CURL_CMD="curl -v -X OPTIONS -L --max-redirs $MAX_REDIRECTS \"$URL\" -H \"Origin: https://chat.stage.divinci.app\" -H \"Access-Control-Request-Method: GET\" -H \"Access-Control-Request-Headers: Content-Type, Authorization\""
    echo "Running: $CURL_CMD"
  fi
  
  # Run the command
  RESPONSE=$(eval $CURL_CMD)
  
  # Save the response to a file for easier processing
  echo "$RESPONSE" > options_response.txt
  
  # Check for redirect loop
  if grep -q "Maximum ($MAX_REDIRECTS) redirects followed" options_response.txt; then
    echo -e "${RED}Redirect loop detected for OPTIONS request!${NC}"
    
    # Extract redirect URLs
    REDIRECTS=$(grep -i "Location:" options_response.txt)
    
    echo -e "${YELLOW}Redirect chain:${NC}"
    echo "$REDIRECTS"
  else
    echo -e "${GREEN}No redirect loop detected for OPTIONS request.${NC}"
    
    # Extract status code
    STATUS_CODE=$(grep -i "HTTP/" options_response.txt | tail -1 | awk '{print $2}')
    
    echo -e "${BLUE}Final status code: $STATUS_CODE${NC}"
  fi
  
  # If verbose, show full response
  if [ "$VERBOSE" = true ]; then
    echo -e "${BLUE}Full response:${NC}"
    cat options_response.txt
  fi
  
  # Clean up
  rm options_response.txt
}

# Main function
main() {
  echo -e "${BLUE}=== Testing for Redirect Loops ===${NC}"
  echo -e "${BLUE}URL: $URL${NC}"
  echo -e "${BLUE}Max redirects: $MAX_REDIRECTS${NC}"
  
  # Test for redirect loops
  test_redirect
  
  # Test OPTIONS request for redirect loops
  test_options_redirect
  
  echo -e "\n${GREEN}Tests completed.${NC}"
}

# Run the main function
main
