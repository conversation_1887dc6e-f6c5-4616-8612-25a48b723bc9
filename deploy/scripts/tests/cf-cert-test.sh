#!/bin/bash
# Script to test Cloudflare connectivity and certificate configuration

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

DOMAIN="chat.dev.divinci.app"
CF_TRACE_URL="https://www.cloudflare.com/cdn-cgi/trace"

echo -e "${BLUE}=== Cloudflare Certificate and Trace Test ===${NC}"
echo -e "${BLUE}Testing domain: ${DOMAIN}${NC}"
echo

# Test 1: Direct Cloudflare trace
echo -e "${BLUE}=== Test 1: Cloudflare Trace Information ===${NC}"
echo -e "This will show if your request is going through Cloudflare and basic request info"
echo

CF_TRACE=$(curl -s "${CF_TRACE_URL}")
echo "$CF_TRACE"

# Extract Cloudflare data
CF_IP=$(echo "$CF_TRACE" | grep "ip=" | cut -d= -f2)
CF_COLO=$(echo "$CF_TRACE" | grep "colo=" | cut -d= -f2)
CF_HTTP=$(echo "$CF_TRACE" | grep "http=" | cut -d= -f2)
CF_VISIT=$(echo "$CF_TRACE" | grep "visit=" | cut -d= -f2)
CF_TLS=$(echo "$CF_TRACE" | grep "tls=" | cut -d= -f2)

echo
echo -e "${YELLOW}Summary:${NC}"
echo -e "Your IP: ${CF_IP}"
echo -e "Cloudflare Data Center: ${CF_COLO}"
echo -e "HTTP Version: ${CF_HTTP}"
echo -e "TLS Version: ${CF_TLS}"
echo

# Test 2: Domain-specific trace
echo -e "${BLUE}=== Test 2: Domain-Specific Trace Information ===${NC}"
echo -e "Testing direct trace for ${DOMAIN}"
echo

# This will work for domains on Cloudflare, but may not work if the domain doesn't have the trace endpoint
DOMAIN_TRACE=$(curl -s --max-time 5 "https://${DOMAIN}/cdn-cgi/trace" || echo "Failed to get domain trace")
if [[ "$DOMAIN_TRACE" == *"Failed"* ]]; then
  echo -e "${YELLOW}Could not get direct trace from the domain. This is normal if the domain doesn't expose the trace endpoint.${NC}"
else
  echo "$DOMAIN_TRACE"
fi

echo

# Test 3: Certificate inspection from Cloudflare edge
echo -e "${BLUE}=== Test 3: Certificate Served by Cloudflare Edge ===${NC}"
echo -e "This shows the certificate that Cloudflare presents to visitors"
echo

echo -e "${YELLOW}Certificate Chain for ${DOMAIN} (via Cloudflare):${NC}"
openssl s_client -connect ${DOMAIN}:443 -servername ${DOMAIN} < /dev/null 2>/dev/null | grep -E "subject=|issuer=" | sed 's/^/  /'

echo
echo -e "${YELLOW}Certificate Details for ${DOMAIN}:${NC}"
CERT_EXPIRY=$(openssl s_client -connect ${DOMAIN}:443 -servername ${DOMAIN} < /dev/null 2>/dev/null | openssl x509 -noout -dates | sed 's/^/  /')
echo "$CERT_EXPIRY"

# Check if it's a Cloudflare certificate
ISSUER=$(openssl s_client -connect ${DOMAIN}:443 -servername ${DOMAIN} < /dev/null 2>/dev/null | openssl x509 -noout -issuer)
if [[ "$ISSUER" == *"Cloudflare"* ]]; then
  echo -e "  ${GREEN}✓ Certificate is issued by Cloudflare${NC}"
else
  echo -e "  ${YELLOW}⚠ Certificate is not issued by Cloudflare${NC}"
fi

echo

# Test 4: HTTP response codes
echo -e "${BLUE}=== Test 4: HTTP Response Test ===${NC}"
echo -e "Testing HTTP response for ${DOMAIN}"
echo

HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://${DOMAIN})
echo -e "HTTP Status Code: ${HTTP_CODE}"

if [[ "$HTTP_CODE" == "526" ]]; then
  echo -e "${RED}✗ Error 526: Invalid SSL certificate${NC}"
  echo -e "${YELLOW}This confirms there's an SSL certificate issue between Cloudflare and your origin server.${NC}"
elif [[ "$HTTP_CODE" == "200" ]]; then
  echo -e "${GREEN}✓ Website is returning a 200 OK response${NC}"
elif [[ "$HTTP_CODE" == "302" || "$HTTP_CODE" == "301" ]]; then
  echo -e "${YELLOW}⚠ Website is returning a redirect (${HTTP_CODE})${NC}"
  REDIRECT=$(curl -s -I https://${DOMAIN} | grep -i "location:" | sed 's/^/  /')
  echo -e "Redirect location:"
  echo "$REDIRECT"
else
  echo -e "${YELLOW}⚠ Website is returning status code: ${HTTP_CODE}${NC}"
fi

echo

# Test 5: Origin Certificate Check (if you can connect directly)
echo -e "${BLUE}=== Test 5: Direct Connection to Origin (if possible) ===${NC}"
echo -e "${YELLOW}Note: This test requires direct access to your origin server and will fail if Cloudflare is enforcing access.${NC}"
echo -e "Enter your origin server IP address (leave empty to skip):"
read ORIGIN_IP

if [ -n "$ORIGIN_IP" ]; then
  echo -e "Testing direct connection to ${ORIGIN_IP}..."
  
  # Try to connect directly to origin
  ORIGIN_CERT=$(timeout 5 openssl s_client -connect ${ORIGIN_IP}:443 -servername ${DOMAIN} < /dev/null 2>/dev/null | openssl x509 -noout -subject -issuer -dates || echo "Failed to connect")
  
  if [[ "$ORIGIN_CERT" == *"Failed"* ]]; then
    echo -e "${YELLOW}Could not connect directly to origin. This is normal if Cloudflare is restricting access.${NC}"
  else
    echo -e "${YELLOW}Certificate information from origin server:${NC}"
    echo "$ORIGIN_CERT" | sed 's/^/  /'
    
    # Check if it's the Cloudflare Origin Certificate
    if [[ "$ORIGIN_CERT" == *"CloudFlare Origin"* ]]; then
      echo -e "  ${GREEN}✓ Origin is using Cloudflare Origin Certificate${NC}"
    else
      echo -e "  ${RED}✗ Origin is NOT using Cloudflare Origin Certificate${NC}"
      echo -e "  ${YELLOW}This is likely causing the 526 error.${NC}"
    fi
  fi
else
  echo -e "${YELLOW}Skipping direct origin server test.${NC}"
fi

echo

# Test 6: Check for CF authorization token
echo -e "${BLUE}=== Test 6: Cloudflare API Trace Test ===${NC}"
echo -e "This test requires a Cloudflare API token with appropriate permissions."
echo -e "Enter your Cloudflare API token (leave empty to skip):"
read CF_AUTH_TOKEN

if [ -n "$CF_AUTH_TOKEN" ]; then
  echo -e "Running Cloudflare API trace test..."
  ACCOUNT_ID="14a6fa23390363382f378b5bd4a0f849" # From your previous information
  
  TRACE_RESPONSE=$(curl -s --request POST \
    --url "https://api.cloudflare.com/client/v4/accounts/$ACCOUNT_ID/request-tracer/tracer" \
    --header 'Content-Type: application/json' \
    --header "Authorization: Bearer $CF_AUTH_TOKEN" \
    --data '{
    "url": "https://'"$DOMAIN"'/",
    "method": "GET"
  }')
  
  # Save the response to a file for inspection
  echo "$TRACE_RESPONSE" > cf_trace_response.json
  echo -e "${YELLOW}Trace response saved to cf_trace_response.json${NC}"
  
  # Extract and show the most relevant parts
  SUCCESS=$(echo "$TRACE_RESPONSE" | grep -o '"success":[a-z]*' | cut -d: -f2)
  HTTP_STATUS=$(echo "$TRACE_RESPONSE" | grep -o '"http_status":[0-9]*' | cut -d: -f2)
  
  echo -e "API Trace Result: success=$SUCCESS, http_status=$HTTP_STATUS"
  
  if [[ "$SUCCESS" == "true" ]]; then
    echo -e "${GREEN}✓ Trace completed successfully${NC}"
  else
    echo -e "${RED}✗ Trace failed${NC}"
  fi
  
  if [[ "$HTTP_STATUS" == "526" ]]; then
    echo -e "${RED}✗ Error 526 confirmed: Invalid SSL certificate${NC}"
  elif [[ "$HTTP_STATUS" == "200" ]]; then
    echo -e "${GREEN}✓ Site returning 200 OK response${NC}"
  elif [[ "$HTTP_STATUS" == "302" || "$HTTP_STATUS" == "301" ]]; then
    echo -e "${YELLOW}⚠ Site returning a redirect (${HTTP_STATUS})${NC}"
  else
    echo -e "${YELLOW}⚠ Site returning status code: ${HTTP_STATUS}${NC}"
  fi
else
  echo -e "${YELLOW}Skipping Cloudflare API trace test.${NC}"
fi

echo
echo -e "${BLUE}=== Test Results Summary ===${NC}"

# HTTP Response summary
if [[ "$HTTP_CODE" == "526" ]]; then
  echo -e "${RED}✗ Your site is returning Error 526 (Invalid SSL certificate)${NC}"
  echo -e "${YELLOW}This indicates a problem with the SSL certificate on your origin server.${NC}"
elif [[ "$HTTP_CODE" == "200" ]]; then
  echo -e "${GREEN}✓ Your site is accessible (HTTP 200)${NC}"
elif [[ "$HTTP_CODE" == "302" || "$HTTP_CODE" == "301" ]]; then
  echo -e "${YELLOW}⚠ Your site is redirecting (HTTP ${HTTP_CODE})${NC}"
else
  echo -e "${YELLOW}⚠ Your site is returning HTTP ${HTTP_CODE}${NC}"
fi

echo
echo -e "${BLUE}=== Recommendations ===${NC}"
echo -e "1. If you're getting Error 526:"
echo -e "   - Ensure your origin server uses the Cloudflare Origin Certificate (origin.crt/key)"
echo -e "   - Check that your GCP secrets are properly updated with these certificates"
echo -e "   - Restart your services after updating certificates"
echo
echo -e "2. If you're getting redirects (301/302):"
echo -e "   - Check if this is intentional or part of your application's logic"
echo -e "   - If unintentional, check your web server configuration for redirect rules"
echo
echo -e "3. Double-check Cloudflare settings:"
echo -e "   - SSL/TLS mode set to Full (Strict)"
echo -e "   - Origin Server settings correctly configured"
echo -e "   - Always Use HTTPS enabled if desired"
echo
echo -e "${GREEN}Tests completed!${NC}"