#!/bin/bash
# GCP Cloud Run Network Test
# This test verifies network connectivity to and from the GCP Cloud Run service.

set -e

# Configuration
SERVICE_NAME=${1:-"public-api-live"}
REGION=${2:-"us-central1"}
PROJECT_ID=${3:-$(gcloud config get-value project)}
OUTPUT_DIR="./test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== GCP Cloud Run Network Test ==="
echo "Service Name: $SERVICE_NAME"
echo "Region: $REGION"
echo "Project ID: $PROJECT_ID"
echo

# Step 1: Get service information
echo "Step 1: Getting service information..."
SERVICE_INFO=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format=json 2>/dev/null)

# Check if the service exists
if [ -z "$SERVICE_INFO" ]; then
  echo "❌ Service $SERVICE_NAME not found in region $REGION"
  echo "   Available services in region $REGION:"
  gcloud run services list --region=$REGION --format="table(metadata.name,status.url,status.conditions.status)" | grep -v "READY"
  echo "$SERVICE_INFO" > "$OUTPUT_DIR/${SERVICE_NAME}_service_info_not_found.txt"
  echo "⚠️ Skipping remaining tests for this service"
  exit 0
fi

echo "$SERVICE_INFO" > "$OUTPUT_DIR/${SERVICE_NAME}_service_info.json"
echo "✅ Service $SERVICE_NAME found in region $REGION"

# Extract service URL
SERVICE_URL=$(echo "$SERVICE_INFO" | jq -r '.status.url')
echo "   Service URL: $SERVICE_URL"

# Step 2: Check VPC connector
echo "Step 2: Checking VPC connector..."
VPC_CONNECTOR=$(echo "$SERVICE_INFO" | jq -r '.spec.template.metadata.annotations["run.googleapis.com/vpc-access-connector"]')

if [ -z "$VPC_CONNECTOR" ] || [ "$VPC_CONNECTOR" == "null" ]; then
  echo "⚠️ No VPC connector found"
else
  echo "✅ VPC connector found: $VPC_CONNECTOR"

  # Get VPC connector details
  VPC_CONNECTOR_INFO=$(gcloud compute networks vpc-access connectors describe $(basename $VPC_CONNECTOR) --region=$REGION --format=json)
  echo "$VPC_CONNECTOR_INFO" > "$OUTPUT_DIR/vpc_connector_info.json"

  # Check VPC connector status
  VPC_CONNECTOR_STATUS=$(echo "$VPC_CONNECTOR_INFO" | jq -r '.state')
  if [ "$VPC_CONNECTOR_STATUS" == "READY" ]; then
    echo "✅ VPC connector is ready"
  else
    echo "❌ VPC connector is not ready"
    echo "   Status: $VPC_CONNECTOR_STATUS"
  fi

  # Get VPC network
  VPC_NETWORK=$(echo "$VPC_CONNECTOR_INFO" | jq -r '.network')
  echo "   VPC network: $VPC_NETWORK"

  # Get VPC subnet
  VPC_SUBNET=$(echo "$VPC_CONNECTOR_INFO" | jq -r '.subnet.name')
  echo "   VPC subnet: $VPC_SUBNET"
fi

# Step 3: Check ingress settings
echo "Step 3: Checking ingress settings..."
INGRESS=$(echo "$SERVICE_INFO" | jq -r '.spec.template.metadata.annotations["run.googleapis.com/ingress"]')

if [ -z "$INGRESS" ] || [ "$INGRESS" == "null" ]; then
  echo "⚠️ No ingress setting found, using default (all)"
  INGRESS="all"
else
  echo "✅ Ingress setting found: $INGRESS"
fi

# Check ingress implications
if [ "$INGRESS" == "all" ]; then
  echo "   Service is accessible from the internet and within the VPC"
elif [ "$INGRESS" == "internal" ]; then
  echo "   Service is only accessible within the VPC"
elif [ "$INGRESS" == "internal-and-cloud-load-balancing" ]; then
  echo "   Service is accessible within the VPC and through Cloud Load Balancing"
fi

# Step 4: Check egress settings
echo "Step 4: Checking egress settings..."
EGRESS=$(echo "$SERVICE_INFO" | jq -r '.spec.template.metadata.annotations["run.googleapis.com/vpc-access-egress"]')

if [ -z "$EGRESS" ] || [ "$EGRESS" == "null" ]; then
  echo "⚠️ No egress setting found, using default (private-ranges-only)"
  EGRESS="private-ranges-only"
else
  echo "✅ Egress setting found: $EGRESS"
fi

# Check egress implications
if [ "$EGRESS" == "private-ranges-only" ]; then
  echo "   Traffic to private IP ranges goes through the VPC connector"
elif [ "$EGRESS" == "all" ]; then
  echo "   All traffic goes through the VPC connector"
elif [ "$EGRESS" == "all-traffic" ]; then
  echo "   All traffic goes through the VPC connector"
fi

# Step 5: Check firewall rules
echo "Step 5: Checking firewall rules..."
if [ -n "$VPC_NETWORK" ] && [ "$VPC_NETWORK" != "null" ]; then
  FIREWALL_RULES=$(gcloud compute firewall-rules list --filter="network:$VPC_NETWORK" --format=json)
  echo "$FIREWALL_RULES" > "$OUTPUT_DIR/firewall_rules.json"

  # Check if firewall rules exist
  if [ "$FIREWALL_RULES" == "[]" ]; then
    echo "⚠️ No firewall rules found for network $VPC_NETWORK"
  else
    echo "✅ Firewall rules found for network $VPC_NETWORK"

    # Check for egress rules
    EGRESS_RULES=$(echo "$FIREWALL_RULES" | jq -r '.[] | select(.direction=="EGRESS")')
    if [ -z "$EGRESS_RULES" ]; then
      echo "⚠️ No egress rules found"
    else
      echo "✅ Egress rules found"

      # Check for rules allowing HTTPS (port 443)
      HTTPS_RULES=$(echo "$EGRESS_RULES" | jq -r '.[] | select(.allowed[].ports[] | contains("443"))')
      if [ -z "$HTTPS_RULES" ]; then
        echo "⚠️ No rules allowing HTTPS (port 443) found"
      else
        echo "✅ Rules allowing HTTPS (port 443) found"
      fi
    fi

    # Check for ingress rules
    INGRESS_RULES=$(echo "$FIREWALL_RULES" | jq -r '.[] | select(.direction=="INGRESS")')
    if [ -z "$INGRESS_RULES" ]; then
      echo "⚠️ No ingress rules found"
    else
      echo "✅ Ingress rules found"

      # Check for rules allowing HTTPS (port 443)
      HTTPS_RULES=$(echo "$INGRESS_RULES" | jq -r '.[] | select(.allowed[].ports[] | contains("443"))')
      if [ -z "$HTTPS_RULES" ]; then
        echo "⚠️ No rules allowing HTTPS (port 443) found"
      else
        echo "✅ Rules allowing HTTPS (port 443) found"
      fi
    fi
  fi
else
  echo "⚠️ Cannot check firewall rules without VPC network information"
fi

# Step 6: Check DNS configuration
echo "Step 6: Checking DNS configuration..."
if [ -n "$VPC_NETWORK" ] && [ "$VPC_NETWORK" != "null" ]; then
  DNS_CONFIG=$(gcloud dns managed-zones list --filter="visibility=private AND networks.networkUrl~$VPC_NETWORK" --format=json)
  echo "$DNS_CONFIG" > "$OUTPUT_DIR/dns_config.json"

  # Check if DNS zones exist
  if [ "$DNS_CONFIG" == "[]" ]; then
    echo "⚠️ No private DNS zones found for network $VPC_NETWORK"
  else
    echo "✅ Private DNS zones found for network $VPC_NETWORK"

    # List DNS zones
    echo "   Private DNS zones:"
    echo "$DNS_CONFIG" | jq -r '.[].dnsName'
  fi
else
  echo "⚠️ Cannot check DNS configuration without VPC network information"
fi

# Step 7: Test connectivity to Cloudflare IPs
echo "Step 7: Testing connectivity to Cloudflare IPs..."
echo "⚠️ Cannot directly test connectivity from Cloud Run to Cloudflare IPs"
echo "   This would require deploying a test container to the service"

# Step 8: Check load balancer configuration (if applicable)
echo "Step 8: Checking load balancer configuration..."
if [ "$INGRESS" == "internal-and-cloud-load-balancing" ]; then
  # Try to find load balancers targeting this service
  LB_BACKENDS=$(gcloud compute backend-services list --format=json | jq -r '.[] | select(.backends[].group | contains("run.app"))')
  echo "$LB_BACKENDS" > "$OUTPUT_DIR/lb_backends.json"

  # Check if load balancers exist
  if [ -z "$LB_BACKENDS" ]; then
    echo "⚠️ No load balancers found targeting Cloud Run services"
  else
    echo "✅ Load balancers found targeting Cloud Run services"

    # List load balancers
    echo "   Load balancers:"
    echo "$LB_BACKENDS" | jq -r '.name'

    # Check for SSL certificates
    LB_CERTS=$(gcloud compute ssl-certificates list --format=json)
    echo "$LB_CERTS" > "$OUTPUT_DIR/lb_certs.json"

    if [ "$LB_CERTS" == "[]" ]; then
      echo "⚠️ No SSL certificates found for load balancers"
    else
      echo "✅ SSL certificates found for load balancers"

      # List certificates
      echo "   SSL certificates:"
      echo "$LB_CERTS" | jq -r '.[].name'
    fi
  fi
else
  echo "⚠️ Service is not configured for Cloud Load Balancing"
fi

echo
echo "=== GCP Cloud Run Network Test Complete ==="
echo "Results saved to $OUTPUT_DIR/"
