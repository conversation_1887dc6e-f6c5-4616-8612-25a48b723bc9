#!/bin/bash
# CORS mTLS Test with Cloudflare Access Headers
# This script tests CORS preflight requests with mTLS and Cloudflare Access headers

# Source master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
DOMAIN="api.stage.divinci.app"
ORIGIN="https://chat.stage.divinci.app"
ENDPOINT="/ai-chat/trending"
REPO_ROOT="$(get_repo_root)"
OUTPUT_DIR="$SCRIPT_DIR/test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== CORS Test with Cloudflare Access Headers (No mTLS) ==="
echo "API Domain: $DOMAIN"
echo "Origin: $ORIGIN"
echo "Endpoint: $ENDPOINT"
echo

# Test: OPTIONS request (CORS preflight) with Cloudflare Access headers
echo "Test 1: OPTIONS request (CORS preflight) with Cloudflare Access headers"
echo "Command:"
echo "curl -v -X OPTIONS https://$DOMAIN$ENDPOINT \\"
echo "  -H \"Origin: $ORIGIN\" \\"
echo "  -H \"Access-Control-Request-Method: GET\" \\"
echo "  -H \"CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID\" \\"
echo "  -H \"CF-Access-Client-Secret: [HIDDEN]\" \\"

# Execute the command
curl -v -X OPTIONS "https://$DOMAIN$ENDPOINT" \
  -H "Origin: $ORIGIN" \
  -H "Access-Control-Request-Method: GET" \
  -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
  -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
  > "$OUTPUT_DIR/cors_preflight_output.txt" 2>&1

# Display the output
echo
echo "Response:"
cat "$OUTPUT_DIR/cors_preflight_output.txt"
echo

# Test 2: GET request with Cloudflare Access headers
echo "Test 2: GET request with Cloudflare Access headers"
echo "Command:"
echo "curl -v https://$DOMAIN$ENDPOINT \\"
echo "  -H \"Origin: $ORIGIN\" \\"
echo "  -H \"CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID\" \\"
echo "  -H \"CF-Access-Client-Secret: [HIDDEN]\" \\"

# Execute the command
curl -v "https://$DOMAIN$ENDPOINT" \
  -H "Origin: $ORIGIN" \
  -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
  -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
  > "$OUTPUT_DIR/cors_get_output.txt" 2>&1

# Display the output
echo
echo "Response:"
cat "$OUTPUT_DIR/cors_get_output.txt"
echo

echo "=== CORS Test Complete ==="
echo "Results saved to $OUTPUT_DIR/"
