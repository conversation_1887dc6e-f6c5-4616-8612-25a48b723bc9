#!/bin/bash
# Certificate Chain Verification Test
# This test verifies the completeness and validity of the certificate chain presented by your origin server.

set -e

# Source common functions and master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
DOMAIN=${1:-"$DEFAULT_DOMAIN"}
ENVIRONMENT=${2:-"$DEFAULT_ENVIRONMENT"}
REPO_ROOT="$(get_repo_root)"
CERT_DIR="$REPO_ROOT/private-keys/${ENVIRONMENT}/certs/mtls"
OUTPUT_DIR="$SCRIPT_DIR/test-results"
mkdir -p "$OUTPUT_DIR"

echo "Repository root: $REPO_ROOT"
echo "Certificate directory: $CERT_DIR"

echo "=== Certificate Chain Verification Test ==="
echo "Domain: $DOMAIN"
echo "Environment: $ENVIRONMENT"
echo

# Step 1: Extract the certificate from GCP Secret Manager
echo "Step 1: Extracting certificate from GCP Secret Manager..."
gcloud secrets versions access latest --secret=server-crt > "$OUTPUT_DIR/server-crt-from-gcp.pem"
echo "✅ Certificate extracted from GCP Secret Manager"

# Step 2: Extract the certificate from the local file system
echo "Step 2: Extracting certificate from local file system..."
if [ -f "$CERT_DIR/server.crt" ]; then
  cp "$CERT_DIR/server.crt" "$OUTPUT_DIR/server-crt-from-local.pem"
  echo "✅ Certificate extracted from local file system"
else
  echo "❌ Certificate not found in local file system: $CERT_DIR/server.crt"
fi

# Step 3: Verify the certificate chain using OpenSSL
echo "Step 3: Verifying certificate chain using OpenSSL..."
CERT_COUNT=$(grep -c "BEGIN CERTIFICATE" "$OUTPUT_DIR/server-crt-from-gcp.pem")
echo "Certificate chain contains $CERT_COUNT certificates"

if [ "$CERT_COUNT" -ge 2 ]; then
  echo "✅ Certificate chain is complete (contains multiple certificates)"
else
  echo "❌ Certificate chain is incomplete (contains only $CERT_COUNT certificate)"
  echo "   The chain should include both the server certificate and the Cloudflare Root CA certificate"
fi

# Step 4: Check for the presence of the Cloudflare Root CA certificate
echo "Step 4: Checking for Cloudflare Root CA certificate..."
if grep -q "CloudFlare Origin SSL" "$OUTPUT_DIR/server-crt-from-gcp.pem"; then
  echo "✅ Certificate is issued by Cloudflare Origin SSL CA"
else
  echo "❌ Certificate is not issued by Cloudflare Origin SSL CA"
fi

# Step 5: Extract certificate details
echo "Step 5: Extracting certificate details..."
echo "Certificate details:"
openssl x509 -in "$OUTPUT_DIR/server-crt-from-gcp.pem" -text -noout | grep -E "Subject:|Issuer:|Not Before:|Not After:|DNS:" > "$OUTPUT_DIR/certificate-details.txt"
cat "$OUTPUT_DIR/certificate-details.txt"

# Step 6: Validate that the certificate covers the correct domain(s)
echo "Step 6: Validating certificate domains..."
if openssl x509 -in "$OUTPUT_DIR/server-crt-from-gcp.pem" -text -noout | grep -q "$DOMAIN"; then
  echo "✅ Certificate covers the domain: $DOMAIN"
elif openssl x509 -in "$OUTPUT_DIR/server-crt-from-gcp.pem" -text -noout | grep -q "*.${DOMAIN#*.}"; then
  echo "✅ Certificate covers the domain via wildcard: *.${DOMAIN#*.}"
else
  echo "❌ Certificate does not cover the domain: $DOMAIN"
  echo "   Certificate should include either $DOMAIN or *.${DOMAIN#*.}"
fi

# Step 7: Check certificate expiration dates
echo "Step 7: Checking certificate expiration dates..."
NOT_BEFORE=$(openssl x509 -in "$OUTPUT_DIR/server-crt-from-gcp.pem" -noout -startdate | cut -d= -f2)
NOT_AFTER=$(openssl x509 -in "$OUTPUT_DIR/server-crt-from-gcp.pem" -noout -enddate | cut -d= -f2)
CURRENT_DATE=$(date)

# Handle date calculation in a cross-platform way (Linux vs macOS)
if [[ "$(uname)" == "Darwin" ]]; then
  # macOS date command
  NOT_AFTER_SECONDS=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$NOT_AFTER" +%s 2>/dev/null || date -j -f "%b %d %T %Y %Z" "$NOT_AFTER" +%s)
  CURRENT_SECONDS=$(date +%s)
  DAYS_UNTIL_EXPIRY=$(( ($NOT_AFTER_SECONDS - $CURRENT_SECONDS) / 86400 ))
else
  # Linux date command
  DAYS_UNTIL_EXPIRY=$(( ($(date -d "$NOT_AFTER" +%s) - $(date +%s)) / 86400 ))
fi

echo "Certificate valid from: $NOT_BEFORE"
echo "Certificate valid until: $NOT_AFTER"
echo "Current date: $CURRENT_DATE"
echo "Days until expiry: $DAYS_UNTIL_EXPIRY"

if [ "$DAYS_UNTIL_EXPIRY" -gt 30 ]; then
  echo "✅ Certificate is not expiring soon (expires in $DAYS_UNTIL_EXPIRY days)"
elif [ "$DAYS_UNTIL_EXPIRY" -gt 0 ]; then
  echo "⚠️ Certificate is expiring soon (expires in $DAYS_UNTIL_EXPIRY days)"
else
  echo "❌ Certificate has expired"
fi

# Step 8: Verify certificate chain against Cloudflare Root CA
echo "Step 8: Verifying certificate chain against Cloudflare Root CA..."
# Use standard curl without CF Access headers for CF docs (not needed for public documentation)
curl -s https://developers.cloudflare.com/ssl/static/origin_ca_rsa_root.pem -o "$OUTPUT_DIR/cloudflare_origin_ca_rsa_root.pem"
curl -s https://developers.cloudflare.com/ssl/static/origin_ca_ecc_root.pem -o "$OUTPUT_DIR/cloudflare_origin_ca_ecc_root.pem"

# Determine if the certificate is RSA or ECC
if openssl x509 -in "$OUTPUT_DIR/server-crt-from-gcp.pem" -text -noout | grep -q "Public Key Algorithm: rsaEncryption"; then
  ROOT_CA="$OUTPUT_DIR/cloudflare_origin_ca_rsa_root.pem"
  echo "Certificate uses RSA encryption"
else
  ROOT_CA="$OUTPUT_DIR/cloudflare_origin_ca_ecc_root.pem"
  echo "Certificate uses ECC encryption"
fi

# Create a temporary certificate bundle
cat "$OUTPUT_DIR/server-crt-from-gcp.pem" "$ROOT_CA" > "$OUTPUT_DIR/cert-bundle.pem"

# Verify the certificate chain
if openssl verify -CAfile "$ROOT_CA" "$OUTPUT_DIR/server-crt-from-gcp.pem" > /dev/null 2>&1; then
  echo "✅ Certificate chain verifies against Cloudflare Root CA"
else
  echo "❌ Certificate chain does not verify against Cloudflare Root CA"
  echo "   This may indicate a problem with the certificate chain"
fi

echo
echo "=== Certificate Chain Verification Test Complete ==="
echo "Results saved to $OUTPUT_DIR/"
