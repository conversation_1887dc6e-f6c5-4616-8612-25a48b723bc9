# mTLS and CORS Fix Implementation Plan

This document outlines the plan for fixing the mTLS and CORS issues in the Divinci API.

## Issue Summary

Based on our tests, the following issues were identified:

1. **mTLS Configuration**:
   - `ENABLE_MTLS` environment variable is set to "1" instead of "true"
   - Certificate mounts and secrets are inconsistently configured across services

2. **CORS Issues**:
   - OPTIONS preflight requests return 503 Service Unavailable
   - No CORS headers are present in the responses
   - Cloudflare Firewall rules may be blocking OPTIONS requests

3. **TLS Analysis**:
   - TShark tests couldn't capture detailed packet information without sudo
   - SSL mode is set to "strict" in Cloudflare
   - Certificate configuration appears correct, but mTLS authentication is failing

## Implementation Steps

### Step 1: Fix ENABLE_MTLS Environment Variable

Run the following script to update all services to use "true" instead of "1":

```bash
./fix-mtls-enable-value.sh
```

This script will:
- Check all Cloud Run services for `ENABLE_MTLS="1"`
- Update the value to `ENABLE_MTLS="true"`
- Apply the changes to the services

### Step 2: Check Application Logs for OPTIONS Request Errors

Run the following script to check for OPTIONS request errors in the service logs:

```bash
./check-options-errors.sh
```

This script will:
- Search the logs for OPTIONS and CORS-related errors
- Extract relevant error messages
- Check for any successful OPTIONS requests

### Step 3: Fix Cloudflare Firewall Rules for OPTIONS Requests

Run the following script to update the Cloudflare firewall rules:

```bash
./fix-cloudflare-options-rule.sh
```

This script will:
- Check for the "Allow All OPTIONs" rule and create it if missing
- Ensure the rule action is set to "skip"
- Check the mTLS rule to ensure it excludes OPTIONS requests
- Create Transform Rules for adding CORS headers to OPTIONS and regular requests

### Step 4: Verify the CORS Fixes

Run the following script to verify that the CORS fixes are working:

```bash
./verify-cors-fixes.sh
```

This script will:
- Test OPTIONS requests to multiple endpoints
- Test GET requests to the same endpoints
- Check for the presence of CORS headers in the responses
- Verify the status codes are correct (204/200 for OPTIONS, 200 for GET)

### Step 5: Run Comprehensive Tests

Run the comprehensive tests to verify all aspects of the mTLS and CORS implementation:

```bash
./run-comprehensive-tests.sh api.stage.divinci.app
```

This will run all tests, including:
- Certificate chain verification
- mTLS connection test
- Cloudflare SSL mode test
- TLS version test
- Cipher suite test

## Troubleshooting

If issues persist after implementing the fixes, consider the following:

### 1. Check the mTLS Rule Expression

The mTLS rule expression should exclude OPTIONS requests:

```
(http.request.method ne "OPTIONS") and (
  (not cf.tls_client_auth.cert_verified and http.host eq "api.stage.divinci.app") or 
  ...
)
```

### 2. Check the Transform Rules

Verify that the Transform Rules are adding the necessary CORS headers:

- **OPTIONS Transform Rule**: Should add all CORS headers for OPTIONS requests
- **Regular Transform Rule**: Should add basic CORS headers for all responses

### 3. Check the Service Configuration

Verify that the service is correctly configured to handle OPTIONS requests and CORS:

```bash
# Check service configuration
gcloud run services describe divinci-staging-server-api --region=us-central1 --format=json
```

### 4. Check the Certificate Mounts

Verify that the certificate mounts are correctly configured:

```bash
./check-certificate-mounts.sh
```

### 5. Test with curl

Use curl to test the API directly:

```bash
# Test OPTIONS request
curl -v -X OPTIONS "https://api.stage.divinci.app/health" \
  -H "Origin: https://chat.stage.divinci.app" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type, Authorization"

# Test GET request
curl -v -X GET "https://api.stage.divinci.app/health" \
  -H "Origin: https://chat.stage.divinci.app"
```

## Conclusion

By following these steps, we should be able to fix the mTLS and CORS issues in the Divinci API. The key components are:

1. Ensuring consistent mTLS configuration across all services
2. Configuring Cloudflare to allow OPTIONS requests without mTLS
3. Adding CORS headers to all responses
4. Verifying that the fixes work correctly

If further issues are encountered, additional debugging and testing may be required.