#!/bin/bash
# <PERSON>ript to set Cloudflare SSL mode for a domain

set -e

# Load environment variables
if [ -f "/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env" ]; then
  echo "Loading credentials from /Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env"
  source "/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env"
  
  # Debug output (masked for security)
  echo "Debug: CF_ACCESS_CLIENT_SECRET length: ${#CF_ACCESS_CLIENT_SECRET}"
  echo "Debug: CF_EMAIL: ${CF_EMAIL}"
  echo "Debug: CF_API_KEY length: ${#CF_API_KEY}"
  echo "Debug: CF_ACCOUNT_ID: ${CF_ACCOUNT_ID}"
fi

# Get domain and mode from command line
DOMAIN=${1:-"stage.divinci.app"}
MODE=${2:-"full"}

# Validate mode
if [[ ! "$MODE" =~ ^(off|flexible|full|strict)$ ]]; then
  echo "❌ Invalid SSL mode: $MODE"
  echo "Valid modes: off, flexible, full, strict"
  exit 1
fi

echo "Setting Cloudflare SSL mode for domain: $DOMAIN to $MODE"

# Extract zone name (remove subdomain)
ZONE=$(echo "$DOMAIN" | awk -F. '{print $(NF-1)"."$NF}')
echo "Zone: $ZONE"

# Get zone ID
echo "Getting zone ID for $ZONE..."
ZONE_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$ZONE" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_API_KEY" \
  -H "Content-Type: application/json" | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -z "$ZONE_ID" ]; then
  echo "❌ Failed to get zone ID for $ZONE"
  exit 1
fi

echo "Zone ID: $ZONE_ID"

# Set SSL mode
echo "Setting SSL mode for $ZONE_ID to $MODE..."
RESPONSE=$(curl -s -X PATCH "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/settings/ssl" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_API_KEY" \
  -H "Content-Type: application/json" \
  --data "{\"value\":\"$MODE\"}")

SUCCESS=$(echo "$RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2)

if [ "$SUCCESS" = "true" ]; then
  echo "✅ SSL mode successfully set to $MODE"
else
  echo "❌ Failed to set SSL mode"
  echo "Response: $RESPONSE"
  exit 1
fi

# Explain SSL modes
echo ""
echo "SSL Mode Explanation:"
echo "  - off: No encryption between visitor and Cloudflare or Cloudflare and origin"
echo "  - flexible: Encryption between visitor and Cloudflare, but not Cloudflare and origin"
echo "  - full: Encryption between visitor and Cloudflare, and Cloudflare and origin (accepts self-signed certs)"
echo "  - strict: Encryption between visitor and Cloudflare, and Cloudflare and origin (requires valid cert)"

echo ""
echo "Note: If you changed from 'strict' to 'full' to fix Error 526, consider changing back to 'strict'"
echo "once your certificates are properly configured for better security."
