#!/bin/bash

# Network Diagnostics Runner
# This script runs a comprehensive suite of network diagnostic tests
# including mTLS and Cloudflare rule analysis

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="staging"
VERBOSE=false
RUN_MTLS=true
RUN_CLOUDFLARE=true
RUN_CORS=true

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Function to display usage information
usage() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --environment ENV      Environment to test (default: staging)"
  echo "  --verbose              Enable verbose output"
  echo "  --skip-mtls            Skip mTLS tests"
  echo "  --skip-cloudflare      Skip Cloudflare rules analysis"
  echo "  --skip-cors            Skip CORS tests"
  echo "  --help                 Display this help message"
  echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --environment)
      ENVIRONMENT="$2"
      shift
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    --skip-mtls)
      RUN_MTLS=false
      shift
      ;;
    --skip-cloudflare)
      RUN_CLOUDFLARE=false
      shift
      ;;
    --skip-cors)
      RUN_CORS=false
      shift
      ;;
    --help)
      usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      usage
      exit 1
      ;;
  esac
done

# Function to run mTLS diagnostic tests
run_mtls_tests() {
  echo -e "${BLUE}=== Running mTLS Diagnostic Tests ===${NC}"
  
  # Check if mTLS test script exists
  if [ -f "$SCRIPT_DIR/mtls-diagnostics.sh" ]; then
    echo "Running mTLS diagnostics..."
    
    # Build command with options
    CMD="$SCRIPT_DIR/mtls-diagnostics.sh --environment $ENVIRONMENT"
    if [ "$VERBOSE" = true ]; then
      CMD="$CMD --verbose"
    fi
    
    # Run the command
    $CMD
  else
    echo -e "${YELLOW}mTLS diagnostic script not found at $SCRIPT_DIR/mtls-diagnostics.sh${NC}"
    echo "Creating a basic mTLS test..."
    
    # Create a basic mTLS test
    echo -e "${BLUE}Testing mTLS configuration for $ENVIRONMENT environment...${NC}"
    
    # Determine domain based on environment
    if [ "$ENVIRONMENT" = "staging" ]; then
      DOMAIN="api.stage.divinci.app"
    elif [ "$ENVIRONMENT" = "production" ]; then
      DOMAIN="api.divinci.app"
    else
      DOMAIN="api.dev.divinci.app"
    fi
    
    # Test connection with client certificate
    echo -e "${BLUE}Testing connection to $DOMAIN with client certificate...${NC}"
    
    # Try to find client certificate
    CERT_DIR="../../../private-keys/$ENVIRONMENT/certs/mtls"
    if [ -d "$CERT_DIR" ]; then
      CLIENT_CERT="$CERT_DIR/client.crt"
      CLIENT_KEY="$CERT_DIR/client.key"
      
      if [ -f "$CLIENT_CERT" ] && [ -f "$CLIENT_KEY" ]; then
        echo "Using client certificate from $CLIENT_CERT"
        curl -v --cert "$CLIENT_CERT" --key "$CLIENT_KEY" "https://$DOMAIN/health" 2>&1 | grep -E "SSL|TLS|certificate"
      else
        echo -e "${YELLOW}Client certificate not found at $CLIENT_CERT${NC}"
        echo "Testing without client certificate..."
        curl -v "https://$DOMAIN/health" 2>&1 | grep -E "SSL|TLS|certificate"
      fi
    else
      echo -e "${YELLOW}Certificate directory not found at $CERT_DIR${NC}"
      echo "Testing without client certificate..."
      curl -v "https://$DOMAIN/health" 2>&1 | grep -E "SSL|TLS|certificate"
    fi
  fi
}

# Function to run Cloudflare rules analysis
run_cloudflare_analysis() {
  echo -e "${BLUE}=== Running Cloudflare Rules Analysis ===${NC}"
  
  # Check if Cloudflare analyzer script exists
  if [ -f "$SCRIPT_DIR/cloudflare-rules-analyzer.sh" ]; then
    echo "Running Cloudflare rules analyzer..."
    
    # Make script executable
    chmod +x "$SCRIPT_DIR/cloudflare-rules-analyzer.sh"
    
    # Build command with options
    CMD="$SCRIPT_DIR/cloudflare-rules-analyzer.sh --environment $ENVIRONMENT"
    if [ "$VERBOSE" = true ]; then
      CMD="$CMD --verbose"
    fi
    
    # Run the command
    $CMD
  else
    echo -e "${RED}Cloudflare rules analyzer script not found at $SCRIPT_DIR/cloudflare-rules-analyzer.sh${NC}"
    echo "Please ensure the script exists and is executable."
  fi
}

# Function to run CORS tests
run_cors_tests() {
  echo -e "${BLUE}=== Running CORS Tests ===${NC}"
  
  # Determine domain and origin based on environment
  if [ "$ENVIRONMENT" = "staging" ]; then
    DOMAIN="api.stage.divinci.app"
    ORIGIN="https://chat.stage.divinci.app"
  elif [ "$ENVIRONMENT" = "production" ]; then
    DOMAIN="api.divinci.app"
    ORIGIN="https://chat.divinci.app"
  else
    DOMAIN="api.dev.divinci.app"
    ORIGIN="https://chat.dev.divinci.app"
  fi
  
  echo -e "${BLUE}Testing CORS for $DOMAIN with origin $ORIGIN...${NC}"
  
  # Test OPTIONS request
  echo -e "${BLUE}Testing OPTIONS preflight request:${NC}"
  curl -v -X OPTIONS "https://$DOMAIN/health" \
    -H "Origin: $ORIGIN" \
    -H "Access-Control-Request-Method: GET" \
    -H "Access-Control-Request-Headers: Content-Type, Authorization" 2>&1 | grep -E "HTTP|Access-Control-"
  
  # Test regular GET request with Origin header
  echo -e "\n${BLUE}Testing regular GET request with Origin header:${NC}"
  curl -v -X GET "https://$DOMAIN/health" \
    -H "Origin: $ORIGIN" 2>&1 | grep -E "HTTP|Access-Control-"
  
  # Try with Cloudflare Access headers if available
  if [ -f "../../../private-keys/$ENVIRONMENT/cloudflare.env" ]; then
    echo -e "\n${BLUE}Testing with Cloudflare Access headers:${NC}"
    source "../../../private-keys/$ENVIRONMENT/cloudflare.env"
    
    if [ ! -z "$CF_ACCESS_CLIENT_ID" ] && [ ! -z "$CF_ACCESS_CLIENT_SECRET" ]; then
      echo "Using Cloudflare Access credentials from environment file"
      
      # Test OPTIONS with Access headers
      echo -e "${BLUE}Testing OPTIONS with Access headers:${NC}"
      curl -v -X OPTIONS "https://$DOMAIN/health" \
        -H "Origin: $ORIGIN" \
        -H "Access-Control-Request-Method: GET" \
        -H "Access-Control-Request-Headers: Content-Type, Authorization" \
        -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
        -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" 2>&1 | grep -E "HTTP|Access-Control-"
      
      # Test GET with Access headers
      echo -e "\n${BLUE}Testing GET with Access headers:${NC}"
      curl -v -X GET "https://$DOMAIN/health" \
        -H "Origin: $ORIGIN" \
        -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
        -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" 2>&1 | grep -E "HTTP|Access-Control-"
    else
      echo -e "${YELLOW}Cloudflare Access credentials not found in environment file${NC}"
    fi
  else
    echo -e "${YELLOW}Cloudflare environment file not found${NC}"
  fi
}

# Main function
main() {
  echo -e "${BLUE}=== Network Diagnostics Runner ===${NC}"
  echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
  
  # Run mTLS tests if enabled
  if [ "$RUN_MTLS" = true ]; then
    run_mtls_tests
  else
    echo -e "${YELLOW}Skipping mTLS tests${NC}"
  fi
  
  # Run Cloudflare analysis if enabled
  if [ "$RUN_CLOUDFLARE" = true ]; then
    run_cloudflare_analysis
  else
    echo -e "${YELLOW}Skipping Cloudflare rules analysis${NC}"
  fi
  
  # Run CORS tests if enabled
  if [ "$RUN_CORS" = true ]; then
    run_cors_tests
  else
    echo -e "${YELLOW}Skipping CORS tests${NC}"
  fi
  
  echo -e "\n${GREEN}All tests completed.${NC}"
}

# Run the main function
main
