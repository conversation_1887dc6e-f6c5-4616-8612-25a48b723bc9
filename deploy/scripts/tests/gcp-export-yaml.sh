#!/bin/bash
# GCP Export YAML Tool Integration
# This script integrates with your existing GCP Export YAML tool to export GCP resources.

set -e

# Source common functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common-functions.sh"

# Configuration
SERVICE_NAME=${1:-"divinci-staging-server-api-live"}
REGION=${2:-"us-central1"}
OUTPUT_DIR="$SCRIPT_DIR/test-results"
OUTPUT_FILE=${3:-"$OUTPUT_DIR/${SERVICE_NAME}_gcp_resources.yaml"}
mkdir -p "$OUTPUT_DIR"

echo "=== GCP Export YAML Tool Integration ==="
echo "Service Name: $SERVICE_NAME"
echo "Region: $REGION"
echo "Output File: $OUTPUT_FILE"
echo

# Step 1: Check if the service exists
echo "Step 1: Checking if the service exists..."
SERVICE_CHECK=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" 2>&1)
SERVICE_CHECK_EXIT_CODE=$?

if [ $SERVICE_CHECK_EXIT_CODE -ne 0 ]; then
  echo "❌ Service $SERVICE_NAME not found in region $REGION"
  echo "   Error: $(echo "$SERVICE_CHECK" | grep "ERROR:" | head -1)"
  echo "   Available services in region $REGION:"
  gcloud run services list --region="$REGION" --format="table(metadata.name,status.url,status.conditions.status)" | grep -v "READY"
  echo "⚠️ Skipping export for this service"
  exit 0
fi

echo "✅ Service $SERVICE_NAME found in region $REGION"

# Step 2: Export service configuration
echo "Step 2: Exporting service configuration..."
if gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format=yaml > "$OUTPUT_FILE" 2>/dev/null; then
  echo "✅ Service configuration exported to $OUTPUT_FILE"
else
  echo "❌ Failed to export service configuration"
  echo "   Using service description from previous step"
  echo "$SERVICE_CHECK" > "$OUTPUT_FILE"
fi

# Step 3: Export service IAM policy
echo "Step 3: Exporting service IAM policy..."
if gcloud run services get-iam-policy "$SERVICE_NAME" --region="$REGION" --format=yaml > "${OUTPUT_FILE%.yaml}_iam.yaml" 2>/dev/null; then
  echo "✅ Service IAM policy exported to ${OUTPUT_FILE%.yaml}_iam.yaml"
else
  echo "❌ Failed to export service IAM policy"
fi

# Step 4: Export VPC connector (if any)
echo "Step 4: Checking for VPC connector..."
VPC_CONNECTOR=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(spec.template.metadata.annotations.'run.googleapis.com/vpc-access-connector')")

if [ -n "$VPC_CONNECTOR" ]; then
  echo "✅ VPC connector found: $VPC_CONNECTOR"
  CONNECTOR_NAME=$(basename "$VPC_CONNECTOR")

  echo "   Exporting VPC connector configuration..."
  gcloud compute networks vpc-access connectors describe "$CONNECTOR_NAME" --region="$REGION" --format=yaml > "${OUTPUT_FILE%.yaml}_vpc_connector.yaml"
  echo "✅ VPC connector configuration exported to ${OUTPUT_FILE%.yaml}_vpc_connector.yaml"

  # Export VPC network
  NETWORK=$(gcloud compute networks vpc-access connectors describe "$CONNECTOR_NAME" --region="$REGION" --format="value(network)")
  if [ -n "$NETWORK" ]; then
    echo "   Exporting VPC network configuration..."
    gcloud compute networks describe "$NETWORK" --format=yaml > "${OUTPUT_FILE%.yaml}_vpc_network.yaml"
    echo "✅ VPC network configuration exported to ${OUTPUT_FILE%.yaml}_vpc_network.yaml"

    # Export firewall rules
    echo "   Exporting firewall rules..."
    gcloud compute firewall-rules list --filter="network:$NETWORK" --format=yaml > "${OUTPUT_FILE%.yaml}_firewall_rules.yaml"
    echo "✅ Firewall rules exported to ${OUTPUT_FILE%.yaml}_firewall_rules.yaml"
  fi
else
  echo "⚠️ No VPC connector found"
fi

# Step 5: Export Cloud Run revision
echo "Step 5: Exporting Cloud Run revision..."
LATEST_REVISION=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="value(status.latestCreatedRevisionName)")

if [ -n "$LATEST_REVISION" ]; then
  echo "✅ Latest revision found: $LATEST_REVISION"
  gcloud run revisions describe "$LATEST_REVISION" --region="$REGION" --format=yaml > "${OUTPUT_FILE%.yaml}_revision.yaml"
  echo "✅ Revision configuration exported to ${OUTPUT_FILE%.yaml}_revision.yaml"
else
  echo "⚠️ No revision found"
fi

# Step 6: Export secrets used by the service
echo "Step 6: Checking for secrets used by the service..."
VOLUMES=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="yaml(spec.template.spec.volumes)")

if [ -n "$VOLUMES" ] && [ "$VOLUMES" != "null" ]; then
  echo "$VOLUMES" > "${OUTPUT_FILE%.yaml}_volumes.yaml"
  echo "✅ Volumes configuration exported to ${OUTPUT_FILE%.yaml}_volumes.yaml"

  # Extract secret names
  SECRET_NAMES=$(echo "$VOLUMES" | grep -o "secretName: [a-zA-Z0-9_-]*" | awk '{print $2}')

  if [ -n "$SECRET_NAMES" ]; then
    echo "✅ Secrets found:"
    echo "$SECRET_NAMES" | while read -r SECRET_NAME; do
      echo "   - $SECRET_NAME"
      # We can't export secret values, but we can check if they exist
      if gcloud secrets describe "$SECRET_NAME" &>/dev/null; then
        echo "     ✅ Secret exists"
      else
        echo "     ❌ Secret does not exist"
      fi
    done
  else
    echo "⚠️ No secrets found"
  fi
else
  echo "⚠️ No volumes found"
fi

# Step 7: Export domain mappings
echo "Step 7: Checking for domain mappings..."

# The domain-mappings list command doesn't support the --region flag
# Let's try to get domain mappings in a different way
echo "   Attempting to get domain mappings from service description..."
SERVICE_DESCRIPTION=$(gcloud run services describe "$SERVICE_NAME" --region="$REGION" --format="yaml")
DOMAIN_MAPPINGS=$(echo "$SERVICE_DESCRIPTION" | grep -A 10 "domain_mappings:" || echo "")

if [ -n "$DOMAIN_MAPPINGS" ] && [ "$DOMAIN_MAPPINGS" != "No domain mappings found." ]; then
  echo "$DOMAIN_MAPPINGS" > "${OUTPUT_FILE%.yaml}_domain_mappings.yaml"
  echo "✅ Domain mappings exported to ${OUTPUT_FILE%.yaml}_domain_mappings.yaml"
else
  echo "⚠️ No domain mappings found in service description"

  # Try an alternative approach
  echo "   Attempting to get domain mappings using global list..."
  DOMAIN_MAPPINGS_CMD=$(gcloud run domain-mappings list --format="yaml" 2>&1)
  DOMAIN_MAPPINGS_EXIT_CODE=$?

  if [ $DOMAIN_MAPPINGS_EXIT_CODE -eq 0 ]; then
    DOMAIN_MAPPINGS=$(echo "$DOMAIN_MAPPINGS_CMD" | grep -A 20 "$SERVICE_NAME" || echo "")
  else
    echo "   ❌ Failed to list domain mappings: $(echo "$DOMAIN_MAPPINGS_CMD" | grep "ERROR:" | head -1)"
    DOMAIN_MAPPINGS=""
  fi

  if [ -n "$DOMAIN_MAPPINGS" ]; then
    echo "$DOMAIN_MAPPINGS" > "${OUTPUT_FILE%.yaml}_domain_mappings.yaml"
    echo "✅ Domain mappings exported to ${OUTPUT_FILE%.yaml}_domain_mappings.yaml"
  else
    echo "⚠️ No domain mappings found"
  fi
fi

# Step 8: Create a summary file
echo "Step 8: Creating a summary file..."
cat > "${OUTPUT_FILE%.yaml}_summary.txt" << EOF
GCP Resource Export Summary for $SERVICE_NAME
=============================================
Region: $REGION
Date: $(date)

Files Exported:
- Service Configuration: $OUTPUT_FILE
- Service IAM Policy: ${OUTPUT_FILE%.yaml}_iam.yaml
EOF

if [ -n "$VPC_CONNECTOR" ]; then
  cat >> "${OUTPUT_FILE%.yaml}_summary.txt" << EOF
- VPC Connector: ${OUTPUT_FILE%.yaml}_vpc_connector.yaml
- VPC Network: ${OUTPUT_FILE%.yaml}_vpc_network.yaml
- Firewall Rules: ${OUTPUT_FILE%.yaml}_firewall_rules.yaml
EOF
fi

if [ -n "$LATEST_REVISION" ]; then
  cat >> "${OUTPUT_FILE%.yaml}_summary.txt" << EOF
- Latest Revision: ${OUTPUT_FILE%.yaml}_revision.yaml
EOF
fi

if [ -n "$VOLUMES" ] && [ "$VOLUMES" != "null" ]; then
  cat >> "${OUTPUT_FILE%.yaml}_summary.txt" << EOF
- Volumes Configuration: ${OUTPUT_FILE%.yaml}_volumes.yaml
EOF
fi

# Check if we found domain mappings in either approach
if [ -f "${OUTPUT_FILE%.yaml}_domain_mappings.yaml" ]; then
  cat >> "${OUTPUT_FILE%.yaml}_summary.txt" << EOF
- Domain Mappings: ${OUTPUT_FILE%.yaml}_domain_mappings.yaml
EOF
fi

echo "✅ Summary created at ${OUTPUT_FILE%.yaml}_summary.txt"

echo
echo "=== GCP Export YAML Tool Integration Complete ==="
echo "Results saved to $OUTPUT_DIR/"
