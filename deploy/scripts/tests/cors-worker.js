addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

/**
 * Handle the request
 * @param {Request} request
 */
async function handleRequest(request) {
  // Get the request method and URL
  const method = request.method;
  const url = new URL(request.url);
  
  // If this is an OPTIONS request, respond with CORS headers
  if (method === 'OPTIONS') {
    // Get the origin from the request
    const origin = request.headers.get('Origin');
    
    // Only allow requests from chat.stage.divinci.app
    if (origin === 'https://chat.stage.divinci.app') {
      // Create a new response with CORS headers
      return new Response(null, {
        status: 204,
        headers: {
          'Access-Control-Allow-Origin': origin,
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization',
          'Access-Control-Allow-Credentials': 'true',
          'Access-Control-Max-Age': '86400'
        }
      });
    }
  }
  
  // For all other requests, fetch from the origin
  return fetch(request);
}
