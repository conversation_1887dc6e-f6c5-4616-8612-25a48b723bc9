#!/bin/bash
# mTLS Connection Test
# This script tests mutual TLS (mTLS) connections using OpenSSL s_client.

set -e

# Source master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
DOMAIN=${1:-"$DEFAULT_DOMAIN"}
ORIGIN_IP=${2:-"$DEFAULT_ORIGIN_IP"}
PORT=${3:-"$DEFAULT_PORT"}
ENVIRONMENT=${4:-"$DEFAULT_ENVIRONMENT"}
REPO_ROOT="$(get_repo_root)"
CERT_DIR="$REPO_ROOT/private-keys/${ENVIRONMENT}/certs/mtls"
OUTPUT_DIR="$SCRIPT_DIR/test-results"
mkdir -p "$OUTPUT_DIR"

echo "Repository root: $REPO_ROOT"
echo "Certificate directory: $CERT_DIR"

echo "=== mTLS Connection Test ==="
echo "Domain: $DOMAIN"
echo "Origin IP: $ORIGIN_IP"
echo "Port: $PORT"
echo "Environment: $ENVIRONMENT"
echo "Certificate Directory: $CERT_DIR"
echo

# Step 1: Check if client certificate and key exist
echo "Step 1: Checking for client certificate and key..."

# Check new standard path first
if [ -f "/etc/ssl/client/client.crt" ] && [ -f "/etc/ssl/private/client.key" ]; then
  echo "✅ Client certificate and key found at standard paths"
  CLIENT_CERT="/etc/ssl/client/client.crt"
  CLIENT_KEY="/etc/ssl/private/client.key"
# Check legacy path
elif [ -f "/etc/ssl/certs/client.crt" ] && [ -f "/etc/ssl/private/client.key" ]; then
  echo "✅ Client certificate and key found at legacy paths"
  CLIENT_CERT="/etc/ssl/certs/client.crt"
  CLIENT_KEY="/etc/ssl/private/client.key"
# Check local development path
elif [ -f "$CERT_DIR/client.crt" ] && [ -f "$CERT_DIR/client.key" ]; then
  echo "✅ Client certificate and key found in local development directory"
  CLIENT_CERT="$CERT_DIR/client.crt"
  CLIENT_KEY="$CERT_DIR/client.key"
else
  echo "⚠️ Client certificate and key not found in any standard location"
  echo "   Creating self-signed client certificate for testing..."

  # Create a temporary directory
  TEMP_DIR=$(mktemp -d)

  # Generate a self-signed client certificate
  openssl req -x509 -newkey rsa:4096 -keyout "$TEMP_DIR/client.key" -out "$TEMP_DIR/client.crt" -days 365 -nodes -subj "/CN=test-client/O=Divinci/C=US"

  CLIENT_CERT="$TEMP_DIR/client.crt"
  CLIENT_KEY="$TEMP_DIR/client.key"

  echo "✅ Self-signed client certificate created"
fi

# Step 2: Check if server certificate exists
echo "Step 2: Checking for server certificate..."
if [ -f "$CERT_DIR/server.crt" ]; then
  echo "✅ Server certificate found"
  SERVER_CERT="$CERT_DIR/server.crt"
else
  echo "⚠️ Server certificate not found in $CERT_DIR"
  echo "   Using system CA certificates for verification"
  SERVER_CERT="/etc/ssl/certs/ca-certificates.crt"
fi

# Step 3: Test basic TLS connection
echo "Step 3: Testing basic TLS connection..."
if openssl s_client -connect "$ORIGIN_IP:$PORT" -servername "$DOMAIN" </dev/null > "$OUTPUT_DIR/basic_tls_connection.txt" 2>&1; then
  echo "✅ Basic TLS connection successful"
else
  echo "❌ Basic TLS connection failed"
fi

# Check for handshake failure
if grep -q "SSL handshake has read" "$OUTPUT_DIR/basic_tls_connection.txt"; then
  echo "✅ TLS handshake completed"
else
  echo "❌ TLS handshake failed"
fi

# Step 4: Test mTLS connection with client certificate
echo "Step 4: Testing mTLS connection with client certificate..."
if openssl s_client -connect "$ORIGIN_IP:$PORT" -servername "$DOMAIN" -cert "$CLIENT_CERT" -key "$CLIENT_KEY" -CAfile "$SERVER_CERT" </dev/null > "$OUTPUT_DIR/mtls_connection.txt" 2>&1; then
  echo "✅ mTLS connection successful"
else
  echo "❌ mTLS connection failed"
fi

# Check for handshake failure
if grep -q "SSL handshake has read" "$OUTPUT_DIR/mtls_connection.txt"; then
  echo "✅ mTLS handshake completed"
else
  echo "❌ mTLS handshake failed"
fi

# Step 5: Test TLS 1.2 connection
echo "Step 5: Testing TLS 1.2 connection..."
if openssl s_client -connect "$ORIGIN_IP:$PORT" -servername "$DOMAIN" -tls1_2 </dev/null > "$OUTPUT_DIR/tls1_2_connection.txt" 2>&1; then
  echo "✅ TLS 1.2 connection successful"
else
  echo "❌ TLS 1.2 connection failed"
fi

# Step 6: Test TLS 1.3 connection
echo "Step 6: Testing TLS 1.3 connection..."
if openssl s_client -connect "$ORIGIN_IP:$PORT" -servername "$DOMAIN" -tls1_3 </dev/null > "$OUTPUT_DIR/tls1_3_connection.txt" 2>&1; then
  echo "✅ TLS 1.3 connection successful"
else
  echo "❌ TLS 1.3 connection failed"
fi

# Step 7: Test with specific cipher suite
echo "Step 7: Testing with specific cipher suite..."
if openssl s_client -connect "$ORIGIN_IP:$PORT" -servername "$DOMAIN" -cipher "ECDHE-RSA-AES128-GCM-SHA256" </dev/null > "$OUTPUT_DIR/cipher_connection.txt" 2>&1; then
  echo "✅ Connection with ECDHE-RSA-AES128-GCM-SHA256 successful"
else
  echo "❌ Connection with ECDHE-RSA-AES128-GCM-SHA256 failed"
fi

# Step 8: Extract and verify certificate chain
echo "Step 8: Extracting and verifying certificate chain..."
openssl s_client -connect "$ORIGIN_IP:$PORT" -servername "$DOMAIN" -showcerts </dev/null > "$OUTPUT_DIR/certificate_chain.txt" 2>&1

# Count certificates in the chain
CERT_COUNT=$(grep -c "BEGIN CERTIFICATE" "$OUTPUT_DIR/certificate_chain.txt")
echo "Certificate chain contains $CERT_COUNT certificates"

if [ "$CERT_COUNT" -ge 2 ]; then
  echo "✅ Certificate chain is complete (contains multiple certificates)"
else
  echo "❌ Certificate chain is incomplete (contains only $CERT_COUNT certificate)"
fi

# Step 9: Verify certificate against Cloudflare Root CA
echo "Step 9: Verifying certificate against Cloudflare Root CA..."
# Use standard curl without CF Access headers for CF docs (not needed for public documentation)
curl -s https://developers.cloudflare.com/ssl/static/origin_ca_rsa_root.pem -o "$OUTPUT_DIR/cloudflare_origin_ca_rsa_root.pem"
curl -s https://developers.cloudflare.com/ssl/static/origin_ca_ecc_root.pem -o "$OUTPUT_DIR/cloudflare_origin_ca_ecc_root.pem"

# Extract the server certificate from the chain
sed -n '/-----BEGIN CERTIFICATE-----/,/-----END CERTIFICATE-----/p' "$OUTPUT_DIR/certificate_chain.txt" | head -n $(grep -n "END CERTIFICATE" "$OUTPUT_DIR/certificate_chain.txt" | head -1 | cut -d: -f1) > "$OUTPUT_DIR/server_cert.pem"

# Verify against RSA root
if openssl verify -CAfile "$OUTPUT_DIR/cloudflare_origin_ca_rsa_root.pem" "$OUTPUT_DIR/server_cert.pem" > /dev/null 2>&1; then
  echo "✅ Certificate verifies against Cloudflare RSA Root CA"
else
  # Verify against ECC root
  if openssl verify -CAfile "$OUTPUT_DIR/cloudflare_origin_ca_ecc_root.pem" "$OUTPUT_DIR/server_cert.pem" > /dev/null 2>&1; then
    echo "✅ Certificate verifies against Cloudflare ECC Root CA"
  else
    echo "❌ Certificate does not verify against Cloudflare Root CAs"
  fi
fi

echo
echo "=== mTLS Connection Test Complete ==="
echo "Results saved to $OUTPUT_DIR/"
