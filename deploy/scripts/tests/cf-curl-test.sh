#!/bin/bash

DOMAIN="chat.stage.divinci.app"

# Set your Cloudflare Access credentials here or pass as environment variables
CF_ACCESS_CLIENT_ID=${CF_ACCESS_CLIENT_ID:-"your_client_id"}
CF_ACCESS_CLIENT_SECRET=${CF_ACCESS_CLIENT_SECRET:-"your_client_secret"}

echo "Testing $DOMAIN with Cloudflare Access bypass headers"

# Test 1: With CF-Access-Client-Id and CF-Access-Client-Secret
echo -e "\nTest 1: With CF-Access-Client headers"
curl -s -I -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" https://$DOMAIN/

# Test 2: With User-Agent that might bypass some protections
echo -e "\nTest 2: With custom User-Agent"
curl -s -I -H "User-Agent: Divinci-API-Test" -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" https://$DOMAIN/

# Test 3: With all headers from our API tests if you know them
echo -e "\nTest 3: With additional headers"
curl -s -I -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
           -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
           -H "X-Test-Bypass: true" \
           -H "X-Origin-URL: https://$DOMAIN/" \
           -H "x-auth0-access-token: test-token" \
           https://$DOMAIN/

echo -e "\nAll tests completed"
