#!/bin/bash

echo "=== Cloudflare Error 526 Advanced Debugging ==="
echo "This script will help diagnose why you're getting Error 526 in Cloudflare Full (Strict) mode"
echo

# Check for curl installation
if ! command -v curl &> /dev/null; then
    echo "Error: curl is not installed. Please install curl to continue."
    exit 1
fi

# Check for openssl installation
if ! command -v openssl &> /dev/null; then
    echo "Error: openssl is not installed. Please install openssl to continue."
    exit 1
fi

# Set the domain to test
DOMAIN="chat.stage.divinci.app"
echo "Testing domain: $DOMAIN"
echo

# Test 1: Check if the site is accessible through Cloudflare
echo "=== Test 1: Checking if site is accessible through Cloudflare ==="
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN)
if [ "$HTTP_CODE" = "526" ]; then
    echo "❌ Site returns Error 526 through Cloudflare. This confirms the SSL issue."
else
    echo "ℹ️ Site returns HTTP $HTTP_CODE through Cloudflare."
fi
echo

# Test 2: Check TLS version offered by Cloudflare
echo "=== Test 2: Checking TLS version offered by Cloudflare ==="
TLS_VERSION=$(openssl s_client -connect $DOMAIN:443 -servername $DOMAIN -showcerts < /dev/null 2>/dev/null | grep "Protocol" || echo "Unable to determine")
echo "TLS Version: $TLS_VERSION"
echo

# Test 3: Test Cloudflare certificate
echo "=== Test 3: Checking Cloudflare certificate ==="
CERT_INFO=$(openssl s_client -connect $DOMAIN:443 -servername $DOMAIN -showcerts < /dev/null 2>/dev/null | openssl x509 -noout -text | grep -A1 "Subject:" || echo "Unable to get certificate info")
echo "Certificate Subject: "
echo "$CERT_INFO"
echo

# Test 4: Check if origin server is directly accessible (bypassing Cloudflare)
echo "=== Test 4: Checking if origin server is directly accessible ==="
echo "Enter your origin server IP address (if known):"
read ORIGIN_IP

if [ -n "$ORIGIN_IP" ]; then
    echo "Testing direct connection to $ORIGIN_IP..."
    ORIGIN_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 10 --connect-to $DOMAIN:443:$ORIGIN_IP:443 -k https://$DOMAIN)
    echo "Response from direct connection: HTTP $ORIGIN_RESPONSE"
    
    # If we can connect, check the certificate
    echo
    echo "Checking certificate presented by origin server:"
    ORIGIN_CERT=$(openssl s_client -connect $ORIGIN_IP:443 -servername $DOMAIN -showcerts < /dev/null 2>/dev/null | openssl x509 -noout -text | grep -A1 "Subject:" || echo "Unable to get certificate info")
    echo "$ORIGIN_CERT"
else
    echo "Skipping direct origin server test."
fi
echo

# Test 5: Check if GCP secrets are properly updated
echo "=== Test 5: Checking for certificate synchronization issues ==="
echo "When you updated the GCP secrets to use origin.crt/key:"
echo "1. Did you update both the certificate and private key?"
echo "2. Did you wait for the changes to propagate to your servers?"
echo "3. Did you restart your web servers after the update?"
echo
echo "Consider the following potential issues:"
echo "- Delay in GCP secret propagation"
echo "- Webserver not restarted after certificate change"
echo "- Incorrect certificate chain"
echo "- Incorrect implementation in the web server configuration"
echo

# Test 6: Check the origin certificate details
echo "=== Test 6: Checking origin certificate details ==="
ORIGIN_CERT_PATH="/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/certs/mtls/origin.crt"
if [ -f "$ORIGIN_CERT_PATH" ]; then
    echo "Origin certificate information:"
    openssl x509 -in "$ORIGIN_CERT_PATH" -text -noout | grep -E 'Subject:|DNS:|Not After'
    
    # Check if the certificate is valid for the domain
    DOMAIN_IN_CERT=$(openssl x509 -in "$ORIGIN_CERT_PATH" -text -noout | grep -E "DNS:.*$DOMAIN" || echo "Not found")
    if [ -z "$DOMAIN_IN_CERT" ]; then
        WILDCARD_DOMAIN=$(echo "$DOMAIN" | sed 's/[^.]*\./\*\./')
        WILDCARD_IN_CERT=$(openssl x509 -in "$ORIGIN_CERT_PATH" -text -noout | grep -E "DNS:.*$WILDCARD_DOMAIN" || echo "Not found")
        if [ -z "$WILDCARD_IN_CERT" ]; then
            echo "❌ Certificate does not appear to be valid for $DOMAIN or $WILDCARD_DOMAIN"
        else
            echo "✅ Certificate is valid for wildcard domain $WILDCARD_DOMAIN which covers $DOMAIN"
        fi
    else
        echo "✅ Certificate is valid for $DOMAIN"
    fi
else
    echo "❌ Origin certificate not found at expected path: $ORIGIN_CERT_PATH"
fi
echo

# Test 7: Check if certificate is trusted by Cloudflare
echo "=== Test 7: Checking if certificate is trusted by Cloudflare ==="
echo "For Full (Strict) mode, Cloudflare requires one of the following:"
echo "1. Certificate signed by a trusted Certificate Authority"
echo "2. A Cloudflare Origin Certificate"
echo "3. A certificate that chains to Cloudflare's CA"
echo

ISSUER=$(openssl x509 -in "$ORIGIN_CERT_PATH" -text -noout | grep "Issuer:" || echo "Unable to determine")
echo "Certificate Issuer: $ISSUER"

if echo "$ISSUER" | grep -q "CloudFlare"; then
    echo "✅ Certificate is issued by Cloudflare, which is trusted in Full (Strict) mode"
else
    echo "❌ Certificate is not issued by Cloudflare. It must be trusted by a public CA for Full (Strict) mode"
fi
echo

# Test 8: Check Cloudflare SSL/TLS settings
echo "=== Test 8: Cloudflare SSL/TLS Configuration Recommendations ==="
echo "1. Ensure 'SSL/TLS > Overview' is set to 'Full (strict)'"
echo "2. Under 'SSL/TLS > Edge Certificates', verify:"
echo "   - Always Use HTTPS: On"
echo "   - Minimum TLS Version: TLS 1.2"
echo "3. Under 'SSL/TLS > Origin Server', verify:"
echo "   - Origin Certificate is uploaded and selected"
echo "   - Authenticated Origin Pulls is On (if using mTLS)"
echo "4. If you're using Authenticated Origin Pulls, ensure the client certificate is correctly installed at origin"
echo

# Test 9: Final recommendations
echo "=== Final Recommendations ==="
echo "1. If using Cloudflare Origin Certificate:"
echo "   - Confirm you're using the SAME origin.crt/key that was generated by Cloudflare"
echo "   - The cert must be for *.divinci.app or explicitly include chat.stage.divinci.app"
echo
echo "2. If certificate is correctly configured but still getting Error 526:"
echo "   - Try purging the Cloudflare cache: Development > Purge Cache > Purge Everything"
echo "   - Check for strict TLS requirements in Cloudflare settings"
echo "   - Check for any web server misconfigurations (nginx might be serving the wrong certificate)"
echo "   - Verify firewall rules to ensure Cloudflare IPs can reach your origin"
echo
echo "3. If all else fails:"
echo "   - Temporarily switch to Full or Flexible SSL/TLS mode for testing"
echo "   - Once the site works, gradually add security back with proper configuration"
echo "   - Consider generating a new Origin Certificate from Cloudflare and reinstalling it"
echo

echo "===== Debug Complete ====="