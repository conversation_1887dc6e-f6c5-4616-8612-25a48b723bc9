#!/bin/bash
# GCP Cloud Run Health Check
# This test verifies that the GCP Cloud Run service is healthy and correctly configured.

set -e

# Configuration
SERVICE_NAME=${1:-"public-api-live"}
REGION=${2:-"us-central1"}
PROJECT_ID=${3:-$(gcloud config get-value project)}
OUTPUT_DIR="./test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== GCP Cloud Run Health Check ==="
echo "Service Name: $SERVICE_NAME"
echo "Region: $REGION"
echo "Project ID: $PROJECT_ID"
echo

# Step 1: Check the GCP Cloud Run service status
echo "Step 1: Checking GCP Cloud Run service status..."
SERVICE_INFO=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format=json 2>/dev/null)

# Check if the service exists
if [ -z "$SERVICE_INFO" ]; then
  echo "❌ Service $SERVICE_NAME not found in region $REGION"
  echo "   Available services in region $REGION:"
  gcloud run services list --region=$REGION --format="table(metadata.name,status.url,status.conditions.status)" | grep -v "READY"
  echo "$SERVICE_INFO" > "$OUTPUT_DIR/${SERVICE_NAME}_service_info_not_found.txt"
  echo "⚠️ Skipping remaining tests for this service"
  exit 0
fi

echo "$SERVICE_INFO" > "$OUTPUT_DIR/${SERVICE_NAME}_service_info.json"
echo "✅ Service $SERVICE_NAME found in region $REGION"

# Extract service URL
SERVICE_URL=$(echo "$SERVICE_INFO" | jq -r '.status.url')
echo "   Service URL: $SERVICE_URL"

# Check service status
SERVICE_STATUS=$(echo "$SERVICE_INFO" | jq -r '.status.conditions[] | select(.type=="Ready") | .status')
if [ "$SERVICE_STATUS" == "True" ]; then
  echo "✅ Service is ready"
else
  echo "❌ Service is not ready"
  echo "   Status: $(echo "$SERVICE_INFO" | jq -r '.status.conditions[] | select(.type=="Ready") | .message')"
fi

# Step 2: Verify the volume mounts for certificates
echo "Step 2: Verifying volume mounts for certificates..."
VOLUME_MOUNTS=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.containers[0].volumeMounts')
echo "$VOLUME_MOUNTS" > "$OUTPUT_DIR/volume_mounts.json"

# Check if volume mounts exist
if [ "$VOLUME_MOUNTS" == "null" ]; then
  echo "❌ No volume mounts found"
else
  echo "✅ Volume mounts found"

  # Check for certificate volume mounts
  CERT_MOUNT=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.containers[0].volumeMounts[] | select(.mountPath=="/etc/ssl/certs")')
  KEY_MOUNT=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.containers[0].volumeMounts[] | select(.mountPath=="/etc/ssl/private")')

  if [ -n "$CERT_MOUNT" ]; then
    echo "✅ Certificate volume mount found at /etc/ssl/certs"
    echo "   Name: $(echo "$CERT_MOUNT" | jq -r '.name')"
  else
    echo "❌ Certificate volume mount not found at /etc/ssl/certs"
  fi

  if [ -n "$KEY_MOUNT" ]; then
    echo "✅ Key volume mount found at /etc/ssl/private"
    echo "   Name: $(echo "$KEY_MOUNT" | jq -r '.name')"
  else
    echo "❌ Key volume mount not found at /etc/ssl/private"
  fi
fi

# Step 3: Check the volumes
echo "Step 3: Checking volumes..."
VOLUMES=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.volumes')
echo "$VOLUMES" > "$OUTPUT_DIR/volumes.json"

# Check if volumes exist
if [ "$VOLUMES" == "null" ]; then
  echo "❌ No volumes found"
else
  echo "✅ Volumes found"

  # Check for certificate volumes
  CERT_VOLUME=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.volumes[] | select(.name=="mtls-certs")')
  KEY_VOLUME=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.volumes[] | select(.name=="mtls-key")')

  if [ -n "$CERT_VOLUME" ]; then
    echo "✅ Certificate volume found with name mtls-certs"
    echo "   Secret: $(echo "$CERT_VOLUME" | jq -r '.secret.secretName')"
    echo "   Path: $(echo "$CERT_VOLUME" | jq -r '.secret.items[0].path')"
  else
    echo "❌ Certificate volume not found with name mtls-certs"
  fi

  if [ -n "$KEY_VOLUME" ]; then
    echo "✅ Key volume found with name mtls-key"
    echo "   Secret: $(echo "$KEY_VOLUME" | jq -r '.secret.secretName')"
    echo "   Path: $(echo "$KEY_VOLUME" | jq -r '.secret.items[0].path')"
  else
    echo "❌ Key volume not found with name mtls-key"
  fi
fi

# Step 4: Check the environment variables
echo "Step 4: Checking environment variables..."
ENV_VARS=$(echo "$SERVICE_INFO" | jq -r '.spec.template.spec.containers[0].env')
echo "$ENV_VARS" > "$OUTPUT_DIR/env_vars.json"

# Check if environment variables exist
if [ "$ENV_VARS" == "null" ]; then
  echo "❌ No environment variables found"
else
  echo "✅ Environment variables found"

  # Check for SSL-related environment variables
  SSL_ENV_VARS=$(echo "$ENV_VARS" | jq -r '.[] | select(.name | contains("SSL") or contains("TLS") or contains("CERT"))')
  if [ -n "$SSL_ENV_VARS" ]; then
    echo "✅ SSL-related environment variables found:"
    echo "$SSL_ENV_VARS" | jq -r '.name'
  else
    echo "⚠️ No SSL-related environment variables found"
  fi
fi

# Step 5: Analyze the service logs for errors
echo "Step 5: Analyzing service logs for errors..."
LOGS=$(gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=$SERVICE_NAME AND severity>=ERROR" --limit=10 --format=json)
echo "$LOGS" > "$OUTPUT_DIR/service_logs.json"

# Check if logs exist
if [ "$LOGS" == "[]" ]; then
  echo "✅ No error logs found in the last 10 entries"
else
  echo "❌ Error logs found:"
  echo "$LOGS" | jq -r '.[].textPayload'
fi

# Step 6: Check service revisions
echo "Step 6: Checking service revisions..."
REVISIONS=$(gcloud run revisions list --service=$SERVICE_NAME --region=$REGION --format=json)
echo "$REVISIONS" > "$OUTPUT_DIR/service_revisions.json"

# Check if revisions exist
if [ "$REVISIONS" == "[]" ]; then
  echo "❌ No revisions found"
else
  echo "✅ Revisions found"

  # Get the latest revision
  LATEST_REVISION=$(echo "$REVISIONS" | jq -r '.[0]')
  REVISION_NAME=$(echo "$LATEST_REVISION" | jq -r '.metadata.name')
  REVISION_STATUS=$(echo "$LATEST_REVISION" | jq -r '.status.conditions[] | select(.type=="Ready") | .status')

  echo "   Latest revision: $REVISION_NAME"
  if [ "$REVISION_STATUS" == "True" ]; then
    echo "   ✅ Latest revision is ready"
  else
    echo "   ❌ Latest revision is not ready"
    echo "      Status: $(echo "$LATEST_REVISION" | jq -r '.status.conditions[] | select(.type=="Ready") | .message')"
  fi
fi

# Step 7: Check service IAM permissions
echo "Step 7: Checking service IAM permissions..."
IAM_POLICY=$(gcloud run services get-iam-policy $SERVICE_NAME --region=$REGION --format=json)
echo "$IAM_POLICY" > "$OUTPUT_DIR/service_iam_policy.json"

# Check if IAM policy exists
if [ -z "$IAM_POLICY" ]; then
  echo "❌ Failed to get IAM policy"
else
  echo "✅ IAM policy retrieved"

  # Check for public access
  PUBLIC_ACCESS=$(echo "$IAM_POLICY" | jq -r '.bindings[] | select(.members[] | contains("allUsers"))')
  if [ -n "$PUBLIC_ACCESS" ]; then
    echo "⚠️ Service is publicly accessible"
  else
    echo "✅ Service is not publicly accessible"
  fi

  # Check for service account access
  SERVICE_ACCOUNT_ACCESS=$(echo "$IAM_POLICY" | jq -r '.bindings[] | select(.members[] | contains("serviceAccount"))')
  if [ -n "$SERVICE_ACCOUNT_ACCESS" ]; then
    echo "✅ Service has service account access"
    echo "   Service accounts:"
    echo "$SERVICE_ACCOUNT_ACCESS" | jq -r '.members[] | select(contains("serviceAccount"))'
  else
    echo "⚠️ No service account access found"
  fi
fi

echo
echo "=== GCP Cloud Run Health Check Complete ==="
echo "Results saved to $OUTPUT_DIR/"
