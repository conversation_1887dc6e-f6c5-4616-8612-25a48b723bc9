#!/bin/bash
# Test Suite Master Configuration
# This file contains shared configuration variables for all test scripts.

# Default domain and endpoint configuration
DEFAULT_DOMAIN="chat.stage.divinci.app"
DEFAULT_ORIGIN_IP="************"
DEFAULT_PORT="443"
DEFAULT_ENVIRONMENT="staging"
DEFAULT_REGION="us-central1"
DEFAULT_CAPTURE_DURATION="10"

# Cloudflare configuration
CF_API_TOKEN="****************************************"
CF_ACCESS_CLIENT_ID="f13e5d39e1997a5ddb674362e73199c5.access"
CF_ACCESS_CLIENT_SECRET="c2190b8f11da9b3e8244e18e0a29225f23e70784561ef244867bbc4a2f925752"
CF_EMAIL="<EMAIL>"
CF_API_KEY="your_cloudflare_api_key"
CF_ACCOUNT_ID="14a6fa23390363382f378b5bd4a0f849"

# Request headers configuration
# Add any additional headers here as needed
USE_CF_ACCESS_HEADERS=1  # Set to 0 to disable Cloudflare Access headers
ADDITIONAL_HEADERS=(
  "User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
  "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
  "Accept-Language: en-US,en;q=0.5"
  "Connection: keep-alive"
)

# Function to load environment variables from file
load_env_var() {
  local var_name=$1
  local env_file=$2
  local default_value=${3:-""}

  if [ -f "$env_file" ]; then
    local value=$(grep "^$var_name=" "$env_file" | cut -d= -f2 || echo "")
    if [ -n "$value" ]; then
      echo "$value"
      return 0
    fi
  fi

  echo "$default_value"
  return 0
}

# Function to get the repository root directory
get_repo_root() {
  git -C "$(dirname "${BASH_SOURCE[0]}")" rev-parse --show-toplevel 2>/dev/null || echo "$(cd "$(dirname "${BASH_SOURCE[0]}")/../../../" && pwd)"
}

# Load environment-specific configuration if available
REPO_ROOT="$(get_repo_root)"
ENV_FILE="$REPO_ROOT/private-keys/${DEFAULT_ENVIRONMENT}/test.env"
COMBINED_ENV_FILE="$REPO_ROOT/private-keys/.combined-secrets.env"
LOCAL_CREDS_FILE="$(dirname "${BASH_SOURCE[0]}")/cloudflare-credentials.env"

# Try to load CF_ACCESS_CLIENT_SECRET from environment files
if [ -f "$ENV_FILE" ]; then
  echo "Loading credentials from $ENV_FILE"
  CF_ACCESS_CLIENT_SECRET=$(load_env_var "CF_ACCESS_CLIENT_SECRET" "$ENV_FILE" "$CF_ACCESS_CLIENT_SECRET")
  CF_EMAIL=$(load_env_var "CF_EMAIL" "$ENV_FILE" "$CF_EMAIL")
  CF_API_KEY=$(load_env_var "CF_API_KEY" "$ENV_FILE" "$CF_API_KEY")
  CF_ACCOUNT_ID=$(load_env_var "CF_ACCOUNT_ID" "$ENV_FILE" "$CF_ACCOUNT_ID")
fi

if [ -z "$CF_ACCESS_CLIENT_SECRET" ] && [ -f "$COMBINED_ENV_FILE" ]; then
  echo "Loading credentials from $COMBINED_ENV_FILE"
  CF_ACCESS_CLIENT_SECRET=$(load_env_var "CF_ACCESS_CLIENT_SECRET" "$COMBINED_ENV_FILE" "$CF_ACCESS_CLIENT_SECRET")
  CF_EMAIL=$(load_env_var "CF_EMAIL" "$COMBINED_ENV_FILE" "$CF_EMAIL")
  CF_API_KEY=$(load_env_var "CF_API_KEY" "$COMBINED_ENV_FILE" "$CF_API_KEY")
  CF_ACCOUNT_ID=$(load_env_var "CF_ACCOUNT_ID" "$COMBINED_ENV_FILE" "$CF_ACCOUNT_ID")
fi

if [ -z "$CF_ACCESS_CLIENT_SECRET" ] && [ -f "$LOCAL_CREDS_FILE" ]; then
  echo "Loading credentials from $LOCAL_CREDS_FILE"
  CF_ACCESS_CLIENT_SECRET=$(source "$LOCAL_CREDS_FILE" && echo "$CF_ACCESS_CLIENT_SECRET")
  CF_EMAIL=$(source "$LOCAL_CREDS_FILE" && echo "$CF_EMAIL")
  CF_API_KEY=$(source "$LOCAL_CREDS_FILE" && echo "$CF_API_KEY")
  CF_ACCOUNT_ID=$(source "$LOCAL_CREDS_FILE" && echo "$CF_ACCOUNT_ID")
fi

# Debug output for credentials
echo "Debug: CF_ACCESS_CLIENT_SECRET length: ${#CF_ACCESS_CLIENT_SECRET}"
echo "Debug: CF_EMAIL: $CF_EMAIL"
echo "Debug: CF_API_KEY length: ${#CF_API_KEY}"
echo "Debug: CF_ACCOUNT_ID: $CF_ACCOUNT_ID"

# Function to add Cloudflare Access headers to a curl command
add_cf_access_headers() {
  local curl_command="$1"
  
  if [ "$USE_CF_ACCESS_HEADERS" -eq 1 ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
    # Add Cloudflare Access headers
    curl_command="$curl_command -H \"CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID\" -H \"CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET\""
  fi
  
  # Add any additional headers
  for header in "${ADDITIONAL_HEADERS[@]}"; do
    if [ "${#ADDITIONAL_HEADERS[@]}" -gt 0 ] && [ -n "$header" ]; then
      curl_command="$curl_command -H \"$header\""
    fi
  done
  
  echo "$curl_command"
}

# Function to print request headers for documentation
print_request_headers() {
  if [ "$USE_CF_ACCESS_HEADERS" -eq 1 ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
    echo "Using Cloudflare Access headers:"
    echo "  CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID"
    echo "  CF-Access-Client-Secret: [Hidden]"
  else
    echo "Cloudflare Access headers: Disabled"
  fi
  
  if [ "${#ADDITIONAL_HEADERS[@]}" -gt 0 ]; then
    echo "Additional headers:"
    for header in "${ADDITIONAL_HEADERS[@]}"; do
      if [ -n "$header" ]; then
        echo "  $header"
      fi
    done
  else
    echo "Additional headers: None"
  fi
}

# Export all variables
export DEFAULT_DOMAIN
export DEFAULT_ORIGIN_IP
export DEFAULT_PORT
export DEFAULT_ENVIRONMENT
export DEFAULT_REGION
export DEFAULT_CAPTURE_DURATION
export CF_API_TOKEN
export CF_ACCESS_CLIENT_ID
export CF_ACCESS_CLIENT_SECRET
export CF_EMAIL
export CF_API_KEY
export CF_ACCOUNT_ID
export USE_CF_ACCESS_HEADERS
export ADDITIONAL_HEADERS