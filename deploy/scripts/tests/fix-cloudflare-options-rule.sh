#!/bin/bash
# Script to verify and update Cloudflare firewall rules for OPTIONS requests

# Source master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
CLOUDFLARE_ZONE_ID="9b26e2c415f36b0f656204133c8ab87c"
CLOUDFLARE_API_TOKEN="****************************************"
OUTPUT_DIR="$SCRIPT_DIR/test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== Verifying and Updating Cloudflare Firewall Rules ==="
echo "Zone ID: $CLOUDFLARE_ZONE_ID"
echo

# Function to check if OPTIONS rule exists
check_options_rule() {
  echo "📋 Checking current firewall rules..."
  curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/firewall/rules" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -H "Content-Type: application/json" > "$OUTPUT_DIR/firewall_rules.json"
  
  # Check for OPTIONS rule
  OPTIONS_RULE_ID=$(jq -r '.result[] | select(.description | contains("OPTIONS")) | .id' "$OUTPUT_DIR/firewall_rules.json")
  
  if [ -n "$OPTIONS_RULE_ID" ]; then
    echo "✅ OPTIONS rule found with ID: $OPTIONS_RULE_ID"
    # Check the action - should be "skip"
    OPTIONS_RULE_ACTION=$(jq -r ".result[] | select(.id == \"$OPTIONS_RULE_ID\") | .action" "$OUTPUT_DIR/firewall_rules.json")
    echo "  Action: $OPTIONS_RULE_ACTION"
    if [ "$OPTIONS_RULE_ACTION" != "skip" ]; then
      echo "⚠️ OPTIONS rule action is not 'skip'. Updating..."
      update_options_rule "$OPTIONS_RULE_ID"
    else
      echo "✅ OPTIONS rule is correctly configured with 'skip' action"
    fi
  else
    echo "❌ No OPTIONS rule found. Creating new rule..."
    create_options_rule
  fi
  
  # Check for mTLS rule that should exclude OPTIONS
  MTLS_RULE_ID=$(jq -r '.result[] | select(.description | contains("mTLS")) | .id' "$OUTPUT_DIR/firewall_rules.json")
  
  if [ -n "$MTLS_RULE_ID" ]; then
    echo "✅ mTLS rule found with ID: $MTLS_RULE_ID"
    # Check if it excludes OPTIONS
    MTLS_RULE_EXPRESSION=$(jq -r ".result[] | select(.id == \"$MTLS_RULE_ID\") | .filter.expression" "$OUTPUT_DIR/firewall_rules.json")
    if [[ $MTLS_RULE_EXPRESSION == *"http.request.method ne \"OPTIONS\""* ]]; then
      echo "✅ mTLS rule correctly excludes OPTIONS requests"
    else
      echo "⚠️ mTLS rule does not exclude OPTIONS requests. Updating..."
      update_mtls_rule "$MTLS_RULE_ID" "$MTLS_RULE_EXPRESSION"
    fi
  else
    echo "❌ No mTLS rule found. Please check your Cloudflare configuration."
  fi
}

# Function to update OPTIONS rule
update_options_rule() {
  local rule_id=$1
  echo "🔄 Updating OPTIONS rule ($rule_id) to 'skip' action..."
  
  curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/firewall/rules/$rule_id" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -H "Content-Type: application/json" \
    --data "{
      \"action\": \"skip\",
      \"description\": \"Allow All OPTIONs\",
      \"paused\": false
    }" > "$OUTPUT_DIR/update_options_rule_response.json"
  
  if jq -e '.success == true' "$OUTPUT_DIR/update_options_rule_response.json" > /dev/null; then
    echo "✅ OPTIONS rule updated successfully"
  else
    echo "❌ Failed to update OPTIONS rule"
    echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/update_options_rule_response.json")"
  fi
}

# Function to create OPTIONS rule
create_options_rule() {
  echo "🔄 Creating new OPTIONS rule..."
  
  # First, we need to create a filter for the rule
  curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/filters" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -H "Content-Type: application/json" \
    --data "[{
      \"expression\": \"(http.request.method eq \\\"OPTIONS\\\")\",
      \"paused\": false
    }]" > "$OUTPUT_DIR/create_filter_response.json"
  
  if jq -e '.[0].success == true' "$OUTPUT_DIR/create_filter_response.json" > /dev/null; then
    echo "✅ Filter for OPTIONS rule created successfully"
    FILTER_ID=$(jq -r '.[0].result.id' "$OUTPUT_DIR/create_filter_response.json")
    
    # Now create the rule using the filter
    curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/firewall/rules" \
      -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
      -H "Content-Type: application/json" \
      --data "[{
        \"filter\": {
          \"id\": \"$FILTER_ID\"
        },
        \"action\": \"skip\",
        \"description\": \"Allow All OPTIONs\",
        \"paused\": false
      }]" > "$OUTPUT_DIR/create_rule_response.json"
    
    if jq -e '.[0].success == true' "$OUTPUT_DIR/create_rule_response.json" > /dev/null; then
      echo "✅ OPTIONS rule created successfully"
    else
      echo "❌ Failed to create OPTIONS rule"
      echo "Error: $(jq -r '.[0].errors[0].message' "$OUTPUT_DIR/create_rule_response.json")"
    fi
  else
    echo "❌ Failed to create filter for OPTIONS rule"
    echo "Error: $(jq -r '.[0].errors[0].message' "$OUTPUT_DIR/create_filter_response.json")"
  fi
}

# Function to update mTLS rule to exclude OPTIONS
update_mtls_rule() {
  local rule_id=$1
  local current_expression=$2
  
  echo "🔄 Updating mTLS rule to exclude OPTIONS requests..."
  
  # Prepare the new expression
  NEW_EXPRESSION="(http.request.method ne \"OPTIONS\") and ($current_expression)"
  
  # Update the filter first
  FILTER_ID=$(jq -r ".result[] | select(.id == \"$rule_id\") | .filter.id" "$OUTPUT_DIR/firewall_rules.json")
  
  if [ -n "$FILTER_ID" ]; then
    curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/filters/$FILTER_ID" \
      -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
      -H "Content-Type: application/json" \
      --data "{
        \"expression\": \"$NEW_EXPRESSION\",
        \"paused\": false
      }" > "$OUTPUT_DIR/update_filter_response.json"
    
    if jq -e '.success == true' "$OUTPUT_DIR/update_filter_response.json" > /dev/null; then
      echo "✅ mTLS filter updated successfully to exclude OPTIONS requests"
    else
      echo "❌ Failed to update mTLS filter"
      echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/update_filter_response.json")"
    fi
  else
    echo "❌ Could not find filter ID for mTLS rule"
  fi
}

# Function to create transform rules for CORS headers
create_transform_rules() {
  echo "📋 Checking current transform rules..."
  curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/rulesets" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -H "Content-Type: application/json" > "$OUTPUT_DIR/transform_rules.json"
  
  # Check if transform ruleset exists
  TRANSFORM_RULESET_ID=$(jq -r '.result[] | select(.kind == "zone" and .phase == "http_response_headers_transform") | .id' "$OUTPUT_DIR/transform_rules.json")
  
  if [ -n "$TRANSFORM_RULESET_ID" ]; then
    echo "✅ Transform ruleset found with ID: $TRANSFORM_RULESET_ID"
    
    # Get the rules in the ruleset
    curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/rulesets/$TRANSFORM_RULESET_ID" \
      -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
      -H "Content-Type: application/json" > "$OUTPUT_DIR/transform_ruleset.json"
    
    # Check for OPTIONS CORS rule
    OPTIONS_RULE_ID=$(jq -r '.result.rules[] | select(.description | contains("OPTIONS")) | .id' "$OUTPUT_DIR/transform_ruleset.json")
    
    if [ -n "$OPTIONS_RULE_ID" ]; then
      echo "✅ OPTIONS CORS transform rule found"
    else
      echo "⚠️ No OPTIONS CORS transform rule found. Creating..."
      create_options_transform_rule "$TRANSFORM_RULESET_ID"
    fi
    
    # Check for regular CORS rule
    REGULAR_RULE_ID=$(jq -r '.result.rules[] | select(.description | contains("Regular")) | .id' "$OUTPUT_DIR/transform_ruleset.json")
    
    if [ -n "$REGULAR_RULE_ID" ]; then
      echo "✅ Regular CORS transform rule found"
    else
      echo "⚠️ No regular CORS transform rule found. Creating..."
      create_regular_transform_rule "$TRANSFORM_RULESET_ID"
    fi
  else
    echo "⚠️ No transform ruleset found. Creating new ruleset with CORS rules..."
    create_transform_ruleset
  fi
}

# Function to create OPTIONS transform rule
create_options_transform_rule() {
  local ruleset_id=$1
  
  echo "🔄 Creating OPTIONS CORS transform rule..."
  
  curl -s -X PATCH "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/rulesets/$ruleset_id" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -H "Content-Type: application/json" \
    --data "{
      \"rules\": [
        {
          \"action\": \"rewrite_response_headers\",
          \"action_parameters\": {
            \"headers\": [
              {
                \"name\": \"Access-Control-Allow-Origin\",
                \"operation\": \"set\",
                \"expression\": \"http.request.headers.origin\"
              },
              {
                \"name\": \"Access-Control-Allow-Methods\",
                \"operation\": \"set\",
                \"value\": \"GET, POST, PUT, DELETE, OPTIONS, PATCH\"
              },
              {
                \"name\": \"Access-Control-Allow-Headers\",
                \"operation\": \"set\",
                \"value\": \"Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization\"
              },
              {
                \"name\": \"Access-Control-Allow-Credentials\",
                \"operation\": \"set\",
                \"value\": \"true\"
              },
              {
                \"name\": \"Access-Control-Max-Age\",
                \"operation\": \"set\",
                \"value\": \"86400\"
              }
            ]
          },
          \"expression\": \"(http.request.method eq \\\"OPTIONS\\\")\",
          \"description\": \"Add CORS Headers for OPTIONS Requests\"
        }
      ]
    }" > "$OUTPUT_DIR/create_options_transform_rule_response.json"
  
  if jq -e '.success == true' "$OUTPUT_DIR/create_options_transform_rule_response.json" > /dev/null; then
    echo "✅ OPTIONS CORS transform rule created successfully"
  else
    echo "❌ Failed to create OPTIONS CORS transform rule"
    echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/create_options_transform_rule_response.json")"
  fi
}

# Function to create regular transform rule
create_regular_transform_rule() {
  local ruleset_id=$1
  
  echo "🔄 Creating regular CORS transform rule..."
  
  curl -s -X PATCH "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/rulesets/$ruleset_id" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -H "Content-Type: application/json" \
    --data "{
      \"rules\": [
        {
          \"action\": \"rewrite_response_headers\",
          \"action_parameters\": {
            \"headers\": [
              {
                \"name\": \"Access-Control-Allow-Origin\",
                \"operation\": \"set\",
                \"expression\": \"http.request.headers.origin\"
              },
              {
                \"name\": \"Access-Control-Allow-Credentials\",
                \"operation\": \"set\",
                \"value\": \"true\"
              }
            ]
          },
          \"expression\": \"true\",
          \"description\": \"Add CORS Headers for Regular Requests\"
        }
      ]
    }" > "$OUTPUT_DIR/create_regular_transform_rule_response.json"
  
  if jq -e '.success == true' "$OUTPUT_DIR/create_regular_transform_rule_response.json" > /dev/null; then
    echo "✅ Regular CORS transform rule created successfully"
  else
    echo "❌ Failed to create regular CORS transform rule"
    echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/create_regular_transform_rule_response.json")"
  fi
}

# Function to create transform ruleset
create_transform_ruleset() {
  echo "🔄 Creating new transform ruleset with CORS rules..."
  
  curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/rulesets" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -H "Content-Type: application/json" \
    --data "{
      \"name\": \"CORS Headers\",
      \"kind\": \"zone\",
      \"phase\": \"http_response_headers_transform\",
      \"rules\": [
        {
          \"action\": \"rewrite_response_headers\",
          \"action_parameters\": {
            \"headers\": [
              {
                \"name\": \"Access-Control-Allow-Origin\",
                \"operation\": \"set\",
                \"expression\": \"http.request.headers.origin\"
              },
              {
                \"name\": \"Access-Control-Allow-Methods\",
                \"operation\": \"set\",
                \"value\": \"GET, POST, PUT, DELETE, OPTIONS, PATCH\"
              },
              {
                \"name\": \"Access-Control-Allow-Headers\",
                \"operation\": \"set\",
                \"value\": \"Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization\"
              },
              {
                \"name\": \"Access-Control-Allow-Credentials\",
                \"operation\": \"set\",
                \"value\": \"true\"
              },
              {
                \"name\": \"Access-Control-Max-Age\",
                \"operation\": \"set\",
                \"value\": \"86400\"
              }
            ]
          },
          \"expression\": \"(http.request.method eq \\\"OPTIONS\\\")\",
          \"description\": \"Add CORS Headers for OPTIONS Requests\"
        },
        {
          \"action\": \"rewrite_response_headers\",
          \"action_parameters\": {
            \"headers\": [
              {
                \"name\": \"Access-Control-Allow-Origin\",
                \"operation\": \"set\",
                \"expression\": \"http.request.headers.origin\"
              },
              {
                \"name\": \"Access-Control-Allow-Credentials\",
                \"operation\": \"set\",
                \"value\": \"true\"
              }
            ]
          },
          \"expression\": \"true\",
          \"description\": \"Add CORS Headers for Regular Requests\"
        }
      ]
    }" > "$OUTPUT_DIR/create_transform_ruleset_response.json"
  
  if jq -e '.success == true' "$OUTPUT_DIR/create_transform_ruleset_response.json" > /dev/null; then
    echo "✅ Transform ruleset with CORS rules created successfully"
  else
    echo "❌ Failed to create transform ruleset"
    echo "Error: $(jq -r '.errors[0].message' "$OUTPUT_DIR/create_transform_ruleset_response.json")"
  fi
}

# Main execution
echo "📋 Checking and updating Cloudflare firewall rules..."
check_options_rule

echo
echo "📋 Checking and creating CORS transform rules..."
create_transform_rules

echo
echo "=== Cloudflare Rules Update Complete ==="
echo "Please run the CORS tests again to verify the changes"