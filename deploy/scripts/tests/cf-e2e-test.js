const puppeteer = require('puppeteer');

// You'll need to install puppeteer first:
// npm install puppeteer

// Replace these with your actual values if you have them
const CF_ACCESS_CLIENT_ID = process.env.CF_ACCESS_CLIENT_ID || "YOUR_CLIENT_ID";
const CF_ACCESS_CLIENT_SECRET = process.env.CF_ACCESS_CLIENT_SECRET || "YOUR_CLIENT_SECRET";
const DOMAIN = "chat.stage.divinci.app";

async function testWithCloudflareHeaders() {
  console.log(`Testing domain: ${DOMAIN} with Cloudflare Access headers`);
  
  // Launch browser
  const browser = await puppeteer.launch({
    headless: "new",
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Set extra headers to bypass Cloudflare Access
    await page.setExtraHTTPHeaders({
      'CF-Access-Client-Id': CF_ACCESS_CLIENT_ID,
      'CF-Access-Client-Secret': CF_ACCESS_CLIENT_SECRET
    });
    
    // Enable request interception to modify headers
    await page.setRequestInterception(true);
    
    // Add headers to all requests
    page.on('request', request => {
      const headers = request.headers();
      headers['CF-Access-Client-Id'] = CF_ACCESS_CLIENT_ID;
      headers['CF-Access-Client-Secret'] = CF_ACCESS_CLIENT_SECRET;
      request.continue({ headers });
    });
    
    // Capture response status codes
    page.on('response', response => {
      console.log(`URL: ${response.url()}, Status: ${response.status()}`);
    });
    
    // Navigate to the domain
    console.log(`Navigating to https://${DOMAIN}/`);
    await page.goto(`https://${DOMAIN}/`, { waitUntil: 'networkidle2' });
    
    // Check for error messages in the page content
    const content = await page.content();
    if (content.includes('Error 525')) {
      console.log('Error 525 detected in page content: SSL handshake failed');
    } else if (content.includes('Error 526')) {
      console.log('Error 526 detected in page content: Invalid SSL certificate');
    }
    
    // Take a screenshot of what we're seeing
    await page.screenshot({ path: 'cloudflare-access-test.png' });
    console.log('Screenshot saved to cloudflare-access-test.png');
    
    // Get the page title
    const title = await page.title();
    console.log(`Page title: ${title}`);
    
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    await browser.close();
  }
}

// Check for JWT cookie value in our API tests
async function checkWithExistingJWTCookie() {
  console.log('\nChecking with JWT cookie from a session...');
  
  // Replace this with an actual JWT from your browser session if available
  const JWT_COOKIE = process.env.CF_JWT_COOKIE || "";
  
  if (!JWT_COOKIE) {
    console.log('No JWT cookie provided. Skipping this test.');
    return;
  }
  
  // Launch browser
  const browser = await puppeteer.launch({
    headless: "new",
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Set the JWT cookie before navigating
    await page.setCookie({
      name: 'CF_Authorization',
      value: JWT_COOKIE,
      domain: DOMAIN,
      path: '/',
    });
    
    // Navigate to the domain
    console.log(`Navigating to https://${DOMAIN}/ with JWT cookie`);
    await page.goto(`https://${DOMAIN}/`, { waitUntil: 'networkidle2' });
    
    // Take a screenshot
    await page.screenshot({ path: 'cloudflare-jwt-test.png' });
    console.log('Screenshot saved to cloudflare-jwt-test.png');
    
  } catch (error) {
    console.error('Error during JWT test:', error);
  } finally {
    await browser.close();
  }
}

// Run both tests
async function runTests() {
  await testWithCloudflareHeaders();
  await checkWithExistingJWTCookie();
  console.log('All tests completed');
}

runTests();
