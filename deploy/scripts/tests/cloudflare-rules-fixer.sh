#!/bin/bash

# Cloudflare Rules Fixer
# This script automatically fixes common issues with Cloudflare rules
# It runs in a cycle with the analyzer to detect and fix issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ZONE_ID=""
API_TOKEN=""
ENVIRONMENT="staging"
MAX_ATTEMPTS=3
VERBOSE=false
DRY_RUN=false
REPORT_FILE="cloudflare-rules-report.md"

# Function to display usage information
usage() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --zone-id ID           Cloudflare Zone ID (required unless in env)"
  echo "  --api-token TOKEN      Cloudflare API Token (required unless in env)"
  echo "  --environment ENV      Environment to test (default: staging)"
  echo "  --max-attempts N       Maximum number of fix attempts (default: 3)"
  echo "  --report-file FILE     Path to output report file (default: cloudflare-rules-report.md)"
  echo "  --verbose              Enable verbose output"
  echo "  --dry-run              Don't make any actual changes, just show what would be done"
  echo "  --help                 Display this help message"
  echo ""
  echo "Environment variables:"
  echo "  CF_ZONE_ID             Cloudflare Zone ID"
  echo "  CF_API_TOKEN           Cloudflare API Token"
  echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --zone-id)
      ZONE_ID="$2"
      shift
      shift
      ;;
    --api-token)
      API_TOKEN="$2"
      shift
      shift
      ;;
    --environment)
      ENVIRONMENT="$2"
      shift
      shift
      ;;
    --max-attempts)
      MAX_ATTEMPTS="$2"
      shift
      shift
      ;;
    --report-file)
      REPORT_FILE="$2"
      shift
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    --help)
      usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      usage
      exit 1
      ;;
  esac
done

# Check for required dependencies
check_dependencies() {
  echo -e "${BLUE}Checking dependencies...${NC}"
  
  if ! command -v jq &> /dev/null; then
    echo -e "${RED}Error: jq is not installed. Please install jq to use this script.${NC}"
    echo "On macOS: brew install jq"
    echo "On Ubuntu/Debian: apt-get install jq"
    exit 1
  fi
  
  if ! command -v curl &> /dev/null; then
    echo -e "${RED}Error: curl is not installed. Please install curl to use this script.${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}All dependencies are installed.${NC}"
}

# Load environment variables
load_environment() {
  echo -e "${BLUE}Loading environment variables for ${ENVIRONMENT}...${NC}"
  
  # Try to load from environment file if it exists
  ENV_FILE="../../../private-keys/${ENVIRONMENT}/cloudflare.env"
  if [ -f "$ENV_FILE" ]; then
    echo "Loading from $ENV_FILE"
    source "$ENV_FILE"
  fi
  
  # Check for environment variables
  if [ -z "$ZONE_ID" ]; then
    ZONE_ID="$CF_ZONE_ID"
  fi
  
  if [ -z "$API_TOKEN" ]; then
    API_TOKEN="$CF_API_TOKEN"
  fi
  
  # Validate required variables
  if [ -z "$ZONE_ID" ]; then
    echo -e "${RED}Error: Cloudflare Zone ID is required. Set CF_ZONE_ID or use --zone-id.${NC}"
    exit 1
  fi
  
  if [ -z "$API_TOKEN" ]; then
    echo -e "${RED}Error: Cloudflare API Token is required. Set CF_API_TOKEN or use --api-token.${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}Environment variables loaded.${NC}"
}

# Function to find mTLS rules
find_mtls_rules() {
  echo -e "${BLUE}Finding mTLS rules...${NC}"
  
  # Get all rulesets
  echo "Fetching rulesets..."
  RULESETS=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets" \
    -H "Authorization: Bearer $API_TOKEN" \
    -H "Content-Type: application/json")
  
  # Check if the API call was successful
  if [ "$(echo "$RULESETS" | jq -r '.success')" != "true" ]; then
    echo -e "${RED}Error fetching rulesets:${NC}"
    echo "$RULESETS" | jq -r '.errors[]'
    return 1
  fi
  
  # Find rulesets with mTLS rules
  MTLS_RULESET_IDS=()
  MTLS_RULE_IDS=()
  
  for RULESET_ID in $(echo "$RULESETS" | jq -r '.result[].id'); do
    RULESET_DETAILS=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets/$RULESET_ID" \
      -H "Authorization: Bearer $API_TOKEN" \
      -H "Content-Type: application/json")
    
    # Check if the API call was successful
    if [ "$(echo "$RULESET_DETAILS" | jq -r '.success')" != "true" ]; then
      echo -e "${RED}Error fetching ruleset details:${NC}"
      echo "$RULESET_DETAILS" | jq -r '.errors[]'
      continue
    fi
    
    # Check for mTLS related rules
    MTLS_RULES=$(echo "$RULESET_DETAILS" | jq -r '.result.rules[] | select(.expression | contains("cf.tls_client_auth.cert_verified"))')
    
    if [ ! -z "$MTLS_RULES" ]; then
      echo -e "${GREEN}Found mTLS rules in ruleset $RULESET_ID${NC}"
      MTLS_RULESET_IDS+=("$RULESET_ID")
      
      # Extract rule IDs
      for RULE_ID in $(echo "$MTLS_RULES" | jq -r '.id'); do
        MTLS_RULE_IDS+=("$RULE_ID")
        echo -e "${GREEN}Found mTLS rule: $RULE_ID${NC}"
        
        # Get the rule expression
        RULE_EXPRESSION=$(echo "$MTLS_RULES" | jq -r "select(.id == \"$RULE_ID\") | .expression")
        echo -e "${BLUE}Rule expression: ${NC}$RULE_EXPRESSION"
        
        # Check if the rule already excludes OPTIONS requests
        if [[ "$RULE_EXPRESSION" == *"http.request.method ne \"OPTIONS\""* ]]; then
          echo -e "${GREEN}Rule already excludes OPTIONS requests${NC}"
        else
          echo -e "${YELLOW}Rule does not exclude OPTIONS requests - needs fixing${NC}"
        fi
      done
    fi
  done
  
  # Return the results
  if [ ${#MTLS_RULESET_IDS[@]} -eq 0 ]; then
    echo -e "${YELLOW}No mTLS rules found${NC}"
    return 1
  fi
  
  echo -e "${GREEN}Found ${#MTLS_RULESET_IDS[@]} rulesets with mTLS rules${NC}"
  return 0
}

# Function to fix mTLS rules
fix_mtls_rules() {
  echo -e "${BLUE}Fixing mTLS rules...${NC}"
  
  # Check if we have ruleset and rule IDs
  if [ ${#MTLS_RULESET_IDS[@]} -eq 0 ] || [ ${#MTLS_RULE_IDS[@]} -eq 0 ]; then
    echo -e "${YELLOW}No mTLS rules to fix${NC}"
    return 1
  fi
  
  # Process each rule
  for i in "${!MTLS_RULESET_IDS[@]}"; do
    RULESET_ID="${MTLS_RULESET_IDS[$i]}"
    RULE_ID="${MTLS_RULE_IDS[$i]}"
    
    echo -e "${BLUE}Processing rule $RULE_ID in ruleset $RULESET_ID${NC}"
    
    # Get the current rule details
    RULE_DETAILS=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets/$RULESET_ID" \
      -H "Authorization: Bearer $API_TOKEN" \
      -H "Content-Type: application/json" | jq -r ".result.rules[] | select(.id == \"$RULE_ID\")")
    
    # Extract the current expression
    CURRENT_EXPRESSION=$(echo "$RULE_DETAILS" | jq -r '.expression')
    
    # Check if the rule already excludes OPTIONS requests
    if [[ "$CURRENT_EXPRESSION" == *"http.request.method ne \"OPTIONS\""* ]]; then
      echo -e "${GREEN}Rule already excludes OPTIONS requests - no changes needed${NC}"
      continue
    fi
    
    # Create the new expression
    NEW_EXPRESSION="(http.request.method ne \"OPTIONS\") and ($CURRENT_EXPRESSION)"
    
    echo -e "${BLUE}Current expression: ${NC}$CURRENT_EXPRESSION"
    echo -e "${BLUE}New expression: ${NC}$NEW_EXPRESSION"
    
    # Extract other rule properties
    ACTION=$(echo "$RULE_DETAILS" | jq -r '.action')
    DESCRIPTION=$(echo "$RULE_DETAILS" | jq -r '.description // ""')
    
    # Create the update payload
    UPDATE_PAYLOAD=$(jq -n \
      --arg action "$ACTION" \
      --arg expression "$NEW_EXPRESSION" \
      --arg description "$DESCRIPTION" \
      '{
        "action": $action,
        "expression": $expression,
        "description": $description
      }')
    
    # If this is a dry run, just show what would be done
    if [ "$DRY_RUN" = true ]; then
      echo -e "${YELLOW}DRY RUN: Would update rule $RULE_ID with new expression${NC}"
      echo "$UPDATE_PAYLOAD" | jq '.'
      continue
    fi
    
    # Update the rule
    echo -e "${BLUE}Updating rule...${NC}"
    UPDATE_RESULT=$(curl -s -X PATCH "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets/$RULESET_ID/rules/$RULE_ID" \
      -H "Authorization: Bearer $API_TOKEN" \
      -H "Content-Type: application/json" \
      --data "$UPDATE_PAYLOAD")
    
    # Check if the update was successful
    if [ "$(echo "$UPDATE_RESULT" | jq -r '.success')" = "true" ]; then
      echo -e "${GREEN}Successfully updated rule $RULE_ID${NC}"
    else
      echo -e "${RED}Failed to update rule $RULE_ID:${NC}"
      echo "$UPDATE_RESULT" | jq -r '.errors[]'
    fi
  done
  
  return 0
}

# Function to check for Transform Rules
check_transform_rules() {
  echo -e "${BLUE}Checking for Transform Rules...${NC}"
  
  # Get Transform Rules
  TRANSFORM_RULES=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets/phases/http_response_headers_transform/entrypoint" \
    -H "Authorization: Bearer $API_TOKEN" \
    -H "Content-Type: application/json")
  
  # Check if the API call was successful
  if [ "$(echo "$TRANSFORM_RULES" | jq -r '.success')" != "true" ]; then
    echo -e "${RED}Error fetching Transform Rules:${NC}"
    echo "$TRANSFORM_RULES" | jq -r '.errors[]'
    return 1
  fi
  
  # Check if there are any rules
  if [ "$(echo "$TRANSFORM_RULES" | jq -r '.result')" = "null" ]; then
    echo -e "${YELLOW}No Transform Rules found.${NC}"
    TRANSFORM_RULESET_ID=""
    return 0
  fi
  
  # Get the ruleset ID
  TRANSFORM_RULESET_ID=$(echo "$TRANSFORM_RULES" | jq -r '.result.id')
  
  # Check for CORS-related Transform Rules
  OPTIONS_TRANSFORM_RULE=$(echo "$TRANSFORM_RULES" | jq -r '.result.rules[] | select(.expression | contains("OPTIONS"))')
  REGULAR_TRANSFORM_RULE=$(echo "$TRANSFORM_RULES" | jq -r '.result.rules[] | select(.expression == "true")')
  
  # Check if we have both rules
  if [ ! -z "$OPTIONS_TRANSFORM_RULE" ] && [ ! -z "$REGULAR_TRANSFORM_RULE" ]; then
    echo -e "${GREEN}Found both OPTIONS and regular Transform Rules${NC}"
    NEEDS_OPTIONS_RULE=false
    NEEDS_REGULAR_RULE=false
  elif [ ! -z "$OPTIONS_TRANSFORM_RULE" ]; then
    echo -e "${YELLOW}Found OPTIONS Transform Rule but missing regular Transform Rule${NC}"
    NEEDS_OPTIONS_RULE=false
    NEEDS_REGULAR_RULE=true
  elif [ ! -z "$REGULAR_TRANSFORM_RULE" ]; then
    echo -e "${YELLOW}Found regular Transform Rule but missing OPTIONS Transform Rule${NC}"
    NEEDS_OPTIONS_RULE=true
    NEEDS_REGULAR_RULE=false
  else
    echo -e "${YELLOW}No CORS Transform Rules found${NC}"
    NEEDS_OPTIONS_RULE=true
    NEEDS_REGULAR_RULE=true
  fi
  
  return 0
}

# Function to create Transform Rules
create_transform_rules() {
  echo -e "${BLUE}Creating Transform Rules...${NC}"
  
  # Check if we need to create any rules
  if [ "$NEEDS_OPTIONS_RULE" = false ] && [ "$NEEDS_REGULAR_RULE" = false ]; then
    echo -e "${GREEN}No Transform Rules need to be created${NC}"
    return 0
  fi
  
  # Check if we need to create a new ruleset
  if [ -z "$TRANSFORM_RULESET_ID" ]; then
    echo -e "${BLUE}Creating new Transform Rules ruleset...${NC}"
    
    # If this is a dry run, just show what would be done
    if [ "$DRY_RUN" = true ]; then
      echo -e "${YELLOW}DRY RUN: Would create new Transform Rules ruleset${NC}"
    else
      # Create the ruleset
      RULESET_PAYLOAD='{
        "name": "CORS Headers",
        "kind": "zone",
        "description": "Add CORS headers to responses",
        "phase": "http_response_headers_transform",
        "rules": []
      }'
      
      RULESET_RESULT=$(curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets" \
        -H "Authorization: Bearer $API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "$RULESET_PAYLOAD")
      
      # Check if the creation was successful
      if [ "$(echo "$RULESET_RESULT" | jq -r '.success')" = "true" ]; then
        echo -e "${GREEN}Successfully created Transform Rules ruleset${NC}"
        TRANSFORM_RULESET_ID=$(echo "$RULESET_RESULT" | jq -r '.result.id')
      else
        echo -e "${RED}Failed to create Transform Rules ruleset:${NC}"
        echo "$RULESET_RESULT" | jq -r '.errors[]'
        return 1
      fi
    fi
  fi
  
  # Create OPTIONS rule if needed
  if [ "$NEEDS_OPTIONS_RULE" = true ]; then
    echo -e "${BLUE}Creating OPTIONS Transform Rule...${NC}"
    
    # Create the rule payload
    OPTIONS_RULE_PAYLOAD='{
      "expression": "(http.request.method eq \"OPTIONS\")",
      "description": "Add CORS headers for OPTIONS requests",
      "action": "rewrite",
      "action_parameters": {
        "headers": [
          {
            "name": "Access-Control-Allow-Origin",
            "operation": "set",
            "value": "dynamic",
            "expression": "http.request.headers[\"Origin\"][0]"
          },
          {
            "name": "Access-Control-Allow-Methods",
            "operation": "set",
            "value": "GET, POST, PUT, DELETE, OPTIONS, PATCH"
          },
          {
            "name": "Access-Control-Allow-Headers",
            "operation": "set",
            "value": "Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization"
          },
          {
            "name": "Access-Control-Allow-Credentials",
            "operation": "set",
            "value": "true"
          },
          {
            "name": "Access-Control-Max-Age",
            "operation": "set",
            "value": "86400"
          }
        ]
      }
    }'
    
    # If this is a dry run, just show what would be done
    if [ "$DRY_RUN" = true ]; then
      echo -e "${YELLOW}DRY RUN: Would create OPTIONS Transform Rule${NC}"
      echo "$OPTIONS_RULE_PAYLOAD" | jq '.'
    else
      # Create the rule
      OPTIONS_RULE_RESULT=$(curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets/$TRANSFORM_RULESET_ID/rules" \
        -H "Authorization: Bearer $API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "$OPTIONS_RULE_PAYLOAD")
      
      # Check if the creation was successful
      if [ "$(echo "$OPTIONS_RULE_RESULT" | jq -r '.success')" = "true" ]; then
        echo -e "${GREEN}Successfully created OPTIONS Transform Rule${NC}"
      else
        echo -e "${RED}Failed to create OPTIONS Transform Rule:${NC}"
        echo "$OPTIONS_RULE_RESULT" | jq -r '.errors[]'
      fi
    fi
  fi
  
  # Create regular rule if needed
  if [ "$NEEDS_REGULAR_RULE" = true ]; then
    echo -e "${BLUE}Creating regular Transform Rule...${NC}"
    
    # Create the rule payload
    REGULAR_RULE_PAYLOAD='{
      "expression": "true",
      "description": "Add CORS headers for all responses",
      "action": "rewrite",
      "action_parameters": {
        "headers": [
          {
            "name": "Access-Control-Allow-Origin",
            "operation": "set",
            "value": "dynamic",
            "expression": "http.request.headers[\"Origin\"][0]"
          },
          {
            "name": "Access-Control-Allow-Credentials",
            "operation": "set",
            "value": "true"
          }
        ]
      }
    }'
    
    # If this is a dry run, just show what would be done
    if [ "$DRY_RUN" = true ]; then
      echo -e "${YELLOW}DRY RUN: Would create regular Transform Rule${NC}"
      echo "$REGULAR_RULE_PAYLOAD" | jq '.'
    else
      # Create the rule
      REGULAR_RULE_RESULT=$(curl -s -X POST "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets/$TRANSFORM_RULESET_ID/rules" \
        -H "Authorization: Bearer $API_TOKEN" \
        -H "Content-Type: application/json" \
        --data "$REGULAR_RULE_PAYLOAD")
      
      # Check if the creation was successful
      if [ "$(echo "$REGULAR_RULE_RESULT" | jq -r '.success')" = "true" ]; then
        echo -e "${GREEN}Successfully created regular Transform Rule${NC}"
      else
        echo -e "${RED}Failed to create regular Transform Rule:${NC}"
        echo "$REGULAR_RULE_RESULT" | jq -r '.errors[]'
      fi
    fi
  fi
  
  return 0
}

# Function to run tests
run_tests() {
  echo -e "${BLUE}Running tests...${NC}"
  
  # Run the network diagnostics
  ./run-network-diagnostics.sh --skip-mtls --skip-cloudflare --environment "$ENVIRONMENT"
  
  # Check if the tests passed
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}Tests passed!${NC}"
    return 0
  else
    echo -e "${RED}Tests failed!${NC}"
    return 1
  fi
}

# Function to generate a report
generate_report() {
  echo -e "${BLUE}Generating report...${NC}"
  
  # Create the report file
  cat > "$REPORT_FILE" << EOF
# Cloudflare Rules Analysis Report

## Summary

- **Environment:** $ENVIRONMENT
- **Date:** $(date)
- **Status:** ${1:-"Unknown"}

## Issues Found

${2:-"No issues found."}

## Actions Taken

${3:-"No actions taken."}

## Recommendations

${4:-"No recommendations."}

## Test Results

${5:-"No test results available."}
EOF
  
  echo -e "${GREEN}Report generated at $REPORT_FILE${NC}"
}

# Main function
main() {
  echo -e "${BLUE}=== Cloudflare Rules Fixer ===${NC}"
  echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
  echo -e "${BLUE}Max attempts: $MAX_ATTEMPTS${NC}"
  if [ "$DRY_RUN" = true ]; then
    echo -e "${YELLOW}DRY RUN MODE: No changes will be made${NC}"
  fi
  
  check_dependencies
  load_environment
  
  # Initialize variables for the report
  ISSUES_FOUND=""
  ACTIONS_TAKEN=""
  RECOMMENDATIONS=""
  TEST_RESULTS=""
  
  # Run the fix cycle
  for ((attempt=1; attempt<=MAX_ATTEMPTS; attempt++)); do
    echo -e "\n${BLUE}=== Attempt $attempt of $MAX_ATTEMPTS ===${NC}"
    
    # Find mTLS rules
    find_mtls_rules
    FOUND_MTLS=$?
    
    # Check for Transform Rules
    check_transform_rules
    
    # Update the issues found section
    if [ $FOUND_MTLS -eq 0 ]; then
      ISSUES_FOUND+="- Found mTLS rules that need to be updated to exclude OPTIONS requests\n"
    fi
    
    if [ "$NEEDS_OPTIONS_RULE" = true ] || [ "$NEEDS_REGULAR_RULE" = true ]; then
      ISSUES_FOUND+="- Missing CORS Transform Rules\n"
    fi
    
    # Fix the issues
    if [ $FOUND_MTLS -eq 0 ]; then
      fix_mtls_rules
      if [ "$DRY_RUN" = false ]; then
        ACTIONS_TAKEN+="- Updated mTLS rules to exclude OPTIONS requests\n"
      else
        ACTIONS_TAKEN+="- Would update mTLS rules to exclude OPTIONS requests (dry run)\n"
      fi
    fi
    
    if [ "$NEEDS_OPTIONS_RULE" = true ] || [ "$NEEDS_REGULAR_RULE" = true ]; then
      create_transform_rules
      if [ "$DRY_RUN" = false ]; then
        if [ "$NEEDS_OPTIONS_RULE" = true ]; then
          ACTIONS_TAKEN+="- Created Transform Rule for OPTIONS requests\n"
        fi
        if [ "$NEEDS_REGULAR_RULE" = true ]; then
          ACTIONS_TAKEN+="- Created Transform Rule for regular requests\n"
        fi
      else
        if [ "$NEEDS_OPTIONS_RULE" = true ]; then
          ACTIONS_TAKEN+="- Would create Transform Rule for OPTIONS requests (dry run)\n"
        fi
        if [ "$NEEDS_REGULAR_RULE" = true ]; then
          ACTIONS_TAKEN+="- Would create Transform Rule for regular requests (dry run)\n"
        fi
      fi
    fi
    
    # If this is a dry run, don't run the tests
    if [ "$DRY_RUN" = true ]; then
      echo -e "${YELLOW}DRY RUN: Skipping tests${NC}"
      TEST_RESULTS="Tests skipped (dry run)"
      break
    fi
    
    # Run the tests
    echo -e "\n${BLUE}=== Running Tests ===${NC}"
    run_tests
    TEST_RESULT=$?
    
    # If the tests passed, we're done
    if [ $TEST_RESULT -eq 0 ]; then
      echo -e "${GREEN}Tests passed! All issues fixed.${NC}"
      TEST_RESULTS="All tests passed after attempt $attempt"
      break
    fi
    
    # If this was the last attempt, we failed
    if [ $attempt -eq $MAX_ATTEMPTS ]; then
      echo -e "${RED}Failed to fix all issues after $MAX_ATTEMPTS attempts.${NC}"
      TEST_RESULTS="Tests failed after $MAX_ATTEMPTS attempts"
    else
      echo -e "${YELLOW}Tests failed. Trying again...${NC}"
      TEST_RESULTS+="- Attempt $attempt: Tests failed\n"
    fi
  done
  
  # Add recommendations
  if [ "$TEST_RESULT" -ne 0 ]; then
    RECOMMENDATIONS+="- Manually check the Cloudflare rules for issues\n"
    RECOMMENDATIONS+="- Ensure that mTLS rules exclude OPTIONS requests\n"
    RECOMMENDATIONS+="- Ensure that Transform Rules are properly configured for CORS\n"
  fi
  
  # Generate the report
  STATUS="Failed"
  if [ "$TEST_RESULT" -eq 0 ]; then
    STATUS="Success"
  elif [ "$DRY_RUN" = true ]; then
    STATUS="Dry Run"
  fi
  
  generate_report "$STATUS" "$ISSUES_FOUND" "$ACTIONS_TAKEN" "$RECOMMENDATIONS" "$TEST_RESULTS"
  
  echo -e "\n${GREEN}Done.${NC}"
}

# Run the main function
main
