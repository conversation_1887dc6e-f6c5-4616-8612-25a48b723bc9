#!/bin/bash
# tcpdump TLS Capture
# This script uses tcpdump to capture TLS handshake packets for detailed analysis.

set -e

# Source master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
DOMAIN=${1:-"$DEFAULT_DOMAIN"}
ORIGIN_IP=${2:-"$DEFAULT_ORIGIN_IP"}
PORT=${3:-"$DEFAULT_PORT"}
CAPTURE_DURATION=${4:-"$DEFAULT_CAPTURE_DURATION"}
CF_ACCESS_CLIENT_ID=${5:-"$CF_ACCESS_CLIENT_ID"}
CF_ACCESS_CLIENT_SECRET=${6:-"$CF_ACCESS_CLIENT_SECRET"}
NO_SUDO=0

# Check if --no-sudo option is provided
for arg in "$@"; do
  if [ "$arg" == "--no-sudo" ]; then
    NO_SUDO=1
    echo "Running in no-sudo mode"
  fi
done

# Auto-detect if we can use sudo without a terminal
if ! sudo -n true 2>/dev/null; then
  echo "Cannot use sudo without a terminal, switching to no-sudo mode"
  NO_SUDO=1
fi

# Set up output directory
OUTPUT_DIR="$SCRIPT_DIR/test-results"

# Create output directory with proper permissions
mkdir -p "$OUTPUT_DIR"
chmod 777 "$OUTPUT_DIR"

echo "=== tcpdump TLS Capture ==="
echo "Domain: $DOMAIN"
echo "Origin IP: $ORIGIN_IP"
echo "Port: $PORT"
echo "Capture Duration: $CAPTURE_DURATION seconds"
echo "CF Access Client ID: $CF_ACCESS_CLIENT_ID"
if [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
  echo "CF Access Client Secret: Provided (hidden)"
else
  echo "CF Access Client Secret: Not provided"
fi
echo

# Check if tcpdump is installed
if ! command -v tcpdump &> /dev/null; then
  echo "❌ tcpdump is not installed. Please install it with:"
  echo "   sudo apt-get install tcpdump   # Debian/Ubuntu"
  echo "   sudo yum install tcpdump       # CentOS/RHEL"
  echo "   brew install tcpdump           # macOS with Homebrew"
  exit 1
fi

# Step 1: Capture TLS handshake packets
echo "Step 1: Capturing TLS handshake packets..."
echo "Starting capture for $CAPTURE_DURATION seconds..."

# Start a background curl in a separate terminal to trigger the TLS handshake
if [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
  echo "Using Cloudflare Access headers for authentication..."
  (curl -s -v "https://$DOMAIN/" \
    -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
    -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
    > /dev/null 2>&1) &
else
  echo "No Cloudflare Access credentials provided, using standard request..."
  (curl -s -v "https://$DOMAIN/" > /dev/null 2>&1) &
fi

# Capture the TLS handshake packets
if [ $NO_SUDO -eq 1 ]; then
  echo "Attempting to capture packets without sudo (may fail)..."
  # Try to use tcpdump without sudo
  if tcpdump -i any -n -s 0 -w "$OUTPUT_DIR/tls_handshake.pcap" "host $ORIGIN_IP and tcp port $PORT" 2>/dev/null & then
    TCPDUMP_PID=$!
    echo "Started tcpdump without sudo, PID: $TCPDUMP_PID"
  else
    echo "❌ Failed to start tcpdump without sudo"
    echo "Creating a dummy capture file for analysis..."
    touch "$OUTPUT_DIR/tls_handshake.pcap"
    # Skip the actual capture but continue with the script
    SKIP_ANALYSIS=1
  fi
else
  # Use sudo
  sudo tcpdump -i any -n -s 0 -w "$OUTPUT_DIR/tls_handshake.pcap" "host $ORIGIN_IP and tcp port $PORT" &
  TCPDUMP_PID=$!
  echo "Started tcpdump with sudo, PID: $TCPDUMP_PID"
fi

# Wait for the specified duration if we started tcpdump
if [ -z "$SKIP_ANALYSIS" ]; then
  echo "Capturing packets for $CAPTURE_DURATION seconds..."
  sleep $CAPTURE_DURATION

  # Stop tcpdump
  if [ $NO_SUDO -eq 1 ]; then
    kill -TERM $TCPDUMP_PID 2>/dev/null || true
  else
    sudo kill -TERM $TCPDUMP_PID 2>/dev/null || true
  fi
  wait $TCPDUMP_PID 2>/dev/null || true
  echo "✅ Capture completed and saved to $OUTPUT_DIR/tls_handshake.pcap"
else
  echo "⚠️ Skipped packet capture, using dummy file"
fi

# Function to run tcpdump with or without sudo
run_tcpdump() {
  local args="$1"
  local output_file="$2"
  local grep_pattern="$3"

  # Ensure output file has proper permissions
  touch "$output_file"
  chmod 666 "$output_file"

  if [ -n "$SKIP_ANALYSIS" ]; then
    echo "⚠️ Skipping analysis due to missing capture file"
    echo "No data available - capture was skipped" > "$output_file"
    return 0
  fi

  if [ $NO_SUDO -eq 1 ]; then
    # Try without sudo
    if ! tcpdump $args 2>/dev/null | grep $grep_pattern > "$output_file"; then
      echo "⚠️ Failed to analyze with tcpdump without sudo"
      echo "Analysis failed - try running with sudo" > "$output_file"
    fi
  else
    # Use sudo
    # Create a temporary file for sudo output
    local temp_file=$(mktemp)
    sudo tcpdump $args | grep $grep_pattern > "$temp_file"
    # Copy the content to the output file
    cat "$temp_file" > "$output_file"
    # Remove the temporary file
    rm -f "$temp_file"
  fi
}

# Step 2: Analyze the captured packets
echo "Step 2: Analyzing the captured packets..."
run_tcpdump "-r \"$OUTPUT_DIR/tls_handshake.pcap\" -n -A" "$OUTPUT_DIR/tls_handshake_analysis.txt" "-A 10 -B 10 \"TLS\""
echo "✅ Analysis saved to $OUTPUT_DIR/tls_handshake_analysis.txt"

# Step 3: Extract TLS Client Hello
echo "Step 3: Extracting TLS Client Hello..."
run_tcpdump "-r \"$OUTPUT_DIR/tls_handshake.pcap\" -n -A" "$OUTPUT_DIR/tls_client_hello.txt" "-A 20 \"Client Hello\""
echo "✅ TLS Client Hello saved to $OUTPUT_DIR/tls_client_hello.txt"

# Step 4: Extract TLS Server Hello
echo "Step 4: Extracting TLS Server Hello..."
run_tcpdump "-r \"$OUTPUT_DIR/tls_handshake.pcap\" -n -A" "$OUTPUT_DIR/tls_server_hello.txt" "-A 20 \"Server Hello\""
echo "✅ TLS Server Hello saved to $OUTPUT_DIR/tls_server_hello.txt"

# Step 5: Extract TLS Certificate
echo "Step 5: Extracting TLS Certificate..."
run_tcpdump "-r \"$OUTPUT_DIR/tls_handshake.pcap\" -n -A" "$OUTPUT_DIR/tls_certificate.txt" "-A 50 \"Certificate\""
echo "✅ TLS Certificate saved to $OUTPUT_DIR/tls_certificate.txt"

# Step 6: Check for TLS errors
echo "Step 6: Checking for TLS errors..."
if [ -n "$SKIP_ANALYSIS" ]; then
  TLS_ERRORS="Analysis skipped"
else
  if [ $NO_SUDO -eq 1 ]; then
    TLS_ERRORS=$(tcpdump -r "$OUTPUT_DIR/tls_handshake.pcap" -n -A 2>/dev/null | grep -i "error\|alert\|warning\|fail" || echo "")
  else
    TLS_ERRORS=$(sudo tcpdump -r "$OUTPUT_DIR/tls_handshake.pcap" -n -A | grep -i "error\|alert\|warning\|fail" || echo "")
  fi
fi

if [ -n "$TLS_ERRORS" ]; then
  echo "❌ TLS errors found:"
  echo "$TLS_ERRORS" > "$OUTPUT_DIR/tls_errors.txt"
  echo "$TLS_ERRORS"
else
  echo "✅ No TLS errors found"
fi

# Step 7: Generate a summary
echo "Step 7: Generating a summary..."
echo "TLS Handshake Summary:" > "$OUTPUT_DIR/tls_summary.txt"
echo "------------------------" >> "$OUTPUT_DIR/tls_summary.txt"
echo "Domain: $DOMAIN" >> "$OUTPUT_DIR/tls_summary.txt"
echo "Origin IP: $ORIGIN_IP" >> "$OUTPUT_DIR/tls_summary.txt"
echo "Port: $PORT" >> "$OUTPUT_DIR/tls_summary.txt"
echo "Capture Duration: $CAPTURE_DURATION seconds" >> "$OUTPUT_DIR/tls_summary.txt"
echo "No Sudo Mode: $([ $NO_SUDO -eq 1 ] && echo "Yes" || echo "No")" >> "$OUTPUT_DIR/tls_summary.txt"
echo "CF Access Authentication: $([ -n "$CF_ACCESS_CLIENT_SECRET" ] && echo "Yes" || echo "No")" >> "$OUTPUT_DIR/tls_summary.txt"
echo "------------------------" >> "$OUTPUT_DIR/tls_summary.txt"

if [ -n "$SKIP_ANALYSIS" ]; then
  echo "Analysis skipped - no packet capture available" >> "$OUTPUT_DIR/tls_summary.txt"
else
  # Count packets
  if [ $NO_SUDO -eq 1 ]; then
    PACKET_COUNT=$(tcpdump -r "$OUTPUT_DIR/tls_handshake.pcap" -n 2>/dev/null | wc -l || echo "0")
  else
    PACKET_COUNT=$(sudo tcpdump -r "$OUTPUT_DIR/tls_handshake.pcap" -n | wc -l)
  fi
  echo "Total Packets: $PACKET_COUNT" >> "$OUTPUT_DIR/tls_summary.txt"

  # Check for TLS versions
  if [ $NO_SUDO -eq 1 ]; then
    TLS_VERSIONS=$(tcpdump -r "$OUTPUT_DIR/tls_handshake.pcap" -n -A 2>/dev/null | grep -o "TLS.*" | sort | uniq || echo "None detected")
  else
    TLS_VERSIONS=$(sudo tcpdump -r "$OUTPUT_DIR/tls_handshake.pcap" -n -A | grep -o "TLS.*" | sort | uniq || echo "None detected")
  fi
  echo "TLS Versions:" >> "$OUTPUT_DIR/tls_summary.txt"
  echo "$TLS_VERSIONS" >> "$OUTPUT_DIR/tls_summary.txt"

  # Check for cipher suites
  if [ $NO_SUDO -eq 1 ]; then
    CIPHER_SUITES=$(tcpdump -r "$OUTPUT_DIR/tls_handshake.pcap" -n -A 2>/dev/null | grep -o "Cipher Suite:.*" | sort | uniq || echo "None detected")
  else
    CIPHER_SUITES=$(sudo tcpdump -r "$OUTPUT_DIR/tls_handshake.pcap" -n -A | grep -o "Cipher Suite:.*" | sort | uniq || echo "None detected")
  fi
  echo "Cipher Suites:" >> "$OUTPUT_DIR/tls_summary.txt"
  echo "$CIPHER_SUITES" >> "$OUTPUT_DIR/tls_summary.txt"
fi

echo "✅ Summary saved to $OUTPUT_DIR/tls_summary.txt"

echo
echo "=== tcpdump TLS Capture Complete ==="
echo "Results saved to $OUTPUT_DIR/"
