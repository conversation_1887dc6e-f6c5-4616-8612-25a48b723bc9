#!/bin/bash
# nmap SSL/TLS Scan
# This script uses nmap to scan SSL/TLS configurations and vulnerabilities.

set -e

# Configuration
DOMAIN=${1:-"chat.stage.divinci.app"}
ORIGIN_IP=${2:-"************"}
PORT=${3:-"443"}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OUTPUT_DIR="$SCRIPT_DIR/test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== nmap SSL/TLS Scan ==="
echo "Domain: $DOMAIN"
echo "Origin IP: $ORIGIN_IP"
echo "Port: $PORT"
echo

# Check if nmap is installed
if ! command -v nmap &> /dev/null; then
  echo "❌ nmap is not installed. Please install it with:"
  echo "   sudo apt-get install nmap   # Debian/Ubuntu"
  echo "   sudo yum install nmap       # CentOS/RHEL"
  echo "   brew install nmap           # macOS with Homebrew"
  exit 1
fi

# Step 1: Basic SSL/TLS scan
echo "Step 1: Running basic SSL/TLS scan..."
nmap --script ssl-enum-ciphers -p $PORT $ORIGIN_IP > "$OUTPUT_DIR/nmap_ssl_ciphers.txt"
echo "✅ Basic SSL/TLS scan completed and saved to $OUTPUT_DIR/nmap_ssl_ciphers.txt"

# Step 2: Check for SSL/TLS vulnerabilities
echo "Step 2: Checking for SSL/TLS vulnerabilities..."
nmap --script "ssl-*" -p $PORT $ORIGIN_IP > "$OUTPUT_DIR/nmap_ssl_vulnerabilities.txt"
echo "✅ SSL/TLS vulnerability scan completed and saved to $OUTPUT_DIR/nmap_ssl_vulnerabilities.txt"

# Step 3: Check for Heartbleed vulnerability
echo "Step 3: Checking for Heartbleed vulnerability..."
nmap --script ssl-heartbleed -p $PORT $ORIGIN_IP > "$OUTPUT_DIR/nmap_heartbleed.txt"
echo "✅ Heartbleed vulnerability scan completed and saved to $OUTPUT_DIR/nmap_heartbleed.txt"

# Check if Heartbleed is vulnerable
if grep -q "State: VULNERABLE" "$OUTPUT_DIR/nmap_heartbleed.txt"; then
  echo "❌ Server is vulnerable to Heartbleed"
else
  echo "✅ Server is not vulnerable to Heartbleed"
fi

# Step 4: Check for POODLE vulnerability
echo "Step 4: Checking for POODLE vulnerability..."
nmap --script ssl-poodle -p $PORT $ORIGIN_IP > "$OUTPUT_DIR/nmap_poodle.txt"
echo "✅ POODLE vulnerability scan completed and saved to $OUTPUT_DIR/nmap_poodle.txt"

# Check if POODLE is vulnerable
if grep -q "State: VULNERABLE" "$OUTPUT_DIR/nmap_poodle.txt"; then
  echo "❌ Server is vulnerable to POODLE"
else
  echo "✅ Server is not vulnerable to POODLE"
fi

# Step 5: Check for certificate information
echo "Step 5: Checking certificate information..."
nmap --script ssl-cert -p $PORT $ORIGIN_IP > "$OUTPUT_DIR/nmap_ssl_cert.txt"
echo "✅ Certificate information scan completed and saved to $OUTPUT_DIR/nmap_ssl_cert.txt"

# Extract certificate details
CERT_SUBJECT=$(grep "Subject:" "$OUTPUT_DIR/nmap_ssl_cert.txt")
CERT_ISSUER=$(grep "Issuer:" "$OUTPUT_DIR/nmap_ssl_cert.txt")
CERT_VALIDITY=$(grep -A 2 "Not valid" "$OUTPUT_DIR/nmap_ssl_cert.txt")

echo "Certificate Subject: $CERT_SUBJECT"
echo "Certificate Issuer: $CERT_ISSUER"
echo "Certificate Validity: $CERT_VALIDITY"

# Step 6: Check for TLS version support
echo "Step 6: Checking TLS version support..."
TLS_VERSIONS=$(grep "TLSv" "$OUTPUT_DIR/nmap_ssl_ciphers.txt")
echo "$TLS_VERSIONS" > "$OUTPUT_DIR/nmap_tls_versions.txt"
echo "✅ TLS version support check completed and saved to $OUTPUT_DIR/nmap_tls_versions.txt"

# Check for TLSv1.2 and TLSv1.3 support
if echo "$TLS_VERSIONS" | grep -q "TLSv1.2"; then
  echo "✅ TLSv1.2 is supported"
else
  echo "❌ TLSv1.2 is not supported"
fi

if echo "$TLS_VERSIONS" | grep -q "TLSv1.3"; then
  echo "✅ TLSv1.3 is supported"
else
  echo "❌ TLSv1.3 is not supported"
fi

# Step 7: Generate a summary
echo "Step 7: Generating a summary..."
echo "nmap SSL/TLS Scan Summary:" > "$OUTPUT_DIR/nmap_summary.txt"
echo "------------------------" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "Domain: $DOMAIN" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "Origin IP: $ORIGIN_IP" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "Port: $PORT" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "------------------------" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "Certificate Subject: $CERT_SUBJECT" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "Certificate Issuer: $CERT_ISSUER" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "Certificate Validity: $CERT_VALIDITY" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "------------------------" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "TLS Versions:" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "$TLS_VERSIONS" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "------------------------" >> "$OUTPUT_DIR/nmap_summary.txt"
echo "Vulnerabilities:" >> "$OUTPUT_DIR/nmap_summary.txt"

# Check for vulnerabilities
VULNERABILITIES=""
if grep -q "State: VULNERABLE" "$OUTPUT_DIR/nmap_ssl_vulnerabilities.txt"; then
  VULNERABILITIES=$(grep -A 2 "State: VULNERABLE" "$OUTPUT_DIR/nmap_ssl_vulnerabilities.txt")
  echo "$VULNERABILITIES" >> "$OUTPUT_DIR/nmap_summary.txt"
  echo "❌ Vulnerabilities found"
else
  echo "No vulnerabilities found" >> "$OUTPUT_DIR/nmap_summary.txt"
  echo "✅ No vulnerabilities found"
fi

echo "✅ Summary saved to $OUTPUT_DIR/nmap_summary.txt"

echo
echo "=== nmap SSL/TLS Scan Complete ==="
echo "Results saved to $OUTPUT_DIR/"
