#!/bin/bash

# Test CORS Fixes
# This script tests if the CORS fixes have been successfully implemented

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DOMAIN="api.stage.divinci.app"
ORIGIN="https://chat.stage.divinci.app"
VERBOSE=false

# Function to display usage information
usage() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --domain DOMAIN        Domain to test (default: api.stage.divinci.app)"
  echo "  --origin ORIGIN        Origin to use for CORS tests (default: https://chat.stage.divinci.app)"
  echo "  --verbose              Enable verbose output"
  echo "  --help                 Display this help message"
  echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --domain)
      DOMAIN="$2"
      shift
      shift
      ;;
    --origin)
      ORIGIN="$2"
      shift
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    --help)
      usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      usage
      exit 1
      ;;
  esac
done

# Function to test OPTIONS request
test_options_request() {
  echo -e "${BLUE}Testing OPTIONS preflight request to $DOMAIN...${NC}"
  
  # Build curl command
  CURL_CMD="curl -s -i -L -X OPTIONS \"https://$DOMAIN/ai-chat/trending\" -H \"Origin: $ORIGIN\" -H \"Access-Control-Request-Method: GET\" -H \"Access-Control-Request-Headers: Content-Type, Authorization\""
  
  if [ "$VERBOSE" = true ]; then
    CURL_CMD="curl -v -L -X OPTIONS \"https://$DOMAIN/ai-chat/trending\" -H \"Origin: $ORIGIN\" -H \"Access-Control-Request-Method: GET\" -H \"Access-Control-Request-Headers: Content-Type, Authorization\""
    echo "Running: $CURL_CMD"
  fi
  
  # Run the command
  OPTIONS_RESPONSE=$(eval $CURL_CMD)
  
  # Save the response to a file for easier processing
  echo "$OPTIONS_RESPONSE" > options_response.txt
  
  # Extract status code - manually check for specific codes in the output
  if grep -q "HTTP/2 503" options_response.txt; then
    STATUS_CODE="503"
  elif grep -q "HTTP/2 204" options_response.txt; then
    STATUS_CODE="204"
  elif grep -q "HTTP/2 200" options_response.txt; then
    STATUS_CODE="200"
  elif grep -q "HTTP/2 301" options_response.txt; then
    STATUS_CODE="301"
  else
    STATUS_CODE="unknown"
  fi
  echo -e "${BLUE}Status code: $STATUS_CODE${NC}"
  
  # Check for success
  if [[ "$STATUS_CODE" == "204" || "$STATUS_CODE" == "200" ]]; then
    echo -e "${GREEN}OPTIONS request successful!${NC}"
  elif [[ "$STATUS_CODE" == "301" ]]; then
    echo -e "${YELLOW}OPTIONS request redirected (301)${NC}"
  else
    echo -e "${RED}OPTIONS request failed with status code $STATUS_CODE${NC}"
  fi
  
  # Check for CORS headers
  CORS_HEADERS=$(grep -i "Access-Control-" options_response.txt)
  
  if [ ! -z "$CORS_HEADERS" ]; then
    echo -e "${GREEN}CORS headers found:${NC}"
    echo "$CORS_HEADERS"
    
    # Check for specific headers
    if grep -q "Access-Control-Allow-Origin" options_response.txt; then
      echo -e "${GREEN}✓ Access-Control-Allow-Origin header present${NC}"
    else
      echo -e "${RED}✗ Access-Control-Allow-Origin header missing${NC}"
    fi
    
    if grep -q "Access-Control-Allow-Methods" options_response.txt; then
      echo -e "${GREEN}✓ Access-Control-Allow-Methods header present${NC}"
    else
      echo -e "${RED}✗ Access-Control-Allow-Methods header missing${NC}"
    fi
    
    if grep -q "Access-Control-Allow-Headers" options_response.txt; then
      echo -e "${GREEN}✓ Access-Control-Allow-Headers header present${NC}"
    else
      echo -e "${RED}✗ Access-Control-Allow-Headers header missing${NC}"
    fi
    
    if grep -q "Access-Control-Allow-Credentials" options_response.txt; then
      echo -e "${GREEN}✓ Access-Control-Allow-Credentials header present${NC}"
    else
      echo -e "${RED}✗ Access-Control-Allow-Credentials header missing${NC}"
    fi
  else
    echo -e "${RED}No CORS headers found in the response${NC}"
  fi
  
  # If verbose, show full response
  if [ "$VERBOSE" = true ]; then
    echo -e "${BLUE}Full response:${NC}"
    cat options_response.txt
  fi
  
  # Clean up
  rm options_response.txt
}

# Function to test regular GET request
test_get_request() {
  echo -e "\n${BLUE}Testing regular GET request to $DOMAIN...${NC}"
  
  # Build curl command
  CURL_CMD="curl -s -i -L -X GET \"https://$DOMAIN/ai-chat/trending\" -H \"Origin: $ORIGIN\""
  
  if [ "$VERBOSE" = true ]; then
    CURL_CMD="curl -v -L -X GET \"https://$DOMAIN/ai-chat/trending\" -H \"Origin: $ORIGIN\""
    echo "Running: $CURL_CMD"
  fi
  
  # Run the command
  GET_RESPONSE=$(eval $CURL_CMD)
  
  # Save the response to a file for easier processing
  echo "$GET_RESPONSE" > get_response.txt
  
  # Extract status code - manually check for specific codes in the output
  if grep -q "HTTP/2 200" get_response.txt; then
    STATUS_CODE="200"
  elif grep -q "HTTP/2 403" get_response.txt; then
    STATUS_CODE="403"
  elif grep -q "HTTP/2 503" get_response.txt; then
    STATUS_CODE="503"
  elif grep -q "HTTP/2 301" get_response.txt; then
    STATUS_CODE="301"
  else
    STATUS_CODE="unknown"
  fi
  echo -e "${BLUE}Status code: $STATUS_CODE${NC}"
  
  # Check for success
  if [[ "$STATUS_CODE" == "200" ]]; then
    echo -e "${GREEN}GET request successful!${NC}"
  elif [[ "$STATUS_CODE" == "301" ]]; then
    echo -e "${YELLOW}GET request redirected (301)${NC}"
  else
    echo -e "${RED}GET request failed with status code $STATUS_CODE${NC}"
  fi
  
  # Check for CORS headers
  CORS_HEADERS=$(grep -i "Access-Control-" get_response.txt)
  
  if [ ! -z "$CORS_HEADERS" ]; then
    echo -e "${GREEN}CORS headers found:${NC}"
    echo "$CORS_HEADERS"
    
    # Check for specific headers
    if grep -q "Access-Control-Allow-Origin" get_response.txt; then
      echo -e "${GREEN}✓ Access-Control-Allow-Origin header present${NC}"
    else
      echo -e "${RED}✗ Access-Control-Allow-Origin header missing${NC}"
    fi
    
    if grep -q "Access-Control-Allow-Credentials" get_response.txt; then
      echo -e "${GREEN}✓ Access-Control-Allow-Credentials header present${NC}"
    else
      echo -e "${RED}✗ Access-Control-Allow-Credentials header missing${NC}"
    fi
  else
    echo -e "${RED}No CORS headers found in the response${NC}"
  fi
  
  # If verbose, show full response
  if [ "$VERBOSE" = true ]; then
    echo -e "${BLUE}Full response:${NC}"
    cat get_response.txt
  fi
  
  # Clean up
  rm get_response.txt
}

# Main function
main() {
  echo -e "${BLUE}=== Testing CORS Fixes ===${NC}"
  echo -e "${BLUE}Domain: $DOMAIN${NC}"
  echo -e "${BLUE}Origin: $ORIGIN${NC}"
  
  # Test OPTIONS request
  test_options_request
  
  # Test GET request
  test_get_request
  
  echo -e "\n${GREEN}Tests completed.${NC}"
}

# Run the main function
main
