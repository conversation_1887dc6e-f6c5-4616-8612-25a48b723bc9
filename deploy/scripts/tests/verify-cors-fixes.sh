#!/bin/bash
# Script to verify CORS fixes are working correctly

# Source master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
DOMAIN="api.stage.divinci.app"
ORIGIN="https://chat.stage.divinci.app"
ENDPOINTS=(
  "/health"
  "/ai-chat/trending"
  "/api/v1/health"
)
OUTPUT_DIR="$SCRIPT_DIR/test-results/cors-verification"
mkdir -p "$OUTPUT_DIR"

echo "=== Verifying CORS Fixes ==="
echo "API Domain: $DOMAIN"
echo "Origin: $ORIGIN"
echo "Testing endpoints: ${ENDPOINTS[*]}"
echo

# Function to test OPTIONS request
test_options_request() {
  local endpoint=$1
  local test_num=$2
  
  echo "Test $test_num: OPTIONS request to $endpoint"
  echo "Command:"
  echo "curl -v -X OPTIONS https://$DOMAIN$endpoint \\"
  echo "  -H \"Origin: $ORIGIN\" \\"
  echo "  -H \"Access-Control-Request-Method: GET\" \\"
  echo "  -H \"Access-Control-Request-Headers: Content-Type, Authorization\" \\"
  echo "  -H \"CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID\" \\"
  echo "  -H \"CF-Access-Client-Secret: [HIDDEN]\" \\"
  
  # Execute the command
  curl -v -X OPTIONS "https://$DOMAIN$endpoint" \
    -H "Origin: $ORIGIN" \
    -H "Access-Control-Request-Method: GET" \
    -H "Access-Control-Request-Headers: Content-Type, Authorization" \
    -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
    -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
    > "$OUTPUT_DIR/options_${test_num}_output.txt" 2>&1
  
  # Display the output
  echo
  echo "Response:"
  cat "$OUTPUT_DIR/options_${test_num}_output.txt"
  echo
  
  # Check CORS headers
  check_cors_headers "$OUTPUT_DIR/options_${test_num}_output.txt" "options"
}

# Function to test GET request
test_get_request() {
  local endpoint=$1
  local test_num=$2
  
  echo "Test $test_num: GET request to $endpoint"
  echo "Command:"
  echo "curl -v https://$DOMAIN$endpoint \\"
  echo "  -H \"Origin: $ORIGIN\" \\"
  echo "  -H \"CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID\" \\"
  echo "  -H \"CF-Access-Client-Secret: [HIDDEN]\" \\"
  
  # Execute the command
  curl -v "https://$DOMAIN$endpoint" \
    -H "Origin: $ORIGIN" \
    -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
    -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
    > "$OUTPUT_DIR/get_${test_num}_output.txt" 2>&1
  
  # Display the output
  echo
  echo "Response:"
  cat "$OUTPUT_DIR/get_${test_num}_output.txt"
  echo
  
  # Check CORS headers
  check_cors_headers "$OUTPUT_DIR/get_${test_num}_output.txt" "get"
}

# Function to check CORS headers
check_cors_headers() {
  local output_file=$1
  local request_type=$2
  
  echo "Checking CORS headers in the response..."
  
  # Check for CORS headers
  local allow_origin=$(grep -i "access-control-allow-origin:" "$output_file")
  local allow_credentials=$(grep -i "access-control-allow-credentials:" "$output_file")
  
  if [ -n "$allow_origin" ] && [ -n "$allow_credentials" ]; then
    echo "✅ Basic CORS headers found:"
    echo "  $allow_origin"
    echo "  $allow_credentials"
    
    if [ "$request_type" == "options" ]; then
      # Check for OPTIONS-specific headers
      local allow_methods=$(grep -i "access-control-allow-methods:" "$output_file")
      local allow_headers=$(grep -i "access-control-allow-headers:" "$output_file")
      local max_age=$(grep -i "access-control-max-age:" "$output_file")
      
      if [ -n "$allow_methods" ] && [ -n "$allow_headers" ] && [ -n "$max_age" ]; then
        echo "✅ OPTIONS-specific CORS headers found:"
        echo "  $allow_methods"
        echo "  $allow_headers"
        echo "  $max_age"
      else
        echo "❌ Some OPTIONS-specific CORS headers are missing"
      fi
    fi
  else
    echo "❌ Basic CORS headers are missing"
  fi
  
  # Check status code
  local status_code=$(grep -i "< HTTP/" "$output_file" | awk '{print $3}')
  
  if [ "$request_type" == "options" ]; then
    if [ "$status_code" == "204" ] || [ "$status_code" == "200" ]; then
      echo "✅ OPTIONS request status code is good: $status_code"
    else
      echo "❌ OPTIONS request returned unexpected status code: $status_code"
    fi
  else
    if [ "$status_code" == "200" ]; then
      echo "✅ GET request status code is good: $status_code"
    else
      echo "⚠️ GET request returned unexpected status code: $status_code"
    fi
  fi
  
  echo
}

# Run tests for each endpoint
test_num=1
for endpoint in "${ENDPOINTS[@]}"; do
  test_options_request "$endpoint" "$test_num"
  test_num=$((test_num + 1))
  
  test_get_request "$endpoint" "$test_num"
  test_num=$((test_num + 1))
done

echo "=== CORS Verification Complete ==="
echo "Results saved to $OUTPUT_DIR/"
echo
echo "Summary:"
echo "- If OPTIONS requests return 204 No Content with all CORS headers, the CORS preflight is working correctly"
echo "- If GET requests return 200 OK with basic CORS headers, the CORS is working correctly"
echo "- If any test fails, please check the logs for more details"