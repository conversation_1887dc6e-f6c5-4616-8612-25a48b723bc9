# Divinci mTLS Testing Guide

This guide provides a comprehensive overview of the mTLS (Mutual TLS) testing capabilities in the Divinci platform. It explains how to use the various mTLS testing tools and frameworks available in the codebase.

## Overview

The Divinci platform includes several types of mTLS tests:

1. **Unit Tests**: Tests for individual components of the mTLS implementation
2. **Integration Tests**: Shell-based tests for end-to-end mTLS functionality
3. **Certificate Management**: Tools for managing and validating certificates
4. **Troubleshooting Guides**: Documentation for diagnosing and fixing mTLS issues

## Unit Tests

Unit tests for mTLS functionality are located in their respective component directories:

### 1. Server-Side mTLS Tests

Location: `workspace/servers/public-api/tests/unit/mtls/`

These tests verify:
- Certificate loading from different paths
- HTTPS server creation with mTLS options
- Client certificate verification

To run these tests:
```bash
cd workspace/servers/public-api
npm test -- tests/unit/mtls
```

### 2. Client-Side mTLS Tests

Location: `workspace/clients/web/tests/unit/mtls/`

These tests verify:
- Client certificate loading from different paths
- HTTPS agent creation with mTLS options
- Error handling during setup

To run these tests:
```bash
cd workspace/clients/web
npm test -- tests/unit/mtls
```

### 3. Certificate Validation Tests

Location: `workspace/resources/server-utils/tests/unit/certificates/`

These tests verify:
- Certificate and key match validation
- Certificate expiration checking
- Certificate chain verification
- Certificate domain validation

To run these tests:
```bash
cd workspace/resources/server-utils
npm test -- tests/unit/certificates
```

## Integration Tests

The mTLS framework provides shell-based integration tests for testing end-to-end mTLS functionality:

### 1. mTLS Testing Framework

Location: `deploy/scripts/tests/mtls-framework/`

This modular framework provides:
- Common utilities for certificate validation and connection testing
- A comprehensive test runner for verifying mTLS functionality
- A certificate management utility for generating, rotating, and verifying certificates

To run all mTLS tests:
```bash
./deploy/scripts/tests/mtls-framework/run-tests.sh
```

To run specific tests:
```bash
./deploy/scripts/tests/mtls-framework/run-tests.sh --test basic
./deploy/scripts/tests/mtls-framework/run-tests.sh --test certificates
./deploy/scripts/tests/mtls-framework/run-tests.sh --test mtls
./deploy/scripts/tests/mtls-framework/run-tests.sh --test cloudflare
```

To test a specific domain or environment:
```bash
./deploy/scripts/tests/mtls-framework/run-tests.sh --domain api.stage.divinci.app --environment staging
```

### 2. Standalone mTLS Connection Test

Location: `deploy/scripts/tests/mtls-connection-test.sh`

This script tests mTLS connections using OpenSSL s_client:
```bash
./deploy/scripts/tests/mtls-connection-test.sh [domain] [origin_ip] [port] [environment]
```

### 3. Comprehensive mTLS Test

Location: `deploy/scripts/tests/mtls-comprehensive-test.sh`

This script runs a comprehensive set of mTLS tests:
```bash
./deploy/scripts/tests/mtls-comprehensive-test.sh [domain] [environment]
```

## Certificate Management

The certificate manager provides tools for managing mTLS certificates:

Location: `deploy/scripts/tests/mtls-framework/certificate-manager.sh`

```bash
# Check certificate status
./deploy/scripts/tests/mtls-framework/certificate-manager.sh --action status --environment staging

# Generate new certificates
./deploy/scripts/tests/mtls-framework/certificate-manager.sh --action generate --environment staging

# Rotate certificates
./deploy/scripts/tests/mtls-framework/certificate-manager.sh --action rotate --environment staging

# Verify certificates
./deploy/scripts/tests/mtls-framework/certificate-manager.sh --action verify --environment staging
```

## Troubleshooting

For troubleshooting mTLS issues, refer to the [mTLS Troubleshooting Guide](mtls-troubleshooting.md).

## Test Coverage

The mTLS testing framework provides comprehensive coverage of the mTLS implementation, including:

1. **Certificate Validation**:
   - Certificate and key match validation
   - Certificate expiration checking
   - Certificate chain verification
   - Certificate domain validation

2. **Connection Testing**:
   - Basic connectivity testing
   - mTLS connection testing with client certificates
   - HTTP request testing with mTLS
   - Cloudflare SSL mode testing

3. **Error Handling**:
   - Missing certificate handling
   - Invalid certificate handling
   - Connection failure handling
   - Cloudflare error handling

## Avoiding Redundant Tests

To avoid redundancy in mTLS testing:

1. **Use the mTLS Framework for Integration Tests**:
   - The `mtls-framework` directory contains a modular testing framework that should be used for all shell-based integration tests
   - Avoid creating new standalone scripts for mTLS testing
   - If you need to add new tests, add them to the existing framework

2. **Use Unit Tests for Component Testing**:
   - Keep unit tests in their respective component directories
   - Use unit tests for testing individual components of the mTLS implementation
   - Use integration tests for testing end-to-end mTLS functionality

3. **Standardize Certificate Paths**:
   - Use standard certificate paths across all tests
   - For server certificates: `/etc/ssl/certs/server.crt` and `/etc/ssl/private/server.key`
   - For client certificates: `/etc/ssl/client/client.crt` and `/etc/ssl/client/client.key`
   - For environment-specific certificates: `/private-keys/{environment}/certs/mtls/`

4. **Centralize Certificate Validation**:
   - Use the certificate validation utilities in `workspace/resources/server-utils/src/certificates/validation.ts`
   - Avoid duplicating certificate validation logic in other parts of the codebase

## Future Improvements

- Add automated integration tests with a local mTLS server
- Add tests for certificate rotation
- Add tests for certificate expiration monitoring
- Add tests for standardized certificate paths
- Add tests for browser-specific certificate handling
