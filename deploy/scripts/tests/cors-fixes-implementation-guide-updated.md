# CORS Fixes Implementation Guide

This guide provides step-by-step instructions for implementing and testing the CORS fixes for the Divinci API.

## Step 1: Implement the Fixes in Cloudflare

Since we're encountering issues with the Cloudflare API, we'll need to implement the fixes manually through the Cloudflare dashboard.

### 1.1 Update the mTLS Rule

1. Log in to the Cloudflare dashboard for divinci.app
2. Go to **Security** > **WAF** > **Custom Rules**
3. Find the rule that contains the mTLS verification expression (search for rules containing `cf.tls_client_auth.cert_verified`)
4. Click on **Edit** for that rule
5. Modify the expression to exclude OPTIONS requests by adding `(http.request.method ne "OPTIONS") and` at the beginning of the expression

**Original Expression:**
```
(not cf.tls_client_auth.cert_verified and http.host eq "api.stage.divinci.app") or 
(not cf.tls_client_auth.cert_verified and http.host eq "api.dev.divinci.app") or 
(not cf.tls_client_auth.cert_verified and http.host eq "api.divinci.app") or 
...
```

**Modified Expression:**
```
(http.request.method ne "OPTIONS") and (
  (not cf.tls_client_auth.cert_verified and http.host eq "api.stage.divinci.app") or 
  (not cf.tls_client_auth.cert_verified and http.host eq "api.dev.divinci.app") or 
  (not cf.tls_client_auth.cert_verified and http.host eq "api.divinci.app") or 
  ...
)
```

6. Click **Save**

### 1.2 Create Transform Rules for CORS Headers

#### Create Transform Rule for OPTIONS Requests

1. Go to **Rules** > **Transform Rules**
2. Click **Create Transform Rule**
3. Set the following values:
   - **Name**: `Add CORS Headers for OPTIONS Requests`
   - **Expression**: `(http.request.method eq "OPTIONS")`
   - **Description**: `Add CORS headers for OPTIONS preflight requests`

4. In the **Then...** section, select **Modify response header**
5. Add the following headers:
   - **Header 1**:
     - **Name**: `Access-Control-Allow-Origin`
     - **Operation**: `Set dynamic`
     - **Value**: `http.request.headers["Origin"][0]`
   - **Header 2**:
     - **Name**: `Access-Control-Allow-Methods`
     - **Operation**: `Set`
     - **Value**: `GET, POST, PUT, DELETE, OPTIONS, PATCH`
   - **Header 3**:
     - **Name**: `Access-Control-Allow-Headers`
     - **Operation**: `Set`
     - **Value**: `Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization`
   - **Header 4**:
     - **Name**: `Access-Control-Allow-Credentials`
     - **Operation**: `Set`
     - **Value**: `true`
   - **Header 5**:
     - **Name**: `Access-Control-Max-Age`
     - **Operation**: `Set`
     - **Value**: `86400`

6. Click **Save**

#### Create Transform Rule for Regular Requests

1. Go to **Rules** > **Transform Rules**
2. Click **Create Transform Rule**
3. Set the following values:
   - **Name**: `Add CORS Headers for Regular Requests`
   - **Expression**: `true`
   - **Description**: `Add CORS headers for all responses`

4. In the **Then...** section, select **Modify response header**
5. Add the following headers:
   - **Header 1**:
     - **Name**: `Access-Control-Allow-Origin`
     - **Operation**: `Set dynamic`
     - **Value**: `http.request.headers["Origin"][0]`
   - **Header 2**:
     - **Name**: `Access-Control-Allow-Credentials`
     - **Operation**: `Set`
     - **Value**: `true`

6. Click **Save**

## Step 2: Test the Fixes

After implementing the changes, use the `test-cors-fixes.sh` script to verify that the CORS issues have been resolved:

```bash
# Run the test script
./test-cors-fixes.sh --verbose
```

The script will:
1. Test OPTIONS preflight requests to api.stage.divinci.app
2. Test regular GET requests to api.stage.divinci.app
3. Check for the presence of CORS headers in the responses

## Step 3: Verify in the Browser

To verify that the CORS issues have been resolved in the browser:

1. Open Chrome or Firefox
2. Open the Developer Tools (F12)
3. Go to the Network tab
4. Navigate to https://chat.stage.divinci.app
5. Look for requests to api.stage.divinci.app
6. Verify that OPTIONS requests return 204 No Content with the appropriate CORS headers
7. Verify that regular requests return 200 OK with the appropriate CORS headers
8. Check the console for any CORS-related errors

## Troubleshooting

If you still encounter CORS issues after implementing the fixes:

### 1. Check the mTLS Rule

Verify that the mTLS rule has been properly updated to exclude OPTIONS requests:

```
(http.request.method ne "OPTIONS") and (
  (not cf.tls_client_auth.cert_verified and http.host eq "api.stage.divinci.app") or 
  ...
)
```

### 2. Check the Transform Rules

Verify that the Transform Rules have been properly created and are adding the necessary CORS headers:

- **OPTIONS Transform Rule**: Should add all CORS headers for OPTIONS requests
- **Regular Transform Rule**: Should add basic CORS headers for all responses

### 3. Check Rule Order

Ensure that the Transform Rules are being applied after the WAF rules. The order of execution in Cloudflare is:

1. WAF Rules
2. Transform Rules

### 4. Test with curl

Use curl to test the API directly:

```bash
# Test OPTIONS request
curl -v -X OPTIONS "https://api.stage.divinci.app/health" \
  -H "Origin: https://chat.stage.divinci.app" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type, Authorization"

# Test GET request
curl -v -X GET "https://api.stage.divinci.app/health" \
  -H "Origin: https://chat.stage.divinci.app"
```

### 5. Check Cloudflare Logs

Check the Cloudflare logs for any errors or issues:

1. Go to the Cloudflare dashboard
2. Go to Analytics > Logs
3. Look for requests to api.stage.divinci.app
4. Check for any errors or issues with the requests

## Next Steps

After successfully implementing and testing the CORS fixes:

1. Document the changes made
2. Update the team on the changes
3. Consider implementing automated tests to detect CORS issues in the future
4. Consider integrating the CORS tests into your CI/CD pipeline
