#!/bin/bash
# Certificate Presentation Test
# This test verifies that your origin server is correctly presenting the certificate to Cloudflare.

set -e

# Configuration
DOMAIN=${1:-"chat.stage.divinci.app"}
ORIGIN_IP=${2:-"************"}
PORT=${3:-"443"}
OUTPUT_DIR="./test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== Certificate Presentation Test ==="
echo "Domain: $DOMAIN"
echo "Origin IP: $ORIGIN_IP"
echo "Port: $PORT"
echo

# Step 1: Extract the certificate from GCP Secret Manager for comparison
echo "Step 1: Extracting certificate from GCP Secret Manager for comparison..."
gcloud secrets versions access latest --secret=server-crt > "$OUTPUT_DIR/server-crt-from-gcp.pem"
echo "✅ Certificate extracted from GCP Secret Manager"

# Step 2: Make a direct connection to the origin server IP and capture the certificate
echo "Step 2: Making a direct connection to the origin server IP..."
echo "Attempting to connect to $ORIGIN_IP:$PORT..."

# Use OpenSSL to connect to the server and save the certificate
if openssl s_client -connect "$ORIGIN_IP:$PORT" -servername "$DOMAIN" -showcerts </dev/null 2>/dev/null > "$OUTPUT_DIR/origin-connection.txt"; then
  echo "✅ Successfully connected to origin server"
  
  # Extract the certificate from the connection output
  sed -n '/-----BEGIN CERTIFICATE-----/,/-----END CERTIFICATE-----/p' "$OUTPUT_DIR/origin-connection.txt" > "$OUTPUT_DIR/server-crt-from-origin.pem"
  
  if [ -s "$OUTPUT_DIR/server-crt-from-origin.pem" ]; then
    echo "✅ Successfully extracted certificate from origin server"
  else
    echo "❌ Failed to extract certificate from origin server"
    echo "   This may indicate a problem with the TLS handshake"
  fi
else
  echo "❌ Failed to connect to origin server"
  echo "   This may indicate a network connectivity issue or that the server is not listening on port $PORT"
fi

# Step 3: Compare the certificate from the origin with the one from GCP Secret Manager
echo "Step 3: Comparing certificates..."
if [ -s "$OUTPUT_DIR/server-crt-from-origin.pem" ]; then
  # Get fingerprints for comparison
  GCP_FINGERPRINT=$(openssl x509 -in "$OUTPUT_DIR/server-crt-from-gcp.pem" -noout -fingerprint -sha256 | cut -d= -f2)
  ORIGIN_FINGERPRINT=$(openssl x509 -in "$OUTPUT_DIR/server-crt-from-origin.pem" -noout -fingerprint -sha256 | cut -d= -f2)
  
  echo "GCP Certificate Fingerprint: $GCP_FINGERPRINT"
  echo "Origin Certificate Fingerprint: $ORIGIN_FINGERPRINT"
  
  if [ "$GCP_FINGERPRINT" = "$ORIGIN_FINGERPRINT" ]; then
    echo "✅ Certificates match"
    echo "   The origin server is presenting the same certificate that's stored in GCP Secret Manager"
  else
    echo "❌ Certificates do not match"
    echo "   The origin server is presenting a different certificate than what's stored in GCP Secret Manager"
  fi
  
  # Step 4: Check for TLS handshake errors
  echo "Step 4: Checking for TLS handshake errors..."
  if grep -q "Verify return code: 0 (ok)" "$OUTPUT_DIR/origin-connection.txt"; then
    echo "✅ TLS handshake completed successfully"
  else
    echo "❌ TLS handshake errors detected"
    grep "Verify return code:" "$OUTPUT_DIR/origin-connection.txt"
  fi
  
  # Step 5: Check certificate chain presented by the origin
  echo "Step 5: Checking certificate chain presented by the origin..."
  CERT_COUNT=$(grep -c "BEGIN CERTIFICATE" "$OUTPUT_DIR/origin-connection.txt")
  echo "Origin server presented $CERT_COUNT certificates in the chain"
  
  if [ "$CERT_COUNT" -ge 2 ]; then
    echo "✅ Origin server is presenting a complete certificate chain"
  else
    echo "❌ Origin server is presenting an incomplete certificate chain"
    echo "   The chain should include both the server certificate and the Cloudflare Root CA certificate"
  fi
else
  echo "❌ Cannot compare certificates because origin certificate extraction failed"
fi

# Step 6: Test TLS protocol versions
echo "Step 6: Testing supported TLS protocol versions..."
for version in "tls1" "tls1_1" "tls1_2" "tls1_3"; do
  echo "Testing $version..."
  if openssl s_client -connect "$ORIGIN_IP:$PORT" -servername "$DOMAIN" -"$version" </dev/null 2>/dev/null | grep -q "CONNECTED"; then
    echo "✅ $version is supported"
  else
    echo "❌ $version is not supported"
  fi
done

# Step 7: Test cipher suites
echo "Step 7: Testing cipher suites recommended by Cloudflare..."
RECOMMENDED_CIPHERS=(
  "ECDHE-RSA-AES128-GCM-SHA256"
  "ECDHE-ECDSA-AES128-GCM-SHA256"
  "ECDHE-RSA-AES256-GCM-SHA384"
  "ECDHE-ECDSA-AES256-GCM-SHA384"
)

for cipher in "${RECOMMENDED_CIPHERS[@]}"; do
  echo "Testing cipher: $cipher..."
  if openssl s_client -connect "$ORIGIN_IP:$PORT" -servername "$DOMAIN" -cipher "$cipher" </dev/null 2>/dev/null | grep -q "CONNECTED"; then
    echo "✅ Cipher $cipher is supported"
  else
    echo "❌ Cipher $cipher is not supported"
  fi
done

echo
echo "=== Certificate Presentation Test Complete ==="
echo "Results saved to $OUTPUT_DIR/"
