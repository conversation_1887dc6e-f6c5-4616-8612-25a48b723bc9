# Comprehensive Cloudflare and GCP Network Testing Suite

This testing suite provides a comprehensive set of tools for diagnosing connectivity issues between Cloudflare and Google Cloud Platform (GCP) services, with a specific focus on Error 526 (Invalid SSL Certificate) issues, CORS problems, and mTLS configuration.

## Overview

The testing suite consists of several individual test scripts that can be run independently or as a complete suite using the `run-comprehensive-tests.sh` script. Each test focuses on a specific aspect of the connectivity between Cloudflare and GCP Cloud Run.

## Master Configuration System

All tests in this suite now use a centralized configuration approach through the `test-config.sh` file. This provides several benefits:

- Consistent configuration across all tests
- Easy management of shared parameters and credentials
- Support for Cloudflare Access headers and other custom headers
- Convenient loading of environment-specific values

### Using the Master Configuration

The master configuration file (`test-config.sh`) defines default values for common parameters and handles loading credentials from environment files. Key features include:

- **HTTP Headers Management**: Automatically adds Cloudflare Access headers and custom headers to requests
- **Credential Management**: Loads credentials from appropriate environment files
- **Default Parameters**: Provides sensible defaults for domains, IPs, ports, etc.
- **Helper Functions**: Utilities for working with headers and environment variables

### Customizing the Configuration

To modify the default configuration:

1. Edit the `test-config.sh` file
2. Update default values as needed
3. Add any additional headers to the `ADDITIONAL_HEADERS` array
4. Set `USE_CF_ACCESS_HEADERS=0` to disable Cloudflare Access headers

## Prerequisites

### Required Tools
- `bash` shell
- `curl` command-line tool
- `openssl` command-line tool
- `jq` command-line tool for JSON processing
- `gcloud` CLI configured with appropriate permissions

### Authentication
- Cloudflare API token with appropriate permissions
- Cloudflare Access service account credentials (if using Cloudflare Access)

### Optional Advanced Tools
- `sslyze` - SSL/TLS server scanner (install with `pip install sslyze`)
- `nmap` - Network exploration and security auditing tool
- `tcpdump` - Packet analyzer (requires sudo privileges)
- `tshark` - Command-line version of Wireshark (requires sudo privileges)

The test suite will automatically detect which tools are available and skip tests that require missing tools.

## Installation

1. Clone this repository or download the scripts to your local machine.
2. Make the scripts executable:
   ```bash
   chmod +x *.sh
   ```

## Usage

### Running the Complete Test Suite

```bash
./run-comprehensive-tests.sh [OPTIONS] [DOMAIN] [ORIGIN_IP] [PORT] [CAPTURE_DURATION] [SERVICES] [REGION] [ENVIRONMENT] [CF_API_TOKEN] [CF_ACCESS_CLIENT_ID] [CF_ACCESS_CLIENT_SECRET] [CF_EMAIL] [CF_API_KEY] [CF_ACCOUNT_ID]
```

All parameters are optional and will use defaults from the master configuration if not provided:

- `DOMAIN`: The domain to test (default from test-config.sh)
- `ORIGIN_IP`: The IP address of the origin server (default from test-config.sh)
- `PORT`: The port number to connect to (default from test-config.sh)
- `CAPTURE_DURATION`: Duration in seconds for packet captures (default from test-config.sh)
- `SERVICES`: Space-separated list of GCP Cloud Run services to test
- `REGION`: The GCP region where services are deployed (default from test-config.sh)
- `ENVIRONMENT`: The environment (develop, staging, production) (default from test-config.sh)
- `CF_API_TOKEN`: The Cloudflare API token (default from test-config.sh)
- `CF_ACCESS_CLIENT_ID`: The Cloudflare Access Client ID (default from test-config.sh)
- `CF_ACCESS_CLIENT_SECRET`: The Cloudflare Access Client Secret (from test-config.sh if not provided)
- `CF_EMAIL`: The Cloudflare account email (from test-config.sh if not provided)
- `CF_API_KEY`: The Cloudflare API key (from test-config.sh if not provided)
- `CF_ACCOUNT_ID`: The Cloudflare account ID (from test-config.sh if not provided)

You can run with just the non-interactive flag to use all defaults from test-config.sh:

```bash
./run-comprehensive-tests.sh -n
```

### Running Individual Tests

Each test script can be run independently with its own set of parameters. All tests now use the master configuration for default values and credential management.

```bash
./certificate-chain-verification.sh [domain] [environment]
```

Note that all tests will now automatically use the Cloudflare Access headers when credentials are available in the master configuration. This allows you to test protected endpoints without having to manually provide credentials for each test.

You can modify this behavior by editing the `test-config.sh` file and setting `USE_CF_ACCESS_HEADERS=0` to disable Cloudflare Access headers globally.

See each script's header for specific parameter details.

## Test Scripts

### 1. Certificate Chain Verification Test

This test verifies the completeness and validity of the certificate chain presented by your origin server.

```bash
./certificate-chain-verification.sh [domain] [environment]
```

### 2. Certificate Presentation Test

This test verifies that your origin server is correctly presenting the certificate to Cloudflare.

```bash
./certificate-presentation-test.sh [domain] [origin_ip] [port]
```

### 3. GCP Certificate Mount Verification

This test verifies that all GCP Cloud Run services have the proper certificate secret mounts configured correctly.

```bash
./check-certificate-mounts.sh [project_id] [region]
```

### 4. GCP Certificate Mount Fix

This tool fixes certificate mount issues in GCP Cloud Run service configurations.

```bash
./fix-certificate-mounts.sh [service_name] [project_id] [region]
```

### 5. SSLyze SSL/TLS Server Scanner

This test uses SSLyze to perform a comprehensive scan of the SSL/TLS configuration of a server.

```bash
./sslyze-scan.sh [domain] [origin_ip] [port]
```

### 6. nmap SSL/TLS Scan

This test uses nmap to scan SSL/TLS configurations and vulnerabilities.

```bash
./nmap-ssl-scan.sh [domain] [origin_ip] [port]
```

### 7. mTLS Connection Test

This test tests mutual TLS (mTLS) connections using OpenSSL s_client.

```bash
./mtls-connection-test.sh [domain] [origin_ip] [port] [environment]
```

### 8. tcpdump TLS Capture

This test uses tcpdump to capture TLS handshake packets for detailed analysis.

```bash
./tcpdump-tls-capture.sh [domain] [origin_ip] [port] [capture_duration]
```

### 9. TShark TLS Analysis

This test uses TShark to capture and analyze TLS handshakes between the client and server.

```bash
./tshark-tls-analysis.sh [domain] [origin_ip] [port] [capture_duration]
```

### 10. Cloudflare API Trace Test

This test uses Cloudflare's Request Tracer API to trace a request through Cloudflare's infrastructure.

```bash
./cloudflare-api-trace-test.sh [domain] [cf_api_token] [cf_email] [cf_api_key] [cf_account_id]
```

### 11. GCP Cloud Run Health Check

This test verifies that the GCP Cloud Run service is healthy and correctly configured.

```bash
./gcp-cloud-run-health-check.sh [service_name] [region] [project_id]
```

### 12. GCP Cloud Run Network Test

This test verifies network connectivity to and from the GCP Cloud Run service.

```bash
./gcp-cloud-run-network-test.sh [service_name] [region] [project_id]
```

### 13. Cloudflare Access Bypass Test

This test attempts to bypass Cloudflare Access to directly test the origin server.

```bash
./cloudflare-access-bypass-test.sh [domain] [cf_access_client_id] [cf_access_client_secret]
```

### 14. Cloudflare Rules Analyzer

This test analyzes Cloudflare rules for potential conflicts, especially related to CORS and mTLS.

```bash
./cloudflare-rules-analyzer.sh [options]
```

Options:
- `--zone-id ID`: Cloudflare Zone ID (required unless in env)
- `--api-token TOKEN`: Cloudflare API Token (required unless in env)
- `--environment ENV`: Environment to test (default: staging)
- `--domain DOMAIN`: Domain to test (default: api.stage.divinci.app)
- `--origin ORIGIN`: Origin to use for CORS tests (default: https://chat.stage.divinci.app)
- `--verbose`: Enable verbose output

### 15. Cloudflare Rules Fixer

This script automatically fixes common issues with Cloudflare rules, particularly CORS and mTLS conflicts.

```bash
./cloudflare-rules-fixer.sh [options]
```

Options:
- `--zone-id ID`: Cloudflare Zone ID (required unless in env)
- `--api-token TOKEN`: Cloudflare API Token (required unless in env)
- `--environment ENV`: Environment to test (default: staging)
- `--max-attempts N`: Maximum number of fix attempts (default: 3)
- `--report-file FILE`: Path to output report file
- `--verbose`: Enable verbose output
- `--dry-run`: Don't make any actual changes, just show what would be done

### 16. Network Diagnostics Runner

A comprehensive test runner that executes mTLS, CORS, and Cloudflare rules tests.

```bash
./run-network-diagnostics.sh [options]
```

Options:
- `--environment ENV`: Environment to test (default: staging)
- `--verbose`: Enable verbose output
- `--skip-mtls`: Skip mTLS tests
- `--skip-cloudflare`: Skip Cloudflare rules analysis
- `--skip-cors`: Skip CORS tests

### 17. Cloudflare Test-Fix Cycle

This script runs the analyzer, fixer, and tests in a cycle to automatically detect and fix issues.

```bash
./run-cloudflare-test-fix-cycle.sh [options]
```

Options:
- `--environment ENV`: Environment to test (default: staging)
- `--max-cycles N`: Maximum number of test-fix cycles (default: 3)
- `--report-file FILE`: Path to output report file
- `--verbose`: Enable verbose output
- `--dry-run`: Don't make any actual changes, just show what would be done

## Output

All test results are saved to the `./test-results/` directory. Each test creates its own output files, and a summary of all tests is saved to `./test-results/test_summary.txt`.

## Troubleshooting Common Issues

### Error 526 (Invalid SSL Certificate)

If you're experiencing Error 526 (Invalid SSL Certificate), here are some common solutions:

1. **Certificate Domain Mismatch**: Ensure your certificate covers the correct domain. For subdomains like `chat.stage.divinci.app`, you need either a certificate specifically for that domain or a wildcard certificate for `*.stage.divinci.app`.

2. **Incomplete Certificate Chain**: Make sure your certificate chain is complete and includes the Cloudflare Root CA certificate. You can use the `add-ca-to-certificates.sh` script to add the Cloudflare Root CA to your certificate chain.

3. **GCP Secrets Not Updated**: Ensure your GCP secrets are updated with the correct certificates. Use the `create-server-cert-secret.sh` script to update the secrets.

4. **Certificate Secret Mounts Missing**: Check that your Cloud Run services have the proper certificate secret mounts configured. Use the `check-certificate-mounts.sh` script to verify all services have the correct mounts.

5. **Service Not Restarted**: After updating certificates, restart your Cloud Run service to pick up the changes.

6. **Cloudflare SSL Mode**: Temporarily change Cloudflare SSL mode from "Full (Strict)" to "Full" to test if that resolves the issue. If it does, the issue is with certificate validation.

### CORS Issues

If you're experiencing CORS issues, here are some common solutions:

1. **mTLS Rule Blocking OPTIONS Requests**: Modify your Cloudflare mTLS rule to exclude OPTIONS requests. Use the `cloudflare-rules-fixer.sh` script to automatically fix this issue.

2. **Missing CORS Headers**: Add Transform Rules in Cloudflare to add CORS headers to responses. The `cloudflare-rules-fixer.sh` script can create these rules for you.

3. **Incorrect CORS Configuration**: Ensure your server is properly configured to handle CORS. Check the `domains-from-env.ts` file to make sure it includes all necessary domains.

4. **Cloudflare Access Blocking Requests**: If you're using Cloudflare Access, make sure it's properly configured to allow requests from your origin domains.

5. **Run the Test-Fix Cycle**: Use the `run-cloudflare-test-fix-cycle.sh` script to automatically detect and fix CORS issues.

## Integration with GCP Export YAML Tool

If you have the GCP Export YAML tool available, the test suite will automatically use it to export all GCP resources related to your Cloud Run service. This provides a comprehensive view of your GCP infrastructure, which can help identify configuration issues.

To use this feature, make sure the `gcp-export-yaml.sh` script is available in the same directory as the other test scripts.

## License

This testing suite is provided as-is with no warranty. Use at your own risk.
