# CORS and mTLS Analysis

## Issue Summary

The Divinci API is experiencing CORS issues when accessed from chat.stage.divinci.app. The browser is unable to make requests to api.stage.divinci.app due to CORS policy violations.

## Test Results

### CORS Tests

1. **OPTIONS Preflight Request**:
   - Status: 503 Service Unavailable
   - No CORS headers in response
   - This indicates that the server is not properly handling OPTIONS preflight requests

2. **Regular GET Request**:
   - Status: 403 Forbidden
   - No CORS headers in response
   - This suggests that the request is being blocked by Cloudflare's security rules

### Root Cause Analysis

The issue appears to be with Cloudflare's security rules, specifically:

1. **mTLS Rule**: The current mTLS rule is blocking all requests (including OPTIONS preflight requests) that don't have a verified client certificate:
   ```
   (not cf.tls_client_auth.cert_verified and http.host eq "api.stage.divinci.app") or ...
   ```

2. **Missing CORS Headers**: Even when requests pass through security rules, the server is not adding the necessary CORS headers to the response.

## Recommended Solutions

### 1. Modify mTLS Rule to Exclude OPTIONS Requests

Update the mTLS rule to exclude OPTIONS requests:

```
(http.request.method ne "OPTIONS") and (
  (not cf.tls_client_auth.cert_verified and http.host eq "api.stage.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "api.dev.divinci.app") or
  ...
)
```

### 2. Add Transform Rules for CORS Headers

Create two Transform Rules in Cloudflare:

#### Rule 1: Add CORS Headers for OPTIONS Requests
```
Name: Add CORS Headers for OPTIONS Requests
When: (http.request.method eq "OPTIONS")
Then: 
  - Set response header "Access-Control-Allow-Origin" to dynamic value from request header "Origin"
  - Set response header "Access-Control-Allow-Methods" to "GET, POST, PUT, DELETE, OPTIONS, PATCH"
  - Set response header "Access-Control-Allow-Headers" to "Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization"
  - Set response header "Access-Control-Allow-Credentials" to "true"
  - Set response header "Access-Control-Max-Age" to "86400"
```

#### Rule 2: Add CORS Headers for Regular Requests
```
Name: Add CORS Headers for Regular Requests
When: true
Then: 
  - Set response header "Access-Control-Allow-Origin" to dynamic value from request header "Origin"
  - Set response header "Access-Control-Allow-Credentials" to "true"
```

### 3. Server-Side CORS Configuration

Ensure that the server is also configured to handle CORS properly:

1. The changes made to `domains-from-env.ts` to prioritize environment-specific domains are correct
2. The changes to `app.ts` to add specific handling for `*.stage.divinci.app` domains are also correct

However, these changes won't take effect if Cloudflare is blocking the requests before they reach the server.

## Testing After Changes

After implementing these changes, run the network diagnostics again to verify that:

1. OPTIONS requests return a 204 No Content status with the appropriate CORS headers
2. Regular requests return a 200 OK status with the appropriate CORS headers

```bash
./run-network-diagnostics.sh --skip-mtls --skip-cloudflare --environment staging
```

## Long-Term Recommendations

1. **Comprehensive Cloudflare Rules Analysis**: Set up the Cloudflare API credentials and run the full analysis:
   ```bash
   ./run-network-diagnostics.sh --environment staging
   ```

2. **Automated Testing**: Integrate these tests into your CI/CD pipeline to catch CORS issues early

3. **Documentation**: Document the Cloudflare configuration and CORS setup for future reference
