#!/bin/bash
# <PERSON>ript to check Cloudflare SSL mode for a domain

set -e

# Load environment variables
if [ -f "/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env" ]; then
  echo "Loading credentials from /Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env"
  source "/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env"
  
  # Debug output (masked for security)
  echo "Debug: CF_ACCESS_CLIENT_SECRET length: ${#CF_ACCESS_CLIENT_SECRET}"
  echo "Debug: CF_EMAIL: ${CF_EMAIL}"
  echo "Debug: CF_API_KEY length: ${#CF_API_KEY}"
  echo "Debug: CF_ACCOUNT_ID: ${CF_ACCOUNT_ID}"
fi

# Get domain from command line
DOMAIN=${1:-"stage.divinci.app"}
echo "Checking Cloudflare SSL mode for domain: $DOMAIN"

# Extract zone name (remove subdomain)
ZONE=$(echo "$DOMAIN" | awk -F. '{print $(NF-1)"."$NF}')
echo "Zone: $ZONE"

# Get zone ID
echo "Getting zone ID for $ZONE..."
ZONE_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$ZONE" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_API_KEY" \
  -H "Content-Type: application/json" | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -z "$ZONE_ID" ]; then
  echo "❌ Failed to get zone ID for $ZONE"
  exit 1
fi

echo "Zone ID: $ZONE_ID"

# Get SSL mode
echo "Getting SSL mode for $ZONE_ID..."
SSL_MODE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/settings/ssl" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_API_KEY" \
  -H "Content-Type: application/json" | grep -o '"value":"[^"]*' | cut -d'"' -f4)

if [ -z "$SSL_MODE" ]; then
  echo "❌ Failed to get SSL mode for $ZONE_ID"
  exit 1
fi

echo "Current SSL mode: $SSL_MODE"

# Explain SSL modes
echo ""
echo "SSL Mode Explanation:"
echo "  - off: No encryption between visitor and Cloudflare or Cloudflare and origin"
echo "  - flexible: Encryption between visitor and Cloudflare, but not Cloudflare and origin"
echo "  - full: Encryption between visitor and Cloudflare, and Cloudflare and origin (accepts self-signed certs)"
echo "  - strict: Encryption between visitor and Cloudflare, and Cloudflare and origin (requires valid cert)"

# Recommendation
echo ""
echo "Recommendation:"
if [ "$SSL_MODE" = "strict" ]; then
  echo "✅ SSL mode is set to 'strict', which is the most secure option."
  echo "   However, if you're experiencing Error 526, try changing to 'full' mode temporarily."
  echo "   You can change it with: ./set-cloudflare-ssl-mode.sh $DOMAIN full"
elif [ "$SSL_MODE" = "full" ]; then
  echo "⚠️ SSL mode is set to 'full', which accepts self-signed certificates."
  echo "   This is less secure than 'strict' mode but can help resolve Error 526 issues."
  echo "   Once your certificates are properly configured, consider changing to 'strict' mode."
  echo "   You can change it with: ./set-cloudflare-ssl-mode.sh $DOMAIN strict"
else
  echo "❌ SSL mode is set to '$SSL_MODE', which is not recommended for production."
  echo "   Consider changing to 'full' or 'strict' mode."
  echo "   You can change it with: ./set-cloudflare-ssl-mode.sh $DOMAIN full"
fi
