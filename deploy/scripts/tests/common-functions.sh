#!/bin/bash
# Common functions for the testing suite

# Function to get the repository root path
get_repo_root() {
  # Start from the current directory
  local current_dir="$(pwd)"
  
  # Navigate up until we find the repository root (where .git directory exists)
  while [[ "$current_dir" != "/" ]]; do
    if [[ -d "$current_dir/.git" ]]; then
      echo "$current_dir"
      return 0
    fi
    # Move up one directory
    current_dir="$(dirname "$current_dir")"
  done
  
  # If we couldn't find the repository root, use a fallback approach
  # Try to find the server directory by navigating up from the current script location
  local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
  current_dir="$script_dir"
  
  while [[ "$current_dir" != "/" ]]; do
    if [[ "$(basename "$current_dir")" == "server" ]]; then
      echo "$current_dir"
      return 0
    fi
    # Move up one directory
    current_dir="$(dirname "$current_dir")"
  done
  
  # If all else fails, return the parent directory of the scripts/tests directory
  echo "$(cd "$script_dir/../.." && pwd)"
}

# Function to get the absolute path to a file or directory
get_abs_path() {
  local repo_root="$(get_repo_root)"
  local path="$1"
  
  # If the path is already absolute, return it as is
  if [[ "$path" == /* ]]; then
    echo "$path"
    return 0
  fi
  
  # If the path starts with ./, remove it
  if [[ "$path" == ./* ]]; then
    path="${path:2}"
  fi
  
  # Return the absolute path
  echo "$repo_root/$path"
}

# Function to check if a file exists
file_exists() {
  local file_path="$(get_abs_path "$1")"
  [[ -f "$file_path" ]]
}

# Function to check if a directory exists
dir_exists() {
  local dir_path="$(get_abs_path "$1")"
  [[ -d "$dir_path" ]]
}

# Function to create a directory if it doesn't exist
ensure_dir() {
  local dir_path="$(get_abs_path "$1")"
  mkdir -p "$dir_path"
}

# Function to get the contents of a file
get_file_contents() {
  local file_path="$(get_abs_path "$1")"
  if [[ -f "$file_path" ]]; then
    cat "$file_path"
  else
    echo ""
  fi
}

# Function to save contents to a file
save_file_contents() {
  local file_path="$(get_abs_path "$1")"
  local contents="$2"
  ensure_dir "$(dirname "$file_path")"
  echo "$contents" > "$file_path"
}

# Function to append contents to a file
append_file_contents() {
  local file_path="$(get_abs_path "$1")"
  local contents="$2"
  ensure_dir "$(dirname "$file_path")"
  echo "$contents" >> "$file_path"
}

# Function to copy a file
copy_file() {
  local src_path="$(get_abs_path "$1")"
  local dst_path="$(get_abs_path "$2")"
  ensure_dir "$(dirname "$dst_path")"
  cp "$src_path" "$dst_path"
}

# Function to move a file
move_file() {
  local src_path="$(get_abs_path "$1")"
  local dst_path="$(get_abs_path "$2")"
  ensure_dir "$(dirname "$dst_path")"
  mv "$src_path" "$dst_path"
}

# Function to remove a file
remove_file() {
  local file_path="$(get_abs_path "$1")"
  rm -f "$file_path"
}

# Function to remove a directory
remove_dir() {
  local dir_path="$(get_abs_path "$1")"
  rm -rf "$dir_path"
}

# Function to get the current date in a cross-platform way
get_current_date() {
  date +"%Y-%m-%d %H:%M:%S"
}

# Function to calculate days between dates in a cross-platform way
calculate_days_between_dates() {
  local date1="$1"
  local date2="$2"
  
  # Handle date calculation in a cross-platform way (Linux vs macOS)
  if [[ "$(uname)" == "Darwin" ]]; then
    # macOS date command
    local date1_seconds=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$date1" +%s 2>/dev/null || date -j -f "%b %d %T %Y %Z" "$date1" +%s)
    local date2_seconds=$(date -j -f "%b %d %H:%M:%S %Y %Z" "$date2" +%s 2>/dev/null || date -j -f "%b %d %T %Y %Z" "$date2" +%s)
    echo $(( ($date2_seconds - $date1_seconds) / 86400 ))
  else
    # Linux date command
    echo $(( ($(date -d "$date2" +%s) - $(date -d "$date1" +%s)) / 86400 ))
  fi
}
