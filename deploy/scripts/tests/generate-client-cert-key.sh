#!/bin/bash
# Script to generate a new client certificate and key pair for mTLS

set -e

# Get environment from command line
ENVIRONMENT=${1:-"staging"}
REPO_ROOT="/Users/<USER>/Documents/Divinci/server3/server"
CERT_DIR="$REPO_ROOT/private-keys/$ENVIRONMENT/certs/mtls"

echo "Generating new client certificate and key for $ENVIRONMENT environment"
echo "Certificate directory: $CERT_DIR"

# Create directory if it doesn't exist
mkdir -p "$CERT_DIR"

# Backup existing files if they exist
if [ -f "$CERT_DIR/client.crt" ]; then
  echo "Backing up existing client certificate..."
  cp "$CERT_DIR/client.crt" "$CERT_DIR/client.crt.backup.$(date +%Y%m%d_%H%M%S)"
fi

if [ -f "$CERT_DIR/client.key" ]; then
  echo "Backing up existing client key..."
  cp "$CERT_DIR/client.key" "$CERT_DIR/client.key.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Generate a new private key
echo "Generating new private key..."
openssl ecparam -name prime256v1 -genkey -noout -out "$CERT_DIR/client.key"
chmod 600 "$CERT_DIR/client.key"

# Generate a certificate signing request (CSR)
echo "Generating certificate signing request..."
openssl req -new -key "$CERT_DIR/client.key" -out "$CERT_DIR/client.csr" -subj "/C=US/ST=California/L=San Francisco/O=Divinci/OU=Engineering/CN=client.$ENVIRONMENT.divinci.app"

# Generate a self-signed certificate
echo "Generating self-signed certificate..."
openssl x509 -req -days 365 -in "$CERT_DIR/client.csr" -signkey "$CERT_DIR/client.key" -out "$CERT_DIR/client.crt"
chmod 644 "$CERT_DIR/client.crt"

# Clean up CSR
rm "$CERT_DIR/client.csr"

# Verify the certificate and key match
echo "Verifying certificate and key match..."
CERT_PUBKEY=$(openssl x509 -in "$CERT_DIR/client.crt" -noout -pubkey | openssl pkey -pubin -outform DER | md5sum)
KEY_PUBKEY=$(openssl ec -in "$CERT_DIR/client.key" -pubout | openssl pkey -pubin -outform DER | md5sum)

if [ "$CERT_PUBKEY" = "$KEY_PUBKEY" ]; then
  echo "✅ Certificate and key match"
else
  echo "❌ Certificate and key do not match"
  exit 1
fi

# Display certificate details
echo "Certificate details:"
openssl x509 -in "$CERT_DIR/client.crt" -noout -text | grep -E "Subject:|Issuer:|Not Before:|Not After :|Public Key Algorithm:"

echo "✅ Client certificate and key generated successfully"
echo "Certificate: $CERT_DIR/client.crt"
echo "Key: $CERT_DIR/client.key"

echo "Next steps:"
echo "1. Update GCP secrets with the new client certificate"
echo "2. Restart your services to pick up the new certificate"
echo "3. Test mTLS connection with the new certificate"
