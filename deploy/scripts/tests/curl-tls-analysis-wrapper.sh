#!/bin/bash
#
# curl-tls-analysis-wrapper.sh - Wrapper for curl-tls-analysis.sh
#
# This script adapts the curl-tls-analysis.sh script to the format expected
# by the run-comprehensive-tests.sh script.
#

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Load master configuration
source "$SCRIPT_DIR/test-config.sh"

# Get parameters from command line or use defaults from config
DOMAIN=${1:-"$DEFAULT_DOMAIN"}
IP=${2:-"$DEFAULT_ORIGIN_IP"}
PORT=${3:-"$DEFAULT_PORT"}
OUTPUT_DIR="$SCRIPT_DIR/test-results"
mkdir -p "$OUTPUT_DIR"

# Always use the fallback mechanism for now
if false; then
  # This block is intentionally disabled
  CURL_TLS_SCRIPT="$SCRIPT_DIR/curl-tls-analysis.sh"
else
  # Try to use curl directly as a fallback
  echo "=== curl TLS Analysis ==="
  echo "Domain: $DOMAIN"
  echo "IP: $IP"
  echo "Port: $PORT"
  echo

  echo "Step 1: Performing TLS connection with curl..."
  CURL_OUTPUT="$OUTPUT_DIR/curl_output.txt"
  HEADERS_FILE="$OUTPUT_DIR/headers.txt"

  # Print request headers for documentation
  print_request_headers
  
  # Build curl command with appropriate headers
  CURL_CMD="curl -v -D \"$HEADERS_FILE\" \"https://$DOMAIN:$PORT\" -o /dev/null"
  CURL_CMD=$(add_cf_access_headers "$CURL_CMD")
  
  # Run curl with verbose output and headers
  eval $CURL_CMD 2> "$CURL_OUTPUT"

  if [ $? -eq 0 ]; then
    echo "✅ curl request completed"

    # Extract TLS version
    TLS_VERSION=$(grep "SSL connection using" "$CURL_OUTPUT" | sed -E 's/.*SSL connection using (TLS|TLSv[0-9.]+).*/\1/')
    echo "TLS Version: $TLS_VERSION"

    # Extract cipher suite
    CIPHER=$(grep "SSL connection using" "$CURL_OUTPUT" | sed -E 's/.*SSL connection using [^/]+\/([^/]+).*/\1/')
    echo "Cipher Suite: $CIPHER"

    # Extract certificate information
    CERT_INFO=$(grep -A 20 "Server certificate:" "$CURL_OUTPUT")
    echo "Certificate Information:"
    echo "$CERT_INFO" | grep -E "subject:|issuer:|start date:|expire date:"

    # Check for security headers
    echo "Security Headers:"
    grep -E "Strict-Transport-Security:|Content-Security-Policy:|X-Content-Type-Options:|X-Frame-Options:" "$HEADERS_FILE" || echo "No security headers found"

    echo "✅ curl TLS Analysis completed successfully"
  else
    echo "❌ curl request failed"
    echo "❌ curl TLS Analysis failed"
  fi

  echo "Results saved to: $OUTPUT_DIR"
  echo "========================================="
  exit 0
fi

echo "=== curl TLS Analysis ==="
echo "Domain: $DOMAIN"
echo "IP: $IP"
echo "Port: $PORT"
echo

# Run the curl TLS analysis
"$CURL_TLS_SCRIPT" "$DOMAIN" "$IP" "$PORT" --output console

# Check if the analysis was successful
if [ $? -eq 0 ]; then
  echo "✅ curl TLS Analysis completed successfully"
else
  echo "❌ curl TLS Analysis failed"
fi

echo "Results saved to: $OUTPUT_DIR"
echo "========================================="
