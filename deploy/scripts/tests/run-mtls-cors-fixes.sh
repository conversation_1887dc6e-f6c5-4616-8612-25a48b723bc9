#!/bin/bash
# Master script to run all mTLS and CORS fixes

# Source master configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test-config.sh"

# Configuration
OUTPUT_DIR="$SCRIPT_DIR/test-results/mtls-cors-fixes"
mkdir -p "$OUTPUT_DIR"

# Create log file
LOG_FILE="$OUTPUT_DIR/fix_execution_$(date +%Y%m%d_%H%M%S).log"
touch "$LOG_FILE"

echo "=== Running mTLS and CORS Fixes ==="
echo "Log file: $LOG_FILE"
echo

# Function to run a script and log the output
run_script() {
  local script_name=$1
  local script_path="$SCRIPT_DIR/$script_name"
  
  echo "🔄 Running $script_name..."
  echo "===== Running $script_name - $(date) =====" >> "$LOG_FILE"
  
  if [ -x "$script_path" ]; then
    echo "Executing: $script_path"
    $script_path | tee -a "$LOG_FILE"
    local exit_code=${PIPESTATUS[0]}
    
    if [ $exit_code -eq 0 ]; then
      echo "✅ $script_name completed successfully"
    else
      echo "❌ $script_name failed with exit code: $exit_code"
      echo "Check the log file for details: $LOG_FILE"
      echo "===== $script_name failed with exit code: $exit_code =====" >> "$LOG_FILE"
      return $exit_code
    fi
  else
    echo "❌ $script_name not found or not executable"
    echo "===== $script_name not found or not executable =====" >> "$LOG_FILE"
    return 1
  fi
  
  echo "===== Completed $script_name - $(date) =====" >> "$LOG_FILE"
  echo
}

# Step 1: Check for errors in the logs
echo "Step 1: Checking for OPTIONS request errors in the logs..."
run_script "check-options-errors.sh"

# Step 2: Fix Cloudflare firewall rules for OPTIONS requests
echo "Step 2: Fixing Cloudflare firewall rules for OPTIONS requests..."
run_script "fix-cloudflare-options-rule.sh"

# Step 3: Fix ENABLE_MTLS environment variable
echo "Step 3: Fixing ENABLE_MTLS environment variable..."
run_script "fix-mtls-enable-value.sh"

# Step 4: Verify CORS fixes
echo "Step 4: Verifying CORS fixes..."
run_script "verify-cors-fixes.sh"

# Step 5: Run comprehensive tests
echo "Step 5: Running comprehensive tests..."
run_script "run-comprehensive-tests.sh api.stage.divinci.app"

echo "=== mTLS and CORS Fixes Complete ==="
echo "Log file: $LOG_FILE"
echo
echo "Summary:"
echo "- Step 1: Check for OPTIONS request errors in the logs"
echo "- Step 2: Fix Cloudflare firewall rules for OPTIONS requests"
echo "- Step 3: Fix ENABLE_MTLS environment variable"
echo "- Step 4: Verify CORS fixes"
echo "- Step 5: Run comprehensive tests"
echo
echo "If any step failed, check the log file for details and fix the issue before continuing."
echo "If all steps succeeded, the mTLS and CORS issues should be fixed."