#!/bin/bash
#
# tls_utils.sh - Common utilities for TLS testing
#
# This library provides common functions for TLS testing,
# including certificate validation, TLS version checking,
# and cipher suite analysis.
#

# Ensure the script is sourced, not executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  echo "Error: This script should be sourced, not executed."
  exit 1
fi

# Global variables
TLS_UTILS_VERSION="1.0.0"
TLS_UTILS_DEBUG=0

# Enable/disable debug mode
tls_utils_debug() {
  TLS_UTILS_DEBUG=$1
}

# Log a debug message
debug() {
  if [[ $TLS_UTILS_DEBUG -eq 1 ]]; then
    echo "[DEBUG] $*" >&2
  fi
}

# Log an info message
info() {
  echo "[INFO] $*"
}

# Log a warning message
warn() {
  echo "[WARN] $*" >&2
}

# Log an error message
error() {
  echo "[ERROR] $*" >&2
}

# Check if a command exists
command_exists() {
  command -v "$1" &>/dev/null
}

# Check if a tool is available with sudo
check_sudo_tool() {
  local tool="$1"
  if command_exists "$tool" && sudo -n true 2>/dev/null; then
    return 0
  else
    return 1
  fi
}

# Check if TShark is available with sudo
check_tshark_sudo() {
  check_sudo_tool "tshark"
}

# Check if TShark works without sudo
check_tshark_no_sudo() {
  if command_exists "tshark"; then
    # Try a simple capture to see if it works without sudo
    if tshark -i any -c 1 -w /dev/null 2>/dev/null; then
      return 0
    fi
  fi
  return 1
}

# Check if OpenSSL is available
check_openssl() {
  command_exists "openssl"
}

# Check if curl is available
check_curl() {
  command_exists "curl"
}

# Validate a certificate
validate_certificate() {
  local cert_file="$1"
  local domain="$2"
  
  if [[ ! -f "$cert_file" ]]; then
    error "Certificate file not found: $cert_file"
    return 1
  fi
  
  # Check certificate validity
  local validity
  validity=$(openssl x509 -in "$cert_file" -noout -dates 2>/dev/null)
  if [[ $? -ne 0 ]]; then
    error "Failed to check certificate validity"
    return 1
  fi
  
  # Extract validity dates
  local not_before
  not_before=$(echo "$validity" | grep "notBefore" | cut -d= -f2)
  local not_after
  not_after=$(echo "$validity" | grep "notAfter" | cut -d= -f2)
  
  # Check if certificate is expired
  local now
  now=$(date +%s)
  local expire_date
  expire_date=$(date -d "$not_after" +%s 2>/dev/null || date -j -f "%b %d %H:%M:%S %Y %Z" "$not_after" +%s 2>/dev/null)
  
  if [[ $now -gt $expire_date ]]; then
    error "Certificate is expired (Expired on: $not_after)"
    return 1
  fi
  
  # Check if domain matches
  if [[ -n "$domain" ]]; then
    local subject_alt_names
    subject_alt_names=$(openssl x509 -in "$cert_file" -noout -text | grep -A1 "Subject Alternative Name" | tail -n1)
    
    if [[ "$subject_alt_names" != *"$domain"* ]]; then
      local common_name
      common_name=$(openssl x509 -in "$cert_file" -noout -subject | sed -n 's/.*CN=\([^,]*\).*/\1/p')
      
      if [[ "$common_name" != "$domain" && "$common_name" != "*.$domain" && "$domain" != *"$common_name" ]]; then
        error "Domain $domain does not match certificate (CN=$common_name, SAN=$subject_alt_names)"
        return 1
      fi
    fi
  fi
  
  return 0
}

# Check if a TLS version is supported
check_tls_version() {
  local host="$1"
  local port="$2"
  local version="$3"
  
  case "$version" in
    1.0)
      openssl_version="tls1"
      ;;
    1.1)
      openssl_version="tls1_1"
      ;;
    1.2)
      openssl_version="tls1_2"
      ;;
    1.3)
      openssl_version="tls1_3"
      ;;
    *)
      error "Invalid TLS version: $version"
      return 1
      ;;
  esac
  
  if echo | openssl s_client -connect "${host}:${port}" -"$openssl_version" -quiet 2>/dev/null | grep -q "CONNECTED"; then
    return 0
  else
    return 1
  fi
}

# Get supported TLS versions
get_supported_tls_versions() {
  local host="$1"
  local port="$2"
  local versions=()
  
  for version in "1.0" "1.1" "1.2" "1.3"; do
    if check_tls_version "$host" "$port" "$version"; then
      versions+=("$version")
    fi
  done
  
  echo "${versions[*]}"
}

# Check if a cipher suite is supported
check_cipher_suite() {
  local host="$1"
  local port="$2"
  local cipher="$3"
  
  if echo | openssl s_client -connect "${host}:${port}" -cipher "$cipher" -quiet 2>/dev/null | grep -q "CONNECTED"; then
    return 0
  else
    return 1
  fi
}

# Get supported cipher suites
get_supported_cipher_suites() {
  local host="$1"
  local port="$2"
  local ciphers
  
  ciphers=$(openssl ciphers 'ALL:eNULL' | tr ':' ' ')
  local supported_ciphers=()
  
  for cipher in $ciphers; do
    if check_cipher_suite "$host" "$port" "$cipher"; then
      supported_ciphers+=("$cipher")
    fi
  done
  
  echo "${supported_ciphers[*]}"
}

# Rate TLS security
rate_tls_security() {
  local host="$1"
  local port="$2"
  
  local score=0
  local max_score=100
  local deductions=0
  local reasons=()
  
  # Check TLS versions
  local supported_versions
  supported_versions=$(get_supported_tls_versions "$host" "$port")
  
  if [[ "$supported_versions" == *"1.0"* ]]; then
    deductions=$((deductions + 20))
    reasons+=("TLS 1.0 is supported (obsolete)")
  fi
  
  if [[ "$supported_versions" == *"1.1"* ]]; then
    deductions=$((deductions + 10))
    reasons+=("TLS 1.1 is supported (obsolete)")
  fi
  
  if [[ "$supported_versions" != *"1.2"* && "$supported_versions" != *"1.3"* ]]; then
    deductions=$((deductions + 30))
    reasons+=("Neither TLS 1.2 nor TLS 1.3 is supported")
  fi
  
  if [[ "$supported_versions" != *"1.3"* ]]; then
    deductions=$((deductions + 5))
    reasons+=("TLS 1.3 is not supported")
  fi
  
  # TODO: Add checks for weak cipher suites
  
  # Calculate final score
  score=$((max_score - deductions))
  if [[ $score -lt 0 ]]; then
    score=0
  fi
  
  # Return results
  echo "Score: $score/100"
  echo "Deductions: $deductions"
  echo "Reasons:"
  for reason in "${reasons[@]}"; do
    echo "- $reason"
  done
}

# Extract certificate from server
extract_certificate() {
  local host="$1"
  local port="$2"
  local output_file="$3"
  
  openssl s_client -connect "${host}:${port}" -showcerts </dev/null 2>/dev/null | 
    awk '/BEGIN CERTIFICATE/,/END CERTIFICATE/{print}' > "$output_file"
  
  if [[ ! -s "$output_file" ]]; then
    error "Failed to extract certificate from ${host}:${port}"
    return 1
  fi
  
  return 0
}

# Get certificate expiration date
get_certificate_expiration() {
  local host="$1"
  local port="$2"
  
  openssl s_client -connect "${host}:${port}" -showcerts </dev/null 2>/dev/null | 
    openssl x509 -noout -enddate | cut -d= -f2
}

# Check if host is reachable
check_host_reachable() {
  local host="$1"
  local port="$2"
  local timeout="${3:-5}"
  
  if command_exists "nc"; then
    nc -z -w "$timeout" "$host" "$port" &>/dev/null
    return $?
  elif command_exists "curl"; then
    curl --connect-timeout "$timeout" -s "https://${host}:${port}" -o /dev/null &>/dev/null
    return $?
  else
    # Fallback to basic test
    (echo > "/dev/tcp/$host/$port") &>/dev/null
    return $?
  fi
}

# Resolve hostname to IP
resolve_hostname() {
  local hostname="$1"
  
  if command_exists "dig"; then
    dig +short "$hostname" | grep -v ";" | head -1
  elif command_exists "host"; then
    host "$hostname" | grep "has address" | head -1 | awk '{print $4}'
  elif command_exists "nslookup"; then
    nslookup "$hostname" | grep "Address" | tail -1 | awk '{print $2}'
  else
    # Fallback to ping
    ping -c 1 "$hostname" | grep "PING" | head -1 | awk '{print $3}' | tr -d '()'
  fi
}

# Get HTTP security headers
get_security_headers() {
  local url="$1"
  
  curl -s -I "$url" | grep -i -E "strict-transport-security|content-security-policy|x-content-type-options|x-frame-options|x-xss-protection|referrer-policy"
}

# Check for common TLS vulnerabilities
check_tls_vulnerabilities() {
  local host="$1"
  local port="$2"
  
  local vulnerabilities=()
  
  # Check for POODLE
  if openssl s_client -connect "${host}:${port}" -ssl3 2>/dev/null | grep -q "CONNECTED"; then
    vulnerabilities+=("POODLE (SSLv3 supported)")
  fi
  
  # Check for FREAK
  if openssl s_client -connect "${host}:${port}" -cipher EXPORT 2>/dev/null | grep -q "CONNECTED"; then
    vulnerabilities+=("FREAK (EXPORT ciphers supported)")
  fi
  
  # Check for DROWN
  if openssl s_client -connect "${host}:${port}" -ssl2 2>/dev/null | grep -q "CONNECTED"; then
    vulnerabilities+=("DROWN (SSLv2 supported)")
  fi
  
  # Return results
  if [[ ${#vulnerabilities[@]} -eq 0 ]]; then
    echo "No common vulnerabilities detected"
  else
    for vuln in "${vulnerabilities[@]}"; do
      echo "- $vuln"
    done
  fi
}

# Version information
tls_utils_version() {
  echo "TLS Utils version $TLS_UTILS_VERSION"
}
