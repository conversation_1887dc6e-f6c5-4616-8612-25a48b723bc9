#!/bin/bash
#
# reporting.sh - Functions for generating reports
#
# This library provides functions for generating reports in various formats,
# including console output, JSON, and Markdown.
#

# Ensure the script is sourced, not executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  echo "Error: This script should be sourced, not executed."
  exit 1
fi

# Global variables
REPORTING_VERSION="1.0.0"
REPORTING_DEBUG=0

# Enable/disable debug mode
reporting_debug() {
  REPORTING_DEBUG=$1
}

# Log a debug message
debug_reporting() {
  if [[ $REPORTING_DEBUG -eq 1 ]]; then
    echo "[DEBUG] $*" >&2
  fi
}

# Generate a console report
generate_console_report() {
  local domain="$1"
  local port="$2"
  local tls_version="$3"
  local cipher_suite="$4"
  local subject="$5"
  local issuer="$6"
  local valid_from="$7"
  local valid_to="$8"
  local headers_file="$9"
  
  echo "=== TLS Analysis Report ==="
  echo "Domain: $domain"
  echo "Port: $port"
  echo
  echo "TLS Information:"
  echo "  Version: $tls_version"
  echo "  Cipher Suite: $cipher_suite"
  echo
  echo "Certificate Information:"
  echo "  Subject: $subject"
  echo "  Issuer: $issuer"
  echo "  Valid From: $valid_from"
  echo "  Valid To: $valid_to"
  echo
  echo "Security Headers:"
  
  if [[ -f "$headers_file" ]]; then
    # Check for HSTS
    if grep -q -i "Strict-Transport-Security:" "$headers_file"; then
      local hsts
      hsts=$(grep -i "Strict-Transport-Security:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "  ✅ HSTS: $hsts"
    else
      echo "  ❌ HSTS: Not found"
    fi
    
    # Check for CSP
    if grep -q -i "Content-Security-Policy:" "$headers_file"; then
      local csp
      csp=$(grep -i "Content-Security-Policy:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "  ✅ CSP: Found (${#csp} characters)"
    else
      echo "  ❌ CSP: Not found"
    fi
    
    # Check for X-Content-Type-Options
    if grep -q -i "X-Content-Type-Options:" "$headers_file"; then
      local xcto
      xcto=$(grep -i "X-Content-Type-Options:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "  ✅ X-Content-Type-Options: $xcto"
    else
      echo "  ❌ X-Content-Type-Options: Not found"
    fi
    
    # Check for X-Frame-Options
    if grep -q -i "X-Frame-Options:" "$headers_file"; then
      local xfo
      xfo=$(grep -i "X-Frame-Options:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "  ✅ X-Frame-Options: $xfo"
    else
      echo "  ❌ X-Frame-Options: Not found"
    fi
    
    # Check for X-XSS-Protection
    if grep -q -i "X-XSS-Protection:" "$headers_file"; then
      local xss
      xss=$(grep -i "X-XSS-Protection:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "  ✅ X-XSS-Protection: $xss"
    else
      echo "  ❌ X-XSS-Protection: Not found"
    fi
    
    # Check for Referrer-Policy
    if grep -q -i "Referrer-Policy:" "$headers_file"; then
      local referrer
      referrer=$(grep -i "Referrer-Policy:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "  ✅ Referrer-Policy: $referrer"
    else
      echo "  ❌ Referrer-Policy: Not found"
    fi
  else
    echo "  No headers file available"
  fi
  
  echo
  echo "Recommendations:"
  if [[ -f "$headers_file" ]]; then
    analyze_security_headers "$headers_file"
  else
    echo "  No headers file available for analysis"
  fi
}

# Generate a JSON report
generate_json_report() {
  local domain="$1"
  local port="$2"
  local trace_file="$3"
  local curl_output="$4"
  local headers_file="$5"
  
  # Parse curl trace and generate JSON
  parse_curl_trace "$trace_file" "$curl_output" "$headers_file"
}

# Generate a Markdown report
generate_markdown_report() {
  local domain="$1"
  local port="$2"
  local tls_version="$3"
  local cipher_suite="$4"
  local subject="$5"
  local issuer="$6"
  local valid_from="$7"
  local valid_to="$8"
  local headers_file="$9"
  
  cat << EOF
# TLS Analysis Report for $domain:$port

## TLS Information

- **Version**: $tls_version
- **Cipher Suite**: $cipher_suite

## Certificate Information

- **Subject**: $subject
- **Issuer**: $issuer
- **Valid From**: $valid_from
- **Valid To**: $valid_to

## Security Headers

EOF
  
  if [[ -f "$headers_file" ]]; then
    # Check for HSTS
    if grep -q -i "Strict-Transport-Security:" "$headers_file"; then
      local hsts
      hsts=$(grep -i "Strict-Transport-Security:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "- ✅ **HSTS**: \`$hsts\`"
    else
      echo "- ❌ **HSTS**: Not found"
    fi
    
    # Check for CSP
    if grep -q -i "Content-Security-Policy:" "$headers_file"; then
      local csp
      csp=$(grep -i "Content-Security-Policy:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "- ✅ **CSP**: Found (${#csp} characters)"
    else
      echo "- ❌ **CSP**: Not found"
    fi
    
    # Check for X-Content-Type-Options
    if grep -q -i "X-Content-Type-Options:" "$headers_file"; then
      local xcto
      xcto=$(grep -i "X-Content-Type-Options:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "- ✅ **X-Content-Type-Options**: \`$xcto\`"
    else
      echo "- ❌ **X-Content-Type-Options**: Not found"
    fi
    
    # Check for X-Frame-Options
    if grep -q -i "X-Frame-Options:" "$headers_file"; then
      local xfo
      xfo=$(grep -i "X-Frame-Options:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "- ✅ **X-Frame-Options**: \`$xfo\`"
    else
      echo "- ❌ **X-Frame-Options**: Not found"
    fi
    
    # Check for X-XSS-Protection
    if grep -q -i "X-XSS-Protection:" "$headers_file"; then
      local xss
      xss=$(grep -i "X-XSS-Protection:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "- ✅ **X-XSS-Protection**: \`$xss\`"
    else
      echo "- ❌ **X-XSS-Protection**: Not found"
    fi
    
    # Check for Referrer-Policy
    if grep -q -i "Referrer-Policy:" "$headers_file"; then
      local referrer
      referrer=$(grep -i "Referrer-Policy:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
      echo "- ✅ **Referrer-Policy**: \`$referrer\`"
    else
      echo "- ❌ **Referrer-Policy**: Not found"
    fi
  else
    echo "No headers file available"
  fi
  
  cat << EOF

## Recommendations

EOF
  
  if [[ -f "$headers_file" ]]; then
    local recommendations
    recommendations=$(analyze_security_headers "$headers_file")
    if [[ "$recommendations" == "No security header recommendations" ]]; then
      echo "No security header recommendations"
    else
      echo "$recommendations" | sed 's/^/- /'
    fi
  else
    echo "No headers file available for analysis"
  fi
}

# Generate a report in the specified format
generate_report() {
  local format="$1"
  local domain="$2"
  local port="$3"
  local trace_file="$4"
  local curl_output="$5"
  local headers_file="$6"
  
  # Extract required information
  local tls_version
  tls_version=$(extract_tls_version "$trace_file" "$curl_output")
  
  local cipher_suite
  cipher_suite=$(extract_cipher_suite "$trace_file" "$curl_output")
  
  local subject
  subject=$(extract_certificate_subject "$curl_output")
  
  local issuer
  issuer=$(extract_certificate_issuer "$curl_output")
  
  local dates
  dates=$(extract_certificate_dates "$curl_output")
  local valid_from
  valid_from=$(echo "$dates" | cut -d'|' -f1)
  local valid_to
  valid_to=$(echo "$dates" | cut -d'|' -f2)
  
  case "$format" in
    console)
      generate_console_report "$domain" "$port" "$tls_version" "$cipher_suite" "$subject" "$issuer" "$valid_from" "$valid_to" "$headers_file"
      ;;
    json)
      generate_json_report "$domain" "$port" "$trace_file" "$curl_output" "$headers_file"
      ;;
    markdown)
      generate_markdown_report "$domain" "$port" "$tls_version" "$cipher_suite" "$subject" "$issuer" "$valid_from" "$valid_to" "$headers_file"
      ;;
    *)
      echo "Error: Unknown report format: $format"
      return 1
      ;;
  esac
}

# Version information
reporting_version() {
  echo "Reporting version $REPORTING_VERSION"
}
