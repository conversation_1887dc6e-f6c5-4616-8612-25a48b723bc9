#!/bin/bash
#
# curl_tls_parser.sh - Functions for parsing curl trace output
#
# This library provides functions for parsing curl trace output
# to extract TLS handshake information, certificate details,
# and other TLS-related data.
#

# Ensure the script is sourced, not executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  echo "Error: This script should be sourced, not executed."
  exit 1
fi

# Global variables
CURL_TLS_PARSER_VERSION="1.0.0"
CURL_TLS_PARSER_DEBUG=0

# Enable/disable debug mode
curl_tls_parser_debug() {
  CURL_TLS_PARSER_DEBUG=$1
}

# Log a debug message
debug_parser() {
  if [[ $CURL_TLS_PARSER_DEBUG -eq 1 ]]; then
    echo "[DEBUG] $*" >&2
  fi
}

# Extract TLS version from curl trace
extract_tls_version() {
  local trace_file="$1"
  local curl_output="$2"
  
  # First try from curl output (more reliable)
  local tls_version
  tls_version=$(grep -a "SSL connection using" "$curl_output" 2>/dev/null | sed -E 's/.*SSL connection using (TLS|TLSv[0-9.]+).*/\1/' | head -1)
  
  # If not found, try from trace file
  if [[ -z "$tls_version" && -f "$trace_file" ]]; then
    tls_version=$(grep -a "TLSv" "$trace_file" | grep -a "ClientHello" | sed -E 's/.*TLSv([0-9.]+).*/TLSv\1/' | head -1)
  fi
  
  # If still not found, try another pattern in trace file
  if [[ -z "$tls_version" && -f "$trace_file" ]]; then
    tls_version=$(grep -a "SSL connection using" "$trace_file" | sed -E 's/.*SSL connection using (TLS|TLSv[0-9.]+).*/\1/' | head -1)
  fi
  
  echo "$tls_version"
}

# Extract cipher suite from curl trace
extract_cipher_suite() {
  local trace_file="$1"
  local curl_output="$2"
  
  # First try from curl output (more reliable)
  local cipher
  cipher=$(grep -a "SSL connection using" "$curl_output" 2>/dev/null | sed -E 's/.*SSL connection using [^/]+\/([^/]+).*/\1/' | head -1)
  
  # If not found, try from trace file
  if [[ -z "$cipher" && -f "$trace_file" ]]; then
    cipher=$(grep -a "Cipher Suite:" "$trace_file" | sed -E 's/.*Cipher Suite: ([^:]+).*/\1/' | head -1)
  fi
  
  echo "$cipher"
}

# Extract certificate subject from curl output
extract_certificate_subject() {
  local curl_output="$1"
  
  local subject
  subject=$(grep -a "subject:" "$curl_output" 2>/dev/null | sed -E 's/.*subject: ([^;]+).*/\1/' | head -1)
  
  echo "$subject"
}

# Extract certificate issuer from curl output
extract_certificate_issuer() {
  local curl_output="$1"
  
  local issuer
  issuer=$(grep -a "issuer:" "$curl_output" 2>/dev/null | sed -E 's/.*issuer: ([^;]+).*/\1/' | head -1)
  
  echo "$issuer"
}

# Extract certificate validity dates from curl output
extract_certificate_dates() {
  local curl_output="$1"
  
  local start_date
  start_date=$(grep -a "start date:" "$curl_output" 2>/dev/null | sed -E 's/.*start date: ([^;]+).*/\1/' | head -1)
  
  local expiry_date
  expiry_date=$(grep -a "expire date:" "$curl_output" 2>/dev/null | sed -E 's/.*expire date: ([^;]+).*/\1/' | head -1)
  
  echo "$start_date|$expiry_date"
}

# Extract TLS handshake time from curl trace
extract_handshake_time() {
  local trace_file="$1"
  
  if [[ ! -f "$trace_file" ]]; then
    echo "0"
    return
  fi
  
  # Find the start of the handshake
  local handshake_start
  handshake_start=$(grep -a -n "ClientHello" "$trace_file" | head -1 | cut -d: -f1)
  
  # Find the end of the handshake
  local handshake_end
  handshake_end=$(grep -a -n "SSL connection using" "$trace_file" | head -1 | cut -d: -f1)
  
  if [[ -z "$handshake_start" || -z "$handshake_end" ]]; then
    echo "0"
    return
  fi
  
  # Extract timestamps
  local start_time
  start_time=$(sed -n "${handshake_start}p" "$trace_file" | grep -o "^[0-9:.]*" | head -1)
  
  local end_time
  end_time=$(sed -n "${handshake_end}p" "$trace_file" | grep -o "^[0-9:.]*" | head -1)
  
  if [[ -z "$start_time" || -z "$end_time" ]]; then
    echo "0"
    return
  fi
  
  # Convert to seconds
  local start_seconds
  start_seconds=$(date -j -f "%H:%M:%S.%N" "$start_time" +%s.%N 2>/dev/null || echo "0")
  
  local end_seconds
  end_seconds=$(date -j -f "%H:%M:%S.%N" "$end_time" +%s.%N 2>/dev/null || echo "0")
  
  if [[ "$start_seconds" == "0" || "$end_seconds" == "0" ]]; then
    echo "0"
    return
  fi
  
  # Calculate difference
  local time_diff
  time_diff=$(echo "$end_seconds - $start_seconds" | bc)
  
  echo "$time_diff"
}

# Extract TLS extensions from curl trace
extract_tls_extensions() {
  local trace_file="$1"
  
  if [[ ! -f "$trace_file" ]]; then
    echo ""
    return
  fi
  
  # Find the extensions section
  local extensions
  extensions=$(grep -a "Extension:" "$trace_file" | sed -E 's/.*Extension: ([^(]+).*/\1/' | sort | uniq)
  
  echo "$extensions"
}

# Extract security headers from headers file
extract_security_headers() {
  local headers_file="$1"
  
  if [[ ! -f "$headers_file" ]]; then
    echo "{}"
    return
  fi
  
  local hsts
  if grep -q -i "Strict-Transport-Security:" "$headers_file"; then
    hsts=$(grep -i "Strict-Transport-Security:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
  else
    hsts=""
  fi
  
  local csp
  if grep -q -i "Content-Security-Policy:" "$headers_file"; then
    csp=$(grep -i "Content-Security-Policy:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
  else
    csp=""
  fi
  
  local xcto
  if grep -q -i "X-Content-Type-Options:" "$headers_file"; then
    xcto=$(grep -i "X-Content-Type-Options:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
  else
    xcto=""
  fi
  
  local xfo
  if grep -q -i "X-Frame-Options:" "$headers_file"; then
    xfo=$(grep -i "X-Frame-Options:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
  else
    xfo=""
  fi
  
  local xss
  if grep -q -i "X-XSS-Protection:" "$headers_file"; then
    xss=$(grep -i "X-XSS-Protection:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
  else
    xss=""
  fi
  
  local referrer
  if grep -q -i "Referrer-Policy:" "$headers_file"; then
    referrer=$(grep -i "Referrer-Policy:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
  else
    referrer=""
  fi
  
  # Create JSON output
  cat << EOF
{
  "strict_transport_security": $(if [ -n "$hsts" ]; then echo "\"$hsts\""; else echo "null"; fi),
  "content_security_policy": $(if [ -n "$csp" ]; then echo "\"$csp\""; else echo "null"; fi),
  "x_content_type_options": $(if [ -n "$xcto" ]; then echo "\"$xcto\""; else echo "null"; fi),
  "x_frame_options": $(if [ -n "$xfo" ]; then echo "\"$xfo\""; else echo "null"; fi),
  "x_xss_protection": $(if [ -n "$xss" ]; then echo "\"$xss\""; else echo "null"; fi),
  "referrer_policy": $(if [ -n "$referrer" ]; then echo "\"$referrer\""; else echo "null"; fi)
}
EOF
}

# Analyze security headers and provide recommendations
analyze_security_headers() {
  local headers_file="$1"
  
  if [[ ! -f "$headers_file" ]]; then
    echo "No headers file available for analysis"
    return
  fi
  
  local recommendations=()
  
  # Check for HSTS
  if ! grep -q -i "Strict-Transport-Security:" "$headers_file"; then
    recommendations+=("Add Strict-Transport-Security header to enforce HTTPS")
  else
    local hsts
    hsts=$(grep -i "Strict-Transport-Security:" "$headers_file" | sed -E 's/[^:]+: (.*)/\1/')
    if [[ "$hsts" != *"max-age=31536000"* ]]; then
      recommendations+=("Set HSTS max-age to at least 31536000 (1 year)")
    fi
    if [[ "$hsts" != *"includeSubDomains"* ]]; then
      recommendations+=("Add includeSubDomains directive to HSTS header")
    fi
    if [[ "$hsts" != *"preload"* ]]; then
      recommendations+=("Consider adding preload directive to HSTS header")
    fi
  fi
  
  # Check for CSP
  if ! grep -q -i "Content-Security-Policy:" "$headers_file"; then
    recommendations+=("Add Content-Security-Policy header to prevent XSS attacks")
  fi
  
  # Check for X-Content-Type-Options
  if ! grep -q -i "X-Content-Type-Options:" "$headers_file"; then
    recommendations+=("Add X-Content-Type-Options: nosniff header to prevent MIME type sniffing")
  fi
  
  # Check for X-Frame-Options
  if ! grep -q -i "X-Frame-Options:" "$headers_file"; then
    recommendations+=("Add X-Frame-Options header to prevent clickjacking")
  fi
  
  # Check for X-XSS-Protection
  if ! grep -q -i "X-XSS-Protection:" "$headers_file"; then
    recommendations+=("Add X-XSS-Protection header to enable browser XSS protection")
  fi
  
  # Check for Referrer-Policy
  if ! grep -q -i "Referrer-Policy:" "$headers_file"; then
    recommendations+=("Add Referrer-Policy header to control referrer information")
  fi
  
  # Output recommendations
  if [[ ${#recommendations[@]} -eq 0 ]]; then
    echo "No security header recommendations"
  else
    echo "Security header recommendations:"
    for rec in "${recommendations[@]}"; do
      echo "- $rec"
    done
  fi
}

# Parse curl trace file and extract all relevant information
parse_curl_trace() {
  local trace_file="$1"
  local curl_output="$2"
  local headers_file="$3"
  
  if [[ ! -f "$trace_file" || ! -f "$curl_output" ]]; then
    echo "Error: Required files not found"
    return 1
  fi
  
  # Extract TLS information
  local tls_version
  tls_version=$(extract_tls_version "$trace_file" "$curl_output")
  
  local cipher_suite
  cipher_suite=$(extract_cipher_suite "$trace_file" "$curl_output")
  
  # Extract certificate information
  local subject
  subject=$(extract_certificate_subject "$curl_output")
  
  local issuer
  issuer=$(extract_certificate_issuer "$curl_output")
  
  local dates
  dates=$(extract_certificate_dates "$curl_output")
  local start_date
  start_date=$(echo "$dates" | cut -d'|' -f1)
  local expiry_date
  expiry_date=$(echo "$dates" | cut -d'|' -f2)
  
  # Extract performance metrics
  local handshake_time
  handshake_time=$(extract_handshake_time "$trace_file")
  
  # Extract TLS extensions
  local extensions
  extensions=$(extract_tls_extensions "$trace_file")
  
  # Extract security headers
  local security_headers
  if [[ -f "$headers_file" ]]; then
    security_headers=$(extract_security_headers "$headers_file")
  else
    security_headers="{}"
  fi
  
  # Create JSON output
  cat << EOF
{
  "tls": {
    "version": $(if [ -n "$tls_version" ]; then echo "\"$tls_version\""; else echo "null"; fi),
    "cipher_suite": $(if [ -n "$cipher_suite" ]; then echo "\"$cipher_suite\""; else echo "null"; fi),
    "extensions": [$(if [ -n "$extensions" ]; then echo "\"$(echo "$extensions" | tr '\n' ',' | sed 's/,$//' | sed 's/,/","/g')\""; else echo ""; fi)]
  },
  "certificate": {
    "subject": $(if [ -n "$subject" ]; then echo "\"$subject\""; else echo "null"; fi),
    "issuer": $(if [ -n "$issuer" ]; then echo "\"$issuer\""; else echo "null"; fi),
    "valid_from": $(if [ -n "$start_date" ]; then echo "\"$start_date\""; else echo "null"; fi),
    "valid_to": $(if [ -n "$expiry_date" ]; then echo "\"$expiry_date\""; else echo "null"; fi)
  },
  "performance": {
    "handshake_time": $handshake_time
  },
  "security_headers": $security_headers
}
EOF
}

# Version information
curl_tls_parser_version() {
  echo "curl TLS Parser version $CURL_TLS_PARSER_VERSION"
}
