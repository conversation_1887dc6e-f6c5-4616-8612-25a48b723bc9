# curl-based TLS Analysis Developer Guide

## Overview

This document provides technical details for developers who want to understand, modify, or extend the curl-based TLS analysis tool. It covers the architecture, code organization, and key components of the implementation.

## Architecture

The curl-based TLS analysis tool follows a modular architecture with these key components:

1. **Main Script** (`curl-tls-analysis.sh`): Orchestrates the analysis process
2. **TLS Utilities** (`tls_utils.sh`): Common TLS-related functions
3. **curl TLS Parser** (`curl_tls_parser.sh`): Functions for parsing curl trace output
4. **Reporting** (`reporting.sh`): Functions for generating reports in various formats

### Component Interaction

```
┌─────────────────┐
│                 │
│ curl-tls-       │
│ analysis.sh     │◄────────┐
│ (Main Script)   │         │
│                 │         │
└────────┬────────┘         │
         │                  │
         ▼                  │
┌─────────────────┐         │
│                 │         │
│  tls_utils.sh   │         │
│ (TLS Utilities) │         │
│                 │         │
└─────────────────┘         │
         │                  │
         ▼                  │
┌─────────────────┐         │
│                 │         │
│ curl_tls_       │         │
│ parser.sh       ├─────────┘
│ (Parser)        │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│  reporting.sh   │
│ (Reporting)     │
│                 │
└─────────────────┘
```

## Code Organization

### Main Script (`curl-tls-analysis.sh`)

The main script is organized into these sections:

1. **Initialization**: Sets up environment and parses command-line arguments
2. **Connection**: Establishes the TLS connection using curl
3. **Analysis**: Analyzes the TLS handshake, certificate, and security headers
4. **Reporting**: Generates reports in the requested format

#### Key Functions

- `print_usage()`: Displays usage information
- Command-line argument parsing
- curl command construction and execution
- Report generation based on output format

### TLS Utilities (`tls_utils.sh`)

This library provides common TLS-related functions:

#### Key Functions

- `check_tls_version()`: Checks if a TLS version is supported
- `validate_certificate()`: Validates a certificate
- `extract_certificate()`: Extracts a certificate from a server
- `check_host_reachable()`: Checks if a host is reachable
- `get_security_headers()`: Gets security headers from a URL
- `check_tls_vulnerabilities()`: Checks for common TLS vulnerabilities

### curl TLS Parser (`curl_tls_parser.sh`)

This library provides functions for parsing curl trace output:

#### Key Functions

- `extract_tls_version()`: Extracts TLS version from curl trace
- `extract_cipher_suite()`: Extracts cipher suite from curl trace
- `extract_certificate_subject()`: Extracts certificate subject
- `extract_certificate_issuer()`: Extracts certificate issuer
- `extract_certificate_dates()`: Extracts certificate validity dates
- `extract_handshake_time()`: Extracts TLS handshake time
- `extract_tls_extensions()`: Extracts TLS extensions
- `extract_security_headers()`: Extracts security headers
- `analyze_security_headers()`: Analyzes security headers and provides recommendations
- `parse_curl_trace()`: Parses curl trace and extracts all relevant information

### Reporting (`reporting.sh`)

This library provides functions for generating reports:

#### Key Functions

- `generate_console_report()`: Generates a console report
- `generate_json_report()`: Generates a JSON report
- `generate_markdown_report()`: Generates a Markdown report
- `generate_report()`: Generates a report in the specified format

## Data Flow

The data flows through the system as follows:

1. **Input**: Domain, port, and options from command-line arguments
2. **curl Execution**: curl connects to the server and captures trace information
3. **Parsing**: The curl trace is parsed to extract TLS information
4. **Analysis**: The extracted information is analyzed
5. **Reporting**: The analysis results are formatted and presented to the user

## Key Files and Their Purposes

| File | Purpose |
|------|---------|
| `bin/curl-tls-analysis.sh` | Main script for TLS analysis using curl |
| `lib/tls_utils.sh` | Common TLS-related functions |
| `lib/curl_tls_parser.sh` | Functions for parsing curl trace output |
| `lib/reporting.sh` | Functions for generating reports |
| `results/` | Directory for storing analysis results |
| `docs/curl-tls-analysis-user-guide.md` | User documentation |
| `docs/curl-tls-analysis-developer-guide.md` | Developer documentation |

## Implementation Details

### curl Trace Format

The curl trace output contains detailed information about the TLS handshake. Here's an example of what it looks like:

```
== Info: Connected to example.com (*************) port 443 (#0)
== Info: ALPN, offering h2
== Info: ALPN, offering http/1.1
== Info: successfully set certificate verify locations:
== Info:   CAfile: /etc/ssl/certs/ca-certificates.crt
== Info:   CApath: /etc/ssl/certs
== Info: TLSv1.3 (OUT), TLS handshake, Client hello (1):
=> Send SSL data, 512 bytes (0x200)
0000: 16 03 01 01 fb 01 00 01 f7 03 03 5f 5e 15 3a 1e ........._^.:.
...
== Info: TLSv1.3 (IN), TLS handshake, Server hello (2):
<= Recv SSL data, 122 bytes (0x7a)
0000: 16 03 03 00 75 02 00 00 71 03 03 00 00 00 00 00 ....u...q.......
...
== Info: TLSv1.3 (IN), TLS handshake, Encrypted Extensions (8):
<= Recv SSL data, 25 bytes (0x19)
0000: 17 03 03 00 14 d0 0b 99 a0 98 36 b6 b6 ee 8b 59 ..........6....Y
...
== Info: TLSv1.3 (IN), TLS handshake, Certificate (11):
<= Recv SSL data, 2281 bytes (0x8e9)
0000: 17 03 03 08 e4 d0 0b 99 a0 98 36 b6 b6 ee 8b 59 ..........6....Y
...
== Info: TLSv1.3 (IN), TLS handshake, CERT verify (15):
<= Recv SSL data, 264 bytes (0x108)
0000: 17 03 03 01 03 d0 0b 99 a0 98 36 b6 b6 ee 8b 59 ..........6....Y
...
== Info: TLSv1.3 (IN), TLS handshake, Finished (20):
<= Recv SSL data, 52 bytes (0x34)
0000: 17 03 03 00 2f d0 0b 99 a0 98 36 b6 b6 ee 8b 59 ..../.....6....Y
...
== Info: TLSv1.3 (OUT), TLS change cipher, Change cipher spec (1):
=> Send SSL data, 1 bytes (0x1)
0000: 14 03 03 00 01 01                               ......
== Info: TLSv1.3 (OUT), TLS handshake, Finished (20):
=> Send SSL data, 52 bytes (0x34)
0000: 17 03 03 00 2f 00 00 00 00 00 00 00 00 00 00 00 ..../...........
...
== Info: SSL connection using TLSv1.3 / TLS_AES_256_GCM_SHA384
```

The parser extracts information from this trace, including:
- TLS version
- Cipher suite
- Handshake details
- Extensions

### Certificate Information

Certificate information is extracted from the curl verbose output:

```
* Server certificate:
*  subject: CN=example.com
*  start date: Nov  9 00:00:00 2021 GMT
*  expire date: Dec 10 23:59:59 2022 GMT
*  subjectAltName: host "example.com" matched cert's "example.com"
*  issuer: C=US; O=DigiCert Inc; CN=DigiCert TLS RSA SHA256 2020 CA1
*  SSL certificate verify ok.
```

### Security Headers

Security headers are extracted from the HTTP response headers:

```
HTTP/2 200 
age: 499385
cache-control: max-age=604800
content-type: text/html; charset=UTF-8
date: Mon, 13 Jun 2022 12:34:56 GMT
etag: "3147526947+ident"
expires: Mon, 20 Jun 2022 12:34:56 GMT
last-modified: Thu, 17 Oct 2019 07:18:26 GMT
server: ECS (nyb/1D10)
strict-transport-security: max-age=31536000
x-cache: HIT
x-content-type-options: nosniff
content-length: 1256
```

## Extending the Tool

### Adding New Output Formats

To add a new output format:

1. Add a new case to the `OUTPUT_FORMAT` handling in `curl-tls-analysis.sh`
2. Create a new generator function in `reporting.sh`
3. Update the usage information and documentation

Example:

```bash
# In curl-tls-analysis.sh
case "$OUTPUT_FORMAT" in
  json)
    # Generate JSON report
    generate_json_report "$DOMAIN" "$PORT" "$TRACE_FILE" "$RESULTS_DIR/${DOMAIN}_${PORT}_curl_output.txt" "$HEADERS_FILE" > "$OUTPUT_FILE"
    echo "✅ JSON report generated: $OUTPUT_FILE"
    cat "$OUTPUT_FILE"
    ;;
  markdown)
    # Generate Markdown report
    generate_markdown_report "$DOMAIN" "$PORT" "$TLS_VERSION" "$CIPHER" "$SUBJECT" "$ISSUER" "$START_DATE" "$EXPIRY_DATE" "$HEADERS_FILE" > "$RESULTS_DIR/${DOMAIN}_${PORT}_report.md"
    echo "✅ Markdown report generated: $RESULTS_DIR/${DOMAIN}_${PORT}_report.md"
    cat "$RESULTS_DIR/${DOMAIN}_${PORT}_report.md"
    ;;
  html) # New format
    # Generate HTML report
    generate_html_report "$DOMAIN" "$PORT" "$TLS_VERSION" "$CIPHER" "$SUBJECT" "$ISSUER" "$START_DATE" "$EXPIRY_DATE" "$HEADERS_FILE" > "$RESULTS_DIR/${DOMAIN}_${PORT}_report.html"
    echo "✅ HTML report generated: $RESULTS_DIR/${DOMAIN}_${PORT}_report.html"
    ;;
  console)
    # Generate console report (already displayed during execution)
    echo "✅ Console report displayed above"
    ;;
  *)
    echo "Error: Unknown output format: $OUTPUT_FORMAT"
    exit 1
    ;;
esac
```

```bash
# In reporting.sh
# Generate an HTML report
generate_html_report() {
  local domain="$1"
  local port="$2"
  local tls_version="$3"
  local cipher_suite="$4"
  local subject="$5"
  local issuer="$6"
  local valid_from="$7"
  local valid_to="$8"
  local headers_file="$9"
  
  cat << EOF
<!DOCTYPE html>
<html>
<head>
  <title>TLS Analysis for $domain:$port</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1 { color: #333; }
    .success { color: green; }
    .failure { color: red; }
  </style>
</head>
<body>
  <h1>TLS Analysis for $domain:$port</h1>
  
  <h2>TLS Information</h2>
  <ul>
    <li><strong>Version:</strong> $tls_version</li>
    <li><strong>Cipher Suite:</strong> $cipher_suite</li>
  </ul>
  
  <!-- More HTML content -->
</body>
</html>
EOF
}
```

### Adding New Analysis Features

To add new analysis features:

1. Add new extraction functions to `curl_tls_parser.sh`
2. Update the parsing logic in `parse_curl_trace()`
3. Update the report generation functions to include the new information

Example:

```bash
# In curl_tls_parser.sh
# Extract OCSP stapling information
extract_ocsp_stapling() {
  local curl_output="$1"
  
  if grep -q "OCSP response:" "$curl_output"; then
    echo "true"
  else
    echo "false"
  fi
}

# Update parse_curl_trace() to include the new information
parse_curl_trace() {
  # Existing code...
  
  # Extract OCSP stapling information
  local ocsp_stapling
  ocsp_stapling=$(extract_ocsp_stapling "$curl_output")
  
  # Create JSON output
  cat << EOF
{
  "tls": {
    "version": $(if [ -n "$tls_version" ]; then echo "\"$tls_version\""; else echo "null"; fi),
    "cipher_suite": $(if [ -n "$cipher_suite" ]; then echo "\"$cipher_suite\""; else echo "null"; fi),
    "extensions": [$(if [ -n "$extensions" ]; then echo "\"$(echo "$extensions" | tr '\n' ',' | sed 's/,$//' | sed 's/,/","/g')\""; else echo ""; fi)],
    "ocsp_stapling": $(if [ "$ocsp_stapling" = "true" ]; then echo "true"; else echo "false"; fi)
  },
  # Rest of the JSON...
}
EOF
}
```

### Integration with Other Tools

You can integrate the tool with other tools by:

1. Using the JSON output format for machine-readable results
2. Creating wrapper scripts that call the tool and process its output
3. Adding new output formats specific to other tools

Example wrapper script:

```bash
#!/bin/bash
# wrapper.sh - Integrate curl-tls-analysis with another tool

# Run curl-tls-analysis and capture JSON output
json_output=$(./bin/curl-tls-analysis.sh "$1" --output json)

# Extract specific information
tls_version=$(echo "$json_output" | jq -r '.tls.version')
cipher_suite=$(echo "$json_output" | jq -r '.tls.cipher_suite')

# Use the information with another tool
other_tool --tls-version "$tls_version" --cipher "$cipher_suite"
```

## Testing

### Unit Testing

You can create unit tests for the individual functions:

```bash
#!/bin/bash
# test_curl_tls_parser.sh - Unit tests for curl_tls_parser.sh

source "../lib/curl_tls_parser.sh"

# Test extract_tls_version
test_extract_tls_version() {
  local test_file="test_data/curl_output.txt"
  local expected="TLSv1.3"
  local result=$(extract_tls_version "" "$test_file")
  
  if [ "$result" = "$expected" ]; then
    echo "PASS: extract_tls_version"
  else
    echo "FAIL: extract_tls_version - Expected: $expected, Got: $result"
  fi
}

# Run tests
test_extract_tls_version
# More tests...
```

### Integration Testing

You can create integration tests that run the full tool:

```bash
#!/bin/bash
# test_integration.sh - Integration tests for curl-tls-analysis.sh

# Test basic functionality
test_basic() {
  local result=$(./bin/curl-tls-analysis.sh example.com --output json)
  local tls_version=$(echo "$result" | jq -r '.tls.version')
  
  if [ -n "$tls_version" ]; then
    echo "PASS: Basic functionality"
  else
    echo "FAIL: Basic functionality - No TLS version found"
  fi
}

# Run tests
test_basic
# More tests...
```

## Performance Considerations

The tool is generally lightweight, but here are some performance considerations:

1. **Network Latency**: The tool's performance is primarily limited by network latency when connecting to the server.

2. **Trace File Size**: For servers with large certificate chains or many extensions, the trace file can become large, which may impact parsing performance.

3. **Multiple Runs**: When analyzing multiple domains, consider running the tool in parallel to improve overall throughput.

## Security Considerations

When using or extending the tool, consider these security aspects:

1. **Credential Handling**: The tool doesn't currently support client certificates or authentication. If adding this feature, ensure credentials are handled securely.

2. **Server Trust**: The tool trusts the system's certificate store. Be cautious when disabling certificate validation for testing purposes.

3. **Output Sanitization**: When extending the tool to generate HTML or other formats that might be rendered, ensure proper output sanitization to prevent XSS or other injection attacks.

## Conclusion

This developer guide provides the technical details needed to understand, modify, and extend the curl-based TLS analysis tool. By following the modular architecture and coding patterns established in the existing code, you can add new features while maintaining compatibility with the rest of the TLS Testing Framework.
