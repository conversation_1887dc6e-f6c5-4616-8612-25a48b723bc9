# Contributing to the TLS Testing Framework

Thank you for your interest in contributing to the TLS Testing Framework! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Documentation](#documentation)
- [Submitting Changes](#submitting-changes)
- [Review Process](#review-process)

## Code of Conduct

Please be respectful and considerate of others when contributing to this project. We aim to foster an inclusive and welcoming community.

## Getting Started

1. **Fork the Repository**

   Start by forking the repository to your own GitHub account.

2. **Clone Your Fork**

   ```bash
   git clone https://github.com/YOUR-USERNAME/tls-testing.git
   cd tls-testing
   ```

3. **Add Upstream Remote**

   ```bash
   git remote add upstream https://github.com/divinci/tls-testing.git
   ```

4. **Install Dependencies**

   ```bash
   # For Debian/Ubuntu
   sudo apt-get install curl openssl jq tshark nmap

   # For macOS
   brew install curl openssl jq wireshark nmap
   ```

5. **Make Scripts Executable**

   ```bash
   chmod +x bin/*.sh lib/*.sh
   ```

## Development Workflow

1. **Create a Branch**

   Create a branch for your changes:

   ```bash
   git checkout -b feature/your-feature-name
   ```

   Use a descriptive branch name that reflects the changes you're making.

2. **Make Your Changes**

   Implement your changes, following the [coding standards](#coding-standards).

3. **Commit Your Changes**

   Commit your changes with a clear and descriptive commit message:

   ```bash
   git commit -m "Add feature: description of your changes"
   ```

4. **Keep Your Branch Updated**

   Regularly sync your branch with the upstream repository:

   ```bash
   git fetch upstream
   git rebase upstream/main
   ```

5. **Push Your Changes**

   Push your changes to your fork:

   ```bash
   git push origin feature/your-feature-name
   ```

## Coding Standards

### Shell Script Style

- Use 2-space indentation
- Use `#!/bin/bash` as the shebang line
- Add a comment block at the top of each script describing its purpose
- Use lowercase for variable names
- Use uppercase for constants and environment variables
- Use snake_case for function names
- Quote all variable references: `"$variable"`
- Use `[[ ]]` for conditional tests
- Add error handling for commands that might fail
- Use meaningful variable and function names

### Example

```bash
#!/bin/bash
#
# example-script.sh - Example script demonstrating coding standards
#
# This script demonstrates the coding standards for the TLS Testing Framework.
#

# Constants
readonly DEFAULT_TIMEOUT=10
readonly DEFAULT_PORT=443

# Function to print usage information
print_usage() {
  echo "Usage: $0 [options] <domain>"
  echo
  echo "Options:"
  echo "  --timeout <seconds>   Connection timeout (default: $DEFAULT_TIMEOUT)"
  echo "  --help                Show this help message"
}

# Function to check if a command exists
command_exists() {
  command -v "$1" &>/dev/null
}

# Main script logic
main() {
  local domain=""
  local timeout="$DEFAULT_TIMEOUT"
  
  # Parse command-line arguments
  while [[ $# -gt 0 ]]; do
    case "$1" in
      --timeout)
        timeout="$2"
        shift 2
        ;;
      --help)
        print_usage
        exit 0
        ;;
      *)
        if [[ -z "$domain" ]]; then
          domain="$1"
        else
          echo "Error: Unexpected argument: $1"
          print_usage
          exit 1
        fi
        shift
        ;;
    esac
  done
  
  # Check required arguments
  if [[ -z "$domain" ]]; then
    echo "Error: Domain is required"
    print_usage
    exit 1
  fi
  
  # Check dependencies
  if ! command_exists curl; then
    echo "Error: curl is required but not installed"
    exit 1
  fi
  
  # Execute the command with error handling
  if ! curl --connect-timeout "$timeout" -s "https://$domain" -o /dev/null; then
    echo "Error: Failed to connect to $domain"
    exit 1
  fi
  
  echo "Successfully connected to $domain"
}

# Run the main function
main "$@"
```

## Testing

Before submitting your changes, please test them thoroughly:

1. **Test on Different Platforms**

   If possible, test your changes on both Linux and macOS.

2. **Test with Different Domains**

   Test with various domains to ensure your changes work in different scenarios.

3. **Test Edge Cases**

   Consider edge cases such as:
   - Domains with invalid certificates
   - Domains that don't support HTTPS
   - Domains with unusual TLS configurations
   - Domains that are unreachable

4. **Test Without Sudo**

   Ensure that features that don't require sudo work correctly without elevated privileges.

## Documentation

Please update the documentation to reflect your changes:

1. **Update User Guide**

   If you add or modify user-facing features, update the user guide.

2. **Update Developer Guide**

   If you change the internal architecture or add new components, update the developer guide.

3. **Update Quick Start Guide**

   If you add new basic functionality, update the quick start guide.

4. **Update README**

   If necessary, update the main README.md file.

5. **Add Comments**

   Add clear comments to your code explaining complex logic or non-obvious decisions.

## Submitting Changes

1. **Create a Pull Request**

   Create a pull request from your branch to the main repository.

2. **Describe Your Changes**

   In the pull request description:
   - Clearly describe what your changes do
   - Explain why these changes are needed
   - Mention any related issues
   - List any dependencies or requirements

3. **Review Your Code**

   Before submitting, review your own code to ensure it meets the project's standards.

## Review Process

After you submit a pull request:

1. **Automated Checks**

   Automated checks will run to verify that your changes meet the basic requirements.

2. **Code Review**

   A project maintainer will review your code and may suggest changes.

3. **Addressing Feedback**

   If changes are requested, update your branch and push the changes. The pull request will update automatically.

4. **Merging**

   Once your changes are approved, a maintainer will merge them into the main branch.

Thank you for contributing to the TLS Testing Framework!
