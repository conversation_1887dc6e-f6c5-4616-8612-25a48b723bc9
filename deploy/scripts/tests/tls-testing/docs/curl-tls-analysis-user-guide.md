# curl-based TLS Analysis User Guide

## Overview

The curl-based TLS Analysis tool provides detailed insights into TLS connections without requiring sudo privileges. It uses curl's trace functionality to analyze TLS handshakes, certificates, and HTTP security headers, making it suitable for CI/CD pipelines and environments where elevated privileges are not available.

## Features

- **TLS Handshake Analysis**: Examines the TLS handshake process, including version negotiation and cipher suite selection
- **Certificate Validation**: Verifies certificate chain, validity, and domain matching
- **Security Header Evaluation**: Checks for the presence and configuration of HTTP security headers
- **Performance Metrics**: Measures TLS handshake time and connection performance
- **Multiple Output Formats**: Supports console, JSON, and Markdown output formats
- **No Sudo Required**: Works without elevated privileges

## Installation

The curl-based TLS Analysis tool is part of the TLS Testing Framework. To install:

1. Ensure you have the required dependencies:
   ```bash
   # For Debian/Ubuntu
   sudo apt-get install curl openssl jq

   # For macOS
   brew install curl openssl jq
   ```

2. Make the script executable:
   ```bash
   chmod +x tls-testing/bin/curl-tls-analysis.sh
   ```

## Basic Usage

### Analyzing a Domain

To analyze a domain's TLS configuration:

```bash
./bin/curl-tls-analysis.sh example.com
```

This will:
1. Connect to example.com on port 443 (default)
2. Capture the TLS handshake
3. Analyze the certificate
4. Check for security headers
5. Display the results in the console

### Specifying Port and IP

You can specify a port and IP address:

```bash
./bin/curl-tls-analysis.sh example.com ************* 443
```

This is useful when:
- Testing a specific server in a load-balanced environment
- Analyzing a server that's not yet in DNS
- Bypassing DNS for more consistent testing

### Output Formats

The tool supports multiple output formats:

#### Console Output (Default)

```bash
./bin/curl-tls-analysis.sh example.com
```

Displays results directly in the terminal with color-coded indicators.

#### JSON Output

```bash
./bin/curl-tls-analysis.sh example.com --output json
```

Produces a structured JSON output that can be parsed by other tools or stored for later analysis.

#### Markdown Output

```bash
./bin/curl-tls-analysis.sh example.com --output markdown
```

Generates a Markdown report suitable for documentation or sharing with non-technical stakeholders.

## Advanced Options

### TLS Version Selection

You can specify which TLS version to test:

```bash
./bin/curl-tls-analysis.sh example.com --tls-version 1.2
```

Supported versions:
- `1.0` (TLS 1.0)
- `1.1` (TLS 1.1)
- `1.2` (TLS 1.2)
- `1.3` (TLS 1.3, default)

### Connection Timeout

You can set a custom connection timeout:

```bash
./bin/curl-tls-analysis.sh example.com --timeout 15
```

This sets the connection timeout to 15 seconds (default is 10 seconds).

### Verbose Mode

For more detailed output:

```bash
./bin/curl-tls-analysis.sh example.com --verbose
```

This shows additional information, including the exact curl command being executed.

## Understanding the Results

### TLS Information

The tool reports these TLS details:

- **TLS Version**: The negotiated TLS protocol version (e.g., TLSv1.3)
- **Cipher Suite**: The selected cipher suite (e.g., TLS_AES_256_GCM_SHA384)
- **TLS Extensions**: Extensions used in the handshake (if available)
- **Handshake Time**: Time taken to complete the TLS handshake (if available)

### Certificate Information

Certificate details include:

- **Subject**: The certificate subject (who it was issued to)
- **Issuer**: The certificate issuer (who issued it)
- **Valid From**: Start date of the certificate validity period
- **Valid To**: Expiration date of the certificate

### Security Headers

The tool checks for these security headers:

- **Strict-Transport-Security (HSTS)**: Enforces HTTPS connections
- **Content-Security-Policy (CSP)**: Prevents XSS attacks
- **X-Content-Type-Options**: Prevents MIME type sniffing
- **X-Frame-Options**: Prevents clickjacking
- **X-XSS-Protection**: Enables browser XSS filtering
- **Referrer-Policy**: Controls referrer information

### Security Recommendations

Based on the analysis, the tool provides recommendations for improving security, such as:

- Adding missing security headers
- Improving header configurations
- Addressing certificate issues

## Example Output

### Console Output Example

```
=== curl TLS Analysis ===
Domain: example.com
Port: 443
TLS Version: TLSv1.3

Step 1: Performing TLS connection with curl...
✅ curl request completed

Step 2: Analyzing TLS handshake...
TLS Version: TLSv1.3
Cipher Suite: TLS_AES_256_GCM_SHA384
TLS Extensions:
  - server_name
  - ec_point_formats
  - supported_groups
  - session_ticket
  - heartbeat
TLS Handshake Time: 0.342s

Step 3: Analyzing certificate...
Certificate Subject: CN=example.com
Certificate Issuer: C=US, O=DigiCert Inc, CN=DigiCert TLS RSA SHA256 2020 CA1
Valid From: Nov  9 00:00:00 2021 GMT
Valid To: Dec 10 23:59:59 2022 GMT

Step 4: Analyzing HTTP security headers...
✅ HSTS: max-age=31536000
❌ CSP header not found
✅ X-Content-Type-Options: nosniff
❌ X-Frame-Options header not found
❌ X-XSS-Protection header not found
❌ Referrer-Policy header not found

Security Header Recommendations:
- Add Content-Security-Policy header to prevent XSS attacks
- Add X-Frame-Options header to prevent clickjacking
- Add X-XSS-Protection header to enable browser XSS protection
- Add Referrer-Policy header to control referrer information

Step 5: Generating report...
✅ Console report displayed above

=== curl TLS Analysis Complete ===
Results saved to /path/to/tls-testing/results/
```

### JSON Output Example

```json
{
  "tls": {
    "version": "TLSv1.3",
    "cipher_suite": "TLS_AES_256_GCM_SHA384",
    "extensions": ["server_name", "ec_point_formats", "supported_groups", "session_ticket", "heartbeat"]
  },
  "certificate": {
    "subject": "CN=example.com",
    "issuer": "C=US, O=DigiCert Inc, CN=DigiCert TLS RSA SHA256 2020 CA1",
    "valid_from": "Nov  9 00:00:00 2021 GMT",
    "valid_to": "Dec 10 23:59:59 2022 GMT"
  },
  "performance": {
    "handshake_time": 0.342
  },
  "security_headers": {
    "strict_transport_security": "max-age=31536000",
    "content_security_policy": null,
    "x_content_type_options": "nosniff",
    "x_frame_options": null,
    "x_xss_protection": null,
    "referrer_policy": null
  }
}
```

## Troubleshooting

### Connection Issues

If you encounter connection problems:

1. **Check Network Connectivity**:
   ```bash
   ping example.com
   ```

2. **Verify DNS Resolution**:
   ```bash
   dig example.com
   ```

3. **Try with IP Address**:
   ```bash
   ./bin/curl-tls-analysis.sh example.com ************* 443
   ```

4. **Increase Timeout**:
   ```bash
   ./bin/curl-tls-analysis.sh example.com --timeout 30
   ```

### Certificate Issues

If certificate validation fails:

1. **Check System Time**:
   Ensure your system clock is accurate, as certificate validation depends on the current time.

2. **Check Certificate Chain**:
   The server might not be sending the complete certificate chain.

3. **Check for SNI Issues**:
   Some servers require SNI (Server Name Indication) to present the correct certificate.

### Parsing Errors

If the tool fails to parse the output:

1. **Run in Verbose Mode**:
   ```bash
   ./bin/curl-tls-analysis.sh example.com --verbose
   ```

2. **Check the Raw Output**:
   Examine the raw curl output files in the results directory.

## Integration with Other Tools

### CI/CD Integration

You can integrate the tool into CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
name: TLS Security Check
on: [push, pull_request]
jobs:
  tls-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install dependencies
        run: sudo apt-get install -y curl openssl jq
      - name: Run TLS analysis
        run: ./bin/curl-tls-analysis.sh example.com --output json > tls-report.json
      - name: Check for security issues
        run: |
          if grep -q "\"strict_transport_security\": null" tls-report.json; then
            echo "HSTS header missing!"
            exit 1
          fi
```

### Batch Processing

You can use the tool to analyze multiple domains:

```bash
#!/bin/bash
domains=("example.com" "example.org" "example.net")
for domain in "${domains[@]}"; do
  echo "Analyzing $domain..."
  ./bin/curl-tls-analysis.sh "$domain" --output json > "results/${domain}_report.json"
done
```

## Further Reading

- [OWASP Transport Layer Protection Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Transport_Layer_Protection_Cheat_Sheet.html)
- [Mozilla SSL Configuration Generator](https://ssl-config.mozilla.org/)
- [SSL Labs Server Test](https://www.ssllabs.com/ssltest/)
- [Security Headers](https://securityheaders.com/)

## Support

If you encounter any issues or have questions, please:

1. Check the [Troubleshooting](#troubleshooting) section
2. Review the [GitHub Issues](https://github.com/divinci/tls-testing/issues)
3. Submit a new issue if needed
