# TLS Testing Framework - Quick Start Guide

This guide will help you get started with the TLS Testing Framework quickly. It covers installation, basic usage, and common tasks.

## Installation

1. **Clone the Repository**

   ```bash
   git clone https://github.com/divinci/tls-testing.git
   cd tls-testing
   ```

2. **Install Dependencies**

   For Debian/Ubuntu:
   ```bash
   sudo apt-get install curl openssl jq tshark nmap
   ```

   For macOS:
   ```bash
   brew install curl openssl jq wireshark nmap
   ```

3. **Make Scripts Executable**

   ```bash
   chmod +x bin/*.sh
   ```

## Basic Usage

### Running All Tests

To run all available tests on a domain:

```bash
./bin/run-tls-tests.sh example.com
```

This will:
- Detect available testing tools
- Run appropriate tests based on available tools
- Display results in the console
- Save detailed results to the `results/` directory

### Running Specific Tests

To run a specific test:

```bash
# Run curl-based TLS analysis
./bin/curl-tls-analysis.sh example.com

# Run OpenSSL TLS analysis
./bin/openssl-tls-analysis.sh example.com

# Run TShark TLS analysis (requires sudo)
sudo ./bin/tshark-tls-analysis.sh example.com

# Run nmap SSL scan
./bin/nmap-ssl-scan.sh example.com
```

### Output Formats

You can specify different output formats:

```bash
# JSON output
./bin/curl-tls-analysis.sh example.com --output json

# Markdown output
./bin/curl-tls-analysis.sh example.com --output markdown

# Console output (default)
./bin/curl-tls-analysis.sh example.com --output console
```

## Common Tasks

### Testing a Specific Port

```bash
./bin/curl-tls-analysis.sh example.com 443
```

### Testing a Specific IP Address

```bash
./bin/curl-tls-analysis.sh example.com ************* 443
```

### Testing a Specific TLS Version

```bash
./bin/curl-tls-analysis.sh example.com --tls-version 1.2
```

### Running Without Sudo

```bash
./bin/run-tls-tests.sh example.com --no-sudo
```

### Increasing Verbosity

```bash
./bin/curl-tls-analysis.sh example.com --verbose
```

### Setting a Custom Timeout

```bash
./bin/curl-tls-analysis.sh example.com --timeout 15
```

## Understanding Results

The framework provides information about:

1. **TLS Configuration**
   - Protocol version
   - Cipher suite
   - TLS extensions

2. **Certificate Details**
   - Subject
   - Issuer
   - Validity period
   - Key information

3. **Security Headers**
   - HSTS
   - CSP
   - X-Content-Type-Options
   - X-Frame-Options
   - And more...

4. **Security Recommendations**
   - Missing security headers
   - Certificate issues
   - TLS configuration improvements

## Example Workflow

Here's a typical workflow for analyzing a website's TLS configuration:

1. **Run a quick analysis with curl**

   ```bash
   ./bin/curl-tls-analysis.sh example.com
   ```

2. **Check for specific security headers**

   ```bash
   ./bin/curl-tls-analysis.sh example.com | grep -A 10 "Security Headers"
   ```

3. **Generate a report for documentation**

   ```bash
   ./bin/curl-tls-analysis.sh example.com --output markdown > example_com_tls_report.md
   ```

4. **Run a comprehensive analysis (if sudo is available)**

   ```bash
   sudo ./bin/run-tls-tests.sh example.com
   ```

## Next Steps

After getting started with the basic functionality, you might want to:

1. Read the [User Guide](./curl-tls-analysis-user-guide.md) for detailed information about the curl-based TLS analysis tool
2. Check the [Developer Guide](./curl-tls-analysis-developer-guide.md) if you want to extend or modify the tool
3. Review the [Technical Specification](./Technical-Specification.md) for details about the framework architecture
4. Look at the [Roadmap](./ROADMAP.md) to see planned features and improvements

## Troubleshooting

If you encounter issues:

1. **Check Dependencies**

   ```bash
   # Check curl version
   curl --version
   
   # Check OpenSSL version
   openssl version
   
   # Check TShark version
   tshark --version
   ```

2. **Verify Network Connectivity**

   ```bash
   ping example.com
   ```

3. **Run with Verbose Output**

   ```bash
   ./bin/curl-tls-analysis.sh example.com --verbose
   ```

4. **Check Permissions**

   ```bash
   # Make sure scripts are executable
   chmod +x bin/*.sh lib/*.sh
   ```

5. **Check Output Directory**

   ```bash
   # Make sure the results directory exists and is writable
   mkdir -p results
   chmod 755 results
   ```

## Getting Help

If you need further assistance:

1. Check the documentation in the `docs/` directory
2. Look for similar issues in the GitHub repository
3. Submit a new issue with detailed information about your problem

Happy testing!
