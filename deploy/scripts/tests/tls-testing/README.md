# TLS Testing Framework

A comprehensive framework for testing TLS/SSL implementations, with a focus on security, reliability, and compatibility.

## Overview

This framework provides a suite of tools for analyzing TLS connections using multiple methods, including packet capture (TShark/Wireshark), OpenSSL, and curl. It's designed to work in various environments, with or without sudo privileges, and provides detailed insights into TLS implementation quality.

## Features

- **Multiple Analysis Methods**: Uses TShark, OpenSSL, curl, and other tools to provide comprehensive TLS analysis
- **Graceful Fallback**: Automatically selects the best available method based on environment constraints
- **Detailed Reporting**: Provides structured output in multiple formats (console, JSON, Markdown)
- **Security Assessment**: Evaluates TLS configuration against best practices and security standards
- **Certificate Validation**: Verifies certificate chain, validity, and domain matching
- **Performance Metrics**: Measures TLS handshake performance and connection times
- **Error Diagnostics**: Provides detailed error information and troubleshooting guidance

## Directory Structure

```
tls-testing/
├── bin/                  # Executable scripts
├── lib/                  # Shared libraries
├── tests/                # Individual test implementations
├── config/               # Configuration files
├── docs/                 # Documentation
└── results/              # Test results
```

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/divinci/tls-testing.git
   ```

2. Ensure dependencies are installed:
   ```bash
   # For Debian/Ubuntu
   sudo apt-get install tshark openssl curl jq

   # For macOS
   brew install wireshark openssl curl jq
   ```

3. Make scripts executable:
   ```bash
   chmod +x tls-testing/bin/*.sh
   ```

## Usage

### Basic Usage

```bash
# Run all TLS tests
./bin/run-tls-tests.sh example.com

# Specify port
./bin/run-tls-tests.sh example.com 443

# Run specific test
./bin/run-tls-tests.sh example.com 443 --test curl
```

### Advanced Options

```bash
# Output format
./bin/run-tls-tests.sh example.com --output json

# Verbose mode
./bin/run-tls-tests.sh example.com --verbose

# Skip sudo tests
./bin/run-tls-tests.sh example.com --no-sudo

# Use specific TLS version
./bin/run-tls-tests.sh example.com --tls-version 1.2
```

## Test Types

### TShark/Wireshark Analysis

Provides detailed packet-level analysis of the TLS handshake, including:
- TLS version negotiation
- Cipher suite selection
- Certificate chain
- TLS extensions
- Protocol-level details

### OpenSSL Analysis

Uses OpenSSL's s_client to analyze TLS connections:
- TLS version and cipher information
- Certificate details
- Handshake process
- Connection parameters

### curl Analysis

Uses curl with trace options to analyze TLS connections:
- TLS handshake information
- HTTP headers and security features
- Performance metrics
- Certificate details

## Configuration

Configuration files are located in the `config/` directory:

- `default.conf`: Default configuration for all tests
- `tshark.conf`: TShark-specific configuration
- `openssl.conf`: OpenSSL-specific configuration
- `curl.conf`: curl-specific configuration

## Contributing

See [CONTRIBUTING.md](./docs/CONTRIBUTING.md) for guidelines on how to contribute to this project.

## License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## Acknowledgments

- [Wireshark/TShark](https://www.wireshark.org/)
- [OpenSSL](https://www.openssl.org/)
- [curl](https://curl.se/)
- [nmap](https://nmap.org/)
