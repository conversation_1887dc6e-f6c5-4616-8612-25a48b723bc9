#!/bin/bash
#
# curl-tls-analysis.sh - TLS analysis using curl
#
# This script analyzes TLS connections using curl's trace functionality,
# providing detailed information about the TLS handshake, certificate,
# and HTTP security headers without requiring sudo privileges.
#

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LIB_DIR="$(dirname "$SCRIPT_DIR")/lib"
RESULTS_DIR="$(dirname "$SCRIPT_DIR")/results"

# Source common libraries
source "$LIB_DIR/tls_utils.sh"
source "$LIB_DIR/curl_tls_parser.sh"
source "$LIB_DIR/reporting.sh"

# Default values
DOMAIN=""
IP=""
PORT=443
OUTPUT_FORMAT="console"
VERBOSE=0
TLS_VERSION="1.3"
TIMEOUT=10

# Create results directory if it doesn't exist
mkdir -p "$RESULTS_DIR"

# Function to print usage
print_usage() {
  echo "Usage: $0 [options] <domain> [ip] [port]"
  echo
  echo "Options:"
  echo "  --output <format>     Output format: console, json, markdown (default: console)"
  echo "  --verbose             Enable verbose output"
  echo "  --tls-version <ver>   TLS version to test: 1.0, 1.1, 1.2, 1.3 (default: 1.3)"
  echo "  --timeout <seconds>   Connection timeout in seconds (default: 10)"
  echo "  --help                Show this help message"
  echo
  echo "Examples:"
  echo "  $0 example.com"
  echo "  $0 example.com ************* 443"
  echo "  $0 example.com --output json"
  echo "  $0 example.com --tls-version 1.2"
}

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --output)
      OUTPUT_FORMAT="$2"
      shift 2
      ;;
    --verbose)
      VERBOSE=1
      shift
      ;;
    --tls-version)
      TLS_VERSION="$2"
      shift 2
      ;;
    --timeout)
      TIMEOUT="$2"
      shift 2
      ;;
    --help)
      print_usage
      exit 0
      ;;
    -*)
      echo "Unknown option: $1"
      print_usage
      exit 1
      ;;
    *)
      if [ -z "$DOMAIN" ]; then
        DOMAIN="$1"
      elif [ -z "$IP" ]; then
        IP="$1"
      elif [ -z "$PORT" ]; then
        PORT="$1"
      else
        echo "Too many arguments"
        print_usage
        exit 1
      fi
      shift
      ;;
  esac
done

# Check required arguments
if [ -z "$DOMAIN" ]; then
  echo "Error: Domain is required"
  print_usage
  exit 1
fi

# Set TLS version flag for curl
case "$TLS_VERSION" in
  1.0)
    TLS_FLAG="--tlsv1.0"
    ;;
  1.1)
    TLS_FLAG="--tlsv1.1"
    ;;
  1.2)
    TLS_FLAG="--tlsv1.2"
    ;;
  1.3)
    TLS_FLAG="--tlsv1.3"
    ;;
  *)
    echo "Error: Invalid TLS version: $TLS_VERSION"
    print_usage
    exit 1
    ;;
esac

# Print banner
echo "=== curl TLS Analysis ==="
echo "Domain: $DOMAIN"
if [ -n "$IP" ]; then
  echo "IP: $IP"
fi
echo "Port: $PORT"
echo "TLS Version: $TLS_VERSION"
echo

# Create temporary files
TRACE_FILE="$RESULTS_DIR/${DOMAIN}_${PORT}_curl_trace.txt"
HEADERS_FILE="$RESULTS_DIR/${DOMAIN}_${PORT}_headers.txt"
OUTPUT_FILE="$RESULTS_DIR/${DOMAIN}_${PORT}_output.json"

# Step 1: Perform curl request with trace
echo "Step 1: Performing TLS connection with curl..."

# Construct the curl command
CURL_CMD="curl --trace-ascii \"$TRACE_FILE\" --trace-time -v $TLS_FLAG -D \"$HEADERS_FILE\" -o /dev/null -s --connect-timeout $TIMEOUT"

# Add IP resolution if provided
if [ -n "$IP" ]; then
  CURL_CMD="$CURL_CMD --resolve ${DOMAIN}:${PORT}:${IP}"
fi

# Add URL
CURL_CMD="$CURL_CMD https://${DOMAIN}:${PORT}/"

# Execute curl command
if [ $VERBOSE -eq 1 ]; then
  echo "Executing: $CURL_CMD"
fi

eval "$CURL_CMD" 2>&1 | tee -a "$RESULTS_DIR/${DOMAIN}_${PORT}_curl_output.txt"

if [ ${PIPESTATUS[0]} -ne 0 ]; then
  echo "❌ curl request failed"
  # Parse error from curl output
  ERROR_MSG=$(grep -i "curl: " "$RESULTS_DIR/${DOMAIN}_${PORT}_curl_output.txt" | head -1)
  echo "Error: $ERROR_MSG"
  exit 1
fi

echo "✅ curl request completed"

# Step 2: Parse trace file to extract TLS information
echo "Step 2: Analyzing TLS handshake..."

# Use the parser library to extract TLS information
TLS_VERSION=$(extract_tls_version "$TRACE_FILE" "$RESULTS_DIR/${DOMAIN}_${PORT}_curl_output.txt")
echo "TLS Version: $TLS_VERSION"

CIPHER=$(extract_cipher_suite "$TRACE_FILE" "$RESULTS_DIR/${DOMAIN}_${PORT}_curl_output.txt")
echo "Cipher Suite: $CIPHER"

# Extract TLS extensions if available
if [ -f "$TRACE_FILE" ]; then
  EXTENSIONS=$(extract_tls_extensions "$TRACE_FILE")
  if [ -n "$EXTENSIONS" ]; then
    echo "TLS Extensions:"
    echo "$EXTENSIONS" | sed 's/^/  - /'
  fi

  # Extract handshake time
  HANDSHAKE_TIME=$(extract_handshake_time "$TRACE_FILE")
  if [ "$HANDSHAKE_TIME" != "0" ]; then
    echo "TLS Handshake Time: ${HANDSHAKE_TIME}s"
  fi
fi

# Step 3: Analyze certificate
echo "Step 3: Analyzing certificate..."

# Extract certificate information
CERT_INFO=$(grep -a "Server certificate:" -A 20 "$RESULTS_DIR/${DOMAIN}_${PORT}_curl_output.txt")
echo "$CERT_INFO" > "$RESULTS_DIR/${DOMAIN}_${PORT}_certificate.txt"

# Use the parser library to extract certificate details
SUBJECT=$(extract_certificate_subject "$RESULTS_DIR/${DOMAIN}_${PORT}_curl_output.txt")
echo "Certificate Subject: $SUBJECT"

ISSUER=$(extract_certificate_issuer "$RESULTS_DIR/${DOMAIN}_${PORT}_curl_output.txt")
echo "Certificate Issuer: $ISSUER"

DATES=$(extract_certificate_dates "$RESULTS_DIR/${DOMAIN}_${PORT}_curl_output.txt")
START_DATE=$(echo "$DATES" | cut -d'|' -f1)
EXPIRY_DATE=$(echo "$DATES" | cut -d'|' -f2)

echo "Valid From: $START_DATE"
echo "Valid To: $EXPIRY_DATE"

# Step 4: Analyze HTTP security headers
echo "Step 4: Analyzing HTTP security headers..."

# Check for security headers using the parser library
if [ -f "$HEADERS_FILE" ]; then
  # Extract security headers as JSON
  SECURITY_HEADERS=$(extract_security_headers "$HEADERS_FILE")

  # Display security headers in a readable format
  if grep -q -i "Strict-Transport-Security:" "$HEADERS_FILE"; then
    HSTS=$(grep -i "Strict-Transport-Security:" "$HEADERS_FILE" | sed -E 's/[^:]+: (.*)/\1/')
    echo "✅ HSTS: $HSTS"
  else
    echo "❌ HSTS header not found"
  fi

  if grep -q -i "Content-Security-Policy:" "$HEADERS_FILE"; then
    CSP=$(grep -i "Content-Security-Policy:" "$HEADERS_FILE" | sed -E 's/[^:]+: (.*)/\1/')
    echo "✅ CSP: Found (${#CSP} characters)"
  else
    echo "❌ CSP header not found"
  fi

  if grep -q -i "X-Content-Type-Options:" "$HEADERS_FILE"; then
    XCTO=$(grep -i "X-Content-Type-Options:" "$HEADERS_FILE" | sed -E 's/[^:]+: (.*)/\1/')
    echo "✅ X-Content-Type-Options: $XCTO"
  else
    echo "❌ X-Content-Type-Options header not found"
  fi

  if grep -q -i "X-Frame-Options:" "$HEADERS_FILE"; then
    XFO=$(grep -i "X-Frame-Options:" "$HEADERS_FILE" | sed -E 's/[^:]+: (.*)/\1/')
    echo "✅ X-Frame-Options: $XFO"
  else
    echo "❌ X-Frame-Options header not found"
  fi

  if grep -q -i "X-XSS-Protection:" "$HEADERS_FILE"; then
    XXSS=$(grep -i "X-XSS-Protection:" "$HEADERS_FILE" | sed -E 's/[^:]+: (.*)/\1/')
    echo "✅ X-XSS-Protection: $XXSS"
  else
    echo "❌ X-XSS-Protection header not found"
  fi

  if grep -q -i "Referrer-Policy:" "$HEADERS_FILE"; then
    RP=$(grep -i "Referrer-Policy:" "$HEADERS_FILE" | sed -E 's/[^:]+: (.*)/\1/')
    echo "✅ Referrer-Policy: $RP"
  else
    echo "❌ Referrer-Policy header not found"
  fi

  # Analyze security headers and provide recommendations
  echo "\nSecurity Header Recommendations:"
  analyze_security_headers "$HEADERS_FILE"
fi

# Step 5: Generate report
echo "\nStep 5: Generating report..."

# Use the reporting library to generate the report
case "$OUTPUT_FORMAT" in
  json)
    # Generate JSON report
    generate_json_report "$DOMAIN" "$PORT" "$TRACE_FILE" "$RESULTS_DIR/${DOMAIN}_${PORT}_curl_output.txt" "$HEADERS_FILE" > "$OUTPUT_FILE"
    echo "✅ JSON report generated: $OUTPUT_FILE"
    cat "$OUTPUT_FILE"
    ;;
  markdown)
    # Generate Markdown report
    generate_markdown_report "$DOMAIN" "$PORT" "$TLS_VERSION" "$CIPHER" "$SUBJECT" "$ISSUER" "$START_DATE" "$EXPIRY_DATE" "$HEADERS_FILE" > "$RESULTS_DIR/${DOMAIN}_${PORT}_report.md"
    echo "✅ Markdown report generated: $RESULTS_DIR/${DOMAIN}_${PORT}_report.md"
    cat "$RESULTS_DIR/${DOMAIN}_${PORT}_report.md"
    ;;
  console)
    # Generate console report (already displayed during execution)
    echo "✅ Console report displayed above"
    ;;
  *)
    echo "Error: Unknown output format: $OUTPUT_FORMAT"
    exit 1
    ;;
esac

echo
echo "=== curl TLS Analysis Complete ==="
echo "Results saved to $RESULTS_DIR/"
