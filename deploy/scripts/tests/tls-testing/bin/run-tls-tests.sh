#!/bin/bash
#
# run-tls-tests.sh - Main runner for TLS testing framework
#
# This script orchestrates the execution of various TLS tests,
# selecting the appropriate tests based on environment capabilities
# and user preferences.
#

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"
LIB_DIR="$ROOT_DIR/lib"
TESTS_DIR="$ROOT_DIR/tests"
CONFIG_DIR="$ROOT_DIR/config"
RESULTS_DIR="$ROOT_DIR/results"

# Source common libraries
source "$LIB_DIR/tls_utils.sh"

# Default values
DOMAIN=""
IP=""
PORT=443
OUTPUT_FORMAT="console"
VERBOSE=0
NO_SUDO=0
SPECIFIC_TEST=""
TLS_VERSION="1.3"
TIMEOUT=10
NON_INTERACTIVE=0

# Create results directory if it doesn't exist
mkdir -p "$RESULTS_DIR"

# Function to print usage
print_usage() {
  echo "Usage: $0 [options] <domain> [ip] [port]"
  echo
  echo "Options:"
  echo "  --output <format>     Output format: console, json, markdown (default: console)"
  echo "  --verbose             Enable verbose output"
  echo "  --no-sudo             Skip tests that require sudo privileges"
  echo "  --test <name>         Run specific test: tshark, openssl, curl, nmap"
  echo "  --tls-version <ver>   TLS version to test: 1.0, 1.1, 1.2, 1.3 (default: 1.3)"
  echo "  --timeout <seconds>   Connection timeout in seconds (default: 10)"
  echo "  --non-interactive     Run in non-interactive mode (no prompts)"
  echo "  --help                Show this help message"
  echo
  echo "Examples:"
  echo "  $0 example.com"
  echo "  $0 example.com ************* 443"
  echo "  $0 example.com --output json"
  echo "  $0 example.com --test curl"
}

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --output)
      OUTPUT_FORMAT="$2"
      shift 2
      ;;
    --verbose)
      VERBOSE=1
      shift
      ;;
    --no-sudo)
      NO_SUDO=1
      shift
      ;;
    --test)
      SPECIFIC_TEST="$2"
      shift 2
      ;;
    --tls-version)
      TLS_VERSION="$2"
      shift 2
      ;;
    --timeout)
      TIMEOUT="$2"
      shift 2
      ;;
    --non-interactive)
      NON_INTERACTIVE=1
      shift
      ;;
    --help)
      print_usage
      exit 0
      ;;
    -*)
      echo "Unknown option: $1"
      print_usage
      exit 1
      ;;
    *)
      if [ -z "$DOMAIN" ]; then
        DOMAIN="$1"
      elif [ -z "$IP" ]; then
        IP="$1"
      elif [ -z "$PORT" ]; then
        PORT="$1"
      else
        echo "Too many arguments"
        print_usage
        exit 1
      fi
      shift
      ;;
  esac
done

# Check required arguments
if [ -z "$DOMAIN" ]; then
  echo "Error: Domain is required"
  print_usage
  exit 1
fi

# If IP is not provided, try to resolve it
if [ -z "$IP" ]; then
  echo "Resolving IP for $DOMAIN..."
  IP=$(resolve_hostname "$DOMAIN")
  if [ -n "$IP" ]; then
    echo "Resolved $DOMAIN to $IP"
  else
    echo "Warning: Could not resolve IP for $DOMAIN"
  fi
fi

# Enable debug mode if verbose
if [ $VERBOSE -eq 1 ]; then
  tls_utils_debug 1
fi

# Print banner
echo "=== TLS Testing Framework ==="
echo "Domain: $DOMAIN"
if [ -n "$IP" ]; then
  echo "IP: $IP"
fi
echo "Port: $PORT"
echo "TLS Version: $TLS_VERSION"
echo

# Check if host is reachable
echo "Checking if $DOMAIN:$PORT is reachable..."
if check_host_reachable "$DOMAIN" "$PORT" "$TIMEOUT"; then
  echo "✅ $DOMAIN:$PORT is reachable"
else
  echo "❌ $DOMAIN:$PORT is not reachable"
  echo "Error: Cannot connect to $DOMAIN:$PORT"
  exit 1
fi

# Create test results directory for this run
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RUN_DIR="$RESULTS_DIR/${DOMAIN}_${PORT}_${TIMESTAMP}"
mkdir -p "$RUN_DIR"

# Create summary file
SUMMARY_FILE="$RUN_DIR/summary.txt"
echo "TLS Testing Summary for $DOMAIN:$PORT" > "$SUMMARY_FILE"
echo "Timestamp: $(date)" >> "$SUMMARY_FILE"
echo "-----------------------------------" >> "$SUMMARY_FILE"

# Determine available test methods
AVAILABLE_TESTS=()

# Check for TShark with sudo
if [ $NO_SUDO -eq 0 ] && check_tshark_sudo; then
  AVAILABLE_TESTS+=("tshark_sudo")
  echo "✅ TShark with sudo is available"
elif check_tshark_no_sudo; then
  AVAILABLE_TESTS+=("tshark_no_sudo")
  echo "✅ TShark without sudo is available"
else
  echo "❌ TShark is not available"
fi

# Check for OpenSSL
if check_openssl; then
  AVAILABLE_TESTS+=("openssl")
  echo "✅ OpenSSL is available"
else
  echo "❌ OpenSSL is not available"
fi

# Check for curl
if check_curl; then
  AVAILABLE_TESTS+=("curl")
  echo "✅ curl is available"
else
  echo "❌ curl is not available"
fi

# Check for nmap
if command_exists "nmap"; then
  AVAILABLE_TESTS+=("nmap")
  echo "✅ nmap is available"
else
  echo "❌ nmap is not available"
fi

echo

# If no tests are available, exit
if [ ${#AVAILABLE_TESTS[@]} -eq 0 ]; then
  echo "Error: No test methods available"
  exit 1
fi

# If specific test is requested, check if it's available
if [ -n "$SPECIFIC_TEST" ]; then
  case "$SPECIFIC_TEST" in
    tshark)
      if [[ " ${AVAILABLE_TESTS[*]} " != *" tshark_sudo "* && " ${AVAILABLE_TESTS[*]} " != *" tshark_no_sudo "* ]]; then
        echo "Error: TShark test requested but TShark is not available"
        exit 1
      fi
      ;;
    openssl)
      if [[ " ${AVAILABLE_TESTS[*]} " != *" openssl "* ]]; then
        echo "Error: OpenSSL test requested but OpenSSL is not available"
        exit 1
      fi
      ;;
    curl)
      if [[ " ${AVAILABLE_TESTS[*]} " != *" curl "* ]]; then
        echo "Error: curl test requested but curl is not available"
        exit 1
      fi
      ;;
    nmap)
      if [[ " ${AVAILABLE_TESTS[*]} " != *" nmap "* ]]; then
        echo "Error: nmap test requested but nmap is not available"
        exit 1
      fi
      ;;
    *)
      echo "Error: Unknown test: $SPECIFIC_TEST"
      print_usage
      exit 1
      ;;
  esac
fi

# Run tests
echo "Running TLS tests..."

# Run TShark test if available and requested
if [[ -z "$SPECIFIC_TEST" || "$SPECIFIC_TEST" == "tshark" ]]; then
  if [[ " ${AVAILABLE_TESTS[*]} " == *" tshark_sudo "* ]]; then
    echo "Running TShark test with sudo..."
    "$SCRIPT_DIR/tshark-tls-analysis.sh" "$DOMAIN" "$IP" "$PORT" "$TIMEOUT" | tee -a "$RUN_DIR/tshark_output.txt"
    echo "TShark test completed" >> "$SUMMARY_FILE"
  elif [[ " ${AVAILABLE_TESTS[*]} " == *" tshark_no_sudo "* ]]; then
    echo "Running TShark test without sudo..."
    "$SCRIPT_DIR/tshark-tls-analysis.sh" "$DOMAIN" "$IP" "$PORT" "$TIMEOUT" --no-sudo | tee -a "$RUN_DIR/tshark_output.txt"
    echo "TShark test (no sudo) completed" >> "$SUMMARY_FILE"
  fi
fi

# Run OpenSSL test if available and requested
if [[ -z "$SPECIFIC_TEST" || "$SPECIFIC_TEST" == "openssl" ]]; then
  if [[ " ${AVAILABLE_TESTS[*]} " == *" openssl "* ]]; then
    echo "Running OpenSSL test..."
    "$SCRIPT_DIR/openssl-tls-analysis.sh" "$DOMAIN" "$IP" "$PORT" "$TLS_VERSION" | tee -a "$RUN_DIR/openssl_output.txt"
    echo "OpenSSL test completed" >> "$SUMMARY_FILE"
  fi
fi

# Run curl test if available and requested
if [[ -z "$SPECIFIC_TEST" || "$SPECIFIC_TEST" == "curl" ]]; then
  if [[ " ${AVAILABLE_TESTS[*]} " == *" curl "* ]]; then
    echo "Running curl test..."
    "$SCRIPT_DIR/curl-tls-analysis.sh" "$DOMAIN" "$IP" "$PORT" --tls-version "$TLS_VERSION" --timeout "$TIMEOUT" | tee -a "$RUN_DIR/curl_output.txt"
    echo "curl test completed" >> "$SUMMARY_FILE"
  fi
fi

# Run nmap test if available and requested
if [[ -z "$SPECIFIC_TEST" || "$SPECIFIC_TEST" == "nmap" ]]; then
  if [[ " ${AVAILABLE_TESTS[*]} " == *" nmap "* ]]; then
    echo "Running nmap SSL scan..."
    "$SCRIPT_DIR/nmap-ssl-scan.sh" "$DOMAIN" "$IP" "$PORT" | tee -a "$RUN_DIR/nmap_output.txt"
    echo "nmap SSL scan completed" >> "$SUMMARY_FILE"
  fi
fi

# Generate consolidated report
echo "Generating consolidated report..."

# Create JSON report
JSON_REPORT="$RUN_DIR/report.json"
echo "{" > "$JSON_REPORT"
echo "  \"domain\": \"$DOMAIN\"," >> "$JSON_REPORT"
echo "  \"port\": $PORT," >> "$JSON_REPORT"
echo "  \"timestamp\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\"," >> "$JSON_REPORT"
echo "  \"tests_run\": [" >> "$JSON_REPORT"

# Add test results to JSON report
first=true
for test in "${AVAILABLE_TESTS[@]}"; do
  if [ "$first" = true ]; then
    first=false
  else
    echo "," >> "$JSON_REPORT"
  fi
  
  case "$test" in
    tshark_sudo)
      echo "    {\"name\": \"tshark\", \"sudo\": true}" >> "$JSON_REPORT"
      ;;
    tshark_no_sudo)
      echo "    {\"name\": \"tshark\", \"sudo\": false}" >> "$JSON_REPORT"
      ;;
    *)
      echo "    {\"name\": \"$test\", \"sudo\": false}" >> "$JSON_REPORT"
      ;;
  esac
done

echo "  ]" >> "$JSON_REPORT"
echo "}" >> "$JSON_REPORT"

echo "✅ Report generated: $RUN_DIR/report.json"

# Output report based on format
case "$OUTPUT_FORMAT" in
  json)
    cat "$JSON_REPORT"
    ;;
  markdown)
    echo "# TLS Testing Report for $DOMAIN:$PORT"
    echo
    echo "## Summary"
    echo
    echo "- **Domain**: $DOMAIN"
    echo "- **Port**: $PORT"
    echo "- **Timestamp**: $(date)"
    echo "- **Tests Run**: ${#AVAILABLE_TESTS[@]}"
    echo
    echo "## Test Results"
    echo
    for test in "${AVAILABLE_TESTS[@]}"; do
      case "$test" in
        tshark_sudo)
          echo "### TShark (with sudo)"
          ;;
        tshark_no_sudo)
          echo "### TShark (without sudo)"
          ;;
        openssl)
          echo "### OpenSSL"
          ;;
        curl)
          echo "### curl"
          ;;
        nmap)
          echo "### nmap SSL Scan"
          ;;
      esac
      echo
      echo "See detailed results in the test output files."
      echo
    done
    ;;
  console)
    # Already displayed during execution
    ;;
  *)
    echo "Error: Unknown output format: $OUTPUT_FORMAT"
    exit 1
    ;;
esac

echo
echo "=== TLS Testing Complete ==="
echo "Results saved to $RUN_DIR/"
