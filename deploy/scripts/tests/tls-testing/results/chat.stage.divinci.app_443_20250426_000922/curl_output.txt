Too many arguments
Usage: /Users/<USER>/Documents/Divinci/server3/server/deploy/scripts/tests/tls-testing/bin/curl-tls-analysis.sh [options] <domain> [ip] [port]

Options:
  --output <format>     Output format: console, json, markdown (default: console)
  --verbose             Enable verbose output
  --tls-version <ver>   TLS version to test: 1.0, 1.1, 1.2, 1.3 (default: 1.3)
  --timeout <seconds>   Connection timeout in seconds (default: 10)
  --help                Show this help message

Examples:
  /Users/<USER>/Documents/Divinci/server3/server/deploy/scripts/tests/tls-testing/bin/curl-tls-analysis.sh example.com
  /Users/<USER>/Documents/Divinci/server3/server/deploy/scripts/tests/tls-testing/bin/curl-tls-analysis.sh example.com ************* 443
  /Users/<USER>/Documents/Divinci/server3/server/deploy/scripts/tests/tls-testing/bin/curl-tls-analysis.sh example.com --output json
  /Users/<USER>/Documents/Divinci/server3/server/deploy/scripts/tests/tls-testing/bin/curl-tls-analysis.sh example.com --tls-version 1.2
