#!/bin/bash
# Comprehensive mTLS Testing Script
# This script runs a series of tests to diagnose mTLS and SSL/TLS issues

set -e

# Load environment variables
if [ -f "/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env" ]; then
  echo "Loading credentials from /Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env"
  source "/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/test.env"

  # Debug output (masked for security)
  echo "Debug: CF_ACCESS_CLIENT_SECRET length: ${#CF_ACCESS_CLIENT_SECRET}"
  echo "Debug: CF_EMAIL: ${CF_EMAIL}"
  echo "Debug: CF_API_KEY length: ${#CF_API_KEY}"
  echo "Debug: CF_ACCOUNT_ID: ${CF_ACCOUNT_ID}"
fi

# Get domain from command line
DOMAIN=${1:-"api.stage.divinci.app"}
ENVIRONMENT=${2:-"staging"}
REPO_ROOT="/Users/<USER>/Documents/Divinci/server3/server"
CERT_DIR="$REPO_ROOT/private-keys/$ENVIRONMENT/certs/mtls"

echo "Repository root: $REPO_ROOT"
echo "Certificate directory: $CERT_DIR"
echo "=== Comprehensive mTLS Testing ==="
echo "Domain: $DOMAIN"
echo "Environment: $ENVIRONMENT"
echo ""

# Function to print section header
section() {
  echo ""
  echo "=== $1 ==="
  echo ""
}

# Function to check if a command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Test 1: Basic connectivity
section "Test 1: Basic Connectivity"
echo "Testing basic connectivity to $DOMAIN..."
RESPONSE=$(curl -s -I "https://$DOMAIN")
HTTP_CODE=$(echo "$RESPONSE" | grep -i "^HTTP" | awk '{print $2}')

if [ "$HTTP_CODE" = "200" ]; then
  echo "✅ Basic connectivity test passed (HTTP 200)"
elif [ "$HTTP_CODE" = "302" ]; then
  echo "✅ Basic connectivity test passed (HTTP 302 redirect)"
  LOCATION=$(echo "$RESPONSE" | grep -i "^location:" | sed 's/location: //i')
  echo "   Redirect location: $LOCATION"
elif [ "$HTTP_CODE" = "403" ]; then
  echo "⚠️ Basic connectivity test returned HTTP 403 Forbidden"
  echo "   This might be due to Cloudflare security or mTLS requirements"

  # Try with Cloudflare Access headers if available
  if [ -n "$CF_ACCESS_CLIENT_ID" ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
    echo "   Trying with Cloudflare Access headers..."
    CF_RESPONSE=$(curl -s -I \
      -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
      -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
      "https://$DOMAIN")
    CF_HTTP_CODE=$(echo "$CF_RESPONSE" | grep -i "^HTTP" | awk '{print $2}')

    if [ "$CF_HTTP_CODE" = "200" ]; then
      echo "✅ Connectivity test with Cloudflare Access headers passed (HTTP 200)"
    elif [ "$CF_HTTP_CODE" = "302" ]; then
      echo "✅ Connectivity test with Cloudflare Access headers passed (HTTP 302 redirect)"
      CF_LOCATION=$(echo "$CF_RESPONSE" | grep -i "^location:" | sed 's/location: //i')
      echo "   Redirect location: $CF_LOCATION"
    else
      echo "❌ Connectivity test with Cloudflare Access headers failed with HTTP code: $CF_HTTP_CODE"
      echo "   This suggests the issue might be related to mTLS requirements"
    fi
  else
    echo "   Cloudflare Access credentials not found in environment variables"
    echo "   Cannot test with Cloudflare Access headers"
  fi
else
  echo "❌ Basic connectivity test failed with HTTP code: $HTTP_CODE"
  echo "Detailed output:"
  curl -v "https://$DOMAIN" 2>&1 | grep -v "^*" | grep -v "^}" | grep -v "^{" | grep -v "^<" | head -20
fi

# Test 2: Certificate verification
section "Test 2: Certificate Verification"
echo "Verifying server certificate for $DOMAIN..."
if openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" -showcerts </dev/null 2>/dev/null | grep -q "Verify return code: 0 (ok)"; then
  echo "✅ Certificate verification passed"
else
  echo "❌ Certificate verification failed"
  echo "Detailed output:"
  openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" -showcerts </dev/null 2>&1 | grep "Verify"
fi

# Test 3: Certificate chain
section "Test 3: Certificate Chain Analysis"
echo "Analyzing certificate chain for $DOMAIN..."
CERT_CHAIN=$(openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" -showcerts </dev/null 2>/dev/null)
CERT_COUNT=$(echo "$CERT_CHAIN" | grep -c "BEGIN CERTIFICATE")
echo "Certificate chain contains $CERT_COUNT certificates"

if [ "$CERT_COUNT" -gt 1 ]; then
  echo "✅ Certificate chain is complete (contains multiple certificates)"
else
  echo "❌ Certificate chain is incomplete (only contains 1 certificate)"
fi

# Extract and display certificate details
echo "Certificate details:"
echo "$CERT_CHAIN" | openssl x509 -noout -subject -issuer -dates 2>/dev/null

# Test 4: Client certificate validation
section "Test 4: Client Certificate Validation"
if [ -f "$CERT_DIR/client.crt" ] && [ -f "$CERT_DIR/client.key" ]; then
  echo "✅ Client certificate and key found"

  # Check if client certificate and key match
  CLIENT_CERT_MODULUS=$(openssl x509 -noout -modulus -in "$CERT_DIR/client.crt" 2>/dev/null || echo "")
  CLIENT_KEY_MODULUS=$(openssl rsa -noout -modulus -in "$CERT_DIR/client.key" 2>/dev/null || echo "")

  if [ -n "$CLIENT_CERT_MODULUS" ] && [ -n "$CLIENT_KEY_MODULUS" ] && [ "$CLIENT_CERT_MODULUS" = "$CLIENT_KEY_MODULUS" ]; then
    echo "✅ Client certificate and key match"
  else
    echo "❌ Client certificate and key do not match or are not RSA"

    # Check if they are EC keys
    if openssl ec -in "$CERT_DIR/client.key" -noout 2>/dev/null; then
      echo "ℹ️ Client key is an EC key"

      # Extract public key from certificate and key
      CERT_PUBKEY=$(openssl x509 -in "$CERT_DIR/client.crt" -noout -pubkey 2>/dev/null | openssl pkey -pubin -outform DER | md5sum)
      KEY_PUBKEY=$(openssl ec -in "$CERT_DIR/client.key" -pubout 2>/dev/null | openssl pkey -pubin -outform DER | md5sum)

      if [ "$CERT_PUBKEY" = "$KEY_PUBKEY" ]; then
        echo "✅ EC client certificate and key match"
      else
        echo "❌ EC client certificate and key do not match"
      fi
    fi
  fi

  # Display client certificate details
  echo "Client certificate details:"
  openssl x509 -in "$CERT_DIR/client.crt" -noout -subject -issuer -dates 2>/dev/null
else
  echo "❌ Client certificate and/or key not found"
  echo "Expected locations:"
  echo "  - Certificate: $CERT_DIR/client.crt"
  echo "  - Key: $CERT_DIR/client.key"
fi

# Test 5: mTLS connection
section "Test 5: mTLS Connection Test"
if [ -f "$CERT_DIR/client.crt" ] && [ -f "$CERT_DIR/client.key" ]; then
  echo "Testing mTLS connection to $DOMAIN..."
  if openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" -cert "$CERT_DIR/client.crt" -key "$CERT_DIR/client.key" </dev/null 2>/dev/null | grep -q "Verify return code: 0 (ok)"; then
    echo "✅ mTLS connection test passed"
  else
    echo "❌ mTLS connection test failed"
    echo "Detailed output:"
    openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" -cert "$CERT_DIR/client.crt" -key "$CERT_DIR/client.key" </dev/null 2>&1 | grep -E "Verify|error|unable"
  fi
else
  echo "⚠️ Skipping mTLS connection test due to missing client certificate or key"
fi

# Test 6: Cloudflare SSL mode
section "Test 6: Cloudflare SSL Mode"
if [ -n "$CF_EMAIL" ] && [ -n "$CF_API_KEY" ]; then
  echo "Checking Cloudflare SSL mode..."

  # Extract zone name (remove subdomain)
  ZONE=$(echo "$DOMAIN" | awk -F. '{print $(NF-1)"."$NF}')
  echo "Zone: $ZONE"

  # Get zone ID
  ZONE_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$ZONE" \
    -H "X-Auth-Email: $CF_EMAIL" \
    -H "X-Auth-Key: $CF_API_KEY" \
    -H "Content-Type: application/json" | grep -o '"id":"[^"]*' | cut -d'"' -f4)

  if [ -n "$ZONE_ID" ]; then
    echo "Zone ID: $ZONE_ID"

    # Get SSL mode
    SSL_MODE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/settings/ssl" \
      -H "X-Auth-Email: $CF_EMAIL" \
      -H "X-Auth-Key: $CF_API_KEY" \
      -H "Content-Type: application/json" | grep -o '"value":"[^"]*' | cut -d'"' -f4)

    if [ -n "$SSL_MODE" ]; then
      echo "Current SSL mode: $SSL_MODE"

      if [ "$SSL_MODE" = "strict" ]; then
        echo "⚠️ SSL mode is set to 'strict', which requires valid certificates"
        echo "   If you're experiencing Error 526, try changing to 'full' mode temporarily"
      elif [ "$SSL_MODE" = "full" ]; then
        echo "✅ SSL mode is set to 'full', which accepts self-signed certificates"
      else
        echo "❌ SSL mode is set to '$SSL_MODE', which is not recommended for production"
      fi
    else
      echo "❌ Failed to get SSL mode"
    fi
  else
    echo "❌ Failed to get zone ID"
  fi
else
  echo "⚠️ Skipping Cloudflare SSL mode check due to missing API credentials"
fi

# Test 7: HTTP request with client certificate and Cloudflare Access headers
section "Test 7: HTTP Request with Client Certificate and Cloudflare Access Headers"
if [ -f "$CERT_DIR/client.crt" ] && [ -f "$CERT_DIR/client.key" ]; then
  echo "Testing HTTP request with client certificate and Cloudflare Access headers..."

  # Check if Cloudflare Access credentials are available
  if [ -n "$CF_ACCESS_CLIENT_ID" ] && [ -n "$CF_ACCESS_CLIENT_SECRET" ]; then
    echo "✅ Cloudflare Access credentials found"

    # Make request with both client certificate and Cloudflare Access headers
    RESPONSE=$(curl -s -v \
      --cert "$CERT_DIR/client.crt" \
      --key "$CERT_DIR/client.key" \
      -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
      -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" \
      "https://$DOMAIN/health" 2>&1)

    if echo "$RESPONSE" | grep -q "200 OK"; then
      echo "✅ HTTP request with client certificate and Cloudflare Access headers succeeded"
    else
      echo "❌ HTTP request with client certificate and Cloudflare Access headers failed"
      echo "Response status: $(echo "$RESPONSE" | grep -o "HTTP/[0-9.]* [0-9]* [^\"]*" | head -1)"
    fi
  else
    echo "⚠️ Cloudflare Access credentials not found in environment variables"
    echo "   Testing with client certificate only..."

    # Make request with only client certificate
    RESPONSE=$(curl -s -v --cert "$CERT_DIR/client.crt" --key "$CERT_DIR/client.key" "https://$DOMAIN/health" 2>&1)

    if echo "$RESPONSE" | grep -q "200 OK"; then
      echo "✅ HTTP request with client certificate succeeded"
    else
      echo "❌ HTTP request with client certificate failed"
      echo "Response status: $(echo "$RESPONSE" | grep -o "HTTP/[0-9.]* [0-9]* [^\"]*" | head -1)"
      echo "   This is expected if Cloudflare Access is enabled but credentials were not provided"
    fi
  fi
else
  echo "⚠️ Skipping HTTP request test due to missing client certificate or key"
fi

# Test 8: Server configuration check
section "Test 8: Server Configuration Check"
echo "Checking server configuration..."

# Check if server is using mTLS
if curl -s -I "https://$DOMAIN" | grep -q "HTTP/2 403"; then
  echo "⚠️ Server returned 403 Forbidden - this might indicate mTLS is required"
elif curl -s -I "https://$DOMAIN" | grep -q "HTTP/2 401"; then
  echo "⚠️ Server returned 401 Unauthorized - authentication is required"
else
  echo "ℹ️ Server did not return 401/403 status - mTLS might not be enforced"
fi

# Summary
section "Summary"
# Basic connectivity
if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "302" ]; then
  echo "1. Basic connectivity: ✅ Passed ($HTTP_CODE)"
elif [ "$HTTP_CODE" = "403" ]; then
  if [ -n "$CF_HTTP_CODE" ]; then
    if [ "$CF_HTTP_CODE" = "200" ] || [ "$CF_HTTP_CODE" = "302" ]; then
      echo "1. Basic connectivity: ✅ Passed with Cloudflare Access headers ($CF_HTTP_CODE)"
      echo "   This confirms Cloudflare Access is protecting the endpoint"
    else
      echo "1. Basic connectivity: ⚠️ Failed even with Cloudflare Access headers ($CF_HTTP_CODE)"
      echo "   This suggests mTLS or other authentication is required"
    fi
  else
    echo "1. Basic connectivity: ⚠️ Cloudflare security or mTLS requirements ($HTTP_CODE)"
  fi
else
  echo "1. Basic connectivity: ❌ Failed ($HTTP_CODE)"
fi

# Certificate verification
echo "2. Certificate verification: $(openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" -showcerts </dev/null 2>/dev/null | grep -q "Verify return code: 0 (ok)" && echo "✅ Passed" || echo "❌ Failed")"

# Certificate chain
echo "3. Certificate chain: $([ "$CERT_COUNT" -gt 1 ] && echo "✅ Complete" || echo "❌ Incomplete")"

if [ -f "$CERT_DIR/client.crt" ] && [ -f "$CERT_DIR/client.key" ]; then
  if [ -n "$CLIENT_CERT_MODULUS" ] && [ -n "$CLIENT_KEY_MODULUS" ] && [ "$CLIENT_CERT_MODULUS" = "$CLIENT_KEY_MODULUS" ]; then
    echo "4. Client certificate validation: ✅ Passed"
  elif [ "$CERT_PUBKEY" = "$KEY_PUBKEY" ]; then
    echo "4. Client certificate validation: ✅ Passed (EC keys)"
  else
    echo "4. Client certificate validation: ❌ Failed (keys don't match)"
  fi

  if openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" -cert "$CERT_DIR/client.crt" -key "$CERT_DIR/client.key" </dev/null 2>/dev/null | grep -q "Verify return code: 0 (ok)"; then
    echo "5. mTLS connection: ✅ Passed"
  else
    echo "5. mTLS connection: ❌ Failed"
  fi
else
  echo "4. Client certificate validation: ⚠️ Skipped (missing files)"
  echo "5. mTLS connection: ⚠️ Skipped (missing files)"
fi

if [ -n "$SSL_MODE" ]; then
  if [ "$SSL_MODE" = "full" ]; then
    echo "6. Cloudflare SSL mode: ✅ Configured for self-signed certificates ($SSL_MODE)"
  else
    echo "6. Cloudflare SSL mode: ⚠️ Not configured for self-signed certificates ($SSL_MODE)"
  fi
else
  echo "6. Cloudflare SSL mode: ⚠️ Unknown"
fi

echo ""
echo "=== Test Complete ==="
echo "Results saved to test-results/mtls-test-$DOMAIN-$(date +%Y%m%d_%H%M%S).log"

# Save results to file
mkdir -p test-results
exec > >(tee -a "test-results/mtls-test-$DOMAIN-$(date +%Y%m%d_%H%M%S).log") 2>&1
