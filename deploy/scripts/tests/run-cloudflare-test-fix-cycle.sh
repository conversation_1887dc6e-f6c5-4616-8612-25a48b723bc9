#!/bin/bash

# Cloudflare Test-Fix Cycle
# This script runs the analyzer, fixer, and tests in a cycle

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="staging"
MAX_CYCLES=3
VERBOSE=false
DRY_RUN=false
REPORT_FILE="cloudflare-test-fix-report.md"

# Function to display usage information
usage() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --environment ENV      Environment to test (default: staging)"
  echo "  --max-cycles N         Maximum number of test-fix cycles (default: 3)"
  echo "  --report-file FILE     Path to output report file (default: cloudflare-test-fix-report.md)"
  echo "  --verbose              Enable verbose output"
  echo "  --dry-run              Don't make any actual changes, just show what would be done"
  echo "  --help                 Display this help message"
  echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --environment)
      ENVIRONMENT="$2"
      shift
      shift
      ;;
    --max-cycles)
      MAX_CYCLES="$2"
      shift
      shift
      ;;
    --report-file)
      REPORT_FILE="$2"
      shift
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    --help)
      usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      usage
      exit 1
      ;;
  esac
done

# Make sure the scripts are executable
chmod +x ./cloudflare-rules-analyzer.sh
chmod +x ./cloudflare-rules-fixer.sh
chmod +x ./run-network-diagnostics.sh

# Main function
main() {
  echo -e "${BLUE}=== Cloudflare Test-Fix Cycle ===${NC}"
  echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
  echo -e "${BLUE}Max cycles: $MAX_CYCLES${NC}"
  if [ "$DRY_RUN" = true ]; then
    echo -e "${YELLOW}DRY RUN MODE: No changes will be made${NC}"
  fi
  
  # Initialize variables for the report
  CYCLE_RESULTS=""
  FINAL_STATUS="Failed"
  
  # Run the test-fix cycle
  for ((cycle=1; cycle<=MAX_CYCLES; cycle++)); do
    echo -e "\n${BLUE}=== Cycle $cycle of $MAX_CYCLES ===${NC}"
    
    # Run the analyzer
    echo -e "\n${BLUE}Running Cloudflare Rules Analyzer...${NC}"
    ANALYZER_ARGS="--environment $ENVIRONMENT"
    if [ "$VERBOSE" = true ]; then
      ANALYZER_ARGS="$ANALYZER_ARGS --verbose"
    fi
    
    ./cloudflare-rules-analyzer.sh $ANALYZER_ARGS || true
    
    # Run the tests
    echo -e "\n${BLUE}Running Network Diagnostics...${NC}"
    TEST_ARGS="--skip-mtls --skip-cloudflare --environment $ENVIRONMENT"
    
    ./run-network-diagnostics.sh $TEST_ARGS
    TEST_RESULT=$?
    
    # Add to cycle results
    CYCLE_RESULTS+="## Cycle $cycle\n\n"
    CYCLE_RESULTS+="- **Analyzer:** Completed\n"
    
    # If the tests passed, we're done
    if [ $TEST_RESULT -eq 0 ]; then
      echo -e "${GREEN}Tests passed! No issues found.${NC}"
      CYCLE_RESULTS+="- **Tests:** Passed\n"
      CYCLE_RESULTS+="- **Fixer:** Not needed\n\n"
      FINAL_STATUS="Success"
      break
    fi
    
    # Tests failed, run the fixer
    echo -e "${YELLOW}Tests failed. Running fixer...${NC}"
    CYCLE_RESULTS+="- **Tests:** Failed\n"
    
    # Run the fixer
    FIXER_ARGS="--environment $ENVIRONMENT --max-attempts 1 --report-file cycle-${cycle}-report.md"
    if [ "$VERBOSE" = true ]; then
      FIXER_ARGS="$FIXER_ARGS --verbose"
    fi
    if [ "$DRY_RUN" = true ]; then
      FIXER_ARGS="$FIXER_ARGS --dry-run"
    fi
    
    ./cloudflare-rules-fixer.sh $FIXER_ARGS
    FIXER_RESULT=$?
    
    # Add to cycle results
    if [ $FIXER_RESULT -eq 0 ]; then
      CYCLE_RESULTS+="- **Fixer:** Completed successfully\n\n"
    else
      CYCLE_RESULTS+="- **Fixer:** Failed\n\n"
    fi
    
    # If this is a dry run, don't continue
    if [ "$DRY_RUN" = true ]; then
      echo -e "${YELLOW}DRY RUN: Stopping after one cycle${NC}"
      FINAL_STATUS="Dry Run"
      break
    fi
    
    # If this was the last cycle, we failed
    if [ $cycle -eq $MAX_CYCLES ]; then
      echo -e "${RED}Failed to fix all issues after $MAX_CYCLES cycles.${NC}"
    else
      echo -e "${YELLOW}Continuing to next cycle...${NC}"
    fi
  done
  
  # Generate the final report
  echo -e "\n${BLUE}Generating final report...${NC}"
  
  cat > "$REPORT_FILE" << EOF
# Cloudflare Test-Fix Cycle Report

## Summary

- **Environment:** $ENVIRONMENT
- **Date:** $(date)
- **Status:** $FINAL_STATUS
- **Cycles:** $cycle of $MAX_CYCLES

## Cycle Results

$CYCLE_RESULTS

## Recommendations

EOF
  
  # Add recommendations based on the final status
  if [ "$FINAL_STATUS" = "Success" ]; then
    cat >> "$REPORT_FILE" << EOF
- All tests are passing, no further action needed
- Consider running these tests regularly to ensure continued functionality
EOF
  elif [ "$FINAL_STATUS" = "Dry Run" ]; then
    cat >> "$REPORT_FILE" << EOF
- Review the proposed changes in the dry run
- Run the script again without --dry-run to apply the changes
EOF
  else
    cat >> "$REPORT_FILE" << EOF
- Manual intervention is required to fix the remaining issues
- Review the cycle reports for details on what was attempted
- Check the Cloudflare dashboard for any conflicting rules
EOF
  fi
  
  echo -e "${GREEN}Final report generated at $REPORT_FILE${NC}"
  
  # Return success if all tests passed
  if [ "$FINAL_STATUS" = "Success" ]; then
    return 0
  else
    return 1
  fi
}

# Run the main function
main
