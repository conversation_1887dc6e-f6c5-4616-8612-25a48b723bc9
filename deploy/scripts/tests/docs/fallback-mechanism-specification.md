# TLS Testing Fallback Mechanism Specification

## Overview

This document specifies the enhanced fallback mechanism for TLS testing, designed to provide graceful degradation of functionality when preferred testing methods are unavailable. The goal is to ensure that useful TLS analysis is always available, regardless of environment constraints.

## 1. Fallback Chain

### 1.1 Primary and Fallback Methods

The system will attempt methods in this order:

1. **TShark with sudo** (Preferred)
   - Full packet capture and analysis
   - Complete TLS handshake inspection
   - Protocol-level details

2. **TShark without sudo**
   - Limited packet capture capabilities
   - Basic TLS handshake information
   - May fail on some platforms

3. **OpenSSL s_client**
   - TLS connection information
   - Certificate details
   - Cipher and protocol information
   - No packet-level details

4. **curl with trace**
   - TLS handshake information from trace
   - HTTP headers and response analysis
   - Performance metrics
   - Limited protocol details

5. **Basic HTTP client**
   - Simple connection test
   - Basic certificate information
   - Minimal TLS details

### 1.2 Decision Flow

```
┌─────────────────┐
│ Start TLS Test  │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│  TShark with    │ Yes │ Run TShark with │
│  sudo available?├────►│     sudo        │
└────────┬────────┘     └─────────────────┘
         │ No
         ▼
┌─────────────────┐     ┌─────────────────┐
│ TShark without  │ Yes │ Run TShark      │
│ sudo works?     ├────►│ without sudo    │
└────────┬────────┘     └─────────────────┘
         │ No
         ▼
┌─────────────────┐     ┌─────────────────┐
│   OpenSSL       │ Yes │ Run OpenSSL     │
│   available?    ├────►│   analysis      │
└────────┬────────┘     └─────────────────┘
         │ No
         ▼
┌─────────────────┐     ┌─────────────────┐
│     curl        │ Yes │  Run curl with  │
│   available?    ├────►│     trace       │
└────────┬────────┘     └─────────────────┘
         │ No
         ▼
┌─────────────────┐
│  Use basic HTTP │
│     client      │
└─────────────────┘
```

## 2. Implementation Details

### 2.1 Method Detection

Each script will implement a detection function to determine if a method is available:

```bash
# Check if TShark with sudo is available
check_tshark_sudo() {
  if command -v tshark &>/dev/null && sudo -n true 2>/dev/null; then
    return 0  # Available
  else
    return 1  # Not available
  fi
}

# Check if TShark without sudo works
check_tshark_no_sudo() {
  if command -v tshark &>/dev/null; then
    # Try a simple capture to see if it works without sudo
    if tshark -i any -c 1 -w /dev/null 2>/dev/null; then
      return 0  # Works
    fi
  fi
  return 1  # Doesn't work
}

# Similar functions for other methods...
```

### 2.2 Feature Matrix

Each method will provide a subset of features:

| Feature                    | TShark+sudo | TShark | OpenSSL | curl | Basic |
|----------------------------|-------------|--------|---------|------|-------|
| TLS Version                | ✅          | ⚠️     | ✅      | ✅   | ⚠️    |
| Cipher Suite               | ✅          | ⚠️     | ✅      | ✅   | ❌    |
| Certificate Chain          | ✅          | ⚠️     | ✅      | ⚠️   | ⚠️    |
| TLS Extensions             | ✅          | ⚠️     | ⚠️      | ❌   | ❌    |
| Handshake Details          | ✅          | ⚠️     | ⚠️      | ⚠️   | ❌    |
| Protocol Analysis          | ✅          | ⚠️     | ❌      | ❌   | ❌    |
| Performance Metrics        | ✅          | ⚠️     | ⚠️      | ✅   | ⚠️    |
| HTTP Headers               | ⚠️          | ⚠️     | ❌      | ✅   | ✅    |
| Error Detection            | ✅          | ⚠️     | ⚠️      | ⚠️   | ⚠️    |

Legend:
- ✅ Full support
- ⚠️ Limited support
- ❌ Not supported

### 2.3 Data Normalization

To ensure consistent reporting regardless of the method used, all data will be normalized to a common format:

```json
{
  "tls": {
    "version": "TLSv1.3",
    "cipher": "TLS_AES_256_GCM_SHA384",
    "extensions": ["server_name", "ec_point_formats", "supported_groups", "session_ticket", "heartbeat"],
    "certificate": {
      "subject": "CN=example.com",
      "issuer": "C=US, O=Let's Encrypt, CN=R3",
      "valid_from": "2023-01-01T00:00:00Z",
      "valid_to": "2023-12-31T23:59:59Z",
      "key_type": "RSA",
      "key_size": 2048
    }
  },
  "analysis": {
    "method": "openssl",
    "features_available": ["tls_version", "cipher", "certificate"],
    "features_missing": ["protocol_details", "packet_capture"],
    "reliability": 0.8
  }
}
```

## 3. Mock Data Strategy

### 3.1 When to Use Mock Data

Mock data will be used in these scenarios:

1. When all connection methods fail
2. In offline testing environments
3. For demonstration purposes
4. When specified by the user

### 3.2 Mock Data Sources

Mock data will come from:

1. **Pre-recorded captures** - Real TLS handshakes captured previously
2. **Synthetic data** - Generated based on common TLS configurations
3. **Reference implementations** - Standard examples of different TLS versions/configurations

### 3.3 Mock Data Identification

All mock data will be clearly identified:

```
⚠️ USING MOCK DATA: Real connection to example.com failed
⚠️ Mock data based on: TLSv1.3 standard handshake with ECDHE key exchange
⚠️ Mock data may not reflect the actual server configuration
```

## 4. Error Handling

### 4.1 Connection Errors

For each method, specific error handling will be implemented:

```bash
run_tshark_analysis() {
  if ! tshark -i any -f "host $HOST" -a duration:5 -w "$CAPTURE_FILE" 2>/dev/null; then
    log_error "TShark capture failed" "TSHARK_CAPTURE_FAILED"
    log_debug "Command: tshark -i any -f \"host $HOST\" -a duration:5 -w \"$CAPTURE_FILE\""
    log_debug "Exit code: $?"
    return 1
  fi
  # Continue with analysis...
}
```

### 4.2 Error Classification

Errors will be classified into categories:

1. **Network errors** - Cannot reach the server
2. **TLS errors** - TLS handshake failed
3. **Tool errors** - Testing tool failed
4. **Permission errors** - Insufficient privileges
5. **Configuration errors** - Invalid configuration

### 4.3 Troubleshooting Guidance

For each error, specific troubleshooting guidance will be provided:

```
ERROR: TLS handshake failed (TLS_HANDSHAKE_FAILED)
Possible causes:
1. Server does not support the requested TLS version (TLSv1.3)
2. No common cipher suites between client and server
3. Server certificate validation failed

Troubleshooting steps:
1. Try with an older TLS version: --tls-version 1.2
2. Check server TLS configuration
3. Verify certificate chain is complete
4. Check system time is correct (for certificate validation)
```

## 5. Integration with Test Framework

### 5.1 Common Interface

All testing methods will implement a common interface:

```bash
# Function signature for all TLS testing methods
# Returns: 0 on success, non-zero on failure
# Outputs: JSON data to stdout
analyze_tls() {
  local host="$1"
  local port="$2"
  local options="$3"
  
  # Method-specific implementation
  # ...
  
  # Return normalized data
  echo "$json_data"
  return $exit_code
}
```

### 5.2 Method Selection

The main script will select the appropriate method:

```bash
# Try methods in order of preference
if check_tshark_sudo; then
  log_info "Using TShark with sudo"
  analyze_tls_tshark_sudo "$host" "$port" "$options"
elif check_tshark_no_sudo; then
  log_info "Using TShark without sudo"
  analyze_tls_tshark "$host" "$port" "$options"
elif check_openssl; then
  log_info "Using OpenSSL"
  analyze_tls_openssl "$host" "$port" "$options"
elif check_curl; then
  log_info "Using curl"
  analyze_tls_curl "$host" "$port" "$options"
else
  log_info "Using basic HTTP client"
  analyze_tls_basic "$host" "$port" "$options"
fi
```

### 5.3 Result Aggregation

Results from different methods will be aggregated when multiple methods are used:

```json
{
  "summary": {
    "host": "example.com",
    "port": 443,
    "tls_version": "TLSv1.3",
    "cipher": "TLS_AES_256_GCM_SHA384",
    "certificate_valid": true
  },
  "methods": {
    "tshark": {
      "status": "skipped",
      "reason": "sudo not available"
    },
    "openssl": {
      "status": "success",
      "data": { /* OpenSSL-specific data */ }
    },
    "curl": {
      "status": "success",
      "data": { /* curl-specific data */ }
    }
  },
  "consolidated_analysis": {
    /* Combined analysis from all successful methods */
  }
}
```

## 6. Implementation Plan

### Phase 1: Core Fallback Logic

1. Implement method detection functions
2. Create basic fallback chain
3. Add simple error handling
4. Test on different environments

### Phase 2: Data Normalization

1. Define common data format
2. Implement data normalization for each method
3. Create result aggregation logic
4. Add feature availability tracking

### Phase 3: Advanced Features

1. Implement mock data strategy
2. Enhance error handling and troubleshooting
3. Add detailed logging
4. Create comprehensive documentation

## 7. Testing Strategy

The fallback mechanism itself will be tested in various environments:

1. **Full privileges** - All methods available
2. **Limited privileges** - No sudo access
3. **Limited tools** - Missing some tools (e.g., no TShark)
4. **Offline environment** - No network access
5. **Restricted environment** - Firewall blocking connections

## 8. Success Criteria

The fallback mechanism will be considered successful when:

1. It automatically selects the best available method
2. It provides useful TLS analysis in all tested environments
3. Results are consistent across different methods
4. Users are clearly informed about limitations
5. Troubleshooting guidance is helpful and accurate
