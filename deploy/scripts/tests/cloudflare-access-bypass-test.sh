#!/bin/bash
# Cloudflare Access Bypass Test
# This test attempts to bypass Cloudflare Access to directly test the origin server.

set -e

# Configuration
DOMAIN=${1:-"chat.stage.divinci.app"}
CF_ACCESS_CLIENT_ID=${2:-"f13e5d39e1997a5ddb674362e73199c5.access"}
CF_ACCESS_CLIENT_SECRET=${3:-""}
OUTPUT_DIR="./test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== Cloudflare Access Bypass Test ==="
echo "Domain: $DOMAIN"
echo "CF Access Client ID: $CF_ACCESS_CLIENT_ID"
echo

# Check if CF_ACCESS_CLIENT_SECRET is provided
if [ -z "$CF_ACCESS_CLIENT_SECRET" ]; then
  echo "❌ Cloudflare Access Client Secret not provided"
  echo "   Please provide your Cloudflare Access Client Secret as the third parameter"
  exit 1
fi

# Step 1: Test with CF-Access-Client headers
echo "Step 1: Testing with CF-Access-Client headers..."
RESPONSE=$(curl -s -I -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" "https://$DOMAIN/")
echo "$RESPONSE" > "$OUTPUT_DIR/cf_access_response.txt"

# Check the response
STATUS_CODE=$(echo "$RESPONSE" | grep "HTTP/" | awk '{print $2}')
echo "   Status code: $STATUS_CODE"

if [ "$STATUS_CODE" == "526" ]; then
  echo "❌ Error 526 detected with Cloudflare Access bypass headers"
  echo "   This confirms that the issue is with the SSL certificate on your origin server"
elif [ "$STATUS_CODE" == "200" ]; then
  echo "✅ Successfully bypassed Cloudflare Access"
  echo "   The origin server is responding correctly"
else
  echo "⚠️ Unexpected status code: $STATUS_CODE"
  echo "   Response headers:"
  echo "$RESPONSE"
fi

# Step 2: Test with different HTTP methods
echo "Step 2: Testing with different HTTP methods..."
HTTP_METHODS=("GET" "POST" "HEAD" "OPTIONS")

for method in "${HTTP_METHODS[@]}"; do
  echo "   Testing $method method..."
  RESPONSE=$(curl -s -I -X "$method" -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" "https://$DOMAIN/")
  STATUS_CODE=$(echo "$RESPONSE" | grep "HTTP/" | awk '{print $2}')
  echo "      Status code: $STATUS_CODE"
  echo "$RESPONSE" > "$OUTPUT_DIR/cf_access_${method}_response.txt"
done

# Step 3: Test with different paths
echo "Step 3: Testing with different paths..."
PATHS=("/" "/api" "/health" "/status")

for path in "${PATHS[@]}"; do
  echo "   Testing path: $path..."
  RESPONSE=$(curl -s -I -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" "https://$DOMAIN$path")
  STATUS_CODE=$(echo "$RESPONSE" | grep "HTTP/" | awk '{print $2}')
  echo "      Status code: $STATUS_CODE"
  echo "$RESPONSE" > "$OUTPUT_DIR/cf_access_path_${path//\//_}_response.txt"
done

# Step 4: Test with additional headers
echo "Step 4: Testing with additional headers..."
ADDITIONAL_HEADERS=(
  "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
  "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
  "Accept-Language: en-US,en;q=0.5"
  "Connection: keep-alive"
)

HEADER_ARGS=""
for header in "${ADDITIONAL_HEADERS[@]}"; do
  HEADER_ARGS="$HEADER_ARGS -H \"$header\""
done

RESPONSE=$(curl -s -I -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" $HEADER_ARGS "https://$DOMAIN/")
STATUS_CODE=$(echo "$RESPONSE" | grep "HTTP/" | awk '{print $2}')
echo "   Status code with additional headers: $STATUS_CODE"
echo "$RESPONSE" > "$OUTPUT_DIR/cf_access_additional_headers_response.txt"

# Step 5: Test with a full request (not just headers)
echo "Step 5: Testing with a full request..."
FULL_RESPONSE=$(curl -s -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" "https://$DOMAIN/")
echo "$FULL_RESPONSE" | head -n 20 > "$OUTPUT_DIR/cf_access_full_response.txt"

# Check if the response contains HTML
if echo "$FULL_RESPONSE" | grep -q "<html"; then
  echo "✅ Full response contains HTML"
  echo "   This suggests that the origin server is responding correctly"
else
  echo "❌ Full response does not contain HTML"
  echo "   This suggests that there may be an issue with the origin server"
  echo "   First 100 characters of response:"
  echo "$FULL_RESPONSE" | head -c 100
fi

# Step 6: Test with curl verbose mode
echo "Step 6: Testing with curl verbose mode..."
VERBOSE_RESPONSE=$(curl -s -v -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET" "https://$DOMAIN/" 2>&1)
echo "$VERBOSE_RESPONSE" > "$OUTPUT_DIR/cf_access_verbose_response.txt"

# Check for TLS/SSL errors
if echo "$VERBOSE_RESPONSE" | grep -q "SSL"; then
  echo "⚠️ SSL-related messages found in verbose output:"
  echo "$VERBOSE_RESPONSE" | grep "SSL"
else
  echo "✅ No SSL-related messages found in verbose output"
fi

# Step 7: Create a test script for future use
echo "Step 7: Creating a test script for future use..."
cat > "$OUTPUT_DIR/cf_access_test.sh" << EOF
#!/bin/bash
# Cloudflare Access Test Script

# Configuration
DOMAIN="$DOMAIN"
CF_ACCESS_CLIENT_ID="$CF_ACCESS_CLIENT_ID"
CF_ACCESS_CLIENT_SECRET="$CF_ACCESS_CLIENT_SECRET"

# Test with CF-Access-Client headers
echo "Testing with CF-Access-Client headers..."
curl -v -H "CF-Access-Client-Id: \$CF_ACCESS_CLIENT_ID" -H "CF-Access-Client-Secret: \$CF_ACCESS_CLIENT_SECRET" "https://\$DOMAIN/"
EOF

chmod +x "$OUTPUT_DIR/cf_access_test.sh"
echo "✅ Test script created at $OUTPUT_DIR/cf_access_test.sh"

echo
echo "=== Cloudflare Access Bypass Test Complete ==="
echo "Results saved to $OUTPUT_DIR/"
