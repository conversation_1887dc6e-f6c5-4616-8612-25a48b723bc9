#!/bin/bash

# Cloudflare Rules Analyzer
# This script analyzes Cloudflare rules for potential CORS and mTLS conflicts
# It requires jq to be installed and Cloudflare API credentials

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ZONE_ID=""
API_TOKEN=""
ENVIRONMENT="staging"
DOMAIN="api.stage.divinci.app"
ORIGIN="https://chat.stage.divinci.app"
VERBOSE=false

# Function to display usage information
usage() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --zone-id ID           Cloudflare Zone ID (required unless in env)"
  echo "  --api-token TOKEN      Cloudflare API Token (required unless in env)"
  echo "  --environment ENV      Environment to test (default: staging)"
  echo "  --domain DOMAIN        Domain to test (default: api.stage.divinci.app)"
  echo "  --origin ORIGIN        Origin to use for CORS tests (default: https://chat.stage.divinci.app)"
  echo "  --verbose              Enable verbose output"
  echo "  --help                 Display this help message"
  echo ""
  echo "Environment variables:"
  echo "  CF_ZONE_ID             Cloudflare Zone ID"
  echo "  CF_API_TOKEN           Cloudflare API Token"
  echo "  CF_ACCESS_CLIENT_ID    Cloudflare Access Client ID (for Access tests)"
  echo "  CF_ACCESS_CLIENT_SECRET Cloudflare Access Client Secret (for Access tests)"
  echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --zone-id)
      ZONE_ID="$2"
      shift
      shift
      ;;
    --api-token)
      API_TOKEN="$2"
      shift
      shift
      ;;
    --environment)
      ENVIRONMENT="$2"
      shift
      shift
      ;;
    --domain)
      DOMAIN="$2"
      shift
      shift
      ;;
    --origin)
      ORIGIN="$2"
      shift
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    --help)
      usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      usage
      exit 1
      ;;
  esac
done

# Check for required dependencies
check_dependencies() {
  echo -e "${BLUE}Checking dependencies...${NC}"

  if ! command -v jq &> /dev/null; then
    echo -e "${RED}Error: jq is not installed. Please install jq to use this script.${NC}"
    echo "On macOS: brew install jq"
    echo "On Ubuntu/Debian: apt-get install jq"
    exit 1
  fi

  if ! command -v curl &> /dev/null; then
    echo -e "${RED}Error: curl is not installed. Please install curl to use this script.${NC}"
    exit 1
  fi

  echo -e "${GREEN}All dependencies are installed.${NC}"
}

# Load environment variables
load_environment() {
  echo -e "${BLUE}Loading environment variables for ${ENVIRONMENT}...${NC}"

  # Try to load from environment file if it exists
  ENV_FILE="../../../private-keys/${ENVIRONMENT}/cloudflare.env"
  if [ -f "$ENV_FILE" ]; then
    echo "Loading from $ENV_FILE"
    source "$ENV_FILE"
  fi

  # Check for environment variables
  if [ -z "$ZONE_ID" ]; then
    ZONE_ID="$CF_ZONE_ID"
  fi

  if [ -z "$API_TOKEN" ]; then
    API_TOKEN="$CF_API_TOKEN"
  fi

  # Validate required variables
  if [ -z "$ZONE_ID" ]; then
    echo -e "${RED}Error: Cloudflare Zone ID is required. Set CF_ZONE_ID or use --zone-id.${NC}"
    exit 1
  fi

  if [ -z "$API_TOKEN" ]; then
    echo -e "${RED}Error: Cloudflare API Token is required. Set CF_API_TOKEN or use --api-token.${NC}"
    exit 1
  fi

  echo -e "${GREEN}Environment variables loaded.${NC}"
}

# Function to check for CORS-related rules
check_cors_rules() {
  echo -e "${BLUE}Checking for CORS-related rules...${NC}"

  # Get all rulesets
  echo "Fetching rulesets..."
  RULESETS=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets" \
    -H "Authorization: Bearer $API_TOKEN" \
    -H "Content-Type: application/json")

  # Check if the API call was successful
  if [ "$(echo "$RULESETS" | jq -r '.success')" != "true" ]; then
    echo -e "${RED}Error fetching rulesets:${NC}"
    echo "$RULESETS" | jq -r '.errors[]'
    return 1
  fi

  # Display ruleset information
  echo -e "${GREEN}Found $(echo "$RULESETS" | jq -r '.result | length') rulesets:${NC}"
  echo "$RULESETS" | jq -r '.result[] | "- \(.name) (ID: \(.id)) - Phase: \(.phase)"'

  # For each ruleset, get the details
  for RULESET_ID in $(echo "$RULESETS" | jq -r '.result[].id'); do
    echo -e "\n${BLUE}Analyzing ruleset ID: $RULESET_ID${NC}"

    RULESET_DETAILS=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets/$RULESET_ID" \
      -H "Authorization: Bearer $API_TOKEN" \
      -H "Content-Type: application/json")

    # Check if the API call was successful
    if [ "$(echo "$RULESET_DETAILS" | jq -r '.success')" != "true" ]; then
      echo -e "${RED}Error fetching ruleset details:${NC}"
      echo "$RULESET_DETAILS" | jq -r '.errors[]'
      continue
    fi

    RULESET_NAME=$(echo "$RULESET_DETAILS" | jq -r '.result.name')
    RULESET_PHASE=$(echo "$RULESET_DETAILS" | jq -r '.result.phase')
    RULES_COUNT=$(echo "$RULESET_DETAILS" | jq -r '.result.rules | length')

    echo -e "${GREEN}Ruleset: $RULESET_NAME (Phase: $RULESET_PHASE)${NC}"
    echo -e "${GREEN}Contains $RULES_COUNT rules${NC}"

    # Check for rules that might affect OPTIONS requests or CORS
    OPTIONS_RULES=$(echo "$RULESET_DETAILS" | jq -r '.result.rules[] | select(.expression | contains("OPTIONS") or contains("http.request.method"))')
    if [ ! -z "$OPTIONS_RULES" ]; then
      echo -e "${YELLOW}Found rules affecting OPTIONS requests:${NC}"
      echo "$OPTIONS_RULES" | jq -r '.'
    fi

    # Check for rules that might affect headers or origins
    HEADER_RULES=$(echo "$RULESET_DETAILS" | jq -r '.result.rules[] | select(.expression | contains("header") or contains("Origin"))')
    if [ ! -z "$HEADER_RULES" ]; then
      echo -e "${YELLOW}Found rules affecting headers or origins:${NC}"
      echo "$HEADER_RULES" | jq -r '.'
    fi

    # Check for mTLS related rules
    MTLS_RULES=$(echo "$RULESET_DETAILS" | jq -r '.result.rules[] | select(.expression | contains("cf.tls_client_auth.cert_verified"))')
    if [ ! -z "$MTLS_RULES" ]; then
      echo -e "${YELLOW}Found mTLS-related rules:${NC}"
      echo "$MTLS_RULES" | jq -r '.'
    fi

    # If verbose, show all rules
    if [ "$VERBOSE" = true ]; then
      echo -e "${BLUE}All rules in this ruleset:${NC}"
      echo "$RULESET_DETAILS" | jq -r '.result.rules[]'
    fi
  done
}

# Function to check for WAF rules
check_waf_rules() {
  echo -e "${BLUE}Checking WAF rules...${NC}"

  # Get WAF packages
  WAF_PACKAGES=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/firewall/waf/packages" \
    -H "Authorization: Bearer $API_TOKEN" \
    -H "Content-Type: application/json")

  # Check if the API call was successful
  if [ "$(echo "$WAF_PACKAGES" | jq -r '.success')" != "true" ]; then
    echo -e "${RED}Error fetching WAF packages:${NC}"
    echo "$WAF_PACKAGES" | jq -r '.errors[]'
    return 1
  fi

  # Display WAF package information
  echo -e "${GREEN}Found $(echo "$WAF_PACKAGES" | jq -r '.result | length') WAF packages:${NC}"
  echo "$WAF_PACKAGES" | jq -r '.result[] | "- \(.name) (ID: \(.id)) - Status: \(.status)"'

  # For each package, get the rules
  for PACKAGE_ID in $(echo "$WAF_PACKAGES" | jq -r '.result[].id'); do
    echo -e "\n${BLUE}Analyzing WAF package ID: $PACKAGE_ID${NC}"

    WAF_RULES=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/firewall/waf/packages/$PACKAGE_ID/rules" \
      -H "Authorization: Bearer $API_TOKEN" \
      -H "Content-Type: application/json")

    # Check if the API call was successful
    if [ "$(echo "$WAF_RULES" | jq -r '.success')" != "true" ]; then
      echo -e "${RED}Error fetching WAF rules:${NC}"
      echo "$WAF_RULES" | jq -r '.errors[]'
      continue
    fi

    RULES_COUNT=$(echo "$WAF_RULES" | jq -r '.result | length')
    echo -e "${GREEN}Contains $RULES_COUNT rules${NC}"

    # Check for rules that might affect OPTIONS requests or CORS
    OPTIONS_WAF_RULES=$(echo "$WAF_RULES" | jq -r '.result[] | select(.description | contains("OPTIONS") or contains("CORS") or contains("preflight"))')
    if [ ! -z "$OPTIONS_WAF_RULES" ]; then
      echo -e "${YELLOW}Found WAF rules potentially affecting OPTIONS/CORS:${NC}"
      echo "$OPTIONS_WAF_RULES" | jq -r '.'
    fi

    # If verbose, show all rules
    if [ "$VERBOSE" = true ]; then
      echo -e "${BLUE}All WAF rules in this package:${NC}"
      echo "$WAF_RULES" | jq -r '.result[]'
    fi
  done
}

# Function to check for Transform Rules
check_transform_rules() {
  echo -e "${BLUE}Checking Transform Rules...${NC}"

  # Get Transform Rules
  TRANSFORM_RULES=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/rulesets/phases/http_response_headers_transform/entrypoint" \
    -H "Authorization: Bearer $API_TOKEN" \
    -H "Content-Type: application/json")

  # Check if the API call was successful
  if [ "$(echo "$TRANSFORM_RULES" | jq -r '.success')" != "true" ]; then
    echo -e "${RED}Error fetching Transform Rules:${NC}"
    echo "$TRANSFORM_RULES" | jq -r '.errors[]'
    return 1
  fi

  # Check if there are any rules
  if [ "$(echo "$TRANSFORM_RULES" | jq -r '.result')" = "null" ]; then
    echo -e "${YELLOW}No Transform Rules found.${NC}"
    return 0
  fi

  # Display Transform Rules information
  RULES_COUNT=$(echo "$TRANSFORM_RULES" | jq -r '.result.rules | length')
  echo -e "${GREEN}Found $RULES_COUNT Transform Rules:${NC}"

  # Check for CORS-related Transform Rules
  CORS_TRANSFORM_RULES=$(echo "$TRANSFORM_RULES" | jq -r '.result.rules[] | select(.action_parameters.headers[] | select(.name | contains("Access-Control")))')
  if [ ! -z "$CORS_TRANSFORM_RULES" ]; then
    echo -e "${GREEN}Found Transform Rules adding CORS headers:${NC}"
    echo "$CORS_TRANSFORM_RULES" | jq -r '.'
  else
    echo -e "${YELLOW}No Transform Rules found that add CORS headers.${NC}"
  fi

  # If verbose, show all Transform Rules
  if [ "$VERBOSE" = true ]; then
    echo -e "${BLUE}All Transform Rules:${NC}"
    echo "$TRANSFORM_RULES" | jq -r '.result.rules[]'
  fi
}

# Function to test CORS with curl
test_cors() {
  echo -e "${BLUE}Testing CORS with curl...${NC}"

  # Test OPTIONS request
  echo -e "${BLUE}Testing OPTIONS request to $DOMAIN:${NC}"
  OPTIONS_RESPONSE=$(curl -s -i -X OPTIONS "https://$DOMAIN/health" \
    -H "Origin: $ORIGIN" \
    -H "Access-Control-Request-Method: GET" \
    -H "Access-Control-Request-Headers: Content-Type, Authorization")

  # Extract status code
  STATUS_CODE=$(echo "$OPTIONS_RESPONSE" | grep -i "HTTP/" | awk '{print $2}')
  echo -e "${GREEN}Status code: $STATUS_CODE${NC}"

  # Check for CORS headers
  CORS_HEADERS=$(echo "$OPTIONS_RESPONSE" | grep -i "Access-Control-")
  if [ ! -z "$CORS_HEADERS" ]; then
    echo -e "${GREEN}CORS headers found:${NC}"
    echo "$CORS_HEADERS"
  else
    echo -e "${RED}No CORS headers found in the response.${NC}"
  fi

  # If verbose, show full response
  if [ "$VERBOSE" = true ]; then
    echo -e "${BLUE}Full response:${NC}"
    echo "$OPTIONS_RESPONSE"
  fi

  # Test with Cloudflare Access headers if available
  if [ ! -z "$CF_ACCESS_CLIENT_ID" ] && [ ! -z "$CF_ACCESS_CLIENT_SECRET" ]; then
    echo -e "\n${BLUE}Testing OPTIONS request with Cloudflare Access headers:${NC}"
    OPTIONS_ACCESS_RESPONSE=$(curl -s -i -X OPTIONS "https://$DOMAIN/health" \
      -H "Origin: $ORIGIN" \
      -H "Access-Control-Request-Method: GET" \
      -H "Access-Control-Request-Headers: Content-Type, Authorization" \
      -H "CF-Access-Client-Id: $CF_ACCESS_CLIENT_ID" \
      -H "CF-Access-Client-Secret: $CF_ACCESS_CLIENT_SECRET")

    # Extract status code
    ACCESS_STATUS_CODE=$(echo "$OPTIONS_ACCESS_RESPONSE" | grep -i "HTTP/" | awk '{print $2}')
    echo -e "${GREEN}Status code: $ACCESS_STATUS_CODE${NC}"

    # Check for CORS headers
    ACCESS_CORS_HEADERS=$(echo "$OPTIONS_ACCESS_RESPONSE" | grep -i "Access-Control-")
    if [ ! -z "$ACCESS_CORS_HEADERS" ]; then
      echo -e "${GREEN}CORS headers found:${NC}"
      echo "$ACCESS_CORS_HEADERS"
    else
      echo -e "${RED}No CORS headers found in the response.${NC}"
    fi

    # If verbose, show full response
    if [ "$VERBOSE" = true ]; then
      echo -e "${BLUE}Full response:${NC}"
      echo "$OPTIONS_ACCESS_RESPONSE"
    fi
  else
    echo -e "${YELLOW}Skipping Cloudflare Access test (credentials not available)${NC}"
  fi
}

# Function to check for conflicting rules
check_conflicts() {
  echo -e "${BLUE}Analyzing potential rule conflicts...${NC}"

  # Check for mTLS rules that might block OPTIONS requests
  echo -e "${BLUE}Checking for mTLS rules that might block OPTIONS requests...${NC}"

  # This is a simplified check - a more thorough analysis would require parsing the rule expressions
  MTLS_RULES_COUNT=$(echo "$RULESET_DETAILS" | jq -r '.result.rules[] | select(.expression | contains("cf.tls_client_auth.cert_verified") and (contains("OPTIONS") | not))' | jq -r '. | length')

  if [ "$MTLS_RULES_COUNT" -gt 0 ]; then
    echo -e "${RED}Potential conflict: Found $MTLS_RULES_COUNT mTLS rules that might block OPTIONS requests.${NC}"
    echo -e "${YELLOW}Recommendation: Modify mTLS rules to exclude OPTIONS requests with:${NC}"
    echo "(http.request.method ne \"OPTIONS\") and (original mTLS expression)"
  else
    echo -e "${GREEN}No obvious conflicts found between mTLS and OPTIONS requests.${NC}"
  fi

  # Check for CORS headers in Transform Rules
  echo -e "${BLUE}Checking for Transform Rules adding CORS headers...${NC}"

  if [ -z "$CORS_TRANSFORM_RULES" ]; then
    echo -e "${YELLOW}No Transform Rules found that add CORS headers.${NC}"
    echo -e "${YELLOW}Recommendation: Add Transform Rules to ensure CORS headers are added to responses:${NC}"
    echo "1. Create a rule for OPTIONS requests"
    echo "2. Create a rule for regular requests"
  else
    echo -e "${GREEN}Found Transform Rules adding CORS headers.${NC}"
  fi
}

# Main function
main() {
  echo -e "${BLUE}=== Cloudflare Rules Analyzer ===${NC}"
  echo -e "${BLUE}Domain: $DOMAIN${NC}"
  echo -e "${BLUE}Origin: $ORIGIN${NC}"
  echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"

  check_dependencies
  load_environment

  echo -e "\n${BLUE}=== Analyzing Cloudflare Rules ===${NC}"
  check_cors_rules
  check_waf_rules
  check_transform_rules

  echo -e "\n${BLUE}=== Testing CORS ===${NC}"
  test_cors

  echo -e "\n${BLUE}=== Conflict Analysis ===${NC}"
  check_conflicts

  echo -e "\n${GREEN}Analysis complete.${NC}"
}

# Run the main function
main
