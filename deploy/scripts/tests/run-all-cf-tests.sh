#!/bin/bash
# Master script to run all Cloudflare diagnostic tests

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TESTS_DIR="$SCRIPT_DIR"

echo -e "${BLUE}===== Cloudflare Diagnostic Test Suite =====${NC}"
echo -e "${BLUE}This script will run all diagnostic tests for Cloudflare connectivity and SSL issues${NC}"
echo

# Function to run each test with proper error handling
run_test() {
    local test_script=$1
    local test_name=$2
    
    if [ -f "$test_script" ]; then
        echo -e "${BLUE}===== Running Test: $test_name =====${NC}"
        chmod +x "$test_script"
        "$test_script"
        
        # Check exit status
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ $test_name completed successfully${NC}"
        else
            echo -e "${RED}✗ $test_name failed with errors${NC}"
        fi
        
        echo -e "\n${YELLOW}========================================${NC}\n"
    else
        echo -e "${RED}✗ Test script not found: $test_script${NC}"
    fi
}

# Run basic certificate test
run_test "$TESTS_DIR/cf-cert-test.sh" "Certificate Verification Test"

# Run direct access test with credentials
run_test "$TESTS_DIR/cf-direct-test.sh" "Direct Access with Service Credentials"

# Run curl tests with credentials
run_test "$TESTS_DIR/cf-curl-test.sh" "Direct curl Tests with Service Credentials"

# Ask if user wants to run Node.js E2E test
echo -e "${BLUE}Would you like to run the Node.js E2E test? This requires puppeteer to be installed (y/n)${NC}"
read run_e2e

if [[ "$run_e2e" == "y" || "$run_e2e" == "Y" ]]; then
    # Check if puppeteer is installed
    if npm list -g puppeteer &>/dev/null || npm list puppeteer &>/dev/null; then
        echo -e "${GREEN}Puppeteer is installed. Running E2E test...${NC}"
        node "$TESTS_DIR/cf-e2e-test.js"
    else
        echo -e "${YELLOW}Puppeteer is not installed. Would you like to install it now? (y/n)${NC}"
        read install_puppeteer
        
        if [[ "$install_puppeteer" == "y" || "$install_puppeteer" == "Y" ]]; then
            echo -e "${BLUE}Installing puppeteer...${NC}"
            npm install -g puppeteer
            
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}Puppeteer installed successfully. Running E2E test...${NC}"
                node "$TESTS_DIR/cf-e2e-test.js"
            else
                echo -e "${RED}Failed to install puppeteer. Please install it manually with 'npm install -g puppeteer'${NC}"
            fi
        else
            echo -e "${YELLOW}Skipping E2E test.${NC}"
        fi
    fi
else
    echo -e "${YELLOW}Skipping E2E test.${NC}"
fi

echo -e "\n${BLUE}===== Summary of Findings =====${NC}"
echo -e "1. Certificate tests help diagnose certificate validity issues (Error 526)"
echo -e "2. Access tests help confirm if Cloudflare Access is properly configured"
echo -e "3. Direct connection tests help diagnose SSL handshake issues (Error 525)"
echo
echo -e "${YELLOW}To fix Error 525 (SSL handshake failure):${NC}"
echo -e "- Ensure origin server uses Cloudflare Origin Certificate"
echo -e "- Configure TLS 1.2 or 1.3 on your server"
echo -e "- Use compatible cipher suites"
echo -e "- Check for SSL session timeout settings"
echo -e "- Temporarily try 'Full' SSL mode (not Strict) to test"
echo
echo -e "${GREEN}All tests completed!${NC}"