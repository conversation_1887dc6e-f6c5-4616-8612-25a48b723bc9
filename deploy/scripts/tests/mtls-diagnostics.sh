#!/bin/bash

# mTLS Diagnostic Tests
# This script performs comprehensive tests of mTLS configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="staging"
VERBOSE=false
TEST_CERT_PATHS=true
TEST_CONNECTIONS=true
TEST_SERVER_CONFIG=true

# Function to display usage information
usage() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  --environment ENV      Environment to test (default: staging)"
  echo "  --verbose              Enable verbose output"
  echo "  --skip-cert-paths      Skip certificate path tests"
  echo "  --skip-connections     Skip connection tests"
  echo "  --skip-server-config   Skip server configuration tests"
  echo "  --help                 Display this help message"
  echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --environment)
      ENVIRONMENT="$2"
      shift
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    --skip-cert-paths)
      TEST_CERT_PATHS=false
      shift
      ;;
    --skip-connections)
      TEST_CONNECTIONS=false
      shift
      ;;
    --skip-server-config)
      TEST_SERVER_CONFIG=false
      shift
      ;;
    --help)
      usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      usage
      exit 1
      ;;
  esac
done

# Set domain based on environment
if [ "$ENVIRONMENT" = "staging" ]; then
  DOMAIN="api.stage.divinci.app"
elif [ "$ENVIRONMENT" = "production" ]; then
  DOMAIN="api.divinci.app"
else
  DOMAIN="api.dev.divinci.app"
fi

# Function to check certificate paths
check_cert_paths() {
  echo -e "${BLUE}=== Checking Certificate Paths ===${NC}"
  
  # Define possible certificate paths
  CERT_PATHS=(
    "../../../private-keys/$ENVIRONMENT/certs/mtls"
    "/etc/ssl"
    "/etc/ssl/certs"
    "/etc/ssl/private"
    "/etc/ssl/client"
  )
  
  # Check each path
  for PATH in "${CERT_PATHS[@]}"; do
    echo -e "${BLUE}Checking $PATH...${NC}"
    
    if [ -d "$PATH" ]; then
      echo -e "${GREEN}Directory exists${NC}"
      
      # List certificate files
      CERT_FILES=$(find "$PATH" -name "*.crt" -o -name "*.key" -o -name "*.pem" 2>/dev/null)
      
      if [ ! -z "$CERT_FILES" ]; then
        echo -e "${GREEN}Found certificate files:${NC}"
        echo "$CERT_FILES"
        
        # Check file permissions
        for FILE in $CERT_FILES; do
          PERMS=$(ls -la "$FILE" | awk '{print $1}')
          echo -e "${BLUE}$FILE: $PERMS${NC}"
          
          # Check if key files are properly protected
          if [[ "$FILE" == *".key" ]] && [[ "$PERMS" == *"r--r--r--"* ]]; then
            echo -e "${RED}Warning: Key file $FILE has too open permissions${NC}"
          fi
          
          # Check file content (first line only)
          if [ "$VERBOSE" = true ]; then
            FIRST_LINE=$(head -n 1 "$FILE")
            echo "Content starts with: $FIRST_LINE"
          fi
        done
      else
        echo -e "${YELLOW}No certificate files found${NC}"
      fi
    else
      echo -e "${YELLOW}Directory does not exist${NC}"
    fi
  done
}

# Function to test connections with and without certificates
test_connections() {
  echo -e "${BLUE}=== Testing Connections ===${NC}"
  
  # Find client certificate
  CLIENT_CERT=""
  CLIENT_KEY=""
  
  CERT_PATHS=(
    "../../../private-keys/$ENVIRONMENT/certs/mtls"
    "/etc/ssl/client"
  )
  
  for PATH in "${CERT_PATHS[@]}"; do
    if [ -f "$PATH/client.crt" ] && [ -f "$PATH/client.key" ]; then
      CLIENT_CERT="$PATH/client.crt"
      CLIENT_KEY="$PATH/client.key"
      break
    fi
  done
  
  # Test without certificate
  echo -e "${BLUE}Testing connection without client certificate...${NC}"
  NO_CERT_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/health")
  
  if [ "$NO_CERT_RESPONSE" = "200" ]; then
    echo -e "${YELLOW}Warning: Connection succeeded without client certificate (status $NO_CERT_RESPONSE)${NC}"
    echo "This suggests mTLS might not be enforced correctly"
  else
    echo -e "${GREEN}Connection without certificate returned status $NO_CERT_RESPONSE${NC}"
    echo "This is expected if mTLS is enforced"
  fi
  
  # Test with verbose output
  if [ "$VERBOSE" = true ]; then
    echo -e "${BLUE}Verbose connection without certificate:${NC}"
    curl -v "https://$DOMAIN/health" 2>&1
  fi
  
  # Test with certificate if available
  if [ ! -z "$CLIENT_CERT" ] && [ ! -z "$CLIENT_KEY" ]; then
    echo -e "\n${BLUE}Testing connection with client certificate...${NC}"
    echo "Using certificate: $CLIENT_CERT"
    echo "Using key: $CLIENT_KEY"
    
    WITH_CERT_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" --cert "$CLIENT_CERT" --key "$CLIENT_KEY" "https://$DOMAIN/health")
    
    if [ "$WITH_CERT_RESPONSE" = "200" ]; then
      echo -e "${GREEN}Connection with certificate succeeded (status $WITH_CERT_RESPONSE)${NC}"
    else
      echo -e "${RED}Connection with certificate failed (status $WITH_CERT_RESPONSE)${NC}"
      echo "This suggests an issue with the client certificate or server configuration"
    fi
    
    # Test with verbose output
    if [ "$VERBOSE" = true ]; then
      echo -e "${BLUE}Verbose connection with certificate:${NC}"
      curl -v --cert "$CLIENT_CERT" --key "$CLIENT_KEY" "https://$DOMAIN/health" 2>&1
    fi
  else
    echo -e "${YELLOW}No client certificate found for testing${NC}"
  fi
}

# Function to check server mTLS configuration
check_server_config() {
  echo -e "${BLUE}=== Checking Server mTLS Configuration ===${NC}"
  
  # Test server SSL configuration
  echo -e "${BLUE}Testing server SSL configuration...${NC}"
  
  # Use OpenSSL to check server certificate and configuration
  echo -e "${BLUE}Server certificate information:${NC}"
  openssl s_client -connect "$DOMAIN:443" -showcerts </dev/null 2>/dev/null | openssl x509 -text -noout | grep -E "Subject:|Issuer:|Not Before:|Not After :|DNS:"
  
  # Check if server requests client certificate
  echo -e "\n${BLUE}Checking if server requests client certificate...${NC}"
  REQUESTS_CLIENT_CERT=$(openssl s_client -connect "$DOMAIN:443" -showcerts </dev/null 2>&1 | grep -c "CertificateRequest")
  
  if [ "$REQUESTS_CLIENT_CERT" -gt 0 ]; then
    echo -e "${GREEN}Server requests client certificate (mTLS enabled)${NC}"
  else
    echo -e "${YELLOW}Server does not request client certificate (mTLS might not be enabled)${NC}"
  fi
  
  # Check TLS version and cipher
  echo -e "\n${BLUE}TLS version and cipher information:${NC}"
  openssl s_client -connect "$DOMAIN:443" </dev/null 2>/dev/null | grep -E "Protocol|Cipher"
  
  # Check for Cloudflare mTLS settings
  echo -e "\n${BLUE}Checking for Cloudflare mTLS headers...${NC}"
  CF_HEADERS=$(curl -s -I "https://$DOMAIN/health" | grep -i "cf-")
  
  if [ ! -z "$CF_HEADERS" ]; then
    echo -e "${GREEN}Found Cloudflare headers:${NC}"
    echo "$CF_HEADERS"
  else
    echo -e "${YELLOW}No Cloudflare headers found${NC}"
  fi
}

# Main function
main() {
  echo -e "${BLUE}=== mTLS Diagnostic Tests ===${NC}"
  echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
  echo -e "${BLUE}Domain: $DOMAIN${NC}"
  
  # Run certificate path tests if enabled
  if [ "$TEST_CERT_PATHS" = true ]; then
    check_cert_paths
  else
    echo -e "${YELLOW}Skipping certificate path tests${NC}"
  fi
  
  # Run connection tests if enabled
  if [ "$TEST_CONNECTIONS" = true ]; then
    test_connections
  else
    echo -e "${YELLOW}Skipping connection tests${NC}"
  fi
  
  # Run server configuration tests if enabled
  if [ "$TEST_SERVER_CONFIG" = true ]; then
    check_server_config
  else
    echo -e "${YELLOW}Skipping server configuration tests${NC}"
  fi
  
  echo -e "\n${GREEN}mTLS diagnostic tests completed.${NC}"
}

# Run the main function
main
