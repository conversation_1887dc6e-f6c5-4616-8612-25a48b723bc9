#!/bin/bash

# Set the CF_AUTH_TOKEN from environment variable or as an argument
if [ -n "$1" ]; then
  CF_AUTH_TOKEN="$1"
else
  # Try to get from environment variable
  if [ -z "$CF_AUTH_TOKEN" ]; then
    echo "Error: CF_AUTH_TOKEN not provided. Either set it as environment variable or pass as argument."
    echo "Usage: ./cf-trace-debug.sh <CF_AUTH_TOKEN>"
    exit 1
  fi
fi

ACCOUNT_ID="14a6fa23390363382f378b5bd4a0f849"
TARGET_URL="https://chat.stage.divinci.app/"

# Make the trace request
echo "Making trace request to $TARGET_URL..."
TRACE_RESPONSE=$(curl --request POST \
  --url "https://api.cloudflare.com/client/v4/accounts/$ACCOUNT_ID/request-tracer/tracer" \
  --header 'Content-Type: application/json' \
  --header "Authorization: Bearer $CF_AUTH_TOKEN" \
  --data '{
  "url": "'$TARGET_URL'",
  "method": "GET"
}')

echo "$TRACE_RESPONSE" > trace_response.json
echo "Trace response saved to trace_response.json"

# Extract status code
STATUS_CODE=$(echo "$TRACE_RESPONSE" | grep -o '"http_status":[0-9]*' | cut -d':' -f2)
echo "HTTP Status Code: $STATUS_CODE"

# Test direct connection to the origin server (bypassing Cloudflare)
echo -e "\nTesting direct connection to origin server..."
echo "Note: This will bypass Cloudflare and connect directly to your origin server."

# Get the IP address of your origin server (you need to replace this with your actual server IP)
# This is just a placeholder - you'll need to provide your actual origin server IP
read -p "Enter your origin server IP (leave empty to skip direct test): " ORIGIN_IP

if [ -n "$ORIGIN_IP" ]; then
  echo "Testing connection to origin at $ORIGIN_IP..."
  
  # Test HTTP connection
  echo -e "\nTesting HTTP connection..."
  curl -v --connect-to chat.stage.divinci.app:80:$ORIGIN_IP:80 http://chat.stage.divinci.app/
  
  # Test HTTPS connection with server certificate validation disabled
  echo -e "\nTesting HTTPS connection (ignoring certificate validation)..."
  curl -v --connect-to chat.stage.divinci.app:443:$ORIGIN_IP:443 -k https://chat.stage.divinci.app/
  
  # Test HTTPS connection with client certificate
  echo -e "\nTesting HTTPS connection with client certificate..."
  CLIENT_CERT="/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/certs/mtls/client.crt"
  CLIENT_KEY="/Users/<USER>/Documents/Divinci/server3/server/private-keys/staging/certs/mtls/client.key"
  
  if [ -f "$CLIENT_CERT" ] && [ -f "$CLIENT_KEY" ]; then
    curl -v --connect-to chat.stage.divinci.app:443:$ORIGIN_IP:443 -k --cert "$CLIENT_CERT" --key "$CLIENT_KEY" https://chat.stage.divinci.app/
  else
    echo "Client certificate or key not found at the expected path."
  fi
fi

echo -e "\nChecking for common issues in Cloudflare configuration..."

# Check certificate issue for error 526
echo -e "\n1. Certificate Issue Check:"
echo "   - Your origin certificate is for: *.divinci.app, divinci.app"
echo "   - Your server certificate is for: localhost, O=Divinci, C=US"
echo "   - PROBLEM: Server certificate is for 'localhost', not for 'chat.stage.divinci.app'"
echo "   - This mismatch is likely causing the 526 error in Cloudflare Full (Strict) mode"

# Check SSL configuration
echo -e "\n2. SSL Configuration Check:"
echo "   - Make sure your server is accepting TLS 1.2 or higher connections"
echo "   - In Full (Strict) mode, your origin needs to have a valid certificate trusted by Cloudflare"
echo "   - Use the Cloudflare Origin Certificate on your origin server, not a self-signed one"

# Recommendations
echo -e "\n3. Recommendations:"
echo "   - Install the Cloudflare Origin Certificate (origin.crt/origin.key) on your server, not localhost cert"
echo "   - Make sure your server is configured to use that certificate"
echo "   - Update your nginx configuration to use the correct certificate files"
echo "   - Ensure server is accessible directly using HTTPS on port 443"
echo "   - Check firewall settings to ensure Cloudflare IPs can access your origin"

echo -e "\nFor further troubleshooting, visit Cloudflare dashboard and check SSL/TLS > Origin Server section."