(http.request.method ne "OPTIONS") and (
  (not cf.tls_client_auth.cert_verified and http.host eq "api.stage.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "api.dev.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "api.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "open-parse.stage.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "open-parse.dev.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "open-parse.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "pyannote.stage.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "pyannote.dev.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "pyannote.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "ffmpeg.stage.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "ffmpeg.dev.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "ffmpeg.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "webhook.stage.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "webhook.dev.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "webhook.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "live.stage.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "live.dev.divinci.app") or
  (not cf.tls_client_auth.cert_verified and http.host eq "live.divinci.app")
)