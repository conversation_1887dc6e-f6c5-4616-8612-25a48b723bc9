#!/bin/bash -x
set -e
set -o pipefail

: '
📓 Script to run GitHub Actions workflows locally using act. This script prepares the necessary environment variables, combines secret keys,
and runs the specified GitHub Actions workflow. Optional debugging flags are provided for enhanced functionality.

▶️ Usage:
  ./deploy/local-run-act.sh [options]

# Run normally
./deploy/local-run-act.sh

# Run with bind and reuse flags for debugging
./deploy/local-run-act.sh -b -r

🧾 Options:
  -b    Bind the working directory to the container instead of copying it. This allows changes inside the container to reflect in the local directory.
  -r    Reuse the container after the workflow has finished running, allowing you to inspect the container state and files.

🌎 Environment:
  This script assumes the presence of private keys and environment files in the "private-keys/<environment>" directory.
  By default, the environment is set to "develop".

🔗 Dependencies:
  - act: A tool for running GitHub Actions locally.
  - jq: A command-line JSON processor (used in the combine-secret-keys.sh script).
'

# More portable way to get script directory
get_script_dir() {
    local source="${BASH_SOURCE[0]}"
    local dir

    # Resolve $source until the file is no longer a symlink
    while [ -h "$source" ]; do
        dir="$( cd -P "$( dirname "$source" )" && pwd )"
        source="$(readlink "$source")"
        # If $source was a relative symlink, we need to resolve it relative to the path where the symlink file was located
        [[ $source != /* ]] && source="$dir/$source"
    done
    dir="$( cd -P "$( dirname "$source" )" && pwd )"
    echo "$dir"
}

SCRIPT_DIR="$(get_script_dir)"
ROOT_DIR=$(dirname "$SCRIPT_DIR")
PRIVATE_KEYS="$ROOT_DIR/private-keys"
HIDDEN_DIR="$ROOT_DIR/hidden"

echo "$SCRIPT_DIR"
echo "$HIDDEN_DIR"

ENVIRONMENT="develop"

GOOGLE_CREDENTIALS=$(cat "$PRIVATE_KEYS/$ENVIRONMENT/credentials/google-service-key.json")

# Create hidden directory if it doesn't exist
mkdir -p "$HIDDEN_DIR"

output_secrets_file="$HIDDEN_DIR/.deploy.combined-secrets.$ENVIRONMENT.env"

# Remove existing secrets file if it exists
if [ -f "$output_secrets_file" ]; then
    echo "🗑️  Removing existing secrets file"
    rm "$output_secrets_file"
fi

echo "🔐 Creating new secrets file"
bash "$SCRIPT_DIR/util/combine-secret-keys.sh" "$PRIVATE_KEYS/$ENVIRONMENT" "$output_secrets_file"

WORKFLOW_SCRIPT="$ROOT_DIR/.github/workflows/build-deploy-changed-services.yml"

# Default flags
ACT_FLAGS=""

# Check if running on Apple Silicon
if [[ $(uname -m) == 'arm64' ]]; then
    echo "📱 Detected Apple Silicon (M-series) chip, adding architecture flag"
    ACT_FLAGS+=" --container-architecture linux/amd64"
fi

# Check for optional debugging arguments
while getopts "br" opt; do
  case $opt in
    b) ACT_FLAGS+=" -b";;  # Add bind flag
    r) ACT_FLAGS+=" -r";;  # Add reuse flag
    *) echo "Invalid option: -$OPTARG" >&2; exit 1;;
  esac
done

# Run act with optional debugging flags
act -s GOOGLE_CREDENTIALS="$GOOGLE_CREDENTIALS" pull_request -W "$WORKFLOW_SCRIPT" --secret-file "$output_secrets_file" $ACT_FLAGS
