#!/bin/bash

# Divinci CLI Entry Point
# 
# This script is the main entry point for the Divinci CLI.
# It dispatches to the appropriate sub-command based on the first argument.

# Get the directory of this script
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")

# Check if a command was provided
if [ $# -eq 0 ]; then
  echo "Usage: divinci <command> [options]"
  echo ""
  echo "Commands:"
  echo "  deploy    Build and deploy services to Google Cloud Run"
  echo ""
  echo "For more information, run: divinci <command> --help"
  exit 1
fi

# Get the command
COMMAND=$1
shift

# Dispatch to the appropriate command
case $COMMAND in
  deploy)
    # Run the deploy command
    "$SCRIPT_DIR/cli/divinci-deploy.sh" "$@"
    ;;
  *)
    echo "Unknown command: $COMMAND"
    echo "Usage: divinci <command> [options]"
    echo ""
    echo "Commands:"
    echo "  deploy    Build and deploy services to Google Cloud Run"
    echo ""
    echo "For more information, run: divinci <command> --help"
    exit 1
    ;;
esac
